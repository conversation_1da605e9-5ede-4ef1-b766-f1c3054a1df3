plugins {
    id("java")
    alias(libs.plugins.lombok)
}

group = "cz.cuni.mff.d3s"
version = "1.0-SNAPSHOT"

repositories {
    mavenCentral()
}

dependencies {
    implementation(project(mapOf("path" to ":model-java")))
    implementation(project(mapOf("path" to ":instrumentor-common")))
    implementation(libs.log4j)
    implementation(libs.javatuples)
    testImplementation(platform(libs.junit5Bom))
    testImplementation(libs.junit5Jupiter)
}

tasks.test {
    useJUnitPlatform()
}
