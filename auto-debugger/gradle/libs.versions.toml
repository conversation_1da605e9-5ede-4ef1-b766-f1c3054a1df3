[versions]
# libraries
opentest4j = "1.3.0"
log4j = "2.23.1"
javatuples = "1.2"
junit5 = "5.13.2"
mockito = "5.18.0"
picocli = "4.7.6"

# plugins
changelog = "2.2.1"
intelliJPlatform = "2.6.1-SNAPSHOT"
kotlin = "2.1.20"
kover = "0.9.1"
qodana = "2024.3.4"
lombok = "8.14"

[libraries]
opentest4j = { group = "org.opentest4j", name = "opentest4j", version.ref = "opentest4j" }
log4j = { group = "org.apache.logging.log4j", name = "log4j-slf4j2-impl", version.ref = "log4j" }
javatuples = { group = "org.javatuples", name = "javatuples", version.ref = "javatuples" }
junit-core = { group = "junit", name = "junit", version.ref = "junit5" }
junit-bom = { group = "org.junit", name = "junit-bom", version.ref = "junit5" }
junit-jupiter = { group = "org.junit.jupiter", name = "junit-jupiter", version.ref = "junit5" }
mockito = { group = "org.mockito", name = "mockito-core", version.ref = "mockito" }
picocli = { group = "info.picocli", name = "picocli", version.ref = "picocli" }

[plugins]
changelog = { id = "org.jetbrains.changelog", version.ref = "changelog" }
intelliJPlatform = { id = "org.jetbrains.intellij.platform", version.ref = "intelliJPlatform" }
kotlin = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kover = { id = "org.jetbrains.kotlinx.kover", version.ref = "kover" }
qodana = { id = "org.jetbrains.qodana", version.ref = "qodana" }
lombok = { id = "io.freefair.lombok", version.ref = "lombok" }

[bundles]
junit = ["junit-core", "junit-jupiter"]