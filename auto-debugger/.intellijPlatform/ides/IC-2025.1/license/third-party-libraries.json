[{"name": "ASM (JetBrains's fork)", "url": "https://github.com/JetBrains/intellij-deps-asm", "version": "9.6.1", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://github.com/JetBrains/intellij-deps-asm/blob/master/LICENSE.txt"}, {"name": "AhoCorasickDoubleArrayTrie", "url": "https://github.com/hankcs/AhoCorasickDoubleArrayTrie", "version": "1.2.3", "license": "Apache 2.0", "licenseUrl": "https://github.com/hankcs/AhoCorasickDoubleArrayTrie/blob/master/README.md#license"}, {"name": "Amazon Ion Java", "url": "https://github.com/amazon-ion/ion-java", "version": "1.11.9", "license": "Apache 2.0", "licenseUrl": "https://github.com/amazon-ion/ion-java/blob/master/LICENSE"}, {"name": "Android libwebp library", "url": "https://github.com/webmproject/libwebp", "version": "custom revision", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://github.com/webmproject/libwebp/blob/main/COPYING"}, {"name": "Apache Ant", "url": "https://ant.apache.org/", "version": "1.9", "license": "Apache 2.0", "licenseUrl": "https://ant.apache.org/license.html"}, {"name": "Apache Axis", "url": "https://axis.apache.org/axis/", "version": "1.4", "license": "Apache 2.0", "licenseUrl": "https://svn.apache.org/viewvc/axis/axis1/java/trunk/LICENSE?view=markup"}, {"name": "Apache Commons CLI", "url": "https://commons.apache.org/proper/commons-cli/", "version": "1.9.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/commons-cli/blob/master/LICENSE.txt"}, {"name": "Apache Commons Codec", "url": "https://commons.apache.org/proper/commons-codec/", "version": "1.17.1", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/commons-codec/blob/master/LICENSE.txt"}, {"name": "Apache Commons Collections", "url": "https://commons.apache.org/proper/commons-collections/", "version": "3.2.2", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/commons-collections/blob/master/LICENSE.txt"}, {"name": "Apache Commons Compress", "url": "https://commons.apache.org/proper/commons-compress/", "version": "1.26.1", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/commons-compress/blob/master/LICENSE.txt"}, {"name": "Apache Commons Discovery", "url": "https://commons.apache.org/dormant/commons-discovery/", "version": "0.4", "license": "Apache 2.0", "licenseUrl": "https://commons.apache.org/dormant/commons-discovery/license.html"}, {"name": "Apache Commons HTTPClient", "url": "https://hc.apache.org/httpclient-3.x", "version": "3.1 (with patch by JetBrains)", "license": "Apache 2.0", "licenseUrl": "https://svn.apache.org/viewvc/httpcomponents/oac.hc3x/trunk/LICENSE.txt?view=markup"}, {"name": "Apache Commons IO", "url": "https://commons.apache.org/proper/commons-io/", "version": "2.16.1", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/commons-io/blob/master/LICENSE.txt"}, {"name": "Apache Commons Imaging (JetBrains's fork)", "url": "https://github.com/JetBrains/intellij-deps-commons-imaging", "version": "1.0-RC-1", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/intellij-deps-commons-imaging/blob/master/LICENSE.txt"}, {"name": "Apache Commons Lang", "url": "https://commons.apache.org/proper/commons-lang/", "version": "3.17.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/commons-lang/blob/master/LICENSE.txt"}, {"name": "Apache Commons Logging", "url": "https://commons.apache.org/proper/commons-logging/", "version": "1.2", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/commons-logging/blob/master/LICENSE.txt"}, {"name": "Apache Commons Text", "url": "https://github.com/apache/commons-text", "version": "1.13.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/commons-text/blob/master/LICENSE.txt"}, {"name": "Apache Lucene", "url": "https://lucene.apache.org/java", "version": "9.12.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/lucene/blob/main/LICENSE.txt"}, {"name": "AsciiDoc support for Visual Studio Code", "url": "https://github.com/asciidoctor/asciidoctor-vscode", "version": "3.2.4", "license": "MIT", "licenseUrl": "https://github.com/asciidoctor/asciidoctor-vscode/blob/master/README.md"}, {"name": "AssertJ Swing", "url": "https://github.com/assertj/assertj-swing", "version": "3.17.1", "license": "Apache 2.0", "licenseUrl": "https://github.com/assertj/assertj-swing/blob/main/licence-header.txt"}, {"name": "AssertJ fluent assertions", "url": "https://github.com/assertj/assertj-core", "version": "3.26.3", "license": "Apache 2.0", "licenseUrl": "https://github.com/assertj/assertj-core/blob/main/LICENSE.txt"}, {"name": "Atlassian Commonmark", "url": "https://github.com/commonmark/commonmark-java", "version": "0.17.0", "license": "BSD 2-<PERSON><PERSON>", "licenseUrl": "https://github.com/commonmark/commonmark-java/blob/main/LICENSE.txt"}, {"name": "Automaton", "url": "https://www.brics.dk/automaton/", "version": "1.12-4", "license": "BSD 2-<PERSON><PERSON>", "licenseUrl": "https://github.com/cs-au-dk/dk.brics.automaton/blob/master/COPYING"}, {"name": "Bash-Preexec", "url": "https://github.com/rcaloras/bash-preexec", "version": "0.5.0", "license": "MIT", "licenseUrl": "https://github.com/rcaloras/bash-preexec/blob/master/LICENSE.md"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/airbnb/lottie-web", "version": "5.5.10", "license": "MIT", "licenseUrl": "https://github.com/airbnb/lottie-web/blob/master/LICENSE.md"}, {"name": "CGLib", "url": "https://github.com/cglib/cglib/", "version": "3.3.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/cglib/cglib/blob/master/LICENSE"}, {"name": "CMake For VisualStudio Code", "url": "https://github.com/twxs/vs.language.cmake", "version": "0.0.17", "license": "MIT", "licenseUrl": "https://github.com/twxs/vs.language.cmake/blob/master/LICENSE"}, {"name": "Clikt", "url": "https://github.com/ajalt/clikt", "version": "3.5.4", "license": "Apache 2.0", "licenseUrl": "https://github.com/ajalt/clikt/blob/master/LICENSE.txt"}, {"name": "Command Line Interface Parser for Java", "url": "https://github.com/spullara/cli-parser?tab=readme-ov-file", "version": "1.1.6", "license": "Apache 2.0", "licenseUrl": "https://github.com/spullara/cli-parser/blob/95edeb2d1a21fb13760b4f96f976a7f3108e0942/README.md?plain=1#L65"}, {"name": "Common Annotations for the JavaTM Platform API", "url": "https://github.com/javaee/javax.annotation", "version": "1.3.2", "license": "CDDL 1.1", "licenseUrl": "https://github.com/javaee/javax.annotation/blob/master/LICENSE"}, {"name": "Compose Multiplatform", "url": "https://github.com/JetBrains/compose-multiplatform", "version": "1.7.1", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/compose-multiplatform/blob/master/LICENSE.txt"}, {"name": "Eclipse JDT Core", "url": "https://www.eclipse.org/jdt/core/index.php", "version": "4.2.1", "license": "EPL 2.0", "licenseUrl": "https://github.com/eclipse-jdt/eclipse.jdt.core/blob/master/LICENSE"}, {"name": "EditorConfig Java Parser", "url": "https://github.com/ec4j/ec4j", "version": "0.3.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/ec4j/ec4j/blob/master/LICENSE"}, {"name": "FiraCode", "url": "https://github.com/tonsky/FiraCode", "version": "1.206", "license": "OFL", "licenseUrl": "https://github.com/tonsky/FiraCode/blob/master/LICENSE"}, {"name": "Flexmark", "url": "https://github.com/vsch/flexmark-java/", "version": "0.64.8", "license": "BSD 2-<PERSON><PERSON>", "licenseUrl": "https://github.com/vsch/flexmark-java/blob/master/LICENSE.txt"}, {"name": "FreeMarker", "url": "https://freemarker.apache.org", "version": "2.3.30", "license": "Apache 2.0", "licenseUrl": "https://freemarker.apache.org/docs/app_license.html"}, {"name": "GGDSL Utilities", "url": "https://github.com/Kotlin/ggdsl", "version": "0.4.0-dev-10", "license": "Apache 2.0", "licenseUrl": "https://github.com/Kotlin/ggdsl/blob/main/LICENSE"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://gradle.org/", "version": "8.12", "license": "Apache 2.0", "licenseUrl": "https://github.com/gradle/gradle/blob/master/LICENSE"}, {"name": "Groovy", "url": "https://groovy-lang.org/", "version": "3.0.19", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/groovy/blob/master/LICENSE"}, {"name": "Groovy JSON", "url": "https://groovy-lang.org/", "version": "3.0.19", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/groovy/blob/master/LICENSE"}, {"name": "Groovy JSR-223", "url": "https://groovy-lang.org/", "version": "3.0.19", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/groovy/blob/master/LICENSE"}, {"name": "Groovy Templates", "url": "https://groovy-lang.org/", "version": "3.0.19", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/groovy/blob/master/LICENSE"}, {"name": "Groovy XML", "url": "https://groovy-lang.org/", "version": "3.0.19", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/groovy/blob/master/LICENSE"}, {"name": "Gson", "url": "https://github.com/google/gson", "version": "2.12.1", "license": "Apache 2.0", "licenseUrl": "https://github.com/google/gson/blob/master/LICENSE"}, {"name": "Guava", "url": "https://github.com/google/guava", "version": "33.4.0-jre", "license": "Apache 2.0", "licenseUrl": "https://github.com/google/guava/raw/master/LICENSE"}, {"name": "HDR Histogram", "url": "https://github.com/HdrHistogram/HdrHistogram", "version": "2.2.2", "license": "Public Domain (CC0)", "licenseUrl": "https://github.com/HdrHistogram/HdrHistogram/blob/master/LICENSE.txt"}, {"name": "HashiCorp Syntax", "url": "https://github.com/asciidoctor/asciidoctor-vscode", "version": "0.6.0", "license": "MPL 2.0", "licenseUrl": "https://github.com/hashicorp/syntax/blob/main/LICENSE"}, {"name": "HttpComponents HttpClient", "url": "https://github.com/apache/httpcomponents-client/", "version": "4.5.14", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/httpcomponents-client/blob/master/LICENSE.txt"}, {"name": "HttpComponents HttpClient Fluent API", "url": "https://github.com/apache/httpcomponents-client/", "version": "4.5.13", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/httpcomponents-client/blob/master/LICENSE.txt"}, {"name": "ICU4J", "url": "https://icu.unicode.org/", "version": "73.2", "license": "Unicode", "licenseUrl": "https://www.unicode.org/copyright.html"}, {"name": "ISO RELAX", "url": "https://sourceforge.net/projects/iso-relax/", "version": "20030108", "license": "MIT", "licenseUrl": "https://sourceforge.net/projects/iso-relax/"}, {"name": "Ice4j", "url": "https://github.com/jitsi/ice4j", "version": "3.0-54-g2baae57", "license": "Apache 2.0", "licenseUrl": "https://github.com/jitsi/ice4j/blob/master/LICENSE"}, {"name": "Inconsolata", "url": "https://github.com/google/fonts/tree/main/ofl/inconsolata", "version": "001.010", "license": "OFL", "licenseUrl": "https://github.com/google/fonts/blob/master/ofl/inconsolata/OFL.txt"}, {"name": "Incremental DOM", "url": "https://github.com/google/incremental-dom", "version": "0.7.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/google/incremental-dom/blob/master/LICENSE"}, {"name": "IntelliJ IDEA Code Coverage Agent", "url": "https://github.com/jetbrains/intellij-coverage", "version": "1.0.766", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/intellij-coverage/blob/master/LICENSE"}, {"name": "IntelliJ IDEA Test Discovery Agent", "url": "https://github.com/JetBrains/intellij-coverage/tree/master/test-discovery", "version": "1.0.763", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/intellij-coverage/blob/master/LICENSE"}, {"name": "JAXB (JSR 222) Reference Implementation", "url": "https://github.com/javaee/jaxb-v2", "version": "2.3.9", "license": "CDDL 1.1", "licenseUrl": "https://github.com/javaee/jaxb-v2/blob/master/LICENSE"}, {"name": "JAXB (Java Architecture for XML Binding) API", "url": "https://github.com/javaee/jaxb-spec", "version": "2.3.1", "license": "CDDL 1.1", "licenseUrl": "https://github.com/javaee/jaxb-spec/blob/master/LICENSE.txt"}, {"name": "JCEF", "url": "https://bitbucket.org/chromiumembedded/java-cef", "version": "122.1.9-gd14e051-chromium-122.0.6261.94-api-1.18-251-b27", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://bitbucket.org/chromiumembedded/java-cef/src/master/LICENSE.txt"}, {"name": "JCIP Annotations", "url": "https://jcip.net", "version": "1.0", "license": "Creative Commons 2.5 Attribution", "licenseUrl": "https://creativecommons.org/licenses/by/2.5"}, {"name": "JCodings", "url": "https://github.com/jruby/jcodings", "version": "1.0.55", "license": "MIT", "licenseUrl": "https://github.com/jruby/jcodings/blob/master/LICENSE.txt"}, {"name": "JDOM (JetBrains's fork)", "url": "https://github.com/JetBrains/intellij-deps-jdom/", "version": "2", "license": "JDOM License", "licenseUrl": "https://github.com/JetBrains/intellij-deps-jdom/blob/master/LICENSE.txt"}, {"name": "JGit (Settings Sync and SettingsRepo)", "url": "https://www.eclipse.org/jgit/", "version": "6.6.1.202309021850-r-jb-202407181518", "license": "Eclipse Distribution License 1.0", "licenseUrl": "https://www.eclipse.org/org/documents/edl-v10.php"}, {"name": "JGoodies Common", "url": "https://www.jgoodies.com/freeware/libraries/looks/", "version": "1.4.0", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://opensource.org/licenses/BSD-3-Clause"}, {"name": "JGoodies Forms", "url": "https://www.jgoodies.com/freeware/libraries/forms/", "version": "1.1-preview", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://opensource.org/licenses/BSD-3-Clause"}, {"name": "JGraphT", "url": "https://sourceforge.net/projects/jgrapht/", "version": "1.5.1", "license": "EPL 2.0", "licenseUrl": "http://www.eclipse.org/legal/epl-v20.html"}, {"name": "JNA", "url": "https://github.com/java-native-access/jna", "version": "5.14.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/java-native-access/jna/blob/master/LICENSE"}, {"name": "JSO<PERSON> (schema.json)", "url": "https://json-schema.org/draft-04/schema#", "version": "draft-04", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://github.com/json-schema-org/json-schema-spec/blob/main/LICENSE"}, {"name": "JSON Schema (schema06.json)", "url": "https://json-schema.org/draft-06/schema#", "version": "draft-06", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://github.com/json-schema-org/json-schema-spec/blob/main/LICENSE"}, {"name": "JSON Schema (schema07.json)", "url": "https://json-schema.org/draft-07/schema#", "version": "draft-07", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://github.com/json-schema-org/json-schema-spec/blob/main/LICENSE"}, {"name": "JSON in Java", "url": "https://github.com/stleary/JSON-java", "version": "20240205", "license": "JSON License", "licenseUrl": "https://www.json.org/license.html"}, {"name": "JSTUN", "url": "https://github.com/tking/JSTUN", "version": "0.7.5.20200723", "license": "Apache 2.0", "licenseUrl": "https://github.com/tking/JSTUN/blob/master/apache-license-2.0.txt"}, {"name": "JUnit4", "url": "https://junit.org/junit4/", "version": "4.13.2", "license": "EPL 1.0", "licenseUrl": "https://junit.org/junit4/license.html"}, {"name": "JUnit5", "url": "https://junit.org/junit5/", "version": "5.11.3", "license": "EPL 2.0", "licenseUrl": "https://github.com/junit-team/junit5/blob/main/LICENSE.md"}, {"name": "JUnit5Jupiter", "url": "https://junit.org/junit5/", "version": "5.11.3", "license": "EPL 2.0", "licenseUrl": "https://github.com/junit-team/junit5/blob/main/LICENSE.md"}, {"name": "JaCoCo", "url": "https://www.eclemma.org/jacoco/", "version": "0.8.12", "license": "EPL 1.0", "licenseUrl": "https://www.jacoco.org/jacoco/trunk/doc/license.html"}, {"name": "<PERSON>", "url": "https://github.com/FasterXML/jackson", "version": "2.17.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/FasterXML/jackson-core/blob/2.14/LICENSE"}, {"name": "<PERSON>", "url": "https://github.com/FasterXML/jackson-databind", "version": "2.17.2", "license": "Apache 2.0", "licenseUrl": "https://github.com/FasterXML/jackson-databind/blob/2.16/LICENSE"}, {"name": "Jackson Dataformat CBOR", "url": "https://github.com/FasterXML/jackson-dataformats-binary", "version": "2.16.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/FasterXML/jackson-dataformats-binary/blob/2.14/pom.xml"}, {"name": "Jackson Dataformat TOML", "url": "https://github.com/FasterXML/jackson-dataformats-text", "version": "2.17.2", "license": "Apache 2.0", "licenseUrl": "https://github.com/FasterXML/jackson-dataformats-text/blob/2.16/pom.xml"}, {"name": "Jackson Dataformat YAML", "url": "https://github.com/FasterXML/jackson-dataformats-text", "version": "2.17.2", "license": "Apache 2.0", "licenseUrl": "https://github.com/FasterXML/jackson-dataformats-text/blob/2.16/pom.xml"}, {"name": "<PERSON>", "url": "https://github.com/FasterXML/jackson-module-kotlin", "version": "2.17.2", "license": "Apache 2.0", "licenseUrl": "https://github.com/FasterXML/jackson-module-kotlin/blob/2.16/LICENSE"}, {"name": "Jakarta ORO", "url": "https://jakarta.apache.org/oro/", "version": "2.0.8", "license": "Apache 2.0", "licenseUrl": "https://svn.apache.org/repos/asf/jakarta/oro/trunk/LICENSE"}, {"name": "Java Compatibility", "url": "https://github.com/JetBrains/intellij-deps-java-compatibility", "version": "1.0.1", "license": "GPL 2.0 + Classpath", "licenseUrl": "https://github.com/JetBrains/intellij-deps-java-compatibility/raw/master/LICENSE"}, {"name": "Java Server Pages (JSP) for Visual Studio Code", "url": "https://github.com/pthorsson/vscode-jsp", "version": "0.0.3", "license": "MIT", "licenseUrl": "https://github.com/pthorsson/vscode-jsp/blob/master/LICENSE"}, {"name": "Java String Similarity", "url": "https://github.com/tdebatty/java-string-similarity", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/tdebatty/java-string-similarity/blob/master/LICENSE.md"}, {"name": "Java-WebSocket", "url": "https://github.com/TooTallNate/Java-WebSocket", "version": "1.6.0", "license": "MIT", "licenseUrl": "https://github.com/TooTallNate/Java-WebSocket/blob/master/LICENSE"}, {"name": "JavaBeans Activation Framework", "url": "https://github.com/javaee/activation", "version": "1.2.0", "license": "CDDL 1.1", "licenseUrl": "https://github.com/javaee/activation/blob/master/LICENSE.txt"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/jaxen-xpath/jaxen", "version": "1.2.0", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jaxen-xpath/jaxen/blob/master/LICENSE.txt"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/json-path/JsonPath", "version": "2.9.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/json-path/JsonPath/blob/master/LICENSE"}, {"name": "JetBrains Annotations", "url": "https://github.com/JetBrains/java-annotations", "version": "24.0.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/java-annotations/blob/master/LICENSE.txt"}, {"name": "JetBrains Annotations for Java 5", "url": "https://github.com/JetBrains/java-annotations", "version": "24.0.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/java-annotations/blob/master/LICENSE.txt"}, {"name": "JetBrains Runtime", "url": "https://github.com/JetBrains/JetBrainsRuntime", "version": "21", "license": "GPL 2.0 + Classpath", "licenseUrl": "https://github.com/JetBrains/JetBrainsRuntime/blob/master/LICENSE"}, {"name": "JetBrains Runtime API", "url": "https://github.com/JetBrains/JetBrainsRuntime", "version": "1.2.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/JetBrainsRuntime/blob/main/LICENSE"}, {"name": "<PERSON>", "url": "https://relaxng.org/jclark/jing.html", "version": "20030619", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://opensource.org/license/bsd-3-clause/"}, {"name": "Jitsi Utilities", "url": "https://github.com/jitsi/jitsi-utils", "version": "1.0-112-gd92472a", "license": "Apache 2.0", "licenseUrl": "https://github.com/jitsi/jitsi-utils/blob/master/LICENSE"}, {"name": "Jline", "url": "https://github.com/jline/jline3", "version": "3.24.1", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jline/jline3/blob/master/LICENSE.txt"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/jruby/joni", "version": "2.2.3", "license": "MIT", "licenseUrl": "https://github.com/jruby/joni/blob/master/LICENSE"}, {"name": "Jupyter dialect of Kotlin compiler and preprocessor", "url": "https://github.com/Kotlin/kotlin-jupyter", "version": "0.12.0-385", "license": "Apache 2.0", "licenseUrl": "https://github.com/Kotlin/kotlin-jupyter/blob/master/LICENSE"}, {"name": "Kconfig for the Zephyr Project", "url": "https://github.com/trond-snekvik/vscode-kconfig", "version": "1.2.0", "license": "MIT", "licenseUrl": "https://github.com/trond-snekvik/vscode-kconfig/blob/master/LICENSE"}, {"name": "<PERSON><PERSON>in Coroutines for Guava", "url": "https://github.com/Kotlin/kotlinx.coroutines", "version": "1.8.0-intellij-13", "license": "Apache 2.0", "licenseUrl": "https://github.com/Kotlin/kotlinx.coroutines/blob/master/LICENSE.txt"}, {"name": "Kotlin Coroutines for JDK 8", "url": "https://github.com/Kotlin/kotlinx.coroutines", "version": "1.8.0-intellij-13", "license": "Apache 2.0", "licenseUrl": "https://github.com/Kotlin/kotlinx.coroutines/blob/master/LICENSE.txt"}, {"name": "Kotlin Coroutines for Slf4j", "url": "https://github.com/Kotlin/kotlinx.coroutines", "version": "1.8.0-intellij-13", "license": "Apache 2.0", "licenseUrl": "https://github.com/Kotlin/kotlinx.coroutines/blob/master/LICENSE.txt"}, {"name": "Kotlin Standard Library", "url": "https://github.com/JetBrains/kotlin", "version": "2.1.10", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/kotlin/blob/master/license/LICENSE.txt"}, {"name": "Kotlin multiplatform / multi-format serialization", "url": "https://github.com/Kotlin/kotlinx.serialization", "version": "1.7.3", "license": "Apache 2.0", "licenseUrl": "https://github.com/Kotlin/kotlinx.serialization/blob/master/LICENSE.txt"}, {"name": "Kotlin multiplatform / multi-format serialization", "url": "https://github.com/Kotlin/kotlinx.serialization", "version": "1.7.3", "license": "Apache 2.0", "licenseUrl": "https://github.com/Kotlin/kotlinx.serialization/blob/master/LICENSE.txt"}, {"name": "Kotlin multiplatform / multi-format serialization", "url": "https://github.com/Kotlin/kotlinx.serialization", "version": "1.7.3", "license": "Apache 2.0", "licenseUrl": "https://github.com/Kotlin/kotlinx.serialization/blob/master/LICENSE.txt"}, {"name": "Kotlin multiplatform / multi-format serialization", "url": "https://github.com/Kotlin/kotlinx.serialization", "version": "1.7.3", "license": "Apache 2.0", "licenseUrl": "https://github.com/Kotlin/kotlinx.serialization/blob/master/LICENSE.txt"}, {"name": "Kotlin reflection library", "url": "https://github.com/JetBrains/kotlin", "version": "2.1.10", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/kotlin/blob/master/license/LICENSE.txt"}, {"name": "Kryo5", "url": "https://github.com/EsotericSoftware/kryo", "version": "5.6.0", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://github.com/EsotericSoftware/kryo/blob/master/LICENSE.md"}, {"name": "Ktor Client Core", "url": "https://github.com/ktorio/ktor/tree/main/ktor-client/ktor-client-core", "version": "3.0.3", "license": "Apache 2.0", "licenseUrl": "https://github.com/ktorio/ktor/blob/main/LICENSE"}, {"name": "Ktor Client OkHttp", "url": "https://github.com/ktorio/ktor/tree/main/ktor-client/ktor-client-okhttp", "version": "3.0.3", "license": "Apache 2.0", "licenseUrl": "https://github.com/ktorio/ktor/blob/main/LICENSE"}, {"name": "Language Tool (JetBrains's fork)", "url": "https://github.com/JetBrains/languagetool", "version": "6.5.0.12", "license": "LGPL 2.1", "licenseUrl": "https://github.com/JetBrains/languagetool/blob/master/COPYING.txt"}, {"name": "Language Tool (JetBrains's fork, English)", "url": "https://github.com/JetBrains/languagetool", "version": "6.5.0.12", "license": "LGPL 2.1", "licenseUrl": "https://github.com/JetBrains/languagetool/blob/master/COPYING.txt"}, {"name": "Log4j", "url": "https://www.slf4j.org/legacy.html#log4j-over-slf4j", "version": "1.7.36", "license": "Apache 2.0", "licenseUrl": "https://github.com/qos-ch/slf4j/blob/master/log4j-over-slf4j/LICENSE.txt"}, {"name": "MDX for Visual Studio Code", "url": "https://github.com/mdx-js/mdx-analyzer/tree/main/packages/vscode-mdx", "version": "1.8.7", "license": "MIT", "licenseUrl": "https://github.com/mdx-js/mdx-analyzer/blob/main/packages/vscode-mdx/LICENSE"}, {"name": "<PERSON>ven Resolver Provider", "url": "https://maven.apache.org/ref/3.6.1/maven-resolver-provider/", "version": "3.9.9", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/maven-resolver/blob/master/LICENSE"}, {"name": "Maven archetype catalog", "url": "https://maven.apache.org/archetype/archetype-common/index.html", "version": "3.2.1", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/maven-archetype"}, {"name": "Maven archetype common", "url": "https://maven.apache.org/archetype/archetype-common/index.html", "version": "3.2.1", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/maven-archetype"}, {"name": "Maven core", "url": "https://maven.apache.org/ref/3.8.6/maven-core/", "version": "3.8.3", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/maven/blob/master/LICENSE"}, {"name": "<PERSON>ven indexer", "url": "https://maven.apache.org/maven-indexer/indexer-core/index.html", "version": "2023.2", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/maven-indexer"}, {"name": "Maven wagon provider api", "url": "https://maven.apache.org/wagon/wagon-provider-api/index.html", "version": "3.5.2", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/maven-wagon"}, {"name": "Maven3", "url": "https://maven.apache.org/", "version": "3.6.1", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/maven/blob/master/LICENSE"}, {"name": "MigLayout", "url": "https://github.com/mikaelgrev/miglayout/", "version": "11.4", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://github.com/mikaelgrev/miglayout/blob/master/src/site/resources/docs/license.txt"}, {"name": "NanoXML", "url": "https://central.sonatype.com/artifact/be.cyberelf.nanoxml/nanoxml/2.2.3", "version": "2.2.3", "license": "zlib/libpng", "licenseUrl": "https://github.com/saulhidalgoaular/nanoxml/raw/master/LICENSE.txt"}, {"name": "OkHttp", "url": "https://square.github.io/okhttp/", "version": "5.0.0-alpha.14", "license": "Apache 2.0", "licenseUrl": "https://square.github.io/okhttp/#license"}, {"name": "Package Search API-Client", "url": "https://github.com/JetBrains/package-search-api-models", "version": "3.4.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/package-search-api-models/blob/master/LICENSE"}, {"name": "Plexus Utils", "url": "https://github.com/codehaus-plexus/plexus-utils", "version": "3.5.1", "license": "Apache 2.0", "licenseUrl": "https://github.com/codehaus-plexus/plexus-utils/blob/master/LICENSE.txt"}, {"name": "Protocol Buffers", "url": "https://developers.google.com/protocol-buffers", "version": "3.24.4-jb.2", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://github.com/google/protobuf/blob/master/LICENSE"}, {"name": "Proxy Vole", "url": "https://github.com/akuhtz/proxy-vole", "version": "1.1.6", "license": "Apache 2.0", "licenseUrl": "https://github.com/akuhtz/proxy-vole/blob/master/LICENSE.md"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/cloudflare/quiche", "version": "90", "license": "BSD 2-<PERSON><PERSON>", "licenseUrl": "https://github.com/cloudflare/quiche/blob/master/COPYING"}, {"name": "Relax NG Object Model", "url": "https://github.com/kohsuke/rngom", "version": "custom revision", "license": "MIT", "licenseUrl": "https://github.com/kohsuke/rngom/blob/master/licenceheader.txt"}, {"name": "Rhino JavaScript Engine", "url": "https://github.com/mozilla/rhino", "version": "1.7.15", "license": "MPL 2.0", "licenseUrl": "https://github.com/mozilla/rhino/blob/master/LICENSE.txt"}, {"name": "Roboto", "url": "https://github.com/googlefonts/roboto", "version": "1.100141", "license": "Apache 2.0", "licenseUrl": "https://github.com/google/roboto/blob/master/LICENSE"}, {"name": "SSHJ", "url": "https://github.com/hierynomus/sshj", "version": "0.38.0-idea2", "license": "Apache 2.0", "licenseUrl": "https://github.com/hierynomus/sshj/blob/master/LICENSE"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/JetBrains/skiko/", "version": "0.8.18", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/skiko/blob/master/LICENSE"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/JetBrains/skiko/", "version": "0.8.18", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/skiko/blob/master/LICENSE"}, {"name": "SnakeYAML", "url": "https://bitbucket.org/snakeyaml/snakeyaml/", "version": "2.3", "license": "Apache 2.0", "licenseUrl": "https://bitbucket.org/snakeyaml/snakeyaml/src/master/LICENSE.txt"}, {"name": "Sonatype Nexus: Indexer", "url": "https://central.sonatype.com/artifact/org.sonatype.nexus/nexus-indexer", "version": "3.0.4", "license": "EPL 1.0", "licenseUrl": "https://central.sonatype.com/artifact/org.sonatype.nexus/nexus-indexer"}, {"name": "SourceCodePro", "url": "https://github.com/adobe-fonts/source-code-pro", "version": "2.010", "license": "OFL", "licenseUrl": "https://github.com/adobe-fonts/source-code-pro/blob/master/LICENSE.md"}, {"name": "Squareup Okio", "url": "https://github.com/square/okio", "version": "3.9.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/square/okio/blob/master/LICENSE.txt"}, {"name": "Squareup Wire", "url": "https://github.com/square/wire", "version": "4.9.6", "license": "Apache 2.0", "licenseUrl": "https://github.com/square/wire/blob/master/LICENSE.txt"}, {"name": "StreamEx", "url": "https://github.com/amaembo/streamex", "version": "0.8.2", "license": "Apache 2.0", "licenseUrl": "https://github.com/amaembo/streamex/blob/master/LICENSE"}, {"name": "TLS Channel", "url": "https://github.com/marianobarrios/tls-channel", "version": "0.8.0", "license": "MIT", "licenseUrl": "https://github.com/marianobarrios/tls-channel/blob/master/LICENSE.txt"}, {"name": "TestNG", "url": "https://testng.org/", "version": "7.8.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/cbeust/testng/blob/master/LICENSE.txt"}, {"name": "The Erlang LS extension for VSCode", "url": "https://github.com/mblode/vscode-twig-language-2", "version": "0.0.43", "license": "Apache 2.0", "licenseUrl": "https://github.com/erlang-ls/vscode/blob/main/LICENSE.md"}, {"name": "<PERSON><PERSON>", "url": "https://relaxng.org/jclark/trang.html", "version": "custom revision", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://opensource.org/license/bsd-3-clause/"}, {"name": "Trove4j (JetBrains's fork)", "url": "https://github.com/JetBrains/intellij-deps-trove4j", "version": "1.0.20221201", "license": "LGPL 2.1", "licenseUrl": "https://github.com/JetBrains/intellij-deps-trove4j/blob/master/LICENSE.txt"}, {"name": "VS Code Twig Language 2", "url": "https://github.com/mblode/vscode-twig-language-2", "version": "0.9.4", "license": "MIT", "licenseUrl": "https://github.com/mblode/vscode-twig-language-2/blob/master/LICENSE.md"}, {"name": "Velocity", "url": "https://velocity.apache.org/", "version": "2.3", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/velocity-engine/blob/master/LICENSE"}, {"name": "<PERSON><PERSON>t language support for Atom", "url": "https://github.com/AlexPl292/language-viml", "version": "1.2.1", "license": "MIT", "licenseUrl": "https://github.com/AlexPl292/language-viml/blob/master/LICENSE.txt"}, {"name": "Visual Studio Code", "url": "https://github.com/Microsoft/vscode/", "version": "1.90.0", "license": "MIT", "licenseUrl": "https://github.com/Microsoft/vscode-react-native/blob/master/LICENSE.txt"}, {"name": "XMLBeans", "url": "https://xmlbeans.apache.org/", "version": "5.1.1", "license": "Apache 2.0", "licenseUrl": "https://svn.jetbrains.org/idea/Trunk/bundled/WebServices/resources/lib/xmlbeans-2.3.0/xmlbeans.LICENSE"}, {"name": "XStream", "url": "https://x-stream.github.io/", "version": "1.4.20", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://x-stream.github.io/license.html"}, {"name": "XZ for Java", "url": "https://tukaani.org/xz/java.html", "version": "1.10", "license": "Public Domain (CC0)", "licenseUrl": "https://git.tukaani.org/?p=xz-java.git;a=blob;f=COPYING;h=8dd17645c4610c3d5eed9bcdd2699ecfac00406b;hb=refs/heads/master"}, {"name": "Xerces", "url": "https://xerces.apache.org/xerces2-j/", "version": "2.12.2", "license": "Apache 2.0", "licenseUrl": "https://svn.apache.org/repos/asf/xerces/java/trunk/LICENSE"}, {"name": "Xerial SQLite JDBC", "url": "https://github.com/xerial/sqlite-jdbc", "version": "3.42.0-jb.1", "license": "Apache 2.0", "licenseUrl": "https://github.com/xerial/sqlite-jdbc/blob/master/LICENSE"}, {"name": "Xerial SQLite JDBC", "url": "https://github.com/xerial/sqlite-jdbc", "version": "3.48.0.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/xerial/sqlite-jdbc/blob/master/LICENSE"}, {"name": "XmlRPC", "url": "https://ws.apache.org/xmlrpc/xmlrpc2/", "version": "2.0.1", "license": "Apache 2.0", "licenseUrl": "https://ws.apache.org/xmlrpc/xmlrpc2/license.html"}, {"name": "aalto-xml", "url": "https://github.com/FasterXML/aalto-xml/", "version": "1.3.3", "license": "Apache 2.0", "licenseUrl": "https://github.com/FasterXML/aalto-xml/blob/master/LICENSE"}, {"name": "abab", "url": "https://github.com/jsdom/abab", "version": "2.0.6", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jsdom/abab/LICENSE.md"}, {"name": "abbrev", "url": "https://github.com/isaacs/abbrev-js", "version": "1.1.1", "license": "ISC", "licenseUrl": "https://github.com/isaacs/abbrev-js/LICENSE"}, {"name": "accepts", "url": "https://github.com/jshttp/accepts", "version": "1.3.8", "license": "MIT", "licenseUrl": "https://github.com/jshttp/accepts/LICENSE"}, {"name": "acorn", "url": "https://github.com/acornjs/acorn", "version": "8.7.1", "license": "MIT", "licenseUrl": "https://github.com/acornjs/acorn/LICENSE"}, {"name": "acorn", "url": "https://github.com/acornjs/acorn", "version": "6.4.2", "license": "MIT", "licenseUrl": "https://github.com/acornjs/acorn/LICENSE"}, {"name": "acorn", "url": "https://github.com/acornjs/acorn", "version": "7.4.1", "license": "MIT", "licenseUrl": "https://github.com/acornjs/acorn/LICENSE"}, {"name": "acorn-dynamic-import", "url": "https://github.com/kesne/acorn-dynamic-import", "version": "4.0.0", "license": "MIT", "licenseUrl": "https://github.com/kesne/acorn-dynamic-import/LICENSE"}, {"name": "acorn-import-assertions", "url": "https://github.com/xtuc/acorn-import-assertions", "version": "1.8.0", "license": "MIT", "licenseUrl": "https://github.com/xtuc/acorn-import-assertions/README.md"}, {"name": "acorn-jsx", "url": "https://github.com/acornjs/acorn-jsx", "version": "5.3.2", "license": "MIT", "licenseUrl": "https://github.com/acornjs/acorn-jsx/LICENSE"}, {"name": "acorn-walk", "url": "https://github.com/acornjs/acorn", "version": "8.2.0", "license": "MIT", "licenseUrl": "https://github.com/acornjs/acorn/LICENSE"}, {"name": "adjust-sourcemap-loader", "url": "https://github.com/bholloway/adjust-sourcemap-loader", "version": "4.0.0", "license": "MIT", "licenseUrl": "https://github.com/bholloway/adjust-sourcemap-loader/LICENSE"}, {"name": "agent-base", "url": "https://github.com/TooTallNate/node-agent-base", "version": "6.0.2", "license": "MIT", "licenseUrl": "https://github.com/TooTallNate/node-agent-base/README.md"}, {"name": "agentkeepalive", "url": "https://github.com/node-modules/agentkeepalive", "version": "4.2.1", "license": "MIT", "licenseUrl": "https://github.com/node-modules/agentkeepalive/LICENSE"}, {"name": "aggregate-error", "url": "https://github.com/sindresorhus/aggregate-error", "version": "3.1.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/aggregate-error/license"}, {"name": "ajv", "url": "https://github.com/ajv-validator/ajv", "version": "8.9.0", "license": "MIT", "licenseUrl": "https://github.com/ajv-validator/ajv/LICENSE"}, {"name": "ajv", "url": "https://github.com/ajv-validator/ajv", "version": "6.12.6", "license": "MIT", "licenseUrl": "https://github.com/ajv-validator/ajv/LICENSE"}, {"name": "ajv", "url": "https://github.com/ajv-validator/ajv", "version": "8.13.0", "license": "MIT", "licenseUrl": "https://github.com/ajv-validator/ajv/LICENSE"}, {"name": "ajv-formats", "url": "https://github.com/ajv-validator/ajv-formats", "version": "2.1.1", "license": "MIT", "licenseUrl": "https://github.com/ajv-validator/ajv-formats/LICENSE"}, {"name": "ajv-keywords", "url": "https://github.com/epoberezkin/ajv-keywords", "version": "5.1.0", "license": "MIT", "licenseUrl": "https://github.com/epoberezkin/ajv-keywords/LICENSE"}, {"name": "ajv-keywords", "url": "https://github.com/epoberezkin/ajv-keywords", "version": "3.5.2", "license": "MIT", "licenseUrl": "https://github.com/epoberezkin/ajv-keywords/LICENSE"}, {"name": "alphanum-sort", "url": "https://github.com/TrySound/alphanum-sort", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/TrySound/alphanum-sort/LICENSE"}, {"name": "ampproject/remapping", "url": "https://github.com/ampproject/remapping", "version": "2.2.0", "license": "Apache-2.0", "licenseUrl": "https://github.com/ampproject/remapping/LICENSE"}, {"name": "angular-builders/custom-webpack", "url": "https://github.com/just-jeb/angular-builders", "version": "13.1.0", "license": "MIT", "licenseUrl": "https://github.com/just-jeb/angular-builders/LICENSE"}, {"name": "angular-devkit/architect", "url": "https://github.com/angular/angular-cli", "version": "0.1303.8", "license": "MIT", "licenseUrl": "https://github.com/angular/angular-cli/LICENSE"}, {"name": "angular-devkit/build-angular", "url": "https://github.com/angular/angular-cli", "version": "13.3.8", "license": "MIT", "licenseUrl": "https://github.com/angular/angular-cli/LICENSE"}, {"name": "angular-devkit/build-webpack", "url": "https://github.com/angular/angular-cli", "version": "0.1303.8", "license": "MIT", "licenseUrl": "https://github.com/angular/angular-cli/LICENSE"}, {"name": "angular-devkit/core", "url": "https://github.com/angular/angular-cli", "version": "13.3.8", "license": "MIT", "licenseUrl": "https://github.com/angular/angular-cli/LICENSE"}, {"name": "angular-devkit/schematics", "url": "https://github.com/angular/angular-cli", "version": "13.3.8", "license": "MIT", "licenseUrl": "https://github.com/angular/angular-cli/LICENSE"}, {"name": "angular-eslint/builder", "url": "https://github.com/angular-eslint/angular-eslint", "version": "13.5.0", "license": "MIT", "licenseUrl": "https://github.com/angular-eslint/angular-eslint/LICENSE"}, {"name": "angular-eslint/bundled-angular-compiler", "url": "https://github.com/angular-eslint/angular-eslint", "version": "13.5.0", "license": "MIT", "licenseUrl": "https://github.com/angular-eslint/angular-eslint/LICENSE"}, {"name": "angular-eslint/eslint-plugin", "url": "https://github.com/angular-eslint/angular-eslint", "version": "13.5.0", "license": "MIT", "licenseUrl": "https://github.com/angular-eslint/angular-eslint/LICENSE"}, {"name": "angular-eslint/eslint-plugin-template", "url": "https://github.com/angular-eslint/angular-eslint", "version": "13.5.0", "license": "MIT", "licenseUrl": "https://github.com/angular-eslint/angular-eslint/LICENSE"}, {"name": "angular-eslint/schematics", "url": "https://github.com/angular-eslint/angular-eslint", "version": "13.5.0", "license": "MIT", "licenseUrl": "https://github.com/angular-eslint/angular-eslint/LICENSE"}, {"name": "angular-eslint/template-parser", "url": "https://github.com/angular-eslint/angular-eslint", "version": "13.5.0", "license": "MIT", "licenseUrl": "https://github.com/angular-eslint/angular-eslint/LICENSE"}, {"name": "angular/cli", "url": "https://github.com/angular/angular-cli", "version": "13.3.8", "license": "MIT", "licenseUrl": "https://github.com/angular/angular-cli/LICENSE"}, {"name": "angular/common", "url": "https://github.com/angular/angular", "version": "13.3.11", "license": "MIT", "licenseUrl": "https://github.com/angular/angular/README.md"}, {"name": "angular/compiler", "url": "https://github.com/angular/angular", "version": "13.3.11", "license": "MIT", "licenseUrl": "https://github.com/angular/angular/README.md"}, {"name": "angular/compiler-cli", "url": "https://github.com/angular/angular", "version": "13.3.11", "license": "MIT", "licenseUrl": "https://npmjs.org/package/@angular/compiler-cli@13.3.11"}, {"name": "angular/core", "url": "https://github.com/angular/angular", "version": "13.3.11", "license": "MIT", "licenseUrl": "https://github.com/angular/angular/README.md"}, {"name": "angular/platform-browser", "url": "https://github.com/angular/angular", "version": "13.3.11", "license": "MIT", "licenseUrl": "https://github.com/angular/angular/README.md"}, {"name": "angular/platform-browser-dynamic", "url": "https://github.com/angular/angular", "version": "13.3.11", "license": "MIT", "licenseUrl": "https://github.com/angular/angular/README.md"}, {"name": "ansi-colors", "url": "https://github.com/doowb/ansi-colors", "version": "4.1.1", "license": "MIT", "licenseUrl": "https://github.com/doowb/ansi-colors/LICENSE"}, {"name": "ansi-escapes", "url": "https://github.com/sindresorhus/ansi-escapes", "version": "4.3.2", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/ansi-escapes/license"}, {"name": "ansi-html-community", "url": "https://github.com/mahdyar/ansi-html-community", "version": "0.0.8", "license": "Apache-2.0", "licenseUrl": "https://github.com/mahdyar/ansi-html-community/LICENSE"}, {"name": "ansi-regex", "url": "https://github.com/chalk/ansi-regex", "version": "6.0.1", "license": "MIT", "licenseUrl": "https://github.com/chalk/ansi-regex/license"}, {"name": "ansi-regex", "url": "https://github.com/chalk/ansi-regex", "version": "5.0.1", "license": "MIT", "licenseUrl": "https://github.com/chalk/ansi-regex/license"}, {"name": "ansi-styles", "url": "https://github.com/chalk/ansi-styles", "version": "3.2.1", "license": "MIT", "licenseUrl": "https://github.com/chalk/ansi-styles/license"}, {"name": "ansi-styles", "url": "https://github.com/chalk/ansi-styles", "version": "5.2.0", "license": "MIT", "licenseUrl": "https://github.com/chalk/ansi-styles/license"}, {"name": "ansi-styles", "url": "https://github.com/chalk/ansi-styles", "version": "4.3.0", "license": "MIT", "licenseUrl": "https://github.com/chalk/ansi-styles/license"}, {"name": "anymatch", "url": "https://github.com/micromatch/anymatch", "version": "3.1.2", "license": "ISC", "licenseUrl": "https://github.com/micromatch/anymatch/LICENSE"}, {"name": "ap-validation", "url": "https://github.com/JetBrains/ap-validation", "version": "76", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/ap-validation/blob/master/LICENSE"}, {"name": "apache.commons.math4.core", "url": "https://commons.apache.org/proper/commons-math/commons-math4-core/", "version": "4.0-beta1", "license": "Apache 2.0", "licenseUrl": "https://github.com/apache/commons-math/blob/4.0-beta1-release/commons-math-core/LICENCE"}, {"name": "apache.logging.log4j.to.slf4j", "url": "https://ant.apache.org/", "version": "2.20.0", "license": "Apache 2.0", "licenseUrl": "https://logging.apache.org/log4j/log4j-2.2/license.html"}, {"name": "aproba", "url": "https://github.com/iarna/aproba", "version": "2.0.0", "license": "ISC", "licenseUrl": "https://github.com/iarna/aproba/LICENSE"}, {"name": "are-we-there-yet", "url": "https://github.com/npm/are-we-there-yet", "version": "3.0.0", "license": "ISC", "licenseUrl": "https://github.com/npm/are-we-there-yet/LICENSE.md"}, {"name": "arg", "url": "https://github.com/zeit/arg", "version": "4.1.3", "license": "MIT", "licenseUrl": "https://github.com/zeit/arg/LICENSE.md"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/nodeca/argparse", "version": "1.0.10", "license": "MIT", "licenseUrl": "https://github.com/nodeca/argparse/LICENSE"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/nodeca/argparse", "version": "2.0.1", "license": "Python-2.0", "licenseUrl": "https://github.com/nodeca/argparse/LICENSE"}, {"name": "aria-query", "url": "https://github.com/A11yance/aria-query", "version": "4.2.2", "license": "Apache-2.0", "licenseUrl": "https://github.com/A11yance/aria-query/LICENSE"}, {"name": "array-find-index", "url": "https://github.com/sindresorhus/array-find-index", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/array-find-index/license"}, {"name": "array-flatten", "url": "https://github.com/blakeembrey/array-flatten", "version": "1.1.1", "license": "MIT", "licenseUrl": "https://github.com/blakeembrey/array-flatten/LICENSE"}, {"name": "array-flatten", "url": "https://github.com/blakeembrey/array-flatten", "version": "2.1.2", "license": "MIT", "licenseUrl": "https://github.com/blakeembrey/array-flatten/LICENSE"}, {"name": "array-union", "url": "https://github.com/sindresorhus/array-union", "version": "3.0.1", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/array-union/license"}, {"name": "array-union", "url": "https://github.com/sindresorhus/array-union", "version": "2.1.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/array-union/license"}, {"name": "array.prototype.reduce", "url": "https://github.com/es-shims/Array.prototype.reduce", "version": "1.0.4", "license": "MIT", "licenseUrl": "https://github.com/es-shims/Array.prototype.reduce/LICENSE"}, {"name": "asap", "url": "https://github.com/kriskowal/asap", "version": "2.0.6", "license": "MIT", "licenseUrl": "https://github.com/kriskowal/asap/LICENSE.md"}, {"name": "assemblyscript/loader", "url": "https://github.com/AssemblyScript/assemblyscript", "version": "0.10.1", "license": "Apache-2.0", "licenseUrl": "https://github.com/AssemblyScript/assemblyscript/README.md"}, {"name": "assertion-error", "url": "https://github.com/chaijs/assertion-error", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/chaijs/assertion-error/README.md"}, {"name": "async", "url": "https://github.com/caolan/async", "version": "3.2.4", "license": "MIT", "licenseUrl": "https://github.com/caolan/async/LICENSE"}, {"name": "async", "url": "https://github.com/caolan/async", "version": "2.6.4", "license": "MIT", "licenseUrl": "https://github.com/caolan/async/LICENSE"}, {"name": "at-least-node", "url": "https://github.com/RyanZim/at-least-node", "version": "1.0.0", "license": "ISC", "licenseUrl": "https://github.com/RyanZim/at-least-node/LICENSE"}, {"name": "atob", "url": "git://git.coolaj86.com/coolaj86/atob.js", "version": "2.1.2", "license": "(MIT OR Apache-2.0)", "licenseUrl": "git://git.coolaj86.com/coolaj86/atob.js/LICENSE"}, {"name": "autoprefixer", "url": "https://github.com/postcss/autoprefixer", "version": "10.4.7", "license": "MIT", "licenseUrl": "https://github.com/postcss/autoprefixer/LICENSE"}, {"name": "axobject-query", "url": "https://github.com/A11yance/axobject-query", "version": "2.2.0", "license": "Apache-2.0", "licenseUrl": "https://github.com/A11yance/axobject-query/LICENSE"}, {"name": "babel-loader", "url": "https://github.com/babel/babel-loader", "version": "8.2.5", "license": "MIT", "licenseUrl": "https://github.com/babel/babel-loader/LICENSE"}, {"name": "babel-plugin-dynamic-import-node", "url": "https://github.com/airbnb/babel-plugin-dynamic-import-node", "version": "2.3.3", "license": "MIT", "licenseUrl": "https://github.com/airbnb/babel-plugin-dynamic-import-node/LICENSE"}, {"name": "babel-plugin-istanbul", "url": "https://github.com/istanbuljs/babel-plugin-istanbul", "version": "6.1.1", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/istanbuljs/babel-plugin-istanbul/LICENSE"}, {"name": "babel-plugin-polyfill-corejs2", "url": "https://github.com/babel/babel-polyfills", "version": "0.3.1", "license": "MIT", "licenseUrl": "https://github.com/babel/babel-polyfills/LICENSE"}, {"name": "babel-plugin-polyfill-corejs3", "url": "https://github.com/babel/babel-polyfills", "version": "0.5.2", "license": "MIT", "licenseUrl": "https://github.com/babel/babel-polyfills/LICENSE"}, {"name": "babel-plugin-polyfill-regenerator", "url": "https://github.com/babel/babel-polyfills", "version": "0.3.1", "license": "MIT", "licenseUrl": "https://github.com/babel/babel-polyfills/LICENSE"}, {"name": "babel/code-frame", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/compat-data", "url": "https://github.com/babel/babel", "version": "7.18.5", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/core", "url": "https://github.com/babel/babel", "version": "7.16.12", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/core", "url": "https://github.com/babel/babel", "version": "7.18.5", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/generator", "url": "https://github.com/babel/babel", "version": "7.16.8", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/generator", "url": "https://github.com/babel/babel", "version": "7.18.2", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/helper-annotate-as-pure", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/helper-builder-binary-assignment-operator-visitor", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/helper-compilation-targets", "url": "https://github.com/babel/babel", "version": "7.18.2", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/helper-create-class-features-plugin", "url": "https://github.com/babel/babel", "version": "7.18.0", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/helper-create-regexp-features-plugin", "url": "https://github.com/babel/babel", "version": "7.17.12", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/helper-define-polyfill-provider", "url": "https://github.com/babel/babel-polyfills", "version": "0.3.1", "license": "MIT", "licenseUrl": "https://github.com/babel/babel-polyfills/LICENSE"}, {"name": "babel/helper-environment-visitor", "url": "https://github.com/babel/babel", "version": "7.18.2", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/helper-explode-assignable-expression", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/helper-function-name", "url": "https://github.com/babel/babel", "version": "7.17.9", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/helper-hoist-variables", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/helper-member-expression-to-functions", "url": "https://github.com/babel/babel", "version": "7.17.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/helper-module-imports", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/helper-module-transforms", "url": "https://github.com/babel/babel", "version": "7.18.0", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/helper-optimise-call-expression", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/helper-plugin-utils", "url": "https://github.com/babel/babel", "version": "7.17.12", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/helper-remap-async-to-generator", "url": "https://github.com/babel/babel", "version": "7.16.8", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/helper-replace-supers", "url": "https://github.com/babel/babel", "version": "7.18.2", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/helper-simple-access", "url": "https://github.com/babel/babel", "version": "7.18.2", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/helper-skip-transparent-expression-wrappers", "url": "https://github.com/babel/babel", "version": "7.16.0", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/helper-split-export-declaration", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/helper-validator-identifier", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/helper-validator-option", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/helper-wrap-function", "url": "https://github.com/babel/babel", "version": "7.16.8", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/helpers", "url": "https://github.com/babel/babel", "version": "7.18.2", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/highlight", "url": "https://github.com/babel/babel", "version": "7.17.12", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/parser", "url": "https://github.com/babel/babel", "version": "7.18.5", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression", "url": "https://github.com/babel/babel", "version": "7.17.12", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining", "url": "https://github.com/babel/babel", "version": "7.17.12", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-proposal-async-generator-functions", "url": "https://github.com/babel/babel", "version": "7.16.8", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-proposal-class-properties", "url": "https://github.com/babel/babel", "version": "7.17.12", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-proposal-class-static-block", "url": "https://github.com/babel/babel", "version": "7.18.0", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-proposal-dynamic-import", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-proposal-export-namespace-from", "url": "https://github.com/babel/babel", "version": "7.17.12", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-proposal-json-strings", "url": "https://github.com/babel/babel", "version": "7.17.12", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-proposal-logical-assignment-operators", "url": "https://github.com/babel/babel", "version": "7.17.12", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-proposal-nullish-coalescing-operator", "url": "https://github.com/babel/babel", "version": "7.17.12", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-proposal-numeric-separator", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-proposal-object-rest-spread", "url": "https://github.com/babel/babel", "version": "7.18.0", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-proposal-optional-catch-binding", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-proposal-optional-chaining", "url": "https://github.com/babel/babel", "version": "7.17.12", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-proposal-private-methods", "url": "https://github.com/babel/babel", "version": "7.17.12", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-proposal-private-property-in-object", "url": "https://github.com/babel/babel", "version": "7.17.12", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-proposal-unicode-property-regex", "url": "https://github.com/babel/babel", "version": "7.17.12", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-syntax-async-generators", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators", "version": "7.8.4", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-async-generators/LICENSE"}, {"name": "babel/plugin-syntax-class-properties", "url": "https://github.com/babel/babel", "version": "7.12.13", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-syntax-class-static-block", "url": "https://github.com/babel/babel", "version": "7.14.5", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-syntax-dynamic-import", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-dynamic-import", "version": "7.8.3", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-dynamic-import/LICENSE"}, {"name": "babel/plugin-syntax-export-namespace-from", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-export-namespace-from", "version": "7.8.3", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-export-namespace-from/LICENSE"}, {"name": "babel/plugin-syntax-json-strings", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings", "version": "7.8.3", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-json-strings/LICENSE"}, {"name": "babel/plugin-syntax-logical-assignment-operators", "url": "https://github.com/babel/babel", "version": "7.10.4", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-syntax-nullish-coalescing-operator", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator", "version": "7.8.3", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-nullish-coalescing-operator/LICENSE"}, {"name": "babel/plugin-syntax-numeric-separator", "url": "https://github.com/babel/babel", "version": "7.10.4", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-syntax-object-rest-spread", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread", "version": "7.8.3", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-object-rest-spread/LICENSE"}, {"name": "babel/plugin-syntax-optional-catch-binding", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding", "version": "7.8.3", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-catch-binding/LICENSE"}, {"name": "babel/plugin-syntax-optional-chaining", "url": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining", "version": "7.8.3", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/tree/master/packages/babel-plugin-syntax-optional-chaining/LICENSE"}, {"name": "babel/plugin-syntax-private-property-in-object", "url": "https://github.com/babel/babel", "version": "7.14.5", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-syntax-top-level-await", "url": "https://github.com/babel/babel", "version": "7.14.5", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-arrow-functions", "url": "https://github.com/babel/babel", "version": "7.17.12", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-async-to-generator", "url": "https://github.com/babel/babel", "version": "7.16.8", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-block-scoped-functions", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-block-scoping", "url": "https://github.com/babel/babel", "version": "7.18.4", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-classes", "url": "https://github.com/babel/babel", "version": "7.18.4", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-computed-properties", "url": "https://github.com/babel/babel", "version": "7.17.12", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-destructuring", "url": "https://github.com/babel/babel", "version": "7.18.0", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-dotall-regex", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-duplicate-keys", "url": "https://github.com/babel/babel", "version": "7.17.12", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-exponentiation-operator", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-for-of", "url": "https://github.com/babel/babel", "version": "7.18.1", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-function-name", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-literals", "url": "https://github.com/babel/babel", "version": "7.17.12", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-member-expression-literals", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-modules-amd", "url": "https://github.com/babel/babel", "version": "7.18.0", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-modules-commonjs", "url": "https://github.com/babel/babel", "version": "7.18.2", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-modules-systemjs", "url": "https://github.com/babel/babel", "version": "7.18.5", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-modules-umd", "url": "https://github.com/babel/babel", "version": "7.18.0", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-named-capturing-groups-regex", "url": "https://github.com/babel/babel", "version": "7.17.12", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-new-target", "url": "https://github.com/babel/babel", "version": "7.18.5", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-object-super", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-parameters", "url": "https://github.com/babel/babel", "version": "7.17.12", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-property-literals", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-regenerator", "url": "https://github.com/babel/babel", "version": "7.18.0", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-reserved-words", "url": "https://github.com/babel/babel", "version": "7.17.12", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-runtime", "url": "https://github.com/babel/babel", "version": "7.16.10", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-shorthand-properties", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-spread", "url": "https://github.com/babel/babel", "version": "7.17.12", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-sticky-regex", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-template-literals", "url": "https://github.com/babel/babel", "version": "7.18.2", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-typeof-symbol", "url": "https://github.com/babel/babel", "version": "7.17.12", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-unicode-escapes", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/plugin-transform-unicode-regex", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/preset-env", "url": "https://github.com/babel/babel", "version": "7.16.11", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/preset-modules", "url": "https://github.com/babel/preset-modules", "version": "0.1.5", "license": "MIT", "licenseUrl": "https://github.com/babel/preset-modules/LICENSE"}, {"name": "babel/runtime", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/runtime-corejs3", "url": "https://github.com/babel/babel", "version": "7.18.3", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/template", "url": "https://github.com/babel/babel", "version": "7.16.7", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/traverse", "url": "https://github.com/babel/babel", "version": "7.18.5", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "babel/types", "url": "https://github.com/babel/babel", "version": "7.18.4", "license": "MIT", "licenseUrl": "https://github.com/babel/babel/LICENSE"}, {"name": "backbone", "url": "https://github.com/jashkenas/backbone", "version": "1.4.0", "license": "MIT", "licenseUrl": "https://github.com/jashkenas/backbone/LICENSE"}, {"name": "balanced-match", "url": "https://github.com/juliangruber/balanced-match", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/juliangruber/balanced-match/LICENSE.md"}, {"name": "base64-js", "url": "https://github.com/beatgammit/base64-js", "version": "1.5.1", "license": "MIT", "licenseUrl": "https://github.com/beatgammit/base64-js/LICENSE"}, {"name": "base64id", "url": "https://github.com/faeldt/base64id", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/faeldt/base64id/LICENSE"}, {"name": "batch", "url": "https://github.com/visionmedia/batch", "version": "0.6.1", "license": "MIT", "licenseUrl": "https://github.com/visionmedia/batch/LICENSE"}, {"name": "batik", "url": "https://xmlgraphics.apache.org/batik/", "version": "1.16.0-35", "license": "Apache 2.0", "licenseUrl": "https://xmlgraphics.apache.org/batik/license.html"}, {"name": "big.js", "url": "https://github.com/MikeMcl/big.js", "version": "5.2.2", "license": "MIT", "licenseUrl": "https://github.com/MikeMcl/big.js/LICENCE"}, {"name": "binary-extensions", "url": "https://github.com/sindresorhus/binary-extensions", "version": "2.2.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/binary-extensions/license"}, {"name": "bl", "url": "https://github.com/rvagg/bl", "version": "4.1.0", "license": "MIT", "licenseUrl": "https://github.com/rvagg/bl/LICENSE.md"}, {"name": "bl", "url": "https://github.com/rvagg/bl", "version": "1.2.3", "license": "MIT", "licenseUrl": "https://github.com/rvagg/bl/LICENSE.md"}, {"name": "bl", "url": "https://github.com/rvagg/bl", "version": "2.2.1", "license": "MIT", "licenseUrl": "https://github.com/rvagg/bl/LICENSE.md"}, {"name": "blockmap", "url": "https://github.com/JetBrains/plugin-blockmap-patches", "version": "1.0.7", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/plugin-blockmap-patches/blob/master/LICENSE"}, {"name": "blueprintjs/colors", "url": "https://github.com/palantir/blueprint", "version": "4.1.3", "license": "Apache-2.0", "licenseUrl": "https://github.com/palantir/blueprint/LICENSE"}, {"name": "blueprintjs/core", "url": "https://github.com/palantir/blueprint", "version": "3.54.0", "license": "Apache-2.0", "licenseUrl": "https://github.com/palantir/blueprint/LICENSE"}, {"name": "blueprintjs/icons", "url": "https://github.com/palantir/blueprint", "version": "3.33.0", "license": "Apache-2.0", "licenseUrl": "https://github.com/palantir/blueprint/LICENSE"}, {"name": "blueprintjs/select", "url": "https://github.com/palantir/blueprint", "version": "3.19.1", "license": "Apache-2.0", "licenseUrl": "https://github.com/palantir/blueprint/LICENSE"}, {"name": "body-parser", "url": "https://github.com/expressjs/body-parser", "version": "1.20.0", "license": "MIT", "licenseUrl": "https://github.com/expressjs/body-parser/LICENSE"}, {"name": "bonjour", "url": "https://github.com/watson/bonjour", "version": "3.5.0", "license": "MIT", "licenseUrl": "https://github.com/watson/bonjour/LICENSE"}, {"name": "boolbase", "url": "https://github.com/fb55/boolbase", "version": "1.0.0", "license": "ISC", "licenseUrl": "https://github.com/fb55/boolbase/README.md"}, {"name": "bootstrap", "url": "https://github.com/twbs/bootstrap", "version": "3.4.1", "license": "MIT", "licenseUrl": "https://github.com/twbs/bootstrap/LICENSE"}, {"name": "bouncy-castle-pgp", "url": "https://www.bouncycastle.org", "version": "1.80", "license": "MIT", "licenseUrl": "https://www.bouncycastle.org/license.html"}, {"name": "bouncy-castle-provider", "url": "https://www.bouncycastle.org", "version": "1.80", "license": "MIT", "licenseUrl": "https://www.bouncycastle.org/license.html"}, {"name": "brace-expansion", "url": "https://github.com/juliangruber/brace-expansion", "version": "1.1.11", "license": "MIT", "licenseUrl": "https://github.com/juliangruber/brace-expansion/LICENSE"}, {"name": "brace-expansion", "url": "https://github.com/juliangruber/brace-expansion", "version": "2.0.1", "license": "MIT", "licenseUrl": "https://github.com/juliangruber/brace-expansion/LICENSE"}, {"name": "braces", "url": "https://github.com/micromatch/braces", "version": "3.0.2", "license": "MIT", "licenseUrl": "https://github.com/micromatch/braces/LICENSE"}, {"name": "braintree/sanitize-url", "url": "https://github.com/braintree/sanitize-url", "version": "6.0.4", "license": "MIT", "licenseUrl": "https://github.com/braintree/sanitize-url/LICENSE"}, {"name": "browser-stdout", "url": "https://github.com/kumavis/browser-stdout", "version": "1.3.1", "license": "ISC", "licenseUrl": "https://github.com/kumavis/browser-stdout/LICENSE"}, {"name": "browserslist", "url": "https://github.com/browserslist/browserslist", "version": "4.21.0", "license": "MIT", "licenseUrl": "https://github.com/browserslist/browserslist/LICENSE"}, {"name": "buble", "url": "https://github.com/bublejs/buble", "version": "0.20.0", "license": "MIT", "licenseUrl": "https://github.com/bublejs/buble/LICENSE.md"}, {"name": "bubleify", "url": "https://github.com/garthenweb/bubleify", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/garthenweb/bubleify/LICENSE"}, {"name": "buffer", "url": "https://github.com/feross/buffer", "version": "5.7.1", "license": "MIT", "licenseUrl": "https://github.com/feross/buffer/LICENSE"}, {"name": "buffer-from", "url": "https://github.com/LinusU/buffer-from", "version": "1.1.2", "license": "MIT", "licenseUrl": "https://github.com/LinusU/buffer-from/LICENSE"}, {"name": "buffer-indexof", "url": "https://github.com/soldair/node-buffer-indexof", "version": "1.1.1", "license": "MIT", "licenseUrl": "https://github.com/soldair/node-buffer-indexof/LICENSE"}, {"name": "builtins", "url": "https://github.com/juliangruber/builtins", "version": "1.0.3", "license": "MIT", "licenseUrl": "https://github.com/juliangruber/builtins/License"}, {"name": "bytes", "url": "https://github.com/visionmedia/bytes.js", "version": "3.0.0", "license": "MIT", "licenseUrl": "https://github.com/visionmedia/bytes.js/LICENSE"}, {"name": "bytes", "url": "https://github.com/visionmedia/bytes.js", "version": "3.1.2", "license": "MIT", "licenseUrl": "https://github.com/visionmedia/bytes.js/LICENSE"}, {"name": "cacache", "url": "https://github.com/npm/cacache", "version": "15.3.0", "license": "ISC", "licenseUrl": "https://github.com/npm/cacache/LICENSE.md"}, {"name": "cacache", "url": "https://github.com/npm/cacache", "version": "16.1.1", "license": "ISC", "licenseUrl": "https://github.com/npm/cacache/LICENSE.md"}, {"name": "caffeine", "url": "https://github.com/ben-manes/caffeine", "version": "3.2.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/ben-manes/caffeine/blob/master/LICENSE"}, {"name": "call-bind", "url": "https://github.com/ljharb/call-bind", "version": "1.0.7", "license": "MIT", "licenseUrl": "https://github.com/ljharb/call-bind/LICENSE"}, {"name": "caller-callsite", "url": "https://github.com/sindresorhus/caller-callsite", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/caller-callsite/license"}, {"name": "caller-path", "url": "https://github.com/sindresorhus/caller-path", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/caller-path/license"}, {"name": "callsites", "url": "https://github.com/sindresorhus/callsites", "version": "3.1.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/callsites/license"}, {"name": "callsites", "url": "https://github.com/sindresorhus/callsites", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/callsites/license"}, {"name": "camelcase", "url": "https://github.com/sindresorhus/camelcase", "version": "5.3.1", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/camelcase/license"}, {"name": "camelcase", "url": "https://github.com/sindresorhus/camelcase", "version": "6.3.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/camelcase/license"}, {"name": "caniuse-api", "url": "https://github.com/nyalab/caniuse-api", "version": "3.0.0", "license": "MIT", "licenseUrl": "https://github.com/nyalab/caniuse-api/LICENSE"}, {"name": "caniuse-lite", "url": "https://github.com/browserslist/caniuse-lite", "version": "1.0.30001359", "license": "CC-BY-4.0", "licenseUrl": "https://github.com/browserslist/caniuse-lite/LICENSE"}, {"name": "chai", "url": "https://github.com/chaijs/chai", "version": "4.3.10", "license": "MIT", "licenseUrl": "https://github.com/chaijs/chai/LICENSE"}, {"name": "chalk", "url": "https://github.com/chalk/chalk", "version": "4.1.0", "license": "MIT", "licenseUrl": "https://github.com/chalk/chalk/license"}, {"name": "chalk", "url": "https://github.com/chalk/chalk", "version": "2.4.2", "license": "MIT", "licenseUrl": "https://github.com/chalk/chalk/license"}, {"name": "chalk", "url": "https://github.com/chalk/chalk", "version": "4.1.2", "license": "MIT", "licenseUrl": "https://github.com/chalk/chalk/license"}, {"name": "character-entities", "url": "https://github.com/wooorm/character-entities", "version": "2.0.2", "license": "MIT", "licenseUrl": "https://github.com/wooorm/character-entities/license"}, {"name": "chardet", "url": "https://github.com/runk/node-chardet", "version": "0.7.0", "license": "MIT", "licenseUrl": "https://github.com/runk/node-chardet/LICENSE"}, {"name": "check-error", "url": "https://github.com/chaijs/check-error", "version": "1.0.3", "license": "MIT", "licenseUrl": "https://github.com/chaijs/check-error/LICENSE"}, {"name": "chokidar", "url": "https://github.com/paulmillr/chokidar", "version": "3.5.3", "license": "MIT", "licenseUrl": "https://github.com/paulmillr/chokidar/LICENSE"}, {"name": "choojs/findup", "url": "https://github.com/choojs/findup", "version": "0.2.1", "license": "MIT", "licenseUrl": "https://github.com/choojs/findup/README.md"}, {"name": "chownr", "url": "https://github.com/isaacs/chownr", "version": "2.0.0", "license": "ISC", "licenseUrl": "https://github.com/isaacs/chownr/LICENSE"}, {"name": "chrome-trace-event", "url": "https://github.com/samccone/chrome-trace-event", "version": "1.0.3", "license": "MIT", "licenseUrl": "https://github.com/samccone/chrome-trace-event/LICENSE.txt"}, {"name": "ci-info", "url": "https://github.com/watson/ci-info", "version": "3.3.2", "license": "MIT", "licenseUrl": "https://github.com/watson/ci-info/LICENSE"}, {"name": "circular-dependency-plugin", "url": "https://github.com/aackerman/circular-dependency-plugin", "version": "5.2.2", "license": "ISC", "licenseUrl": "https://github.com/aackerman/circular-dependency-plugin/LICENSE"}, {"name": "classgraph", "url": "https://github.com/classgraph/classgraph", "version": "4.8.179", "license": "codehaus", "licenseUrl": "https://github.com/codehaus/classworlds/blob/master/classworlds/LICENSE.txt"}, {"name": "classnames", "url": "https://github.com/Jed<PERSON>atson/classnames", "version": "2.3.1", "license": "MIT", "licenseUrl": "https://github.com/JedWatson/classnames/LICENSE"}, {"name": "clean-stack", "url": "https://github.com/sindresorhus/clean-stack", "version": "2.2.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/clean-stack/license"}, {"name": "cli-cursor", "url": "https://github.com/sindresorhus/cli-cursor", "version": "3.1.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/cli-cursor/license"}, {"name": "cli-spinners", "url": "https://github.com/sindresorhus/cli-spinners", "version": "2.6.1", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/cli-spinners/license"}, {"name": "cli-width", "url": "https://github.com/knownasilya/cli-width", "version": "3.0.0", "license": "ISC", "licenseUrl": "https://github.com/knownasilya/cli-width/LICENSE"}, {"name": "cliui", "url": "https://github.com/yargs/cliui", "version": "7.0.4", "license": "ISC", "licenseUrl": "https://github.com/yargs/cliui/LICENSE.txt"}, {"name": "clone", "url": "https://github.com/pvorb/node-clone", "version": "1.0.4", "license": "MIT", "licenseUrl": "https://github.com/pvorb/node-clone/LICENSE"}, {"name": "clone-deep", "url": "https://github.com/jonschlinkert/clone-deep", "version": "4.0.1", "license": "MIT", "licenseUrl": "https://github.com/jonschlinkert/clone-deep/LICENSE"}, {"name": "coa", "url": "https://github.com/veged/coa", "version": "2.0.2", "license": "MIT", "licenseUrl": "https://github.com/veged/coa/LICENSE"}, {"name": "codemirror", "url": "https://github.com/codemirror/CodeMirror", "version": "5.61.1", "license": "MIT", "licenseUrl": "https://github.com/codemirror/CodeMirror/LICENSE"}, {"name": "codemirror/autocomplete", "url": "https://github.com/codemirror/autocomplete", "version": "6.16.0", "license": "MIT", "licenseUrl": "https://github.com/codemirror/autocomplete/LICENSE"}, {"name": "codemirror/commands", "url": "https://github.com/codemirror/commands", "version": "6.5.0", "license": "MIT", "licenseUrl": "https://github.com/codemirror/commands/LICENSE"}, {"name": "codemirror/lang-cpp", "url": "https://github.com/codemirror/lang-cpp", "version": "6.0.2", "license": "MIT", "licenseUrl": "https://github.com/codemirror/lang-cpp/LICENSE"}, {"name": "codemirror/lang-css", "url": "https://github.com/codemirror/lang-css", "version": "6.2.1", "license": "MIT", "licenseUrl": "https://github.com/codemirror/lang-css/LICENSE"}, {"name": "codemirror/lang-html", "url": "https://github.com/codemirror/lang-html", "version": "6.4.9", "license": "MIT", "licenseUrl": "https://github.com/codemirror/lang-html/LICENSE"}, {"name": "codemirror/lang-java", "url": "https://github.com/codemirror/lang-java", "version": "6.0.1", "license": "MIT", "licenseUrl": "https://github.com/codemirror/lang-java/LICENSE"}, {"name": "codemirror/lang-javascript", "url": "https://github.com/codemirror/lang-javascript", "version": "6.2.2", "license": "MIT", "licenseUrl": "https://github.com/codemirror/lang-javascript/LICENSE"}, {"name": "codemirror/lang-json", "url": "https://github.com/codemirror/lang-json", "version": "6.0.1", "license": "MIT", "licenseUrl": "https://github.com/codemirror/lang-json/LICENSE"}, {"name": "codemirror/lang-markdown", "url": "https://github.com/codemirror/lang-markdown", "version": "6.2.5", "license": "MIT", "licenseUrl": "https://github.com/codemirror/lang-markdown/LICENSE"}, {"name": "codemirror/lang-php", "url": "https://github.com/codemirror/lang-php", "version": "6.0.1", "license": "MIT", "licenseUrl": "https://github.com/codemirror/lang-php/LICENSE"}, {"name": "codemirror/lang-python", "url": "https://github.com/codemirror/lang-python", "version": "6.1.6", "license": "MIT", "licenseUrl": "https://github.com/codemirror/lang-python/LICENSE"}, {"name": "codemirror/lang-rust", "url": "https://github.com/codemirror/lang-rust", "version": "6.0.1", "license": "MIT", "licenseUrl": "https://github.com/codemirror/lang-rust/LICENSE"}, {"name": "codemirror/lang-sql", "url": "https://github.com/codemirror/lang-sql", "version": "6.6.3", "license": "MIT", "licenseUrl": "https://github.com/codemirror/lang-sql/LICENSE"}, {"name": "codemirror/lang-wast", "url": "https://github.com/codemirror/lang-wast", "version": "6.0.2", "license": "MIT", "licenseUrl": "https://github.com/codemirror/lang-wast/LICENSE"}, {"name": "codemirror/lang-xml", "url": "https://github.com/codemirror/lang-xml", "version": "6.1.0", "license": "MIT", "licenseUrl": "https://github.com/codemirror/lang-xml/LICENSE"}, {"name": "codemirror/language", "url": "https://github.com/codemirror/language", "version": "6.10.1", "license": "MIT", "licenseUrl": "https://github.com/codemirror/language/LICENSE"}, {"name": "codemirror/legacy-modes", "url": "https://github.com/codemirror/legacy-modes", "version": "6.4.0", "license": "MIT", "licenseUrl": "https://github.com/codemirror/legacy-modes/LICENSE"}, {"name": "codemirror/lint", "url": "https://github.com/codemirror/lint", "version": "6.7.0", "license": "MIT", "licenseUrl": "https://github.com/codemirror/lint/LICENSE"}, {"name": "codemirror/search", "url": "https://github.com/codemirror/search", "version": "6.5.6", "license": "MIT", "licenseUrl": "https://github.com/codemirror/search/LICENSE"}, {"name": "codemirror/state", "url": "https://github.com/codemirror/state", "version": "6.4.1", "license": "MIT", "licenseUrl": "https://github.com/codemirror/state/LICENSE"}, {"name": "codemirror/view", "url": "https://github.com/codemirror/view", "version": "6.26.3", "license": "MIT", "licenseUrl": "https://github.com/codemirror/view/LICENSE"}, {"name": "color", "url": "https://github.com/Qix-/color", "version": "3.2.1", "license": "MIT", "licenseUrl": "https://github.com/Qix-/color/LICENSE"}, {"name": "color-convert", "url": "https://github.com/Qix-/color-convert", "version": "2.0.1", "license": "MIT", "licenseUrl": "https://github.com/Qix-/color-convert/LICENSE"}, {"name": "color-convert", "url": "https://github.com/Qix-/color-convert", "version": "1.9.3", "license": "MIT", "licenseUrl": "https://github.com/Qix-/color-convert/LICENSE"}, {"name": "color-name", "url": "https://github.com/dfcreative/color-name", "version": "1.1.3", "license": "MIT", "licenseUrl": "https://github.com/dfcreative/color-name/LICENSE"}, {"name": "color-name", "url": "https://github.com/colorjs/color-name", "version": "1.1.4", "license": "MIT", "licenseUrl": "https://github.com/colorjs/color-name/LICENSE"}, {"name": "color-string", "url": "https://github.com/Qix-/color-string", "version": "1.9.1", "license": "MIT", "licenseUrl": "https://github.com/Qix-/color-string/LICENSE"}, {"name": "color-support", "url": "https://github.com/isaacs/color-support", "version": "1.1.3", "license": "ISC", "licenseUrl": "https://github.com/isaacs/color-support/LICENSE"}, {"name": "colorette", "url": "https://github.com/jorgebucaran/colorette", "version": "2.0.19", "license": "MIT", "licenseUrl": "https://github.com/jorgebucaran/colorette/LICENSE.md"}, {"name": "colors", "url": "https://github.com/Marak/colors.js", "version": "0.6.2", "license": "Custom: http://i.imgur.com/goJdO.png", "licenseUrl": "https://github.com/Marak/colors.js/ReadMe.md"}, {"name": "colors/colors", "url": "https://github.com/DABH/colors.js", "version": "1.5.0", "license": "MIT", "licenseUrl": "https://github.com/DABH/colors.js/LICENSE"}, {"name": "commander", "url": "https://github.com/tj/commander.js", "version": "8.3.0", "license": "MIT", "licenseUrl": "https://github.com/tj/commander.js/LICENSE"}, {"name": "commander", "url": "https://github.com/visionmedia/commander.js", "version": "2.1.0", "license": "MIT*", "licenseUrl": "https://github.com/visionmedia/commander.js/Readme.md"}, {"name": "commander", "url": "https://github.com/tj/commander.js", "version": "9.2.0", "license": "MIT", "licenseUrl": "https://github.com/tj/commander.js/LICENSE"}, {"name": "commander", "url": "https://github.com/tj/commander.js", "version": "7.2.0", "license": "MIT", "licenseUrl": "https://github.com/tj/commander.js/LICENSE"}, {"name": "commander", "url": "https://github.com/tj/commander.js", "version": "2.20.3", "license": "MIT", "licenseUrl": "https://github.com/tj/commander.js/LICENSE"}, {"name": "commondir", "url": "https://github.com/substack/node-commondir", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/substack/node-commondir/LICENSE"}, {"name": "component-emitter", "url": "https://github.com/component/emitter", "version": "1.3.0", "license": "MIT", "licenseUrl": "https://github.com/component/emitter/LICENSE"}, {"name": "compressible", "url": "https://github.com/jshttp/compressible", "version": "2.0.18", "license": "MIT", "licenseUrl": "https://github.com/jshttp/compressible/LICENSE"}, {"name": "compression", "url": "https://github.com/expressjs/compression", "version": "1.7.4", "license": "MIT", "licenseUrl": "https://github.com/expressjs/compression/LICENSE"}, {"name": "compute-gcd", "url": "https://github.com/compute-io/gcd", "version": "1.2.1", "license": "MIT", "licenseUrl": "https://github.com/compute-io/gcd/LICENSE"}, {"name": "compute-lcm", "url": "https://github.com/compute-io/lcm", "version": "1.1.2", "license": "MIT", "licenseUrl": "https://github.com/compute-io/lcm/LICENSE"}, {"name": "concat-map", "url": "https://github.com/substack/node-concat-map", "version": "0.0.1", "license": "MIT", "licenseUrl": "https://github.com/substack/node-concat-map/LICENSE"}, {"name": "concat-stream", "url": "https://github.com/maxogden/concat-stream", "version": "1.6.2", "license": "MIT", "licenseUrl": "https://github.com/maxogden/concat-stream/LICENSE"}, {"name": "connect", "url": "https://github.com/senchalabs/connect", "version": "3.7.0", "license": "MIT", "licenseUrl": "https://github.com/senchalabs/connect/LICENSE"}, {"name": "connect-history-api-fallback", "url": "https://github.com/bripkens/connect-history-api-fallback", "version": "1.6.0", "license": "MIT", "licenseUrl": "https://github.com/bripkens/connect-history-api-fallback/LICENSE"}, {"name": "console-control-strings", "url": "https://github.com/iarna/console-control-strings", "version": "1.1.0", "license": "ISC", "licenseUrl": "https://github.com/iarna/console-control-strings/LICENSE"}, {"name": "content-disposition", "url": "https://github.com/jshttp/content-disposition", "version": "0.5.4", "license": "MIT", "licenseUrl": "https://github.com/jshttp/content-disposition/LICENSE"}, {"name": "content-type", "url": "https://github.com/jshttp/content-type", "version": "1.0.4", "license": "MIT", "licenseUrl": "https://github.com/jshttp/content-type/LICENSE"}, {"name": "convert-source-map", "url": "https://github.com/thlorenz/convert-source-map", "version": "1.8.0", "license": "MIT", "licenseUrl": "https://github.com/thlorenz/convert-source-map/LICENSE"}, {"name": "cookie", "url": "https://github.com/jshttp/cookie", "version": "0.5.0", "license": "MIT", "licenseUrl": "https://github.com/jshttp/cookie/LICENSE"}, {"name": "cookie", "url": "https://github.com/jshttp/cookie", "version": "0.4.2", "license": "MIT", "licenseUrl": "https://github.com/jshttp/cookie/LICENSE"}, {"name": "cookie-signature", "url": "https://github.com/visionmedia/node-cookie-signature", "version": "1.0.6", "license": "MIT", "licenseUrl": "https://github.com/visionmedia/node-cookie-signature/Readme.md"}, {"name": "copy-anything", "url": "https://github.com/mesqueeb/copy-anything", "version": "2.0.6", "license": "MIT", "licenseUrl": "https://github.com/mesqueeb/copy-anything/LICENSE"}, {"name": "copy-webpack-plugin", "url": "https://github.com/webpack-contrib/copy-webpack-plugin", "version": "10.2.1", "license": "MIT", "licenseUrl": "https://github.com/webpack-contrib/copy-webpack-plugin/LICENSE"}, {"name": "core-js", "url": "https://github.com/zloirock/core-js", "version": "3.20.3", "license": "MIT", "licenseUrl": "https://github.com/zloirock/core-js/LICENSE"}, {"name": "core-js-compat", "url": "https://github.com/zloirock/core-js", "version": "3.23.3", "license": "MIT", "licenseUrl": "https://github.com/zloirock/core-js/LICENSE"}, {"name": "core-js-pure", "url": "https://github.com/zloirock/core-js", "version": "3.23.3", "license": "MIT", "licenseUrl": "https://github.com/zloirock/core-js/LICENSE"}, {"name": "core-util-is", "url": "https://github.com/isaacs/core-util-is", "version": "1.0.3", "license": "MIT", "licenseUrl": "https://github.com/isaacs/core-util-is/LICENSE"}, {"name": "cors", "url": "https://github.com/expressjs/cors", "version": "2.8.5", "license": "MIT", "licenseUrl": "https://github.com/expressjs/cors/LICENSE"}, {"name": "cose-base", "url": "https://github.com/iVis-at-Bilkent/cose-base", "version": "1.0.3", "license": "MIT", "licenseUrl": "https://github.com/iVis-at-Bilkent/cose-base/LICENSE"}, {"name": "cosmiconfig", "url": "https://github.com/davidtheclark/cosmiconfig", "version": "5.2.1", "license": "MIT", "licenseUrl": "https://github.com/davidtheclark/cosmiconfig/LICENSE"}, {"name": "cosmiconfig", "url": "https://github.com/davidtheclark/cosmiconfig", "version": "7.0.1", "license": "MIT", "licenseUrl": "https://github.com/davidtheclark/cosmiconfig/LICENSE"}, {"name": "coverage-report", "url": "https://github.com/JetBrains/coverage-report", "version": "1.0.25", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/coverage-report/blob/master/LICENSE"}, {"name": "create-require", "url": "https://github.com/nuxt-contrib/create-require", "version": "1.1.1", "license": "MIT", "licenseUrl": "https://github.com/nuxt-contrib/create-require/LICENSE"}, {"name": "crelt", "url": "https://github.com/marijnh/crelt", "version": "1.0.6", "license": "MIT", "licenseUrl": "https://github.com/marijnh/crelt/LICENSE"}, {"name": "critters", "url": "https://github.com/GoogleChromeLabs/critters", "version": "0.0.16", "license": "Apache-2.0", "licenseUrl": "https://github.com/GoogleChromeLabs/critters/README.md"}, {"name": "cross-spawn", "url": "https://github.com/moxystudio/node-cross-spawn", "version": "7.0.3", "license": "MIT", "licenseUrl": "https://github.com/moxystudio/node-cross-spawn/LICENSE"}, {"name": "crypto", "url": "https://github.com/npm/deprecate-holder", "version": "1.0.1", "license": "ISC", "licenseUrl": "https://github.com/npm/deprecate-holder/README.md"}, {"name": "cspotcode/source-map-support", "url": "https://github.com/cspotcode/node-source-map-support", "version": "0.8.1", "license": "MIT", "licenseUrl": "https://github.com/cspotcode/node-source-map-support/LICENSE.md"}, {"name": "css", "url": "https://github.com/reworkcss/css", "version": "3.0.0", "license": "MIT", "licenseUrl": "https://github.com/reworkcss/css/LICENSE"}, {"name": "css-blank-pseudo", "url": "https://github.com/csstools/postcss-plugins", "version": "3.0.3", "license": "CC0-1.0", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "css-color-names", "url": "https://github.com/bahamas10/css-color-names", "version": "0.0.4", "license": "MIT", "licenseUrl": "https://github.com/bahamas10/css-color-names/README.md"}, {"name": "css-declaration-sorter", "url": "https://github.com/Siilwyn/css-declaration-sorter", "version": "4.0.1", "license": "MIT", "licenseUrl": "https://github.com/Siilwyn/css-declaration-sorter/license.md"}, {"name": "css-has-pseudo", "url": "https://github.com/csstools/postcss-plugins", "version": "3.0.4", "license": "CC0-1.0", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "css-loader", "url": "https://github.com/webpack-contrib/css-loader", "version": "6.5.1", "license": "MIT", "licenseUrl": "https://github.com/webpack-contrib/css-loader/LICENSE"}, {"name": "css-prefers-color-scheme", "url": "https://github.com/csstools/postcss-plugins", "version": "6.0.3", "license": "CC0-1.0", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "css-select", "url": "https://github.com/fb55/css-select", "version": "4.3.0", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/fb55/css-select/LICENSE"}, {"name": "css-select", "url": "https://github.com/fb55/css-select", "version": "2.1.0", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/fb55/css-select/LICENSE"}, {"name": "css-select-base-adapter", "url": "https://github.com/nrkn/css-select-base-adapter", "version": "0.1.1", "license": "MIT", "licenseUrl": "https://github.com/nrkn/css-select-base-adapter/LICENSE"}, {"name": "css-tree", "url": "https://github.com/csstree/csstree", "version": "1.1.3", "license": "MIT", "licenseUrl": "https://github.com/csstree/csstree/LICENSE"}, {"name": "css-tree", "url": "https://github.com/csstree/csstree", "version": "1.0.0-alpha.37", "license": "MIT", "licenseUrl": "https://github.com/csstree/csstree/LICENSE"}, {"name": "css-what", "url": "https://github.com/fb55/css-what", "version": "3.4.2", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/fb55/css-what/LICENSE"}, {"name": "css-what", "url": "https://github.com/fb55/css-what", "version": "6.1.0", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/fb55/css-what/LICENSE"}, {"name": "cssdb", "url": "https://github.com/csstools/cssdb", "version": "5.1.0", "license": "CC0-1.0", "licenseUrl": "https://github.com/csstools/cssdb/LICENSE.md"}, {"name": "cssesc", "url": "https://github.com/mathiasbynens/cssesc", "version": "3.0.0", "license": "MIT", "licenseUrl": "https://github.com/mathiasbynens/cssesc/LICENSE-MIT.txt"}, {"name": "cssnano", "url": "https://github.com/cssnano/cssnano", "version": "4.1.11", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "cssnano-preset-default", "url": "https://github.com/cssnano/cssnano", "version": "4.0.8", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "cssnano-util-get-arguments", "url": "https://github.com/cssnano/cssnano", "version": "4.0.0", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "cssnano-util-get-match", "url": "https://github.com/cssnano/cssnano", "version": "4.0.0", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "cssnano-util-raw-cache", "url": "https://github.com/cssnano/cssnano", "version": "4.0.1", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "cssnano-util-same-parent", "url": "https://github.com/cssnano/cssnano", "version": "4.0.1", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "csso", "url": "https://github.com/css/csso", "version": "4.2.0", "license": "MIT", "licenseUrl": "https://github.com/css/csso/LICENSE"}, {"name": "csstools/postcss-progressive-custom-properties", "url": "https://github.com/csstools/postcss-plugins", "version": "1.3.0", "license": "CC0-1.0", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "csstools/selector-specificity", "url": "https://github.com/csstools/postcss-plugins", "version": "2.0.1", "license": "CC0-1.0", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "csstype", "url": "https://github.com/frenic/csstype", "version": "3.0.10", "license": "MIT", "licenseUrl": "https://github.com/frenic/csstype/LICENSE"}, {"name": "custom-event", "url": "https://github.com/webmodules/custom-event", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/webmodules/custom-event/LICENSE"}, {"name": "cytoscape", "url": "https://github.com/cytoscape/cytoscape.js", "version": "3.29.2", "license": "MIT", "licenseUrl": "https://github.com/cytoscape/cytoscape.js/LICENSE"}, {"name": "cytoscape-cose-bilkent", "url": "https://github.com/cytoscape/cytoscape.js-cose-bilkent", "version": "4.1.0", "license": "MIT", "licenseUrl": "https://github.com/cytoscape/cytoscape.js-cose-bilkent/LICENSE"}, {"name": "d3", "url": "https://github.com/d3/d3", "version": "7.9.0", "license": "ISC", "licenseUrl": "https://github.com/d3/d3/LICENSE"}, {"name": "d3-array", "url": "https://github.com/d3/d3-array", "version": "2.12.1", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/d3/d3-array/LICENSE"}, {"name": "d3-array", "url": "https://github.com/d3/d3-array", "version": "3.2.4", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-array/LICENSE"}, {"name": "d3-axis", "url": "https://github.com/d3/d3-axis", "version": "3.0.0", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-axis/LICENSE"}, {"name": "d3-brush", "url": "https://github.com/d3/d3-brush", "version": "3.0.0", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-brush/LICENSE"}, {"name": "d3-chord", "url": "https://github.com/d3/d3-chord", "version": "3.0.1", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-chord/LICENSE"}, {"name": "d3-color", "url": "https://github.com/d3/d3-color", "version": "3.1.0", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-color/LICENSE"}, {"name": "d3-contour", "url": "https://github.com/d3/d3-contour", "version": "4.0.2", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-contour/LICENSE"}, {"name": "d3-delaunay", "url": "https://github.com/d3/d3-delaunay", "version": "6.0.4", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-delaunay/LICENSE"}, {"name": "d3-dispatch", "url": "https://github.com/d3/d3-dispatch", "version": "3.0.1", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-dispatch/LICENSE"}, {"name": "d3-drag", "url": "https://github.com/d3/d3-drag", "version": "3.0.0", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-drag/LICENSE"}, {"name": "d3-dsv", "url": "https://github.com/d3/d3-dsv", "version": "3.0.1", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-dsv/LICENSE"}, {"name": "d3-ease", "url": "https://github.com/d3/d3-ease", "version": "3.0.1", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/d3/d3-ease/LICENSE"}, {"name": "d3-fetch", "url": "https://github.com/d3/d3-fetch", "version": "3.0.1", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-fetch/LICENSE"}, {"name": "d3-force", "url": "https://github.com/d3/d3-force", "version": "3.0.0", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-force/LICENSE"}, {"name": "d3-format", "url": "https://github.com/d3/d3-format", "version": "3.1.0", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-format/LICENSE"}, {"name": "d3-geo", "url": "https://github.com/d3/d3-geo", "version": "3.1.1", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-geo/LICENSE"}, {"name": "d3-hierarchy", "url": "https://github.com/d3/d3-hierarchy", "version": "3.1.2", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-hierarchy/LICENSE"}, {"name": "d3-interpolate", "url": "https://github.com/d3/d3-interpolate", "version": "3.0.1", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-interpolate/LICENSE"}, {"name": "d3-path", "url": "https://github.com/d3/d3-path", "version": "3.1.0", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-path/LICENSE"}, {"name": "d3-path", "url": "https://github.com/d3/d3-path", "version": "1.0.9", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/d3/d3-path/LICENSE"}, {"name": "d3-polygon", "url": "https://github.com/d3/d3-polygon", "version": "3.0.1", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-polygon/LICENSE"}, {"name": "d3-quadtree", "url": "https://github.com/d3/d3-quadtree", "version": "3.0.1", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-quadtree/LICENSE"}, {"name": "d3-random", "url": "https://github.com/d3/d3-random", "version": "3.0.1", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-random/LICENSE"}, {"name": "d3-sankey", "url": "https://github.com/d3/d3-sankey", "version": "0.12.3", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/d3/d3-sankey/LICENSE"}, {"name": "d3-scale", "url": "https://github.com/d3/d3-scale", "version": "4.0.2", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-scale/LICENSE"}, {"name": "d3-scale-chromatic", "url": "https://github.com/d3/d3-scale-chromatic", "version": "3.1.0", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-scale-chromatic/LICENSE"}, {"name": "d3-selection", "url": "https://github.com/d3/d3-selection", "version": "3.0.0", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-selection/LICENSE"}, {"name": "d3-shape", "url": "https://github.com/d3/d3-shape", "version": "3.2.0", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-shape/LICENSE"}, {"name": "d3-shape", "url": "https://github.com/d3/d3-shape", "version": "1.3.7", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/d3/d3-shape/LICENSE"}, {"name": "d3-time", "url": "https://github.com/d3/d3-time", "version": "3.1.0", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-time/LICENSE"}, {"name": "d3-time-format", "url": "https://github.com/d3/d3-time-format", "version": "4.1.0", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-time-format/LICENSE"}, {"name": "d3-timer", "url": "https://github.com/d3/d3-timer", "version": "3.0.1", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-timer/LICENSE"}, {"name": "d3-transition", "url": "https://github.com/d3/d3-transition", "version": "3.0.1", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-transition/LICENSE"}, {"name": "d3-zoom", "url": "https://github.com/d3/d3-zoom", "version": "3.0.0", "license": "ISC", "licenseUrl": "https://github.com/d3/d3-zoom/LICENSE"}, {"name": "dagre-d3-es", "url": "https://github.com/tbo47/dagre-es", "version": "7.0.10", "license": "MIT", "licenseUrl": "https://github.com/tbo47/dagre-es/README.md"}, {"name": "date-format", "url": "https://github.com/nomiddlename/date-format", "version": "4.0.11", "license": "MIT", "licenseUrl": "https://github.com/nomiddlename/date-format/LICENSE"}, {"name": "dayjs", "url": "https://github.com/iamkun/dayjs", "version": "1.11.11", "license": "MIT", "licenseUrl": "https://github.com/iamkun/dayjs/LICENSE"}, {"name": "dbus-java", "url": "https://github.com/hypfvieh/dbus-java", "version": "4.2.1", "license": "LGPL 2.0", "licenseUrl": "https://github.com/hypfvieh/dbus-java/blob/dbus-java-3.0/LICENSE"}, {"name": "debug", "url": "https://github.com/debug-js/debug", "version": "4.3.3", "license": "MIT", "licenseUrl": "https://github.com/debug-js/debug/LICENSE"}, {"name": "debug", "url": "https://github.com/visionmedia/debug", "version": "2.6.9", "license": "MIT", "licenseUrl": "https://github.com/visionmedia/debug/LICENSE"}, {"name": "debug", "url": "https://github.com/visionmedia/debug", "version": "3.2.7", "license": "MIT", "licenseUrl": "https://github.com/visionmedia/debug/LICENSE"}, {"name": "debug", "url": "https://github.com/debug-js/debug", "version": "4.3.4", "license": "MIT", "licenseUrl": "https://github.com/debug-js/debug/LICENSE"}, {"name": "debuglog", "url": "https://github.com/sam-github/node-debuglog", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/sam-github/node-debuglog/LICENSE"}, {"name": "decamelize", "url": "https://github.com/sindresorhus/decamelize", "version": "4.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/decamelize/license"}, {"name": "decode-named-character-reference", "url": "https://github.com/wooorm/decode-named-character-reference", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/wooorm/decode-named-character-reference/license"}, {"name": "decode-uri-component", "url": "https://github.com/SamVerschueren/decode-uri-component", "version": "0.2.0", "license": "MIT", "licenseUrl": "https://github.com/SamVerschueren/decode-uri-component/license"}, {"name": "deep-eql", "url": "https://github.com/chaijs/deep-eql", "version": "4.1.3", "license": "MIT", "licenseUrl": "https://github.com/chaijs/deep-eql/LICENSE"}, {"name": "deep-equal", "url": "https://github.com/substack/node-deep-equal", "version": "1.1.1", "license": "MIT", "licenseUrl": "https://github.com/substack/node-deep-equal/LICENSE"}, {"name": "deep-is", "url": "https://github.com/thlorenz/deep-is", "version": "0.1.4", "license": "MIT", "licenseUrl": "https://github.com/thlorenz/deep-is/LICENSE"}, {"name": "deepmerge", "url": "https://github.com/TehShrike/deepmerge", "version": "4.3.1", "license": "MIT", "licenseUrl": "https://github.com/TehShrike/deepmerge/license.txt"}, {"name": "default-gateway", "url": "https://github.com/silverwind/default-gateway", "version": "6.0.3", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/silverwind/default-gateway/LICENSE"}, {"name": "defaults", "url": "https://github.com/tmpvar/defaults", "version": "1.0.3", "license": "MIT", "licenseUrl": "https://github.com/tmpvar/defaults/LICENSE"}, {"name": "define-data-property", "url": "https://github.com/ljharb/define-data-property", "version": "1.1.4", "license": "MIT", "licenseUrl": "https://github.com/ljharb/define-data-property/LICENSE"}, {"name": "define-lazy-prop", "url": "https://github.com/sindresorhus/define-lazy-prop", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/define-lazy-prop/license"}, {"name": "define-properties", "url": "https://github.com/ljharb/define-properties", "version": "1.1.4", "license": "MIT", "licenseUrl": "https://github.com/ljharb/define-properties/LICENSE"}, {"name": "del", "url": "https://github.com/sindresorhus/del", "version": "6.1.1", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/del/license"}, {"name": "delaunator", "url": "https://github.com/mapbox/delaunator", "version": "5.0.1", "license": "ISC", "licenseUrl": "https://github.com/mapbox/delaunator/LICENSE"}, {"name": "delegates", "url": "https://github.com/visionmedia/node-delegates", "version": "1.0.0", "license": "MIT", "licenseUrl": "https://github.com/visionmedia/node-delegates/License"}, {"name": "depd", "url": "https://github.com/dougwilson/nodejs-depd", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/dougwilson/nodejs-depd/LICENSE"}, {"name": "depd", "url": "https://github.com/dougwilson/nodejs-depd", "version": "1.1.2", "license": "MIT", "licenseUrl": "https://github.com/dougwilson/nodejs-depd/LICENSE"}, {"name": "dependency-graph", "url": "https://github.com/jriecken/dependency-graph", "version": "0.11.0", "license": "MIT", "licenseUrl": "https://github.com/jriecken/dependency-graph/LICENSE"}, {"name": "dequal", "url": "https://github.com/lukeed/dequal", "version": "2.0.3", "license": "MIT", "licenseUrl": "https://github.com/lukeed/dequal/license"}, {"name": "destroy", "url": "https://github.com/stream-utils/destroy", "version": "1.2.0", "license": "MIT", "licenseUrl": "https://github.com/stream-utils/destroy/LICENSE"}, {"name": "detect-node", "url": "https://github.com/iliakan/detect-node", "version": "2.1.0", "license": "MIT", "licenseUrl": "https://github.com/iliakan/detect-node/LICENSE"}, {"name": "dezalgo", "url": "https://github.com/npm/dezalgo", "version": "1.0.4", "license": "ISC", "licenseUrl": "https://github.com/npm/dezalgo/LICENSE"}, {"name": "di", "url": "https://github.com/vojtajina/node-di", "version": "0.0.1", "license": "MIT", "licenseUrl": "https://github.com/vojtajina/node-di/LICENSE"}, {"name": "diff", "url": "https://github.com/kpdecker/jsdiff", "version": "5.0.0", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/kpdecker/jsdiff/LICENSE"}, {"name": "diff", "url": "https://github.com/kpdecker/jsdiff", "version": "4.0.2", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/kpdecker/jsdiff/LICENSE"}, {"name": "diff-sequences", "url": "https://github.com/facebook/jest", "version": "28.1.1", "license": "MIT", "licenseUrl": "https://github.com/facebook/jest/LICENSE"}, {"name": "dir-glob", "url": "https://github.com/kevva/dir-glob", "version": "3.0.1", "license": "MIT", "licenseUrl": "https://github.com/kevva/dir-glob/license"}, {"name": "discoveryjs/json-ext", "url": "https://github.com/discoveryjs/json-ext", "version": "0.5.6", "license": "MIT", "licenseUrl": "https://github.com/discoveryjs/json-ext/LICENSE"}, {"name": "dns-equal", "url": "https://github.com/watson/dns-equal", "version": "1.0.0", "license": "MIT", "licenseUrl": "https://github.com/watson/dns-equal/LICENSE"}, {"name": "dns-packet", "url": "https://github.com/mafintosh/dns-packet", "version": "1.3.4", "license": "MIT", "licenseUrl": "https://github.com/mafintosh/dns-packet/LICENSE"}, {"name": "dns-txt", "url": "https://github.com/watson/dns-txt", "version": "2.0.2", "license": "MIT", "licenseUrl": "https://github.com/watson/dns-txt/LICENSE"}, {"name": "doctrine", "url": "https://github.com/eslint/doctrine", "version": "3.0.0", "license": "Apache-2.0", "licenseUrl": "https://github.com/eslint/doctrine/LICENSE"}, {"name": "dom-helpers", "url": "https://github.com/jquense/dom-helpers", "version": "3.4.0", "license": "MIT", "licenseUrl": "https://github.com/jquense/dom-helpers/LICENSE"}, {"name": "dom-serialize", "url": "https://github.com/webmodules/dom-serialize", "version": "2.2.1", "license": "MIT", "licenseUrl": "https://github.com/webmodules/dom-serialize/README.md"}, {"name": "dom-serializer", "url": "https://github.com/cheeriojs/dom-renderer", "version": "0.2.2", "license": "MIT", "licenseUrl": "https://github.com/cheeriojs/dom-renderer/LICENSE"}, {"name": "dom-serializer", "url": "https://github.com/cheeriojs/dom-renderer", "version": "1.4.1", "license": "MIT", "licenseUrl": "https://github.com/cheeriojs/dom-renderer/LICENSE"}, {"name": "dom4", "url": "https://github.com/WebReflection/dom4", "version": "2.1.6", "license": "MIT", "licenseUrl": "https://github.com/WebReflection/dom4/LICENSE.txt"}, {"name": "domelementtype", "url": "https://github.com/fb55/domelementtype", "version": "1.3.1", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/fb55/domelementtype/LICENSE"}, {"name": "domelementtype", "url": "https://github.com/fb55/domelementtype", "version": "2.3.0", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/fb55/domelementtype/LICENSE"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/fb55/domhandler", "version": "4.3.1", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/fb55/domhandler/LICENSE"}, {"name": "dompurify", "url": "https://github.com/cure53/DOMPurify", "version": "3.1.2", "license": "(MPL-2.0 OR Apache-2.0)", "licenseUrl": "https://github.com/cure53/DOMPurify/LICENSE"}, {"name": "domutils", "url": "https://github.com/fb55/domutils", "version": "2.8.0", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/fb55/domutils/LICENSE"}, {"name": "domutils", "url": "https://github.com/FB55/domutils", "version": "1.7.0", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/FB55/domutils/LICENSE"}, {"name": "dot-prop", "url": "https://github.com/sindresorhus/dot-prop", "version": "5.3.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/dot-prop/license"}, {"name": "dotenv", "url": "https://github.com/motdotla/dotenv", "version": "10.0.0", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/motdotla/dotenv/LICENSE"}, {"name": "duplexer2", "url": "https://github.com/deoxxa/duplexer2", "version": "0.1.4", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/deoxxa/duplexer2/LICENSE.md"}, {"name": "duplexify", "url": "https://github.com/mafintosh/duplexify", "version": "3.7.1", "license": "MIT", "licenseUrl": "https://github.com/mafintosh/duplexify/LICENSE"}, {"name": "ee-first", "url": "https://github.com/jonathanong/ee-first", "version": "1.1.1", "license": "MIT", "licenseUrl": "https://github.com/jonathanong/ee-first/LICENSE"}, {"name": "ejs", "url": "https://github.com/mde/ejs", "version": "3.1.8", "license": "Apache-2.0", "licenseUrl": "https://github.com/mde/ejs/LICENSE"}, {"name": "electron-to-chromium", "url": "https://github.com/kilian/electron-to-chromium", "version": "1.4.170", "license": "ISC", "licenseUrl": "https://github.com/kilian/electron-to-chromium/LICENSE"}, {"name": "elkjs", "url": "https://github.com/kieler/elkjs", "version": "0.9.3", "license": "EPL-2.0", "licenseUrl": "https://github.com/kieler/elkjs/LICENSE.md"}, {"name": "emoji-java", "url": "https://github.com/vdurmont/emoji-java", "version": "5.1.1", "license": "MIT", "licenseUrl": "https://github.com/vdurmont/emoji-java/blob/master/LICENSE.md"}, {"name": "emoji-regex", "url": "https://github.com/mathiasbynens/emoji-regex", "version": "8.0.0", "license": "MIT", "licenseUrl": "https://github.com/mathiasbynens/emoji-regex/LICENSE-MIT.txt"}, {"name": "emojis-list", "url": "https://github.com/kikobeats/emojis-list", "version": "3.0.0", "license": "MIT", "licenseUrl": "https://github.com/kikobeats/emojis-list/LICENSE.md"}, {"name": "encodeurl", "url": "https://github.com/pillarjs/encodeurl", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/pillarjs/encodeurl/LICENSE"}, {"name": "encoding", "url": "https://github.com/andris9/encoding", "version": "0.1.13", "license": "MIT", "licenseUrl": "https://github.com/andris9/encoding/LICENSE"}, {"name": "end-of-stream", "url": "https://github.com/mafintosh/end-of-stream", "version": "1.4.4", "license": "MIT", "licenseUrl": "https://github.com/mafintosh/end-of-stream/LICENSE"}, {"name": "engine.io", "url": "https://github.com/socketio/engine.io", "version": "6.2.0", "license": "MIT", "licenseUrl": "https://github.com/socketio/engine.io/LICENSE"}, {"name": "engine.io-parser", "url": "https://github.com/socketio/engine.io-parser", "version": "5.0.4", "license": "MIT", "licenseUrl": "https://github.com/socketio/engine.io-parser/LICENSE"}, {"name": "enhanced-resolve", "url": "https://github.com/webpack/enhanced-resolve", "version": "5.9.3", "license": "MIT", "licenseUrl": "https://github.com/webpack/enhanced-resolve/LICENSE"}, {"name": "enquirer", "url": "https://github.com/enquirer/enquirer", "version": "2.3.6", "license": "MIT", "licenseUrl": "https://github.com/enquirer/enquirer/LICENSE"}, {"name": "ent", "url": "https://github.com/substack/node-ent", "version": "2.2.0", "license": "MIT", "licenseUrl": "https://github.com/substack/node-ent/LICENSE"}, {"name": "entities", "url": "https://github.com/fb55/entities", "version": "2.2.0", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/fb55/entities/LICENSE"}, {"name": "env-paths", "url": "https://github.com/sindresorhus/env-paths", "version": "2.2.1", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/env-paths/license"}, {"name": "err-code", "url": "https://github.com/IndigoUnited/js-err-code", "version": "2.0.3", "license": "MIT", "licenseUrl": "https://github.com/IndigoUnited/js-err-code/README.md"}, {"name": "errno", "url": "https://github.com/rvagg/node-errno", "version": "0.1.8", "license": "MIT", "licenseUrl": "https://github.com/rvagg/node-errno/README.md"}, {"name": "error-ex", "url": "https://github.com/qix-/node-error-ex", "version": "1.3.2", "license": "MIT", "licenseUrl": "https://github.com/qix-/node-error-ex/LICENSE"}, {"name": "es-abstract", "url": "https://github.com/ljharb/es-abstract", "version": "1.20.1", "license": "MIT", "licenseUrl": "https://github.com/ljharb/es-abstract/LICENSE"}, {"name": "es-array-method-boxes-properly", "url": "https://github.com/ljharb/es-array-method-boxes-properly", "version": "1.0.0", "license": "MIT", "licenseUrl": "https://github.com/ljharb/es-array-method-boxes-properly/LICENSE"}, {"name": "es-define-property", "url": "https://github.com/ljharb/es-define-property", "version": "1.0.0", "license": "MIT", "licenseUrl": "https://github.com/ljharb/es-define-property/LICENSE"}, {"name": "es-errors", "url": "https://github.com/ljharb/es-errors", "version": "1.3.0", "license": "MIT", "licenseUrl": "https://github.com/ljharb/es-errors/LICENSE"}, {"name": "es-module-lexer", "url": "https://github.com/guybedford/es-module-lexer", "version": "0.9.3", "license": "MIT", "licenseUrl": "https://github.com/guybedford/es-module-lexer/LICENSE"}, {"name": "es-to-primitive", "url": "https://github.com/ljharb/es-to-primitive", "version": "1.2.1", "license": "MIT", "licenseUrl": "https://github.com/ljharb/es-to-primitive/LICENSE"}, {"name": "esbuild", "url": "https://github.com/evanw/esbuild", "version": "0.14.22", "license": "MIT", "licenseUrl": "https://github.com/evanw/esbuild/README.md"}, {"name": "esbuild-linux-64", "url": "https://github.com/evanw/esbuild", "version": "0.14.22", "license": "MIT", "licenseUrl": "https://github.com/evanw/esbuild/README.md"}, {"name": "esbuild-wasm", "url": "https://github.com/evanw/esbuild", "version": "0.14.22", "license": "MIT", "licenseUrl": "https://github.com/evanw/esbuild/README.md"}, {"name": "escalade", "url": "https://github.com/lukeed/escalade", "version": "3.1.1", "license": "MIT", "licenseUrl": "https://github.com/lukeed/escalade/license"}, {"name": "escape-html", "url": "https://github.com/component/escape-html", "version": "1.0.3", "license": "MIT", "licenseUrl": "https://github.com/component/escape-html/LICENSE"}, {"name": "escape-string-regexp", "url": "https://github.com/sindresorhus/escape-string-regexp", "version": "1.0.5", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/escape-string-regexp/license"}, {"name": "escape-string-regexp", "url": "https://github.com/sindresorhus/escape-string-regexp", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/escape-string-regexp/license"}, {"name": "escape-string-regexp", "url": "https://github.com/sindresorhus/escape-string-regexp", "version": "4.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/escape-string-regexp/license"}, {"name": "escodegen", "url": "https://github.com/estools/escodegen", "version": "1.14.3", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/estools/escodegen/LICENSE.BSD"}, {"name": "eslint", "url": "https://github.com/eslint/eslint", "version": "8.18.0", "license": "MIT", "licenseUrl": "https://github.com/eslint/eslint/LICENSE"}, {"name": "eslint-plugin-deprecation", "url": "https://github.com/gund/eslint-plugin-deprecation", "version": "1.3.2", "license": "LGPL-3.0-or-later", "licenseUrl": "https://github.com/gund/eslint-plugin-deprecation/LICENSE"}, {"name": "eslint-scope", "url": "https://github.com/eslint/eslint-scope", "version": "5.1.1", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/eslint/eslint-scope/LICENSE"}, {"name": "eslint-scope", "url": "https://github.com/eslint/eslint-scope", "version": "7.1.1", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/eslint/eslint-scope/LICENSE"}, {"name": "eslint-utils", "url": "https://github.com/mysticatea/eslint-utils", "version": "3.0.0", "license": "MIT", "licenseUrl": "https://github.com/mysticatea/eslint-utils/LICENSE"}, {"name": "eslint-visitor-keys", "url": "https://github.com/eslint/eslint-visitor-keys", "version": "2.1.0", "license": "Apache-2.0", "licenseUrl": "https://github.com/eslint/eslint-visitor-keys/LICENSE"}, {"name": "eslint-visitor-keys", "url": "https://github.com/eslint/eslint-visitor-keys", "version": "3.3.0", "license": "Apache-2.0", "licenseUrl": "https://github.com/eslint/eslint-visitor-keys/LICENSE"}, {"name": "eslint/eslintrc", "url": "https://github.com/eslint/eslintrc", "version": "1.3.0", "license": "MIT", "licenseUrl": "https://github.com/eslint/eslintrc/LICENSE"}, {"name": "esm", "url": "https://github.com/standard-things/esm", "version": "3.2.25", "license": "MIT", "licenseUrl": "https://github.com/standard-things/esm/LICENSE"}, {"name": "espree", "url": "https://github.com/eslint/espree", "version": "9.3.2", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/eslint/espree/LICENSE"}, {"name": "esprima", "url": "https://github.com/jquery/esprima", "version": "4.0.1", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/jquery/esprima/LICENSE.BSD"}, {"name": "esquery", "url": "https://github.com/estools/esquery", "version": "1.4.0", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/estools/esquery/license.txt"}, {"name": "esrecurse", "url": "https://github.com/estools/esrecurse", "version": "4.3.0", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/estools/esrecurse/README.md"}, {"name": "estraverse", "url": "https://github.com/estools/estraverse", "version": "5.3.0", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/estools/estraverse/LICENSE.BSD"}, {"name": "estraverse", "url": "https://github.com/estools/estraverse", "version": "4.3.0", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/estools/estraverse/LICENSE.BSD"}, {"name": "esutils", "url": "https://github.com/estools/esutils", "version": "2.0.3", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/estools/esutils/LICENSE.BSD"}, {"name": "etag", "url": "https://github.com/jshttp/etag", "version": "1.8.1", "license": "MIT", "licenseUrl": "https://github.com/jshttp/etag/LICENSE"}, {"name": "eventemitter-asyncresource", "url": "https://github.com/addaleax/eventemitter-asyncresource", "version": "1.0.0", "license": "MIT", "licenseUrl": "https://github.com/addaleax/eventemitter-asyncresource/LICENSE"}, {"name": "eventemitter3", "url": "https://github.com/primus/eventemitter3", "version": "4.0.7", "license": "MIT", "licenseUrl": "https://github.com/primus/eventemitter3/LICENSE"}, {"name": "events", "url": "https://github.com/Gozala/events", "version": "3.3.0", "license": "MIT", "licenseUrl": "https://github.com/Gozala/events/LICENSE"}, {"name": "execa", "url": "https://github.com/sindresorhus/execa", "version": "5.1.1", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/execa/license"}, {"name": "exenv-es6", "url": "https://github.com/chrisdholt/exenv-es6", "version": "1.1.1", "license": "MIT", "licenseUrl": "https://github.com/chrisdholt/exenv-es6/LICENSE"}, {"name": "expect", "url": "https://github.com/facebook/jest", "version": "28.1.1", "license": "MIT", "licenseUrl": "https://github.com/facebook/jest/LICENSE"}, {"name": "express", "url": "https://github.com/expressjs/express", "version": "4.18.1", "license": "MIT", "licenseUrl": "https://github.com/expressjs/express/LICENSE"}, {"name": "extend", "url": "https://github.com/justmoon/node-extend", "version": "3.0.2", "license": "MIT", "licenseUrl": "https://github.com/justmoon/node-extend/LICENSE"}, {"name": "external-editor", "url": "https://github.com/mrkmg/node-external-editor", "version": "3.1.0", "license": "MIT", "licenseUrl": "https://github.com/mrkmg/node-external-editor/LICENSE"}, {"name": "falafel", "url": "https://github.com/substack/node-falafel", "version": "2.2.5", "license": "MIT", "licenseUrl": "https://github.com/substack/node-falafel/LICENSE"}, {"name": "fast-deep-equal", "url": "https://github.com/epoberezkin/fast-deep-equal", "version": "3.1.3", "license": "MIT", "licenseUrl": "https://github.com/epoberezkin/fast-deep-equal/LICENSE"}, {"name": "fast-glob", "url": "https://github.com/mrmlnc/fast-glob", "version": "3.2.7", "license": "MIT", "licenseUrl": "https://github.com/mrmlnc/fast-glob/LICENSE"}, {"name": "fast-glob", "url": "https://github.com/mrmlnc/fast-glob", "version": "3.2.11", "license": "MIT", "licenseUrl": "https://github.com/mrmlnc/fast-glob/LICENSE"}, {"name": "fast-json-stable-stringify", "url": "https://github.com/epoberezkin/fast-json-stable-stringify", "version": "2.1.0", "license": "MIT", "licenseUrl": "https://github.com/epoberezkin/fast-json-stable-stringify/LICENSE"}, {"name": "fast-le<PERSON><PERSON><PERSON>", "url": "https://github.com/hiddentao/fast-levenshtein", "version": "2.0.6", "license": "MIT", "licenseUrl": "https://github.com/hiddentao/fast-levenshtein/LICENSE.md"}, {"name": "fastq", "url": "https://github.com/mcollina/fastq", "version": "1.13.0", "license": "ISC", "licenseUrl": "https://github.com/mcollina/fastq/LICENSE"}, {"name": "fastutil", "url": "https://github.com/vigna/fastutil", "version": "8.5.14-jb1", "license": "Apache 2.0", "licenseUrl": "https://github.com/vigna/fastutil/blob/master/LICENSE-2.0"}, {"name": "faye-websocket", "url": "https://github.com/faye/faye-websocket-node", "version": "0.11.4", "license": "Apache-2.0", "licenseUrl": "https://github.com/faye/faye-websocket-node/LICENSE.md"}, {"name": "figures", "url": "https://github.com/sindresorhus/figures", "version": "3.2.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/figures/license"}, {"name": "file-entry-cache", "url": "https://github.com/royriojas/file-entry-cache", "version": "6.0.1", "license": "MIT", "licenseUrl": "https://github.com/royriojas/file-entry-cache/LICENSE"}, {"name": "file-loader", "url": "https://github.com/webpack-contrib/file-loader", "version": "6.2.0", "license": "MIT", "licenseUrl": "https://github.com/webpack-contrib/file-loader/LICENSE"}, {"name": "filelist", "url": "https://github.com/mde/filelist", "version": "1.0.4", "license": "Apache-2.0", "licenseUrl": "https://github.com/mde/filelist/README.md"}, {"name": "fill-range", "url": "https://github.com/jonschlinkert/fill-range", "version": "7.0.1", "license": "MIT", "licenseUrl": "https://github.com/jonschlinkert/fill-range/LICENSE"}, {"name": "finalhandler", "url": "https://github.com/pillarjs/finalhandler", "version": "1.1.2", "license": "MIT", "licenseUrl": "https://github.com/pillarjs/finalhandler/LICENSE"}, {"name": "finalhandler", "url": "https://github.com/pillarjs/finalhandler", "version": "1.2.0", "license": "MIT", "licenseUrl": "https://github.com/pillarjs/finalhandler/LICENSE"}, {"name": "find-cache-dir", "url": "https://github.com/avajs/find-cache-dir", "version": "3.3.2", "license": "MIT", "licenseUrl": "https://github.com/avajs/find-cache-dir/license"}, {"name": "find-up", "url": "https://github.com/sindresorhus/find-up", "version": "5.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/find-up/license"}, {"name": "find-up", "url": "https://github.com/sindresorhus/find-up", "version": "4.1.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/find-up/license"}, {"name": "findup", "url": "https://github.com/Filirom1/findup", "version": "0.1.5", "license": "MIT", "licenseUrl": "https://github.com/Filirom1/findup/README.md"}, {"name": "flat", "url": "https://github.com/hughsk/flat", "version": "5.0.2", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/hughsk/flat/LICENSE"}, {"name": "flat-cache", "url": "https://github.com/royriojas/flat-cache", "version": "3.0.4", "license": "MIT", "licenseUrl": "https://github.com/royriojas/flat-cache/LICENSE"}, {"name": "flatted", "url": "https://github.com/WebReflection/flatted", "version": "3.2.5", "license": "ISC", "licenseUrl": "https://github.com/WebReflection/flatted/LICENSE"}, {"name": "follow-redirects", "url": "https://github.com/follow-redirects/follow-redirects", "version": "1.15.1", "license": "MIT", "licenseUrl": "https://github.com/follow-redirects/follow-redirects/LICENSE"}, {"name": "font-awesome", "url": "https://github.com/FortAwesome/Font-Awesome", "version": "4.7.0", "license": "(OFL-1.1 AND MIT)", "licenseUrl": "https://github.com/FortAwesome/Font-Awesome/README.md"}, {"name": "fortawesome/fontawesome-free", "url": "https://github.com/FortAwesome/Font-Awesome", "version": "5.15.4", "license": "(CC-BY-4.0 AND OFL-1.1 AND MIT)", "licenseUrl": "https://github.com/FortAwesome/Font-Awesome/LICENSE.txt"}, {"name": "forwarded", "url": "https://github.com/jshttp/forwarded", "version": "0.2.0", "license": "MIT", "licenseUrl": "https://github.com/jshttp/forwarded/LICENSE"}, {"name": "fraction.js", "url": "https://github.com/infusion/Fraction.js", "version": "4.2.0", "license": "MIT", "licenseUrl": "https://github.com/infusion/Fraction.js/LICENSE"}, {"name": "free-style", "url": "https://github.com/blakeembrey/free-style", "version": "3.1.0", "license": "MIT", "licenseUrl": "https://github.com/blakeembrey/free-style/LICENSE"}, {"name": "fresh", "url": "https://github.com/jshttp/fresh", "version": "0.5.2", "license": "MIT", "licenseUrl": "https://github.com/jshttp/fresh/LICENSE"}, {"name": "from2", "url": "https://github.com/hughsk/from2", "version": "2.3.0", "license": "MIT", "licenseUrl": "https://github.com/hughsk/from2/LICENSE.md"}, {"name": "from2-array", "url": "https://github.com/binocarlos/from2-array", "version": "0.0.4", "license": "MIT", "licenseUrl": "https://github.com/binocarlos/from2-array/LICENSE"}, {"name": "fs-constants", "url": "https://github.com/mafintosh/fs-constants", "version": "1.0.0", "license": "MIT", "licenseUrl": "https://github.com/mafintosh/fs-constants/LICENSE"}, {"name": "fs-extra", "url": "https://github.com/jprichardson/node-fs-extra", "version": "9.1.0", "license": "MIT", "licenseUrl": "https://github.com/jprichardson/node-fs-extra/LICENSE"}, {"name": "fs-extra", "url": "https://github.com/jprichardson/node-fs-extra", "version": "10.1.0", "license": "MIT", "licenseUrl": "https://github.com/jprichardson/node-fs-extra/LICENSE"}, {"name": "fs-minipass", "url": "https://github.com/npm/fs-minipass", "version": "2.1.0", "license": "ISC", "licenseUrl": "https://github.com/npm/fs-minipass/LICENSE"}, {"name": "fs-monkey", "url": "https://github.com/streamich/fs-monkey", "version": "1.0.3", "license": "Unlicense", "licenseUrl": "https://github.com/streamich/fs-monkey/LICENSE"}, {"name": "fs.realpath", "url": "https://github.com/isaacs/fs.realpath", "version": "1.0.0", "license": "ISC", "licenseUrl": "https://github.com/isaacs/fs.realpath/LICENSE"}, {"name": "function-bind", "url": "https://github.com/Raynos/function-bind", "version": "1.1.2", "license": "MIT", "licenseUrl": "https://github.com/Raynos/function-bind/LICENSE"}, {"name": "function.prototype.name", "url": "https://github.com/es-shims/Function.prototype.name", "version": "1.1.5", "license": "MIT", "licenseUrl": "https://github.com/es-shims/Function.prototype.name/LICENSE"}, {"name": "functional-red-black-tree", "url": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/functional-red-black-tree", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/functional-red-black-tree/LICENSE"}, {"name": "functions-have-names", "url": "https://github.com/inspect-js/functions-have-names", "version": "1.2.3", "license": "MIT", "licenseUrl": "https://github.com/inspect-js/functions-have-names/LICENSE"}, {"name": "gRPC Kotlin: <PERSON><PERSON>", "url": "https://grpc.io/", "version": "1.4.1", "license": "Apache 2.0", "licenseUrl": "https://github.com/grpc/grpc-java/blob/master/LICENSE"}, {"name": "gRPC: Core", "url": "https://grpc.io/", "version": "1.66.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/grpc/grpc-java/blob/master/LICENSE"}, {"name": "gRPC: <PERSON><PERSON>", "url": "https://grpc.io/", "version": "1.66.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/grpc/grpc-java/blob/master/LICENSE"}, {"name": "gRPC: Protobuf", "url": "https://grpc.io/", "version": "1.66.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/grpc/grpc-java/blob/master/LICENSE"}, {"name": "gRPC: <PERSON>ub", "url": "https://grpc.io/", "version": "1.66.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/grpc/grpc-java/blob/master/LICENSE"}, {"name": "gar/promisify", "url": "https://github.com/wraithgar/gar-promisify", "version": "1.1.3", "license": "MIT", "licenseUrl": "https://github.com/wraithgar/gar-promisify/LICENSE.md"}, {"name": "gauge", "url": "https://github.com/npm/gauge", "version": "4.0.4", "license": "ISC", "licenseUrl": "https://github.com/npm/gauge/LICENSE.md"}, {"name": "gens<PERSON>", "url": "https://github.com/loganfsmyth/gensync", "version": "1.0.0-beta.2", "license": "MIT", "licenseUrl": "https://github.com/loganfsmyth/gensync/LICENSE"}, {"name": "get-caller-file", "url": "https://github.com/stefanpenner/get-caller-file", "version": "2.0.5", "license": "ISC", "licenseUrl": "https://github.com/stefanpenner/get-caller-file/LICENSE.md"}, {"name": "get-func-name", "url": "https://github.com/chaijs/get-func-name", "version": "2.0.2", "license": "MIT", "licenseUrl": "https://github.com/chaijs/get-func-name/LICENSE"}, {"name": "get-intrinsic", "url": "https://github.com/ljharb/get-intrinsic", "version": "1.2.4", "license": "MIT", "licenseUrl": "https://github.com/ljharb/get-intrinsic/LICENSE"}, {"name": "get-package-type", "url": "https://github.com/cfware/get-package-type", "version": "0.1.0", "license": "MIT", "licenseUrl": "https://github.com/cfware/get-package-type/LICENSE"}, {"name": "get-stream", "url": "https://github.com/sindresorhus/get-stream", "version": "6.0.1", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/get-stream/license"}, {"name": "get-symbol-description", "url": "https://github.com/inspect-js/get-symbol-description", "version": "1.0.0", "license": "MIT", "licenseUrl": "https://github.com/inspect-js/get-symbol-description/LICENSE"}, {"name": "github-slugger", "url": "https://github.com/Flet/github-slugger", "version": "2.0.0", "license": "ISC", "licenseUrl": "https://github.com/Flet/github-slugger/LICENSE"}, {"name": "github.oshi.core", "url": "https://github.com/oshi/oshi", "version": "6.6.0", "license": "MIT", "licenseUrl": "https://github.com/oshi/oshi/blob/master/LICENSE"}, {"name": "glob", "url": "https://github.com/isaacs/node-glob", "version": "7.2.0", "license": "ISC", "licenseUrl": "https://github.com/isaacs/node-glob/LICENSE"}, {"name": "glob", "url": "https://github.com/isaacs/node-glob", "version": "8.0.3", "license": "ISC", "licenseUrl": "https://github.com/isaacs/node-glob/LICENSE"}, {"name": "glob", "url": "https://github.com/isaacs/node-glob", "version": "7.1.4", "license": "ISC", "licenseUrl": "https://github.com/isaacs/node-glob/LICENSE"}, {"name": "glob-parent", "url": "https://github.com/gulpjs/glob-parent", "version": "5.1.2", "license": "ISC", "licenseUrl": "https://github.com/gulpjs/glob-parent/LICENSE"}, {"name": "glob-parent", "url": "https://github.com/gulpjs/glob-parent", "version": "6.0.2", "license": "ISC", "licenseUrl": "https://github.com/gulpjs/glob-parent/LICENSE"}, {"name": "glob-to-regexp", "url": "https://github.com/fitzgen/glob-to-regexp", "version": "0.4.1", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/fitzgen/glob-to-regexp/README.md"}, {"name": "globals", "url": "https://github.com/sindresorhus/globals", "version": "13.15.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/globals/license"}, {"name": "globals", "url": "https://github.com/sindresorhus/globals", "version": "11.12.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/globals/license"}, {"name": "globby", "url": "https://github.com/sindresorhus/globby", "version": "11.1.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/globby/license"}, {"name": "globby", "url": "https://github.com/sindresorhus/globby", "version": "12.2.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/globby/license"}, {"name": "glsl-inject-defines", "url": "https://github.com/mattdesl/glsl-inject-defines", "version": "1.0.3", "license": "MIT", "licenseUrl": "https://github.com/mattdesl/glsl-inject-defines/LICENSE.md"}, {"name": "glsl-resolve", "url": "https://github.com/hughsk/glsl-resolve", "version": "0.0.1", "license": "MIT", "licenseUrl": "https://github.com/hughsk/glsl-resolve/LICENSE.md"}, {"name": "glsl-token-assignments", "url": "https://github.com/stackgl/glsl-token-assignments", "version": "2.0.2", "license": "MIT", "licenseUrl": "https://github.com/stackgl/glsl-token-assignments/LICENSE.md"}, {"name": "glsl-token-defines", "url": "https://github.com/stackgl/glsl-token-defines", "version": "1.0.0", "license": "MIT", "licenseUrl": "https://github.com/stackgl/glsl-token-defines/LICENSE.md"}, {"name": "glsl-token-depth", "url": "https://github.com/stackgl/glsl-token-depth", "version": "1.1.2", "license": "MIT", "licenseUrl": "https://github.com/stackgl/glsl-token-depth/LICENSE.md"}, {"name": "glsl-token-descope", "url": "https://github.com/stackgl/glsl-token-descope", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/stackgl/glsl-token-descope/LICENSE.md"}, {"name": "glsl-token-inject-block", "url": "https://github.com/Jam3/glsl-token-inject-block", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/Jam3/glsl-token-inject-block/LICENSE.md"}, {"name": "glsl-token-properties", "url": "https://github.com/stackgl/glsl-token-properties", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/stackgl/glsl-token-properties/LICENSE.md"}, {"name": "glsl-token-scope", "url": "https://github.com/stackgl/glsl-token-scope", "version": "1.1.2", "license": "MIT", "licenseUrl": "https://github.com/stackgl/glsl-token-scope/LICENSE.md"}, {"name": "glsl-token-string", "url": "https://github.com/stackgl/glsl-token-string", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/stackgl/glsl-token-string/LICENSE.md"}, {"name": "glsl-token-whitespace-trim", "url": "https://github.com/hughsk/glsl-token-whitespace-trim", "version": "1.0.0", "license": "MIT", "licenseUrl": "https://github.com/hughsk/glsl-token-whitespace-trim/LICENSE.md"}, {"name": "glsl-tokenizer", "url": "https://github.com/gl-modules/glsl-tokenizer", "version": "2.1.5", "license": "MIT", "licenseUrl": "https://github.com/gl-modules/glsl-tokenizer/LICENSE.md"}, {"name": "glslify", "url": "https://github.com/stackgl/glslify", "version": "7.1.1", "license": "MIT", "licenseUrl": "https://github.com/stackgl/glslify/LICENSE.md"}, {"name": "glslify-bundle", "url": "https://github.com/stackgl/glslify-bundle", "version": "5.1.1", "license": "MIT", "licenseUrl": "https://github.com/stackgl/glslify-bundle/LICENSE.md"}, {"name": "glslify-deps", "url": "https://github.com/stackgl/glslify-deps", "version": "1.3.2", "license": "ISC", "licenseUrl": "https://github.com/stackgl/glslify-deps/README.md"}, {"name": "googlecode.plist.dd", "url": "https://github.com/3breadt/dd-plist/", "version": "1.28", "license": "MIT", "licenseUrl": "https://github.com/3breadt/dd-plist/blob/master/LICENSE.txt"}, {"name": "gopd", "url": "https://github.com/ljharb/gopd", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/ljharb/gopd/LICENSE"}, {"name": "graceful-fs", "url": "https://github.com/isaacs/node-graceful-fs", "version": "4.2.10", "license": "ISC", "licenseUrl": "https://github.com/isaacs/node-graceful-fs/LICENSE"}, {"name": "gud", "url": "https://github.com/jamiebuilds/global-unique-id", "version": "1.0.0", "license": "MIT", "licenseUrl": "https://github.com/jamiebuilds/global-unique-id/README.md"}, {"name": "handle-thing", "url": "https://github.com/indutny/handle-thing", "version": "2.0.1", "license": "MIT", "licenseUrl": "https://github.com/indutny/handle-thing/README.md"}, {"name": "has", "url": "https://github.com/tarruda/has", "version": "1.0.3", "license": "MIT", "licenseUrl": "https://github.com/tarruda/has/LICENSE-MIT"}, {"name": "has-bigints", "url": "https://github.com/ljharb/has-bigints", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/ljharb/has-bigints/LICENSE"}, {"name": "has-flag", "url": "https://github.com/sindresorhus/has-flag", "version": "4.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/has-flag/license"}, {"name": "has-flag", "url": "https://github.com/sindresorhus/has-flag", "version": "3.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/has-flag/license"}, {"name": "has-property-descriptors", "url": "https://github.com/inspect-js/has-property-descriptors", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/inspect-js/has-property-descriptors/LICENSE"}, {"name": "has-proto", "url": "https://github.com/inspect-js/has-proto", "version": "1.0.3", "license": "MIT", "licenseUrl": "https://github.com/inspect-js/has-proto/LICENSE"}, {"name": "has-symbols", "url": "https://github.com/inspect-js/has-symbols", "version": "1.0.3", "license": "MIT", "licenseUrl": "https://github.com/inspect-js/has-symbols/LICENSE"}, {"name": "has-tostringtag", "url": "https://github.com/inspect-js/has-tostringtag", "version": "1.0.0", "license": "MIT", "licenseUrl": "https://github.com/inspect-js/has-tostringtag/LICENSE"}, {"name": "has-unicode", "url": "https://github.com/iarna/has-unicode", "version": "2.0.1", "license": "ISC", "licenseUrl": "https://github.com/iarna/has-unicode/LICENSE"}, {"name": "hash4j", "url": "https://github.com/dynatrace-oss/hash4j", "version": "0.20.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/dynatrace-oss/hash4j/blob/main/LICENSE"}, {"name": "hasown", "url": "https://github.com/inspect-js/hasOwn", "version": "2.0.2", "license": "MIT", "licenseUrl": "https://github.com/inspect-js/hasOwn/LICENSE"}, {"name": "hdr-histogram-js", "url": "https://github.com/HdrHistogram/HdrHistogramJS", "version": "2.0.3", "license": "BSD*", "licenseUrl": "https://github.com/HdrHistogram/HdrHistogramJS/LICENSE"}, {"name": "hdr-histogram-percentiles-obj", "url": "https://github.com/GlenTiki/hdr-histogram-percentiles-obj", "version": "3.0.0", "license": "MIT", "licenseUrl": "https://github.com/GlenTiki/hdr-histogram-percentiles-obj/LICENSE"}, {"name": "he", "url": "https://github.com/mathiasbynens/he", "version": "1.2.0", "license": "MIT", "licenseUrl": "https://github.com/mathiasbynens/he/LICENSE-MIT.txt"}, {"name": "hex-color-regex", "url": "https://github.com/regexps/hex-color-regex", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/regexps/hex-color-regex/LICENSE.md"}, {"name": "hosted-git-info", "url": "https://github.com/npm/hosted-git-info", "version": "2.8.9", "license": "ISC", "licenseUrl": "https://github.com/npm/hosted-git-info/LICENSE"}, {"name": "hosted-git-info", "url": "https://github.com/npm/hosted-git-info", "version": "4.1.0", "license": "ISC", "licenseUrl": "https://github.com/npm/hosted-git-info/LICENSE"}, {"name": "hpack.js", "url": "https://github.com/indutny/hpack.js", "version": "2.1.6", "license": "MIT", "licenseUrl": "https://github.com/indutny/hpack.js/README.md"}, {"name": "hppc", "url": "https://github.com/carrotsearch/hppc", "version": "0.9.1", "license": "Apache 2.0", "licenseUrl": "https://github.com/carrotsearch/hppc/blob/master/LICENSE.txt"}, {"name": "hsl-regex", "url": "https://github.com/regexps/hsl-regex", "version": "1.0.0", "license": "MIT", "licenseUrl": "https://github.com/regexps/hsl-regex/LICENSE.md"}, {"name": "hsla-regex", "url": "https://github.com/regexps/hsla-regex", "version": "1.0.0", "license": "MIT", "licenseUrl": "https://github.com/regexps/hsla-regex/LICENSE.md"}, {"name": "html-entities", "url": "https://github.com/mdevils/html-entities", "version": "2.3.3", "license": "MIT", "licenseUrl": "https://github.com/mdevils/html-entities/LICENSE"}, {"name": "html-escaper", "url": "https://github.com/WebReflection/html-escaper", "version": "2.0.2", "license": "MIT", "licenseUrl": "https://github.com/WebReflection/html-escaper/LICENSE.txt"}, {"name": "htmlparser2", "url": "https://github.com/fb55/htmlparser2", "version": "6.1.0", "license": "MIT", "licenseUrl": "https://github.com/fb55/htmlparser2/LICENSE"}, {"name": "http-cache-semantics", "url": "https://github.com/kornelski/http-cache-semantics", "version": "4.1.0", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/kornelski/http-cache-semantics/LICENSE"}, {"name": "http-deceiver", "url": "https://github.com/indutny/http-deceiver", "version": "1.2.7", "license": "MIT", "licenseUrl": "https://github.com/indutny/http-deceiver/README.md"}, {"name": "http-errors", "url": "https://github.com/jshttp/http-errors", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/jshttp/http-errors/LICENSE"}, {"name": "http-errors", "url": "https://github.com/jshttp/http-errors", "version": "1.6.3", "license": "MIT", "licenseUrl": "https://github.com/jshttp/http-errors/LICENSE"}, {"name": "http-parser-js", "url": "https://github.com/creationix/http-parser-js", "version": "0.5.7", "license": "MIT", "licenseUrl": "https://github.com/creationix/http-parser-js/LICENSE.md"}, {"name": "http-proxy", "url": "https://github.com/http-party/node-http-proxy", "version": "1.18.1", "license": "MIT", "licenseUrl": "https://github.com/http-party/node-http-proxy/LICENSE"}, {"name": "http-proxy-agent", "url": "https://github.com/TooTallNate/node-http-proxy-agent", "version": "4.0.1", "license": "MIT", "licenseUrl": "https://github.com/TooTallNate/node-http-proxy-agent/README.md"}, {"name": "http-proxy-agent", "url": "https://github.com/TooTallNate/node-http-proxy-agent", "version": "5.0.0", "license": "MIT", "licenseUrl": "https://github.com/TooTallNate/node-http-proxy-agent/README.md"}, {"name": "http-proxy-middleware", "url": "https://github.com/chimurai/http-proxy-middleware", "version": "2.0.6", "license": "MIT", "licenseUrl": "https://github.com/chimurai/http-proxy-middleware/LICENSE"}, {"name": "https-proxy-agent", "url": "https://github.com/TooTallNate/node-https-proxy-agent", "version": "5.0.0", "license": "MIT", "licenseUrl": "https://github.com/TooTallNate/node-https-proxy-agent/README.md"}, {"name": "human-signals", "url": "https://github.com/ehmicky/human-signals", "version": "2.1.0", "license": "Apache-2.0", "licenseUrl": "https://github.com/ehmicky/human-signals/LICENSE"}, {"name": "humanize-ms", "url": "https://github.com/node-modules/humanize-ms", "version": "1.2.1", "license": "MIT", "licenseUrl": "https://github.com/node-modules/humanize-ms/LICENSE"}, {"name": "humanwhocodes/config-array", "url": "https://github.com/humanwhocodes/config-array", "version": "0.9.5", "license": "Apache-2.0", "licenseUrl": "https://github.com/humanwhocodes/config-array/LICENSE"}, {"name": "humanwhocodes/object-schema", "url": "https://github.com/humanwhocodes/object-schema", "version": "1.2.1", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/humanwhocodes/object-schema/LICENSE"}, {"name": "hypnosphi/create-react-context", "url": "https://github.com/thejameskyle/create-react-context", "version": "0.3.1", "license": "MIT", "licenseUrl": "https://github.com/thejameskyle/create-react-context/LICENSE"}, {"name": "iconv-lite", "url": "https://github.com/ashtuchkin/iconv-lite", "version": "0.4.24", "license": "MIT", "licenseUrl": "https://github.com/ashtuchkin/iconv-lite/LICENSE"}, {"name": "iconv-lite", "url": "https://github.com/ashtuchkin/iconv-lite", "version": "0.6.3", "license": "MIT", "licenseUrl": "https://github.com/ashtuchkin/iconv-lite/LICENSE"}, {"name": "icss-utils", "url": "https://github.com/css-modules/icss-utils", "version": "5.1.0", "license": "ISC", "licenseUrl": "https://github.com/css-modules/icss-utils/LICENSE.md"}, {"name": "ieee754", "url": "https://github.com/feross/ieee754", "version": "1.2.1", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/feross/ieee754/LICENSE"}, {"name": "ify-loader", "url": "https://github.com/hughsk/ify-loader", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/hughsk/ify-loader/LICENSE.md"}, {"name": "ignore", "url": "https://github.com/kaelzhang/node-ignore", "version": "5.2.0", "license": "MIT", "licenseUrl": "https://github.com/kaelzhang/node-ignore/LICENSE-MIT"}, {"name": "ignore-walk", "url": "https://github.com/isaacs/ignore-walk", "version": "4.0.1", "license": "ISC", "licenseUrl": "https://github.com/isaacs/ignore-walk/LICENSE"}, {"name": "image-size", "url": "https://github.com/image-size/image-size", "version": "0.5.5", "license": "MIT", "licenseUrl": "https://github.com/image-size/image-size/LICENSE"}, {"name": "imgscalr", "url": "https://github.com/thebuzzmedia/imgscalr", "version": "4.2", "license": "Apache 2.0", "licenseUrl": "https://github.com/rkalla/imgscalr/blob/master/LICENSE"}, {"name": "immutable", "url": "https://github.com/immutable-js/immutable-js", "version": "4.1.0", "license": "MIT", "licenseUrl": "https://github.com/immutable-js/immutable-js/LICENSE"}, {"name": "import-fresh", "url": "https://github.com/sindresorhus/import-fresh", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/import-fresh/license"}, {"name": "import-fresh", "url": "https://github.com/sindresorhus/import-fresh", "version": "3.3.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/import-fresh/license"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/jensyt/imurmurhash-js", "version": "0.1.4", "license": "MIT", "licenseUrl": "https://github.com/jensyt/imurmurhash-js/README.md"}, {"name": "indent-string", "url": "https://github.com/sindresorhus/indent-string", "version": "4.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/indent-string/license"}, {"name": "indexes-of", "url": "https://github.com/dominictarr/indexes-of", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/dominictarr/indexes-of/LICENSE"}, {"name": "indriya", "url": "https://github.com/unitsofmeasurement/indriya", "version": "1.3", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://github.com/unitsofmeasurement/indriya/blob/master/LICENSE"}, {"name": "infer-owner", "url": "https://github.com/npm/infer-owner", "version": "1.0.4", "license": "ISC", "licenseUrl": "https://github.com/npm/infer-owner/LICENSE"}, {"name": "inflight", "url": "https://github.com/npm/inflight", "version": "1.0.6", "license": "ISC", "licenseUrl": "https://github.com/npm/inflight/LICENSE"}, {"name": "inherits", "url": "https://github.com/isaacs/inherits", "version": "2.0.3", "license": "ISC", "licenseUrl": "https://github.com/isaacs/inherits/LICENSE"}, {"name": "inherits", "url": "https://github.com/isaacs/inherits", "version": "2.0.4", "license": "ISC", "licenseUrl": "https://github.com/isaacs/inherits/LICENSE"}, {"name": "ini", "url": "https://github.com/isaacs/ini", "version": "2.0.0", "license": "ISC", "licenseUrl": "https://github.com/isaacs/ini/LICENSE"}, {"name": "ini4j (JetBrains's fork)", "url": "https://github.com/JetBrains/intellij-deps-ini4j", "version": "0.5.5-2", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/intellij-deps-ini4j/blob/master/LICENSE.txt"}, {"name": "inquirer", "url": "https://github.com/SBoudrias/Inquirer.js", "version": "8.2.0", "license": "MIT", "licenseUrl": "https://github.com/SBoudrias/Inquirer.js/LICENSE"}, {"name": "intellij-markdown", "url": "https://github.com/JetBrains/markdown", "version": "0.7.2", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/markdown/blob/master/LICENSE"}, {"name": "internal-slot", "url": "https://github.com/ljharb/internal-slot", "version": "1.0.3", "license": "MIT", "licenseUrl": "https://github.com/ljharb/internal-slot/LICENSE"}, {"name": "internmap", "url": "https://github.com/mbostock/internmap", "version": "2.0.3", "license": "ISC", "licenseUrl": "https://github.com/mbostock/internmap/LICENSE"}, {"name": "internmap", "url": "https://github.com/mbostock/internmap", "version": "1.0.1", "license": "ISC", "licenseUrl": "https://github.com/mbostock/internmap/LICENSE"}, {"name": "ip", "url": "https://github.com/indutny/node-ip", "version": "1.1.8", "license": "MIT", "licenseUrl": "https://github.com/indutny/node-ip/README.md"}, {"name": "ipaddr.js", "url": "https://github.com/whitequark/ipaddr.js", "version": "1.9.1", "license": "MIT", "licenseUrl": "https://github.com/whitequark/ipaddr.js/LICENSE"}, {"name": "ipaddr.js", "url": "https://github.com/whitequark/ipaddr.js", "version": "2.0.1", "license": "MIT", "licenseUrl": "https://github.com/whitequark/ipaddr.js/LICENSE"}, {"name": "is-absolute-url", "url": "https://github.com/sindresorhus/is-absolute-url", "version": "2.1.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/is-absolute-url/license"}, {"name": "is-arguments", "url": "https://github.com/inspect-js/is-arguments", "version": "1.1.1", "license": "MIT", "licenseUrl": "https://github.com/inspect-js/is-arguments/LICENSE"}, {"name": "is-arrayish", "url": "https://github.com/qix-/node-is-arrayish", "version": "0.3.2", "license": "MIT", "licenseUrl": "https://github.com/qix-/node-is-arrayish/LICENSE"}, {"name": "is-arrayish", "url": "https://github.com/qix-/node-is-arrayish", "version": "0.2.1", "license": "MIT", "licenseUrl": "https://github.com/qix-/node-is-arrayish/LICENSE"}, {"name": "is-bigint", "url": "https://github.com/inspect-js/is-bigint", "version": "1.0.4", "license": "MIT", "licenseUrl": "https://github.com/inspect-js/is-bigint/LICENSE"}, {"name": "is-binary-path", "url": "https://github.com/sindresorhus/is-binary-path", "version": "2.1.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/is-binary-path/license"}, {"name": "is-boolean-object", "url": "https://github.com/inspect-js/is-boolean-object", "version": "1.1.2", "license": "MIT", "licenseUrl": "https://github.com/inspect-js/is-boolean-object/LICENSE"}, {"name": "is-callable", "url": "https://github.com/inspect-js/is-callable", "version": "1.2.4", "license": "MIT", "licenseUrl": "https://github.com/inspect-js/is-callable/LICENSE"}, {"name": "is-color-stop", "url": "https://github.com/pigcan/is-color-stop", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/pigcan/is-color-stop/LICENSE"}, {"name": "is-core-module", "url": "https://github.com/inspect-js/is-core-module", "version": "2.9.0", "license": "MIT", "licenseUrl": "https://github.com/inspect-js/is-core-module/LICENSE"}, {"name": "is-date-object", "url": "https://github.com/inspect-js/is-date-object", "version": "1.0.5", "license": "MIT", "licenseUrl": "https://github.com/inspect-js/is-date-object/LICENSE"}, {"name": "is-directory", "url": "https://github.com/jonschlinkert/is-directory", "version": "0.3.1", "license": "MIT", "licenseUrl": "https://github.com/jonschlinkert/is-directory/LICENSE"}, {"name": "is-docker", "url": "https://github.com/sindresorhus/is-docker", "version": "2.2.1", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/is-docker/license"}, {"name": "is-extglob", "url": "https://github.com/jonschlinkert/is-extglob", "version": "2.1.1", "license": "MIT", "licenseUrl": "https://github.com/jonschlinkert/is-extglob/LICENSE"}, {"name": "is-fullwidth-code-point", "url": "https://github.com/sindresorhus/is-fullwidth-code-point", "version": "3.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/is-fullwidth-code-point/license"}, {"name": "is-glob", "url": "https://github.com/micromatch/is-glob", "version": "4.0.3", "license": "MIT", "licenseUrl": "https://github.com/micromatch/is-glob/LICENSE"}, {"name": "is-interactive", "url": "https://github.com/sindresorhus/is-interactive", "version": "1.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/is-interactive/license"}, {"name": "is-lambda", "url": "https://github.com/watson/is-lambda", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/watson/is-lambda/LICENSE"}, {"name": "is-negative-zero", "url": "https://github.com/inspect-js/is-negative-zero", "version": "2.0.2", "license": "MIT", "licenseUrl": "https://github.com/inspect-js/is-negative-zero/LICENSE"}, {"name": "is-number", "url": "https://github.com/jonschlinkert/is-number", "version": "7.0.0", "license": "MIT", "licenseUrl": "https://github.com/jonschlinkert/is-number/LICENSE"}, {"name": "is-number-object", "url": "https://github.com/inspect-js/is-number-object", "version": "1.0.7", "license": "MIT", "licenseUrl": "https://github.com/inspect-js/is-number-object/LICENSE"}, {"name": "is-obj", "url": "https://github.com/sindresorhus/is-obj", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/is-obj/license"}, {"name": "is-path-cwd", "url": "https://github.com/sindresorhus/is-path-cwd", "version": "2.2.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/is-path-cwd/license"}, {"name": "is-path-inside", "url": "https://github.com/sindresorhus/is-path-inside", "version": "3.0.3", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/is-path-inside/license"}, {"name": "is-plain-obj", "url": "https://github.com/sindresorhus/is-plain-obj", "version": "3.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/is-plain-obj/license"}, {"name": "is-plain-obj", "url": "https://github.com/sindresorhus/is-plain-obj", "version": "2.1.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/is-plain-obj/license"}, {"name": "is-plain-object", "url": "https://github.com/jonschlinkert/is-plain-object", "version": "2.0.4", "license": "MIT", "licenseUrl": "https://github.com/jonschlinkert/is-plain-object/LICENSE"}, {"name": "is-plain-object", "url": "https://github.com/jonschlinkert/is-plain-object", "version": "5.0.0", "license": "MIT", "licenseUrl": "https://github.com/jonschlinkert/is-plain-object/LICENSE"}, {"name": "is-regex", "url": "https://github.com/inspect-js/is-regex", "version": "1.1.4", "license": "MIT", "licenseUrl": "https://github.com/inspect-js/is-regex/LICENSE"}, {"name": "is-resolvable", "url": "https://github.com/shinnn/is-resolvable", "version": "1.1.0", "license": "ISC", "licenseUrl": "https://github.com/shinnn/is-resolvable/LICENSE"}, {"name": "is-shared-array-buffer", "url": "https://github.com/inspect-js/is-shared-array-buffer", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/inspect-js/is-shared-array-buffer/LICENSE"}, {"name": "is-stream", "url": "https://github.com/sindresorhus/is-stream", "version": "2.0.1", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/is-stream/license"}, {"name": "is-string", "url": "https://github.com/ljharb/is-string", "version": "1.0.7", "license": "MIT", "licenseUrl": "https://github.com/ljharb/is-string/LICENSE"}, {"name": "is-symbol", "url": "https://github.com/inspect-js/is-symbol", "version": "1.0.4", "license": "MIT", "licenseUrl": "https://github.com/inspect-js/is-symbol/LICENSE"}, {"name": "is-unicode-supported", "url": "https://github.com/sindresorhus/is-unicode-supported", "version": "0.1.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/is-unicode-supported/license"}, {"name": "is-weakref", "url": "https://github.com/inspect-js/is-weakref", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/inspect-js/is-weakref/LICENSE"}, {"name": "is-what", "url": "https://github.com/mesqueeb/is-what", "version": "3.14.1", "license": "MIT", "licenseUrl": "https://github.com/mesqueeb/is-what/LICENSE"}, {"name": "is-wsl", "url": "https://github.com/sindresorhus/is-wsl", "version": "2.2.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/is-wsl/license"}, {"name": "isarray", "url": "https://github.com/juliangruber/isarray", "version": "2.0.5", "license": "MIT", "licenseUrl": "https://github.com/juliangruber/isarray/LICENSE"}, {"name": "isarray", "url": "https://github.com/juliangruber/isarray", "version": "1.0.0", "license": "MIT", "licenseUrl": "https://github.com/juliangruber/isarray/README.md"}, {"name": "isarray", "url": "https://github.com/juliangruber/isarray", "version": "0.0.1", "license": "MIT", "licenseUrl": "https://github.com/juliangruber/isarray/README.md"}, {"name": "isbinaryfile", "url": "https://github.com/gjtorikian/isBinaryFile", "version": "4.0.10", "license": "MIT", "licenseUrl": "https://github.com/gjtorikian/isBinaryFile/LICENSE.txt"}, {"name": "isexe", "url": "https://github.com/isaacs/isexe", "version": "2.0.0", "license": "ISC", "licenseUrl": "https://github.com/isaacs/isexe/LICENSE"}, {"name": "isobject", "url": "https://github.com/jonschlinkert/isobject", "version": "3.0.1", "license": "MIT", "licenseUrl": "https://github.com/jonschlinkert/isobject/LICENSE"}, {"name": "isomorphic.js", "url": "https://github.com/dmonad/isomorphic.js", "version": "0.2.5", "license": "MIT", "licenseUrl": "https://github.com/dmonad/isomorphic.js/LICENSE"}, {"name": "istanbul-lib-coverage", "url": "https://github.com/istanbuljs/istanbuljs", "version": "3.2.0", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/istanbuljs/istanbuljs/LICENSE"}, {"name": "istanbul-lib-instrument", "url": "https://github.com/istanbuljs/istanbuljs", "version": "5.2.0", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/istanbuljs/istanbuljs/LICENSE"}, {"name": "istanbul-lib-report", "url": "https://github.com/istanbuljs/istanbuljs", "version": "3.0.0", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/istanbuljs/istanbuljs/LICENSE"}, {"name": "istanbul-lib-source-maps", "url": "https://github.com/istanbuljs/istanbuljs", "version": "4.0.1", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/istanbuljs/istanbuljs/LICENSE"}, {"name": "istanbul-reports", "url": "https://github.com/istanbuljs/istanbuljs", "version": "3.1.4", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/istanbuljs/istanbuljs/LICENSE"}, {"name": "istanbuljs/load-nyc-config", "url": "https://github.com/istanbuljs/load-nyc-config", "version": "1.1.0", "license": "ISC", "licenseUrl": "https://github.com/istanbuljs/load-nyc-config/LICENSE"}, {"name": "istanbuljs/schema", "url": "https://github.com/istanbuljs/schema", "version": "0.1.3", "license": "MIT", "licenseUrl": "https://github.com/istanbuljs/schema/LICENSE"}, {"name": "jackson-jr-objects", "url": "https://github.com/FasterXML/jackson-jr", "version": "2.17.2", "license": "Apache 2.0", "licenseUrl": "https://github.com/FasterXML/jackson-jr/blob/2.16/LICENSE"}, {"name": "jake", "url": "https://github.com/jakejs/jake", "version": "10.8.5", "license": "Apache-2.0", "licenseUrl": "https://github.com/jakejs/jake/README.md"}, {"name": "jasmine-core", "url": "https://github.com/jasmine/jasmine", "version": "4.2.0", "license": "MIT", "licenseUrl": "https://github.com/jasmine/jasmine/README.md"}, {"name": "javax inject", "url": "https://github.com/javax-inject/javax-inject", "version": "1", "license": "Apache 2.0", "licenseUrl": "https://github.com/javax-inject/javax-inject"}, {"name": "jb-jdi", "url": "https://github.com/JetBrains/intellij-deps-jdi", "version": "2.41", "license": "GPL 2.0 + Classpath", "licenseUrl": "https://github.com/JetBrains/intellij-deps-jdi/raw/master/LICENSE.txt"}, {"name": "jediterm-core", "url": "https://github.com/JetBrains/jediterm", "version": "3.50", "license": "LGPL 3.0", "licenseUrl": "https://github.com/JetBrains/jediterm/blob/master/LICENSE-LGPLv3.txt"}, {"name": "jediterm-ui", "url": "https://github.com/JetBrains/jediterm", "version": "3.50", "license": "LGPL 3.0", "licenseUrl": "https://github.com/JetBrains/jediterm/blob/master/LICENSE-LGPLv3.txt"}, {"name": "jest-diff", "url": "https://github.com/facebook/jest", "version": "28.1.1", "license": "MIT", "licenseUrl": "https://github.com/facebook/jest/LICENSE"}, {"name": "jest-get-type", "url": "https://github.com/facebook/jest", "version": "28.0.2", "license": "MIT", "licenseUrl": "https://github.com/facebook/jest/LICENSE"}, {"name": "jest-matcher-utils", "url": "https://github.com/facebook/jest", "version": "28.1.1", "license": "MIT", "licenseUrl": "https://github.com/facebook/jest/LICENSE"}, {"name": "jest-message-util", "url": "https://github.com/facebook/jest", "version": "28.1.1", "license": "MIT", "licenseUrl": "https://github.com/facebook/jest/LICENSE"}, {"name": "jest-util", "url": "https://github.com/facebook/jest", "version": "28.1.1", "license": "MIT", "licenseUrl": "https://github.com/facebook/jest/LICENSE"}, {"name": "jest-worker", "url": "https://github.com/facebook/jest", "version": "27.5.1", "license": "MIT", "licenseUrl": "https://github.com/facebook/jest/LICENSE"}, {"name": "jest/expect-utils", "url": "https://github.com/facebook/jest", "version": "28.1.1", "license": "MIT", "licenseUrl": "https://github.com/facebook/jest/LICENSE"}, {"name": "jest/schemas", "url": "https://github.com/facebook/jest", "version": "28.0.2", "license": "MIT", "licenseUrl": "https://github.com/facebook/jest/LICENSE"}, {"name": "jest/types", "url": "https://github.com/facebook/jest", "version": "28.1.1", "license": "MIT", "licenseUrl": "https://github.com/facebook/jest/LICENSE"}, {"name": "jetCheck", "url": "https://github.com/JetBrains/jetCheck", "version": "0.2.2", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/jetCheck/blob/master/LICENSE"}, {"name": "jnaerator-runtime", "url": "https://github.com/nativelibs4java/JNAerator", "version": "0.12", "license": "LGPL 3.0", "licenseUrl": "https://github.com/nativelibs4java/JNAerator/blob/master/LICENSE.LGPL-3.0.txt"}, {"name": "jps-javac-extension", "url": "https://github.com/JetBrains/jps-javac-extension/", "version": "10", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/jps-javac-extension/blob/master/LICENSE.txt"}, {"name": "j<PERSON>y", "url": "https://github.com/jquery/jquery", "version": "3.6.0", "license": "MIT", "licenseUrl": "https://github.com/jquery/jquery/LICENSE.txt"}, {"name": "jridgewell/gen-mapping", "url": "https://github.com/jridgewell/gen-mapping", "version": "0.1.1", "license": "MIT", "licenseUrl": "https://github.com/jridgewell/gen-mapping/LICENSE"}, {"name": "jridgewell/gen-mapping", "url": "https://github.com/jridgewell/gen-mapping", "version": "0.3.1", "license": "MIT", "licenseUrl": "https://github.com/jridgewell/gen-mapping/LICENSE"}, {"name": "j<PERSON><PERSON>/resolve-uri", "url": "https://github.com/jridgewell/resolve-uri", "version": "3.0.7", "license": "MIT", "licenseUrl": "https://github.com/jridgewell/resolve-uri/LICENSE"}, {"name": "jridgewell/set-array", "url": "https://github.com/jridgewell/set-array", "version": "1.1.1", "license": "MIT", "licenseUrl": "https://github.com/jridgewell/set-array/LICENSE"}, {"name": "jridgewell/sourcemap-codec", "url": "https://github.com/jridgewell/sourcemap-codec", "version": "1.4.13", "license": "MIT", "licenseUrl": "https://github.com/jridgewell/sourcemap-codec/LICENSE"}, {"name": "jridgewell/trace-mapping", "url": "https://github.com/jridgewell/trace-mapping", "version": "0.3.9", "license": "MIT", "licenseUrl": "https://github.com/jridgewell/trace-mapping/LICENSE"}, {"name": "js-tokens", "url": "https://github.com/lydell/js-tokens", "version": "4.0.0", "license": "MIT", "licenseUrl": "https://github.com/lydell/js-tokens/LICENSE"}, {"name": "js-yaml", "url": "https://github.com/nodeca/js-yaml", "version": "4.1.0", "license": "MIT", "licenseUrl": "https://github.com/nodeca/js-yaml/LICENSE"}, {"name": "js-yaml", "url": "https://github.com/nodeca/js-yaml", "version": "3.14.1", "license": "MIT", "licenseUrl": "https://github.com/nodeca/js-yaml/LICENSE"}, {"name": "jsch-agent-proxy", "url": "https://github.com/ymnk/jsch-agent-proxy", "version": "0.0.9", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://github.com/ymnk/jsch-agent-proxy/blob/master/LICENSE.txt"}, {"name": "jsch-agent-proxy-sshj", "url": "https://github.com/ymnk/jsch-agent-proxy", "version": "0.0.9", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://github.com/ymnk/jsch-agent-proxy/blob/master/LICENSE.txt"}, {"name": "jsesc", "url": "https://github.com/mathiasbynens/jsesc", "version": "0.5.0", "license": "MIT", "licenseUrl": "https://github.com/mathiasbynens/jsesc/LICENSE-MIT.txt"}, {"name": "jsesc", "url": "https://github.com/mathiasbynens/jsesc", "version": "2.5.2", "license": "MIT", "licenseUrl": "https://github.com/mathiasbynens/jsesc/LICENSE-MIT.txt"}, {"name": "json-parse-better-errors", "url": "https://github.com/zkat/json-parse-better-errors", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/zkat/json-parse-better-errors/LICENSE.md"}, {"name": "json-parse-even-better-errors", "url": "https://github.com/npm/json-parse-even-better-errors", "version": "2.3.1", "license": "MIT", "licenseUrl": "https://github.com/npm/json-parse-even-better-errors/LICENSE.md"}, {"name": "json-schema-compare", "url": "https://github.com/mokkabonna/json-schema-compare", "version": "0.2.2", "license": "MIT", "licenseUrl": "https://github.com/mokkabonna/json-schema-compare/LICENSE"}, {"name": "json-schema-merge-allof", "url": "https://github.com/mokkabonna/json-schema-merge-allof", "version": "0.6.0", "license": "MIT", "licenseUrl": "https://github.com/mokkabonna/json-schema-merge-allof/README.md"}, {"name": "json-schema-merge-allof", "url": "https://github.com/mokkabonna/json-schema-merge-allof", "version": "0.8.1", "license": "MIT", "licenseUrl": "https://github.com/mokkabonna/json-schema-merge-allof/README.md"}, {"name": "json-schema-traverse", "url": "https://github.com/epoberezkin/json-schema-traverse", "version": "1.0.0", "license": "MIT", "licenseUrl": "https://github.com/epoberezkin/json-schema-traverse/LICENSE"}, {"name": "json-schema-traverse", "url": "https://github.com/epoberezkin/json-schema-traverse", "version": "0.4.1", "license": "MIT", "licenseUrl": "https://github.com/epoberezkin/json-schema-traverse/LICENSE"}, {"name": "json-stable-stringify-without-jsonify", "url": "https://github.com/samn/json-stable-stringify", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/samn/json-stable-stringify/LICENSE"}, {"name": "json5", "url": "https://github.com/json5/json5", "version": "2.2.3", "license": "MIT", "licenseUrl": "https://github.com/json5/json5/LICENSE.md"}, {"name": "json5", "url": "https://github.com/json5/json5", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/json5/json5/LICENSE.md"}, {"name": "jsonc-parser", "url": "https://github.com/microsoft/node-jsonc-parser", "version": "3.0.0", "license": "MIT", "licenseUrl": "https://github.com/microsoft/node-jsonc-parser/LICENSE.md"}, {"name": "jsonfile", "url": "https://github.com/jprichardson/node-jsonfile", "version": "6.1.0", "license": "MIT", "licenseUrl": "https://github.com/jprichardson/node-jsonfile/LICENSE"}, {"name": "jsonparse", "url": "https://github.com/creationix/jsonparse", "version": "1.3.1", "license": "MIT", "licenseUrl": "https://github.com/creationix/jsonparse/LICENSE"}, {"name": "<PERSON><PERSON><PERSON>er", "url": "https://github.com/janl/node-jsonpointer", "version": "5.0.1", "license": "MIT", "licenseUrl": "https://github.com/janl/node-jsonpointer/LICENSE.md"}, {"name": "jsoup", "url": "https://jsoup.org", "version": "1.18.3", "license": "MIT", "licenseUrl": "https://jsoup.org/license"}, {"name": "jsvg", "url": "https://github.com/weisJ/jsvg", "version": "1.3.0-jb.8", "license": "MIT", "licenseUrl": "https://github.com/weisJ/jsvg/blob/master/LICENSE"}, {"name": "juggle/resize-observer", "url": "https://github.com/juggle/resize-observer", "version": "3.3.1", "license": "Apache-2.0", "licenseUrl": "https://github.com/juggle/resize-observer/LICENSE"}, {"name": "jupyter-widgets/base", "url": "https://github.com/jupyter-widgets/ipywidgets", "version": "6.0.7", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyter-widgets/ipywidgets/LICENSE"}, {"name": "jupyter-widgets/base-manager", "url": "https://github.com/jupyter-widgets/ipywidgets", "version": "1.0.7", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyter-widgets/ipywidgets/LICENSE"}, {"name": "jupyter-widgets/controls", "url": "https://github.com/jupyter-widgets/ipywidgets", "version": "5.0.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyter-widgets/ipywidgets/LICENSE"}, {"name": "jupyter-widgets/html-manager", "url": "https://github.com/jupyter-widgets/ipywidgets", "version": "1.0.9", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyter-widgets/ipywidgets/LICENSE"}, {"name": "jupyter-widgets/jupyterlab-manager", "url": "https://github.com/jupyter-widgets/ipywidgets", "version": "5.0.9", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyter-widgets/ipywidgets/LICENSE"}, {"name": "jupyter-widgets/output", "url": "https://github.com/jupyter-widgets/ipywidgets", "version": "6.0.7", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyter-widgets/ipywidgets/LICENSE"}, {"name": "jupyter-widgets/schema", "url": "https://github.com/jupyter-widgets/ipywidgets", "version": "0.5.3", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyter-widgets/ipywidgets/LICENSE"}, {"name": "jupyter/react-components", "url": "https://github.com/jupyterlab-contrib/jupyter-ui-toolkit", "version": "0.15.3", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab-contrib/jupyter-ui-toolkit/README.md"}, {"name": "jupyter/web-components", "url": "https://github.com/jupyterlab-contrib/jupyter-ui-toolkit", "version": "0.15.3", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab-contrib/jupyter-ui-toolkit/README.md"}, {"name": "jupyter/ydoc", "url": "https://github.com/jupyter-server/jupyter_ydoc", "version": "0.2.5", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@jupyter/ydoc@0.2.5"}, {"name": "jupyter/ydoc", "url": "https://github.com/jupyter-server/jupyter_ydoc", "version": "1.1.1", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyter-server/jupyter_ydoc/README.md"}, {"name": "jupyterlab/application", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/apputils", "url": "https://github.com/jupyterlab/jupyterlab", "version": "3.6.7", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/apputils", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.2.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/attachments", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/cells", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/codeeditor", "url": "https://github.com/jupyterlab/jupyterlab", "version": "3.6.7", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/codeeditor", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/codemirror", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/codemirror", "url": "https://github.com/jupyterlab/jupyterlab", "version": "3.6.7", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/coreutils", "url": "https://github.com/jupyterlab/jupyterlab", "version": "6.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/coreutils", "url": "https://github.com/jupyterlab/jupyterlab", "version": "5.6.7", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/docmanager", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/docregistry", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/documentsearch", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@jupyterlab/documentsearch@4.1.8"}, {"name": "jupyterlab/filebrowser", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/logconsole", "url": "https://github.com/jupyterlab/jupyterlab", "version": "3.6.6", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/lsp", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@jupyterlab/lsp@4.1.8"}, {"name": "jupyterlab/mainmenu", "url": "https://github.com/jupyterlab/jupyterlab", "version": "3.6.6", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/markedparser-extension", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@jupyterlab/markedparser-extension@4.1.8"}, {"name": "jupyterlab/mathjax-extension", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/mermaid", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/nbformat", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@jupyterlab/nbformat@4.1.8"}, {"name": "jupyterlab/nbformat", "url": "https://github.com/jupyterlab/jupyterlab", "version": "3.6.7", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@jupyterlab/nbformat@3.6.7"}, {"name": "jupyterlab/notebook", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/observables", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.6.7", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/observables", "url": "https://github.com/jupyterlab/jupyterlab", "version": "5.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/outputarea", "url": "https://github.com/jupyterlab/jupyterlab", "version": "3.6.6", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/outputarea", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/rendermime", "url": "https://github.com/jupyterlab/jupyterlab", "version": "3.6.7", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/rendermime", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/rendermime-interfaces", "url": "https://github.com/jupyterlab/jupyterlab", "version": "3.9.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/services", "url": "https://github.com/jupyterlab/jupyterlab", "version": "7.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/services", "url": "https://github.com/jupyterlab/jupyterlab", "version": "6.6.7", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/settingregistry", "url": "https://github.com/jupyterlab/jupyterlab", "version": "3.6.7", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@jupyterlab/settingregistry@3.6.7"}, {"name": "jupyterlab/settingregistry", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@jupyterlab/settingregistry@4.1.8"}, {"name": "jupyterlab/shared-models", "url": "https://github.com/jupyterlab/jupyterlab", "version": "3.6.7", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@jupyterlab/shared-models@3.6.7"}, {"name": "jupyterlab/statedb", "url": "https://github.com/jupyterlab/jupyterlab", "version": "3.6.7", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@jupyterlab/statedb@3.6.7"}, {"name": "jupyterlab/statedb", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@jupyterlab/statedb@4.1.8"}, {"name": "jupyterlab/statusbar", "url": "https://github.com/jupyterlab/jupyterlab", "version": "3.6.7", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@jupyterlab/statusbar@3.6.7"}, {"name": "jupyterlab/statusbar", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@jupyterlab/statusbar@4.1.8"}, {"name": "jupyterlab/theme-dark-extension", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/theme-light-extension", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/toc", "url": "https://github.com/jupyterlab/jupyterlab", "version": "6.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/translation", "url": "https://github.com/jupyterlab/jupyterlab", "version": "3.6.7", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/translation", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/ui-components", "url": "https://github.com/jupyterlab/jupyterlab", "version": "4.1.8", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jupyterlab/ui-components", "url": "https://github.com/jupyterlab/jupyterlab", "version": "3.6.7", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/jupyterlab/README.md"}, {"name": "jzlib", "url": "http://www.jcraft.com/jzlib/", "version": "1.1.3", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://github.com/ymnk/jzlib/raw/master/LICENSE.txt"}, {"name": "karma", "url": "https://github.com/karma-runner/karma", "version": "6.4.0", "license": "MIT", "licenseUrl": "https://github.com/karma-runner/karma/LICENSE"}, {"name": "karma-chrome-launcher", "url": "https://github.com/karma-runner/karma-chrome-launcher", "version": "3.1.1", "license": "MIT", "licenseUrl": "https://github.com/karma-runner/karma-chrome-launcher/LICENSE"}, {"name": "karma-coverage", "url": "https://github.com/karma-runner/karma-coverage", "version": "2.2.0", "license": "MIT", "licenseUrl": "https://github.com/karma-runner/karma-coverage/LICENSE"}, {"name": "karma-jasmine", "url": "https://github.com/karma-runner/karma-jasmine", "version": "5.1.0", "license": "MIT", "licenseUrl": "https://github.com/karma-runner/karma-jasmine/LICENSE"}, {"name": "karma-jasmine-html-reporter", "url": "https://github.com/dfederm/karma-jasmine-html-reporter", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/dfederm/karma-jasmine-html-reporter/LICENSE"}, {"name": "karma-source-map-support", "url": "https://github.com/tschaub/karma-source-map-support", "version": "1.4.0", "license": "MIT", "licenseUrl": "https://github.com/tschaub/karma-source-map-support/LICENSE"}, {"name": "katex", "url": "https://github.com/KaTeX/KaTeX", "version": "0.16.10", "license": "MIT", "licenseUrl": "https://github.com/KaTeX/KaTeX/LICENSE"}, {"name": "khroma", "url": "https://github.com/fabiospampinato/khroma", "version": "2.1.0", "license": "MIT*", "licenseUrl": "https://github.com/fabiospampinato/khroma/license"}, {"name": "kind-of", "url": "https://github.com/jonschlinkert/kind-of", "version": "6.0.3", "license": "MIT", "licenseUrl": "https://github.com/jonschlinkert/kind-of/LICENSE"}, {"name": "kleur", "url": "https://github.com/lukeed/kleur", "version": "4.1.5", "license": "MIT", "licenseUrl": "https://github.com/lukeed/kleur/license"}, {"name": "klona", "url": "https://github.com/lukeed/klona", "version": "2.0.5", "license": "MIT", "licenseUrl": "https://github.com/lukeed/klona/license"}, {"name": "kotlin-codepoints", "url": "https://github.com/cketti/kotlin-codepoints", "version": "0.9.0", "license": "MIT", "licenseUrl": "https://github.com/cketti/kotlin-codepoints/blob/main/LICENSE"}, {"name": "kotlin-metadata", "url": "https://github.com/JetBrains/kotlin", "version": "2.1.20-Beta2", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/kotlin/blob/master/license/LICENSE.txt"}, {"name": "kotlinx-datetime-jvm", "url": "https://github.com/Kotlin/kotlinx-datetime", "version": "0.6.2", "license": "Apache 2.0", "licenseUrl": "https://github.com/Kotlin/kotlinx-datetime/blob/master/LICENSE.txt"}, {"name": "kotlinx-document-store-mvstore", "url": "https://github.com/lamba92/kotlin.document.store", "version": "0.0.4", "license": "Apache 2.0", "licenseUrl": "https://github.com/lamba92/kotlin.document.store/blob/master/LICENSE"}, {"name": "kotlinx.html", "url": "https://github.com/Kotlin/kotlinx.html", "version": "0.12.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/Kotlin/kotlinx.html/blob/master/LICENSE"}, {"name": "ktor-client-auth", "url": "https://github.com/ktorio/ktor", "version": "3.0.3", "license": "Apache 2.0", "licenseUrl": "https://github.com/ktorio/ktor/blob/main/LICENSE"}, {"name": "ktor-client-cio-internal", "url": "https://github.com/ktorio/ktor", "version": "3.0.3", "license": "Apache 2.0", "licenseUrl": "https://github.com/ktorio/ktor/blob/main/LICENSE"}, {"name": "ktor-client-content-negotiation", "url": "https://github.com/ktorio/ktor", "version": "3.0.3", "license": "Apache 2.0", "licenseUrl": "https://github.com/ktorio/ktor/blob/main/LICENSE"}, {"name": "ktor-client-encoding", "url": "https://github.com/ktorio/ktor", "version": "3.0.3", "license": "Apache 2.0", "licenseUrl": "https://github.com/ktorio/ktor/blob/main/LICENSE"}, {"name": "ktor-client-java", "url": "https://github.com/ktorio/ktor", "version": "3.0.3", "license": "Apache 2.0", "licenseUrl": "https://github.com/ktorio/ktor/blob/main/LICENSE"}, {"name": "ktor-client-logging", "url": "https://github.com/ktorio/ktor", "version": "3.0.3", "license": "Apache 2.0", "licenseUrl": "https://github.com/ktorio/ktor/blob/main/LICENSE"}, {"name": "ktor-serialization-kotlinx-json", "url": "https://github.com/ktorio/ktor", "version": "3.0.3", "license": "Apache 2.0", "licenseUrl": "https://github.com/ktorio/ktor/blob/main/LICENSE"}, {"name": "ktor.io TLS", "url": "https://github.com/ktorio/ktor", "version": "3.0.3", "license": "Apache 2.0", "licenseUrl": "https://github.com/ktorio/ktor/blob/main/LICENSE"}, {"name": "layout-base", "url": "https://github.com/iVis-at-Bilkent/layout-base", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/iVis-at-Bilkent/layout-base/LICENSE"}, {"name": "less", "url": "https://github.com/less/less.js", "version": "4.1.2", "license": "Apache-2.0", "licenseUrl": "https://github.com/less/less.js/README.md"}, {"name": "less-loader", "url": "https://github.com/webpack-contrib/less-loader", "version": "10.2.0", "license": "MIT", "licenseUrl": "https://github.com/webpack-contrib/less-loader/LICENSE"}, {"name": "levn", "url": "https://github.com/gkz/levn", "version": "0.4.1", "license": "MIT", "licenseUrl": "https://github.com/gkz/levn/LICENSE"}, {"name": "levn", "url": "https://github.com/gkz/levn", "version": "0.3.0", "license": "MIT", "licenseUrl": "https://github.com/gkz/levn/LICENSE"}, {"name": "lezer/common", "url": "https://github.com/lezer-parser/common", "version": "1.2.1", "license": "MIT", "licenseUrl": "https://github.com/lezer-parser/common/LICENSE"}, {"name": "lezer/cpp", "url": "https://github.com/lezer-parser/cpp", "version": "1.1.2", "license": "MIT", "licenseUrl": "https://github.com/lezer-parser/cpp/LICENSE"}, {"name": "lezer/css", "url": "https://github.com/lezer-parser/css", "version": "1.1.8", "license": "MIT", "licenseUrl": "https://github.com/lezer-parser/css/LICENSE"}, {"name": "lezer/generator", "url": "https://github.com/lezer-parser/generator", "version": "1.7.0", "license": "MIT", "licenseUrl": "https://github.com/lezer-parser/generator/LICENSE"}, {"name": "lezer/highlight", "url": "https://github.com/lezer-parser/highlight", "version": "1.2.0", "license": "MIT", "licenseUrl": "https://github.com/lezer-parser/highlight/LICENSE"}, {"name": "lezer/html", "url": "https://github.com/lezer-parser/html", "version": "1.3.9", "license": "MIT", "licenseUrl": "https://github.com/lezer-parser/html/LICENSE"}, {"name": "lezer/java", "url": "https://github.com/lezer-parser/java", "version": "1.1.2", "license": "MIT", "licenseUrl": "https://github.com/lezer-parser/java/LICENSE"}, {"name": "lezer/javascript", "url": "https://github.com/lezer-parser/javascript", "version": "1.4.15", "license": "MIT", "licenseUrl": "https://github.com/lezer-parser/javascript/LICENSE"}, {"name": "lezer/json", "url": "https://github.com/lezer-parser/json", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/lezer-parser/json/LICENSE"}, {"name": "lezer/lr", "url": "https://github.com/lezer-parser/lr", "version": "1.4.0", "license": "MIT", "licenseUrl": "https://github.com/lezer-parser/lr/LICENSE"}, {"name": "lezer/markdown", "url": "https://github.com/lezer-parser/markdown", "version": "1.3.0", "license": "MIT", "licenseUrl": "https://github.com/lezer-parser/markdown/LICENSE"}, {"name": "lezer/php", "url": "https://github.com/lezer-parser/php", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/lezer-parser/php/LICENSE"}, {"name": "lezer/python", "url": "https://github.com/lezer-parser/python", "version": "1.1.13", "license": "MIT", "licenseUrl": "https://github.com/lezer-parser/python/LICENSE"}, {"name": "lezer/rust", "url": "https://github.com/lezer-parser/rust", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/lezer-parser/rust/LICENSE"}, {"name": "lezer/xml", "url": "https://github.com/lezer-parser/xml", "version": "1.0.5", "license": "MIT", "licenseUrl": "https://github.com/lezer-parser/xml/LICENSE"}, {"name": "lib0", "url": "https://github.com/dmonad/lib0", "version": "0.2.88", "license": "MIT", "licenseUrl": "https://github.com/dmonad/lib0/LICENSE"}, {"name": "license-checker", "url": "https://github.com/davglass/license-checker", "version": "25.0.1", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/davglass/license-checker/LICENSE"}, {"name": "license-webpack-plugin", "url": "https://github.com/xz64/license-webpack-plugin", "version": "4.0.2", "license": "ISC", "licenseUrl": "https://github.com/xz64/license-webpack-plugin/LICENSE"}, {"name": "lines-and-columns", "url": "https://github.com/eventualbuddha/lines-and-columns", "version": "1.2.4", "license": "MIT", "licenseUrl": "https://github.com/eventualbuddha/lines-and-columns/LICENSE"}, {"name": "llama.cpp", "url": "https://github.com/ggerganov/llama.cpp", "version": "git snapshot", "license": "MIT", "licenseUrl": "https://github.com/ggerganov/llama.cpp/blob/master/LICENSE"}, {"name": "loader-runner", "url": "https://github.com/webpack/loader-runner", "version": "4.3.0", "license": "MIT", "licenseUrl": "https://github.com/webpack/loader-runner/LICENSE"}, {"name": "loader-utils", "url": "https://github.com/webpack/loader-utils", "version": "3.2.0", "license": "MIT", "licenseUrl": "https://github.com/webpack/loader-utils/LICENSE"}, {"name": "loader-utils", "url": "https://github.com/webpack/loader-utils", "version": "2.0.2", "license": "MIT", "licenseUrl": "https://github.com/webpack/loader-utils/LICENSE"}, {"name": "locate-path", "url": "https://github.com/sindresorhus/locate-path", "version": "6.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/locate-path/license"}, {"name": "locate-path", "url": "https://github.com/sindresorhus/locate-path", "version": "5.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/locate-path/license"}, {"name": "lodash", "url": "https://github.com/lodash/lodash", "version": "4.17.21", "license": "MIT", "licenseUrl": "https://github.com/lodash/lodash/LICENSE"}, {"name": "lodash-es", "url": "https://github.com/lodash/lodash", "version": "4.17.21", "license": "MIT", "licenseUrl": "https://github.com/lodash/lodash/LICENSE"}, {"name": "lodash.debounce", "url": "https://github.com/lodash/lodash", "version": "4.0.8", "license": "MIT", "licenseUrl": "https://github.com/lodash/lodash/LICENSE"}, {"name": "lodash.escape", "url": "https://github.com/lodash/lodash", "version": "4.0.1", "license": "MIT", "licenseUrl": "https://github.com/lodash/lodash/LICENSE"}, {"name": "lodash.memoize", "url": "https://github.com/lodash/lodash", "version": "4.1.2", "license": "MIT", "licenseUrl": "https://github.com/lodash/lodash/LICENSE"}, {"name": "lodash.merge", "url": "https://github.com/lodash/lodash", "version": "4.6.2", "license": "MIT", "licenseUrl": "https://github.com/lodash/lodash/LICENSE"}, {"name": "lodash.mergewith", "url": "https://github.com/lodash/lodash", "version": "4.6.2", "license": "MIT", "licenseUrl": "https://github.com/lodash/lodash/LICENSE"}, {"name": "lodash.uniq", "url": "https://github.com/lodash/lodash", "version": "4.5.0", "license": "MIT", "licenseUrl": "https://github.com/lodash/lodash/LICENSE"}, {"name": "log-symbols", "url": "https://github.com/sindresorhus/log-symbols", "version": "4.1.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/log-symbols/license"}, {"name": "log4js", "url": "https://github.com/log4js-node/log4js-node", "version": "6.5.2", "license": "Apache-2.0", "licenseUrl": "https://github.com/log4js-node/log4js-node/LICENSE"}, {"name": "loose-envify", "url": "https://github.com/zertosh/loose-envify", "version": "1.4.0", "license": "MIT", "licenseUrl": "https://github.com/zertosh/loose-envify/LICENSE"}, {"name": "loupe", "url": "https://github.com/chaijs/loupe", "version": "2.3.7", "license": "MIT", "licenseUrl": "https://github.com/chaijs/loupe/LICENSE"}, {"name": "lru-cache", "url": "https://github.com/isaacs/node-lru-cache", "version": "6.0.0", "license": "ISC", "licenseUrl": "https://github.com/isaacs/node-lru-cache/LICENSE"}, {"name": "lru-cache", "url": "https://github.com/isaacs/node-lru-cache", "version": "7.10.2", "license": "ISC", "licenseUrl": "https://github.com/isaacs/node-lru-cache/LICENSE"}, {"name": "lumino/algorithm", "url": "https://github.com/jupyterlab/lumino", "version": "1.9.2", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/algorithm@1.9.2"}, {"name": "lumino/algorithm", "url": "https://github.com/jupyterlab/lumino", "version": "2.0.1", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/algorithm@2.0.1"}, {"name": "lumino/application", "url": "https://github.com/jupyterlab/lumino", "version": "2.3.1", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/application@2.3.1"}, {"name": "lumino/collections", "url": "https://github.com/jupyterlab/lumino", "version": "2.0.1", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/collections@2.0.1"}, {"name": "lumino/collections", "url": "https://github.com/jupyterlab/lumino", "version": "1.9.3", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/collections@1.9.3"}, {"name": "lumino/commands", "url": "https://github.com/jupyterlab/lumino", "version": "2.3.0", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/commands@2.3.0"}, {"name": "lumino/commands", "url": "https://github.com/jupyterlab/lumino", "version": "1.21.1", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/commands@1.21.1"}, {"name": "lumino/coreutils", "url": "https://github.com/jupyterlab/lumino", "version": "2.1.2", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/coreutils@2.1.2"}, {"name": "lumino/coreutils", "url": "https://github.com/jupyterlab/lumino", "version": "1.12.1", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/coreutils@1.12.1"}, {"name": "lumino/disposable", "url": "https://github.com/jupyterlab/lumino", "version": "2.1.2", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/disposable@2.1.2"}, {"name": "lumino/disposable", "url": "https://github.com/jupyterlab/lumino", "version": "1.10.4", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/disposable@1.10.4"}, {"name": "lumino/domutils", "url": "https://github.com/jupyterlab/lumino", "version": "2.0.1", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/domutils@2.0.1"}, {"name": "lumino/domutils", "url": "https://github.com/jupyterlab/lumino", "version": "1.8.2", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/domutils@1.8.2"}, {"name": "lumino/dragdrop", "url": "https://github.com/jupyterlab/lumino", "version": "1.14.5", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/dragdrop@1.14.5"}, {"name": "lumino/dragdrop", "url": "https://github.com/jupyterlab/lumino", "version": "2.1.4", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/dragdrop@2.1.4"}, {"name": "lumino/keyboard", "url": "https://github.com/jupyterlab/lumino", "version": "2.0.1", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/keyboard@2.0.1"}, {"name": "lumino/keyboard", "url": "https://github.com/jupyterlab/lumino", "version": "1.8.2", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/keyboard@1.8.2"}, {"name": "lumino/messaging", "url": "https://github.com/jupyterlab/lumino", "version": "1.10.3", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/messaging@1.10.3"}, {"name": "lumino/messaging", "url": "https://github.com/jupyterlab/lumino", "version": "2.0.1", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/messaging@2.0.1"}, {"name": "lumino/polling", "url": "https://github.com/jupyterlab/lumino", "version": "1.10.0", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/polling@1.10.0"}, {"name": "lumino/polling", "url": "https://github.com/jupyterlab/lumino", "version": "2.1.2", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/jupyterlab/lumino/README.md"}, {"name": "lumino/properties", "url": "https://github.com/jupyterlab/lumino", "version": "1.8.2", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/properties@1.8.2"}, {"name": "lumino/properties", "url": "https://github.com/jupyterlab/lumino", "version": "2.0.1", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/properties@2.0.1"}, {"name": "lumino/signaling", "url": "https://github.com/jupyterlab/lumino", "version": "1.11.1", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/signaling@1.11.1"}, {"name": "lumino/signaling", "url": "https://github.com/jupyterlab/lumino", "version": "2.1.2", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/signaling@2.1.2"}, {"name": "lumino/virtualdom", "url": "https://github.com/jupyterlab/lumino", "version": "2.0.1", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/virtualdom@2.0.1"}, {"name": "lumino/virtualdom", "url": "https://github.com/jupyterlab/lumino", "version": "1.14.3", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/virtualdom@1.14.3"}, {"name": "lumino/widgets", "url": "https://github.com/jupyterlab/lumino", "version": "1.37.2", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/widgets@1.37.2"}, {"name": "lumino/widgets", "url": "https://github.com/jupyterlab/lumino", "version": "2.3.2", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://npmjs.org/package/@lumino/widgets@2.3.2"}, {"name": "lz4-java", "url": "https://github.com/lz4/lz4-java", "version": "1.8.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/lz4/lz4-java/blob/master/LICENSE.txt"}, {"name": "magic-string", "url": "https://github.com/rich-harris/magic-string", "version": "0.26.2", "license": "MIT", "licenseUrl": "https://github.com/rich-harris/magic-string/LICENSE"}, {"name": "magic-string", "url": "https://github.com/rich-harris/magic-string", "version": "0.25.7", "license": "MIT", "licenseUrl": "https://github.com/rich-harris/magic-string/LICENSE"}, {"name": "make-dir", "url": "https://github.com/sindresorhus/make-dir", "version": "2.1.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/make-dir/license"}, {"name": "make-dir", "url": "https://github.com/sindresorhus/make-dir", "version": "3.1.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/make-dir/license"}, {"name": "make-error", "url": "https://github.com/JsCommunity/make-error", "version": "1.3.6", "license": "ISC", "licenseUrl": "https://github.com/JsCommunity/make-error/LICENSE"}, {"name": "make-fetch-happen", "url": "https://github.com/npm/make-fetch-happen", "version": "10.1.8", "license": "ISC", "licenseUrl": "https://github.com/npm/make-fetch-happen/LICENSE"}, {"name": "make-fetch-happen", "url": "https://github.com/npm/make-fetch-happen", "version": "9.1.0", "license": "ISC", "licenseUrl": "https://github.com/npm/make-fetch-happen/LICENSE"}, {"name": "map-limit", "url": "https://github.com/hughsk/map-limit", "version": "0.0.1", "license": "MIT", "licenseUrl": "https://github.com/hughsk/map-limit/LICENSE.md"}, {"name": "markdown-to-jsx", "url": "https://github.com/quantizor/markdown-to-jsx", "version": "7.4.7", "license": "MIT", "licenseUrl": "https://github.com/quantizor/markdown-to-jsx/LICENSE"}, {"name": "marked", "url": "https://github.com/markedjs/marked", "version": "4.0.17", "license": "MIT", "licenseUrl": "https://github.com/markedjs/marked/LICENSE.md"}, {"name": "marked", "url": "https://github.com/markedjs/marked", "version": "9.1.6", "license": "MIT", "licenseUrl": "https://github.com/markedjs/marked/LICENSE.md"}, {"name": "marked-gfm-heading-id", "url": "https://github.com/markedjs/marked-gfm-heading-id", "version": "3.1.3", "license": "MIT", "licenseUrl": "https://github.com/markedjs/marked-gfm-heading-id/LICENSE"}, {"name": "marked-mangle", "url": "https://github.com/markedjs/marked-mangle", "version": "1.1.7", "license": "MIT", "licenseUrl": "https://github.com/markedjs/marked-mangle/LICENSE"}, {"name": "mathjax-full", "url": "https://github.com/mathjax/Mathjax-src", "version": "3.2.2", "license": "Apache-2.0", "licenseUrl": "https://github.com/mathjax/Mathjax-src/LICENSE"}, {"name": "mdast-util-from-markdown", "url": "https://github.com/syntax-tree/mdast-util-from-markdown", "version": "1.3.1", "license": "MIT", "licenseUrl": "https://github.com/syntax-tree/mdast-util-from-markdown/license"}, {"name": "mdast-util-to-string", "url": "https://github.com/syntax-tree/mdast-util-to-string", "version": "3.2.0", "license": "MIT", "licenseUrl": "https://github.com/syntax-tree/mdast-util-to-string/license"}, {"name": "mdn-data", "url": "https://github.com/mdn/data", "version": "2.0.4", "license": "CC0-1.0", "licenseUrl": "https://github.com/mdn/data/LICENSE"}, {"name": "mdn-data", "url": "https://github.com/mdn/data", "version": "2.0.14", "license": "CC0-1.0", "licenseUrl": "https://github.com/mdn/data/LICENSE"}, {"name": "media-typer", "url": "https://github.com/jshttp/media-typer", "version": "0.3.0", "license": "MIT", "licenseUrl": "https://github.com/jshttp/media-typer/LICENSE"}, {"name": "memfs", "url": "https://github.com/streamich/memfs", "version": "3.4.7", "license": "Unlicense", "licenseUrl": "https://github.com/streamich/memfs/LICENSE"}, {"name": "mercurial_prompthooks", "url": "https://github.com/willemv/mercurial_prompthooks", "version": "custom revision", "license": "GPLv2 (used as hg extension called from hg executable)", "licenseUrl": "https://github.com/willemv/mercurial_prompthooks/blob/master/LICENSE.txt"}, {"name": "merge-descriptors", "url": "https://github.com/component/merge-descriptors", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/component/merge-descriptors/LICENSE"}, {"name": "merge-stream", "url": "https://github.com/grncdr/merge-stream", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/grncdr/merge-stream/LICENSE"}, {"name": "merge2", "url": "https://github.com/teambition/merge2", "version": "1.4.1", "license": "MIT", "licenseUrl": "https://github.com/teambition/merge2/LICENSE"}, {"name": "mermaid", "url": "https://github.com/mermaid-js/mermaid", "version": "10.9.0", "license": "MIT", "licenseUrl": "https://github.com/mermaid-js/mermaid/LICENSE"}, {"name": "methods", "url": "https://github.com/jshttp/methods", "version": "1.1.2", "license": "MIT", "licenseUrl": "https://github.com/jshttp/methods/LICENSE"}, {"name": "mh<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/mhchem/mhchemParser", "version": "4.2.1", "license": "Apache-2.0", "licenseUrl": "https://github.com/mhchem/mhchemParser/LICENSE.txt"}, {"name": "microba", "url": "https://microba.sourceforge.net/", "version": "custom revision", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://microba.sourceforge.net/license.txt"}, {"name": "micromark", "url": "https://github.com/micromark/micromark/tree/main/packages/micromark", "version": "3.2.0", "license": "MIT", "licenseUrl": "https://github.com/micromark/micromark/tree/main/packages/micromark/readme.md"}, {"name": "micromark-core-commonmark", "url": "https://github.com/micromark/micromark/tree/main/packages/micromark-core-commonmark", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/micromark/micromark/tree/main/packages/micromark-core-commonmark/readme.md"}, {"name": "micromark-factory-destination", "url": "https://github.com/micromark/micromark/tree/main/packages/micromark-factory-destination", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/micromark/micromark/tree/main/packages/micromark-factory-destination/readme.md"}, {"name": "micromark-factory-label", "url": "https://github.com/micromark/micromark/tree/main/packages/micromark-factory-label", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/micromark/micromark/tree/main/packages/micromark-factory-label/readme.md"}, {"name": "micromark-factory-space", "url": "https://github.com/micromark/micromark/tree/main/packages/micromark-factory-space", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/micromark/micromark/tree/main/packages/micromark-factory-space/readme.md"}, {"name": "micromark-factory-title", "url": "https://github.com/micromark/micromark/tree/main/packages/micromark-factory-title", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/micromark/micromark/tree/main/packages/micromark-factory-title/readme.md"}, {"name": "micromark-factory-whitespace", "url": "https://github.com/micromark/micromark/tree/main/packages/micromark-factory-whitespace", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/micromark/micromark/tree/main/packages/micromark-factory-whitespace/readme.md"}, {"name": "micromark-util-character", "url": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-character", "version": "1.2.0", "license": "MIT", "licenseUrl": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-character/readme.md"}, {"name": "micromark-util-chunked", "url": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-chunked", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-chunked/readme.md"}, {"name": "micromark-util-classify-character", "url": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-classify-character", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-classify-character/readme.md"}, {"name": "micromark-util-combine-extensions", "url": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-combine-extensions", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-combine-extensions/readme.md"}, {"name": "micromark-util-decode-numeric-character-reference", "url": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-decode-numeric-character-reference", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-decode-numeric-character-reference/readme.md"}, {"name": "micromark-util-decode-string", "url": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-decode-string", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-decode-string/readme.md"}, {"name": "micromark-util-encode", "url": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-encode", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-encode/readme.md"}, {"name": "micromark-util-html-tag-name", "url": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-html-tag-name", "version": "1.2.0", "license": "MIT", "licenseUrl": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-html-tag-name/readme.md"}, {"name": "micromark-util-normalize-identifier", "url": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-normalize-identifier", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-normalize-identifier/readme.md"}, {"name": "micromark-util-resolve-all", "url": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-resolve-all", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-resolve-all/readme.md"}, {"name": "micromark-util-sanitize-uri", "url": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-sanitize-uri", "version": "1.2.0", "license": "MIT", "licenseUrl": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-sanitize-uri/readme.md"}, {"name": "micromark-util-subtokenize", "url": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-subtokenize", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-subtokenize/readme.md"}, {"name": "micromark-util-symbol", "url": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-symbol", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-symbol/readme.md"}, {"name": "micromark-util-types", "url": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-types", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/micromark/micromark/tree/main/packages/micromark-util-types/readme.md"}, {"name": "micromatch", "url": "https://github.com/micromatch/micromatch", "version": "4.0.5", "license": "MIT", "licenseUrl": "https://github.com/micromatch/micromatch/LICENSE"}, {"name": "microsoft/fast-colors", "url": "https://github.com/Microsoft/fast", "version": "5.3.1", "license": "MIT", "licenseUrl": "https://github.com/Microsoft/fast/README.md"}, {"name": "microsoft/fast-element", "url": "https://github.com/Microsoft/fast", "version": "1.13.0", "license": "MIT", "licenseUrl": "https://github.com/Microsoft/fast/README.md"}, {"name": "microsoft/fast-foundation", "url": "https://github.com/Microsoft/fast", "version": "2.49.6", "license": "MIT", "licenseUrl": "https://github.com/Microsoft/fast/README.md"}, {"name": "microsoft/fast-react-wrapper", "url": "https://github.com/Microsoft/fast", "version": "0.3.24", "license": "MIT", "licenseUrl": "https://github.com/Microsoft/fast/README.md"}, {"name": "microsoft/fast-web-utilities", "url": "https://github.com/Microsoft/fast", "version": "5.4.1", "license": "MIT", "licenseUrl": "https://github.com/Microsoft/fast/README.md"}, {"name": "mime", "url": "https://github.com/broofa/mime", "version": "2.6.0", "license": "MIT", "licenseUrl": "https://github.com/broofa/mime/LICENSE"}, {"name": "mime", "url": "https://github.com/broofa/node-mime", "version": "1.6.0", "license": "MIT", "licenseUrl": "https://github.com/broofa/node-mime/LICENSE"}, {"name": "mime-db", "url": "https://github.com/jshttp/mime-db", "version": "1.52.0", "license": "MIT", "licenseUrl": "https://github.com/jshttp/mime-db/LICENSE"}, {"name": "mime-types", "url": "https://github.com/jshttp/mime-types", "version": "2.1.35", "license": "MIT", "licenseUrl": "https://github.com/jshttp/mime-types/LICENSE"}, {"name": "mimic-fn", "url": "https://github.com/sindresorhus/mimic-fn", "version": "2.1.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/mimic-fn/license"}, {"name": "mini-css-extract-plugin", "url": "https://github.com/webpack-contrib/mini-css-extract-plugin", "version": "2.5.3", "license": "MIT", "licenseUrl": "https://github.com/webpack-contrib/mini-css-extract-plugin/LICENSE"}, {"name": "minimalistic-assert", "url": "https://github.com/calvinmetcalf/minimalistic-assert", "version": "1.0.1", "license": "ISC", "licenseUrl": "https://github.com/calvinmetcalf/minimalistic-assert/LICENSE"}, {"name": "minimatch", "url": "https://github.com/isaacs/minimatch", "version": "3.0.5", "license": "ISC", "licenseUrl": "https://github.com/isaacs/minimatch/LICENSE"}, {"name": "minimatch", "url": "https://github.com/isaacs/minimatch", "version": "5.1.0", "license": "ISC", "licenseUrl": "https://github.com/isaacs/minimatch/LICENSE"}, {"name": "minimatch", "url": "https://github.com/isaacs/minimatch", "version": "5.0.1", "license": "ISC", "licenseUrl": "https://github.com/isaacs/minimatch/LICENSE"}, {"name": "minimatch", "url": "https://github.com/isaacs/minimatch", "version": "3.1.2", "license": "ISC", "licenseUrl": "https://github.com/isaacs/minimatch/LICENSE"}, {"name": "minimist", "url": "https://github.com/substack/minimist", "version": "1.2.6", "license": "MIT", "licenseUrl": "https://github.com/substack/minimist/LICENSE"}, {"name": "minipass", "url": "https://github.com/isaacs/minipass", "version": "3.3.3", "license": "ISC", "licenseUrl": "https://github.com/isaacs/minipass/LICENSE"}, {"name": "minipass-fetch", "url": "https://github.com/npm/minipass-fetch", "version": "1.4.1", "license": "MIT", "licenseUrl": "https://github.com/npm/minipass-fetch/LICENSE"}, {"name": "minipass-fetch", "url": "https://github.com/npm/minipass-fetch", "version": "2.1.0", "license": "MIT", "licenseUrl": "https://github.com/npm/minipass-fetch/LICENSE"}, {"name": "minipass-flush", "url": "https://github.com/isaacs/minipass-flush", "version": "1.0.5", "license": "ISC", "licenseUrl": "https://github.com/isaacs/minipass-flush/LICENSE"}, {"name": "minipass-json-stream", "url": "https://github.com/npm/minipass-json-stream", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/npm/minipass-json-stream/LICENSE"}, {"name": "minipass-sized", "url": "https://github.com/isaacs/minipass-sized", "version": "1.0.3", "license": "ISC", "licenseUrl": "https://github.com/isaacs/minipass-sized/LICENSE"}, {"name": "minizlib", "url": "https://github.com/isaacs/minizlib", "version": "2.1.2", "license": "MIT", "licenseUrl": "https://github.com/isaacs/minizlib/LICENSE"}, {"name": "mj-context-menu", "url": "https://github.com/zorkow/context-menu", "version": "0.6.1", "license": "Apache-2.0", "licenseUrl": "https://github.com/zorkow/context-menu/README.md"}, {"name": "mkdirp", "url": "https://github.com/isaacs/node-mkdirp", "version": "1.0.4", "license": "MIT", "licenseUrl": "https://github.com/isaacs/node-mkdirp/LICENSE"}, {"name": "mkdirp", "url": "https://github.com/substack/node-mkdirp", "version": "0.5.6", "license": "MIT", "licenseUrl": "https://github.com/substack/node-mkdirp/LICENSE"}, {"name": "mocha", "url": "https://github.com/mochajs/mocha", "version": "10.0.0", "license": "MIT", "licenseUrl": "https://github.com/mochajs/mocha/LICENSE"}, {"name": "moment", "url": "https://github.com/moment/moment", "version": "2.29.3", "license": "MIT", "licenseUrl": "https://github.com/moment/moment/LICENSE"}, {"name": "morfologik-fsa", "url": "https://github.com/morfologik/morfologik-stemming", "version": "2.1.9", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://github.com/morfologik/morfologik-stemming/blob/master/LICENSE.txt"}, {"name": "morfologik-fsa-builders", "url": "https://github.com/morfologik/morfologik-stemming", "version": "2.1.9", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://github.com/morfologik/morfologik-stemming/blob/master/LICENSE.txt"}, {"name": "morfologik-speller", "url": "https://github.com/morfologik/morfologik-stemming", "version": "2.1.9", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://github.com/morfologik/morfologik-stemming/blob/master/LICENSE.txt"}, {"name": "morfologik-stemming", "url": "https://github.com/morfologik/morfologik-stemming", "version": "2.1.9", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://github.com/morfologik/morfologik-stemming/blob/master/LICENSE.txt"}, {"name": "mri", "url": "https://github.com/lukeed/mri", "version": "1.2.0", "license": "MIT", "licenseUrl": "https://github.com/lukeed/mri/license.md"}, {"name": "ms", "url": "https://github.com/zeit/ms", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/zeit/ms/license.md"}, {"name": "ms", "url": "https://github.com/zeit/ms", "version": "2.1.2", "license": "MIT", "licenseUrl": "https://github.com/zeit/ms/license.md"}, {"name": "ms", "url": "https://github.com/vercel/ms", "version": "2.1.3", "license": "MIT", "licenseUrl": "https://github.com/vercel/ms/license.md"}, {"name": "multicast-dns", "url": "https://github.com/mafintosh/multicast-dns", "version": "6.2.3", "license": "MIT", "licenseUrl": "https://github.com/mafintosh/multicast-dns/LICENSE"}, {"name": "multicast-dns-service-types", "url": "https://github.com/mafintosh/multicast-dns-service-types", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/mafintosh/multicast-dns-service-types/LICENSE"}, {"name": "multipipe", "url": "https://github.com/juliangruber/multipipe", "version": "0.3.1", "license": "MIT", "licenseUrl": "https://github.com/juliangruber/multipipe/Readme.md"}, {"name": "murmurhash-js", "url": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/murmurhash-js", "version": "1.0.0", "license": "MIT", "licenseUrl": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/murmurhash-js/README.md"}, {"name": "mute-stream", "url": "https://github.com/isaacs/mute-stream", "version": "0.0.8", "license": "ISC", "licenseUrl": "https://github.com/isaacs/mute-stream/LICENSE"}, {"name": "mvstore", "url": "https://github.com/h2database/h2database", "version": "2.3.232", "license": "EPL 1.0", "licenseUrl": "https://github.com/h2database/h2database/blob/master/LICENSE.txt"}, {"name": "nanoid", "url": "https://github.com/ai/nanoid", "version": "3.3.3", "license": "MIT", "licenseUrl": "https://github.com/ai/nanoid/LICENSE"}, {"name": "nanoid", "url": "https://github.com/ai/nanoid", "version": "3.3.7", "license": "MIT", "licenseUrl": "https://github.com/ai/nanoid/LICENSE"}, {"name": "natural-compare", "url": "https://github.com/litejs/natural-compare-lite", "version": "1.4.0", "license": "MIT", "licenseUrl": "https://github.com/litejs/natural-compare-lite/README.md"}, {"name": "needle", "url": "https://github.com/tomas/needle", "version": "2.9.1", "license": "MIT", "licenseUrl": "https://github.com/tomas/needle/license.txt"}, {"name": "negotiator", "url": "https://github.com/jshttp/negotiator", "version": "0.6.3", "license": "MIT", "licenseUrl": "https://github.com/jshttp/negotiator/LICENSE"}, {"name": "neo-async", "url": "https://github.com/suguru03/neo-async", "version": "2.6.2", "license": "MIT", "licenseUrl": "https://github.com/suguru03/neo-async/LICENSE"}, {"name": "net.loomchild.segment", "url": "https://github.com/loomchild/segment", "version": "2.0.3", "license": "MIT", "licenseUrl": "https://github.com/loomchild/segment/blob/master/LICENSE.txt"}, {"name": "netty-buffer", "url": "https://netty.io", "version": "4.2.0.RC2", "license": "Apache 2.0", "licenseUrl": "https://github.com/netty/netty/blob/4.1/LICENSE.txt"}, {"name": "netty-codec-compression", "url": "https://netty.io", "version": "4.2.0.RC2", "license": "Apache 2.0", "licenseUrl": "https://github.com/netty/netty/blob/4.1/LICENSE.txt"}, {"name": "netty-codec-http", "url": "https://netty.io", "version": "4.2.0.RC2", "license": "Apache 2.0", "licenseUrl": "https://github.com/netty/netty/blob/4.1/LICENSE.txt"}, {"name": "netty-codec-protobuf", "url": "https://netty.io", "version": "4.2.0.RC2", "license": "Apache 2.0", "licenseUrl": "https://github.com/netty/netty/blob/4.1/LICENSE.txt"}, {"name": "netty-handler-proxy", "url": "https://netty.io", "version": "4.2.0.RC2", "license": "Apache 2.0", "licenseUrl": "https://github.com/netty/netty/blob/4.1/LICENSE.txt"}, {"name": "ngram-slp", "url": "https://github.com/SLP-team/SLP-Core", "version": "0.0.3", "license": "MIT", "licenseUrl": "https://github.com/SLP-team/SLP-Core/blob/master/LICENSE"}, {"name": "ngtools/webpack", "url": "https://github.com/angular/angular-cli", "version": "13.3.8", "license": "MIT", "licenseUrl": "https://github.com/angular/angular-cli/LICENSE"}, {"name": "nice-napi", "url": "https://github.com/addaleax/nice-napi", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/addaleax/nice-napi/LICENSE"}, {"name": "node-addon-api", "url": "https://github.com/nodejs/node-addon-api", "version": "3.2.1", "license": "MIT", "licenseUrl": "https://github.com/nodejs/node-addon-api/LICENSE.md"}, {"name": "node-fetch", "url": "https://github.com/bitinn/node-fetch", "version": "2.6.7", "license": "MIT", "licenseUrl": "https://github.com/bitinn/node-fetch/LICENSE.md"}, {"name": "node-forge", "url": "https://github.com/digitalbazaar/forge", "version": "1.3.1", "license": "(BSD-3-<PERSON><PERSON> OR GPL-2.0)", "licenseUrl": "https://github.com/digitalbazaar/forge/LICENSE"}, {"name": "node-gyp", "url": "https://github.com/nodejs/node-gyp", "version": "8.4.1", "license": "MIT", "licenseUrl": "https://github.com/nodejs/node-gyp/LICENSE"}, {"name": "node-gyp-build", "url": "https://github.com/prebuild/node-gyp-build", "version": "4.4.0", "license": "MIT", "licenseUrl": "https://github.com/prebuild/node-gyp-build/LICENSE"}, {"name": "node-releases", "url": "https://github.com/chicoxyzzy/node-releases", "version": "2.0.5", "license": "MIT", "licenseUrl": "https://github.com/chicoxyzzy/node-releases/LICENSE"}, {"name": "nodelib/fs.scandir", "url": "https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.scandir", "version": "2.1.5", "license": "MIT", "licenseUrl": "https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.scandir/LICENSE"}, {"name": "nodelib/fs.stat", "url": "https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.stat", "version": "2.0.5", "license": "MIT", "licenseUrl": "https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.stat/LICENSE"}, {"name": "nodelib/fs.walk", "url": "https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.walk", "version": "1.2.8", "license": "MIT", "licenseUrl": "https://github.com/nodelib/nodelib/tree/master/packages/fs/fs.walk/LICENSE"}, {"name": "non-layered-tidy-tree-layout", "url": "https://github.com/stetrevor/non-layered-tidy-tree-layout", "version": "2.0.2", "license": "MIT", "licenseUrl": "https://github.com/stetrevor/non-layered-tidy-tree-layout/LICENSE"}, {"name": "nopt", "url": "https://github.com/npm/nopt", "version": "5.0.0", "license": "ISC", "licenseUrl": "https://github.com/npm/nopt/LICENSE"}, {"name": "nopt", "url": "https://github.com/npm/nopt", "version": "4.0.3", "license": "ISC", "licenseUrl": "https://github.com/npm/nopt/LICENSE"}, {"name": "normalize-package-data", "url": "https://github.com/npm/normalize-package-data", "version": "2.5.0", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/npm/normalize-package-data/LICENSE"}, {"name": "normalize-path", "url": "https://github.com/jonschlinkert/normalize-path", "version": "3.0.0", "license": "MIT", "licenseUrl": "https://github.com/jonschlinkert/normalize-path/LICENSE"}, {"name": "normalize-range", "url": "https://github.com/jamestalmage/normalize-range", "version": "0.1.2", "license": "MIT", "licenseUrl": "https://github.com/jamestalmage/normalize-range/license"}, {"name": "normalize-url", "url": "https://github.com/sindresorhus/normalize-url", "version": "3.3.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/normalize-url/license"}, {"name": "normalize.css", "url": "https://github.com/necolas/normalize.css", "version": "8.0.1", "license": "MIT", "licenseUrl": "https://github.com/necolas/normalize.css/LICENSE.md"}, {"name": "<PERSON>ui<PERSON><PERSON><PERSON>", "url": "https://github.com/leongersen/noUiSlider", "version": "15.4.0", "license": "MIT", "licenseUrl": "https://github.com/leongersen/noUiSlider/LICENSE.md"}, {"name": "npm-bundled", "url": "https://github.com/npm/npm-bundled", "version": "1.1.2", "license": "ISC", "licenseUrl": "https://github.com/npm/npm-bundled/LICENSE"}, {"name": "npm-install-checks", "url": "https://github.com/npm/npm-install-checks", "version": "4.0.0", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/npm/npm-install-checks/LICENSE"}, {"name": "npm-normalize-package-bin", "url": "https://github.com/npm/npm-normalize-package-bin", "version": "1.0.1", "license": "ISC", "licenseUrl": "https://github.com/npm/npm-normalize-package-bin/LICENSE"}, {"name": "npm-package-arg", "url": "https://github.com/npm/npm-package-arg", "version": "8.1.5", "license": "ISC", "licenseUrl": "https://github.com/npm/npm-package-arg/LICENSE"}, {"name": "npm-packlist", "url": "https://github.com/npm/npm-packlist", "version": "3.0.0", "license": "ISC", "licenseUrl": "https://github.com/npm/npm-packlist/LICENSE"}, {"name": "npm-pick-manifest", "url": "https://github.com/npm/npm-pick-manifest", "version": "6.1.1", "license": "ISC", "licenseUrl": "https://github.com/npm/npm-pick-manifest/LICENSE.md"}, {"name": "npm-registry-fetch", "url": "https://github.com/npm/npm-registry-fetch", "version": "12.0.2", "license": "ISC", "licenseUrl": "https://github.com/npm/npm-registry-fetch/LICENSE.md"}, {"name": "npm-run-path", "url": "https://github.com/sindresorhus/npm-run-path", "version": "4.0.1", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/npm-run-path/license"}, {"name": "npmcli/fs", "url": "https://github.com/npm/fs", "version": "2.1.0", "license": "ISC", "licenseUrl": "https://github.com/npm/fs/LICENSE.md"}, {"name": "npmcli/git", "url": "https://github.com/npm/git", "version": "2.1.0", "license": "ISC", "licenseUrl": "https://github.com/npm/git/LICENSE"}, {"name": "npmcli/installed-package-contents", "url": "https://github.com/npm/installed-package-contents", "version": "1.0.7", "license": "ISC", "licenseUrl": "https://github.com/npm/installed-package-contents/LICENSE"}, {"name": "npmcli/move-file", "url": "https://github.com/npm/move-file", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/npm/move-file/LICENSE.md"}, {"name": "npmcli/move-file", "url": "https://github.com/npm/move-file", "version": "1.1.2", "license": "MIT", "licenseUrl": "https://github.com/npm/move-file/LICENSE.md"}, {"name": "npmcli/node-gyp", "url": "https://github.com/npm/node-gyp", "version": "1.0.3", "license": "ISC", "licenseUrl": "https://github.com/npm/node-gyp/README.md"}, {"name": "npmcli/promise-spawn", "url": "https://github.com/npm/promise-spawn", "version": "1.3.2", "license": "ISC", "licenseUrl": "https://github.com/npm/promise-spawn/LICENSE"}, {"name": "npmcli/run-script", "url": "https://github.com/npm/run-script", "version": "2.0.0", "license": "ISC", "licenseUrl": "https://github.com/npm/run-script/LICENSE"}, {"name": "npmlog", "url": "https://github.com/npm/npmlog", "version": "6.0.2", "license": "ISC", "licenseUrl": "https://github.com/npm/npmlog/LICENSE.md"}, {"name": "nrwl/cli", "url": "https://github.com/nrwl/nx", "version": "14.3.6", "license": "MIT", "licenseUrl": "https://github.com/nrwl/nx/LICENSE"}, {"name": "nrwl/devkit", "url": "https://github.com/nrwl/nx", "version": "13.1.3", "license": "MIT", "licenseUrl": "https://github.com/nrwl/nx/LICENSE"}, {"name": "nrwl/tao", "url": "https://github.com/nrwl/nx", "version": "13.1.3", "license": "MIT", "licenseUrl": "https://github.com/nrwl/nx/LICENSE"}, {"name": "nrwl/tao", "url": "https://github.com/nrwl/nx", "version": "14.3.6", "license": "MIT", "licenseUrl": "https://github.com/nrwl/nx/LICENSE"}, {"name": "nth-check", "url": "https://github.com/fb55/nth-check", "version": "2.1.1", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/fb55/nth-check/LICENSE"}, {"name": "nth-check", "url": "https://github.com/fb55/nth-check", "version": "1.0.2", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/fb55/nth-check/LICENSE"}, {"name": "nx", "url": "https://github.com/nrwl/nx", "version": "13.1.3", "license": "MIT", "licenseUrl": "https://github.com/nrwl/nx/LICENSE"}, {"name": "nx", "url": "https://github.com/nrwl/nx", "version": "14.3.6", "license": "MIT", "licenseUrl": "https://github.com/nrwl/nx/LICENSE"}, {"name": "object-assign", "url": "https://github.com/sindresorhus/object-assign", "version": "4.1.1", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/object-assign/license"}, {"name": "object-inspect", "url": "https://github.com/inspect-js/object-inspect", "version": "1.13.1", "license": "MIT", "licenseUrl": "https://github.com/inspect-js/object-inspect/LICENSE"}, {"name": "object-is", "url": "https://github.com/es-shims/object-is", "version": "1.1.5", "license": "MIT", "licenseUrl": "https://github.com/es-shims/object-is/LICENSE"}, {"name": "object-keys", "url": "https://github.com/ljharb/object-keys", "version": "1.1.1", "license": "MIT", "licenseUrl": "https://github.com/ljharb/object-keys/LICENSE"}, {"name": "object.assign", "url": "https://github.com/ljharb/object.assign", "version": "4.1.2", "license": "MIT", "licenseUrl": "https://github.com/ljharb/object.assign/LICENSE"}, {"name": "object.getownpropertydescriptors", "url": "https://github.com/es-shims/object.getownpropertydescriptors", "version": "2.1.4", "license": "MIT", "licenseUrl": "https://github.com/es-shims/object.getownpropertydescriptors/LICENSE"}, {"name": "object.values", "url": "https://github.com/es-shims/Object.values", "version": "1.1.5", "license": "MIT", "licenseUrl": "https://github.com/es-shims/Object.values/LICENSE"}, {"name": "obuf", "url": "https://github.com/indutny/offset-buffer", "version": "1.1.2", "license": "MIT", "licenseUrl": "https://github.com/indutny/offset-buffer/LICENSE"}, {"name": "on-finished", "url": "https://github.com/jshttp/on-finished", "version": "2.3.0", "license": "MIT", "licenseUrl": "https://github.com/jshttp/on-finished/LICENSE"}, {"name": "on-finished", "url": "https://github.com/jshttp/on-finished", "version": "2.4.1", "license": "MIT", "licenseUrl": "https://github.com/jshttp/on-finished/LICENSE"}, {"name": "on-headers", "url": "https://github.com/jshttp/on-headers", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/jshttp/on-headers/LICENSE"}, {"name": "once", "url": "https://github.com/isaacs/once", "version": "1.4.0", "license": "ISC", "licenseUrl": "https://github.com/isaacs/once/LICENSE"}, {"name": "once", "url": "https://github.com/isaacs/once", "version": "1.3.3", "license": "ISC", "licenseUrl": "https://github.com/isaacs/once/LICENSE"}, {"name": "onetime", "url": "https://github.com/sindresorhus/onetime", "version": "5.1.2", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/onetime/license"}, {"name": "open", "url": "https://github.com/sindresorhus/open", "version": "8.4.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/open/license"}, {"name": "opentelemetry", "url": "https://opentelemetry.io/", "version": "1.47.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/open-telemetry/opentelemetry-java/blob/main/LICENSE"}, {"name": "opentelemetry-exporter-otlp-common", "url": "https://opentelemetry.io/", "version": "1.47.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/open-telemetry/opentelemetry-java/blob/main/LICENSE"}, {"name": "opentelemetry-extension-kotlin", "url": "https://opentelemetry.io/", "version": "1.47.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/open-telemetry/opentelemetry-java/blob/main/LICENSE"}, {"name": "opentelemetry-semconv", "url": "https://opentelemetry.io/", "version": "1.30.0-rc.1", "license": "Apache 2.0", "licenseUrl": "https://github.com/open-telemetry/semantic-conventions-java/blob/main/LICENSE"}, {"name": "opentest4j", "url": "https://github.com/ota4j-team/opentest4j", "version": "1.3.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/ota4j-team/opentest4j/blob/master/LICENSE"}, {"name": "optionator", "url": "https://github.com/gkz/optionator", "version": "0.9.1", "license": "MIT", "licenseUrl": "https://github.com/gkz/optionator/LICENSE"}, {"name": "optionator", "url": "https://github.com/gkz/optionator", "version": "0.8.3", "license": "MIT", "licenseUrl": "https://github.com/gkz/optionator/LICENSE"}, {"name": "ora", "url": "https://github.com/sindresorhus/ora", "version": "5.4.1", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/ora/license"}, {"name": "org.scilab.forge:jlatexmath:1.0.7", "url": "https://github.com/opencollab/jlatexmath", "version": "1.0.7", "license": "GPL 2.0 + Classpath", "licenseUrl": "https://github.com/opencollab/jlatexmath/blob/master/LICENSE"}, {"name": "os-homedir", "url": "https://github.com/sindresorhus/os-homedir", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/os-homedir/license"}, {"name": "os-tmpdir", "url": "https://github.com/sindresorhus/os-tmpdir", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/os-tmpdir/license"}, {"name": "osenv", "url": "https://github.com/npm/osenv", "version": "0.1.5", "license": "ISC", "licenseUrl": "https://github.com/npm/osenv/LICENSE"}, {"name": "p-limit", "url": "https://github.com/sindresorhus/p-limit", "version": "2.3.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/p-limit/license"}, {"name": "p-limit", "url": "https://github.com/sindresorhus/p-limit", "version": "3.1.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/p-limit/license"}, {"name": "p-locate", "url": "https://github.com/sindresorhus/p-locate", "version": "5.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/p-locate/license"}, {"name": "p-locate", "url": "https://github.com/sindresorhus/p-locate", "version": "4.1.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/p-locate/license"}, {"name": "p-map", "url": "https://github.com/sindresorhus/p-map", "version": "4.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/p-map/license"}, {"name": "p-retry", "url": "https://github.com/sindresorhus/p-retry", "version": "4.6.2", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/p-retry/license"}, {"name": "p-try", "url": "https://github.com/sindresorhus/p-try", "version": "2.2.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/p-try/license"}, {"name": "pacote", "url": "https://github.com/npm/pacote", "version": "12.0.3", "license": "ISC", "licenseUrl": "https://github.com/npm/pacote/LICENSE"}, {"name": "pako", "url": "https://github.com/nodeca/pako", "version": "1.0.11", "license": "(MIT AND Zlib)", "licenseUrl": "https://github.com/nodeca/pako/LICENSE"}, {"name": "parcel/watcher", "url": "https://github.com/parcel-bundler/watcher", "version": "2.0.4", "license": "MIT", "licenseUrl": "https://github.com/parcel-bundler/watcher/LICENSE"}, {"name": "parent-module", "url": "https://github.com/sindresorhus/parent-module", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/parent-module/license"}, {"name": "parse-json", "url": "https://github.com/sindresorhus/parse-json", "version": "4.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/parse-json/license"}, {"name": "parse-json", "url": "https://github.com/sindresorhus/parse-json", "version": "5.2.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/parse-json/license"}, {"name": "parse-node-version", "url": "https://github.com/gulpjs/parse-node-version", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/gulpjs/parse-node-version/LICENSE"}, {"name": "parse-srcset", "url": "https://github.com/albell/parse-srcset", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/albell/parse-srcset/LICENSE"}, {"name": "parse5", "url": "https://github.com/inikulin/parse5", "version": "6.0.1", "license": "MIT", "licenseUrl": "https://github.com/inikulin/parse5/LICENSE"}, {"name": "parse5-html-rewriting-stream", "url": "https://github.com/inikulin/parse5", "version": "6.0.1", "license": "MIT", "licenseUrl": "https://github.com/inikulin/parse5/LICENSE"}, {"name": "parse5-htmlparser2-tree-adapter", "url": "https://github.com/inikulin/parse5", "version": "6.0.1", "license": "MIT", "licenseUrl": "https://github.com/inikulin/parse5/LICENSE"}, {"name": "parse5-sax-parser", "url": "https://github.com/inikulin/parse5", "version": "6.0.1", "license": "MIT", "licenseUrl": "https://github.com/inikulin/parse5/LICENSE"}, {"name": "parseurl", "url": "https://github.com/pillarjs/parseurl", "version": "1.3.3", "license": "MIT", "licenseUrl": "https://github.com/pillarjs/parseurl/LICENSE"}, {"name": "path-browserify", "url": "https://github.com/browserify/path-browserify", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/browserify/path-browserify/LICENSE"}, {"name": "path-exists", "url": "https://github.com/sindresorhus/path-exists", "version": "4.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/path-exists/license"}, {"name": "path-is-absolute", "url": "https://github.com/sindresorhus/path-is-absolute", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/path-is-absolute/license"}, {"name": "path-key", "url": "https://github.com/sindresorhus/path-key", "version": "3.1.1", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/path-key/license"}, {"name": "path-parse", "url": "https://github.com/jbgu<PERSON>rez/path-parse", "version": "1.0.7", "license": "MIT", "licenseUrl": "https://github.com/jbgutierrez/path-parse/LICENSE"}, {"name": "path-to-regexp", "url": "https://github.com/component/path-to-regexp", "version": "0.1.7", "license": "MIT", "licenseUrl": "https://github.com/component/path-to-regexp/LICENSE"}, {"name": "path-type", "url": "https://github.com/sindresorhus/path-type", "version": "4.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/path-type/license"}, {"name": "pathval", "url": "https://github.com/chaijs/pathval", "version": "1.1.1", "license": "MIT", "licenseUrl": "https://github.com/chaijs/pathval/LICENSE"}, {"name": "picocolors", "url": "https://github.com/alexeyraspopov/picocolors", "version": "0.2.1", "license": "ISC", "licenseUrl": "https://github.com/alexeyraspopov/picocolors/LICENSE"}, {"name": "picocolors", "url": "https://github.com/alexeyraspopov/picocolors", "version": "1.0.0", "license": "ISC", "licenseUrl": "https://github.com/alexeyraspopov/picocolors/LICENSE"}, {"name": "picomatch", "url": "https://github.com/micromatch/picomatch", "version": "2.3.1", "license": "MIT", "licenseUrl": "https://github.com/micromatch/picomatch/LICENSE"}, {"name": "pify", "url": "https://github.com/sindresorhus/pify", "version": "2.3.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/pify/license"}, {"name": "pify", "url": "https://github.com/sindresorhus/pify", "version": "4.0.1", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/pify/license"}, {"name": "piscina", "url": "https://github.com/piscinajs/piscina", "version": "3.2.0", "license": "MIT", "licenseUrl": "https://github.com/piscinajs/piscina/LICENSE"}, {"name": "pkg-dir", "url": "https://github.com/sindresorhus/pkg-dir", "version": "4.2.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/pkg-dir/license"}, {"name": "plexus-archiver", "url": "https://github.com/codehaus-plexus/plexus-archiver", "version": "4.8.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/codehaus-plexus/plexus-archiver/blob/master/LICENSE"}, {"name": "plotly.js-dist", "url": "https://github.com/plotly/plotly.js", "version": "2.35.2", "license": "MIT", "licenseUrl": "https://github.com/plotly/plotly.js/LICENSE"}, {"name": "popper.js", "url": "https://github.com/FezVrasta/popper.js", "version": "1.16.1", "license": "MIT", "licenseUrl": "https://github.com/FezVrasta/popper.js/README.md"}, {"name": "portfinder", "url": "https://github.com/http-party/node-portfinder", "version": "1.0.28", "license": "MIT", "licenseUrl": "https://github.com/http-party/node-portfinder/LICENSE"}, {"name": "postcss", "url": "https://github.com/postcss/postcss", "version": "7.0.39", "license": "MIT", "licenseUrl": "https://github.com/postcss/postcss/LICENSE"}, {"name": "postcss", "url": "https://github.com/postcss/postcss", "version": "8.4.5", "license": "MIT", "licenseUrl": "https://github.com/postcss/postcss/LICENSE"}, {"name": "postcss-attribute-case-insensitive", "url": "https://github.com/csstools/postcss-plugins", "version": "5.0.1", "license": "MIT", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE"}, {"name": "postcss-calc", "url": "https://github.com/postcss/postcss-calc", "version": "7.0.5", "license": "MIT", "licenseUrl": "https://github.com/postcss/postcss-calc/LICENSE"}, {"name": "postcss-color-functional-notation", "url": "https://github.com/csstools/postcss-plugins", "version": "4.2.3", "license": "CC0-1.0", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "postcss-color-hex-alpha", "url": "https://github.com/csstools/postcss-plugins", "version": "8.0.4", "license": "MIT", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "postcss-color-rebeccapurple", "url": "https://github.com/csstools/postcss-plugins", "version": "7.1.0", "license": "CC0-1.0", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "postcss-colormin", "url": "https://github.com/cssnano/cssnano", "version": "4.0.3", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "postcss-convert-values", "url": "https://github.com/cssnano/cssnano", "version": "4.0.1", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "postcss-custom-media", "url": "https://github.com/csstools/postcss-plugins", "version": "8.0.2", "license": "MIT", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "postcss-custom-properties", "url": "https://github.com/csstools/postcss-plugins", "version": "12.1.8", "license": "MIT", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "postcss-custom-selectors", "url": "https://github.com/csstools/postcss-plugins", "version": "6.0.3", "license": "MIT", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "postcss-dir-pseudo-class", "url": "https://github.com/csstools/postcss-plugins", "version": "6.0.4", "license": "CC0-1.0", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "postcss-discard-comments", "url": "https://github.com/cssnano/cssnano", "version": "4.0.2", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "postcss-discard-duplicates", "url": "https://github.com/cssnano/cssnano", "version": "4.0.2", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "postcss-discard-empty", "url": "https://github.com/cssnano/cssnano", "version": "4.0.1", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "postcss-discard-overridden", "url": "https://github.com/cssnano/cssnano", "version": "4.0.1", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE"}, {"name": "postcss-double-position-gradients", "url": "https://github.com/csstools/postcss-plugins", "version": "3.1.1", "license": "CC0-1.0", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "postcss-env-function", "url": "https://github.com/csstools/postcss-plugins", "version": "4.0.6", "license": "CC0-1.0", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "postcss-focus-visible", "url": "https://github.com/csstools/postcss-plugins", "version": "6.0.4", "license": "CC0-1.0", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "postcss-focus-within", "url": "https://github.com/csstools/postcss-plugins", "version": "5.0.4", "license": "CC0-1.0", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "postcss-font-variant", "url": "https://github.com/postcss/postcss-font-variant", "version": "5.0.0", "license": "MIT", "licenseUrl": "https://github.com/postcss/postcss-font-variant/LICENSE"}, {"name": "postcss-gap-properties", "url": "https://github.com/csstools/postcss-plugins", "version": "3.0.3", "license": "CC0-1.0", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "postcss-image-set-function", "url": "https://github.com/csstools/postcss-plugins", "version": "4.0.6", "license": "CC0-1.0", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "postcss-import", "url": "https://github.com/postcss/postcss-import", "version": "14.0.2", "license": "MIT", "licenseUrl": "https://github.com/postcss/postcss-import/LICENSE"}, {"name": "postcss-initial", "url": "https://github.com/maximkoretskiy/postcss-initial", "version": "4.0.1", "license": "MIT", "licenseUrl": "https://github.com/maximkoretskiy/postcss-initial/LICENSE"}, {"name": "postcss-lab-function", "url": "https://github.com/csstools/postcss-plugins", "version": "4.2.0", "license": "CC0-1.0", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "postcss-loader", "url": "https://github.com/webpack-contrib/postcss-loader", "version": "6.2.1", "license": "MIT", "licenseUrl": "https://github.com/webpack-contrib/postcss-loader/LICENSE"}, {"name": "postcss-logical", "url": "https://github.com/csstools/postcss-plugins", "version": "5.0.4", "license": "CC0-1.0", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "postcss-media-minmax", "url": "https://github.com/postcss/postcss-media-minmax", "version": "5.0.0", "license": "MIT", "licenseUrl": "https://github.com/postcss/postcss-media-minmax/LICENSE"}, {"name": "postcss-merge-longhand", "url": "https://github.com/cssnano/cssnano", "version": "4.0.11", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "postcss-merge-rules", "url": "https://github.com/cssnano/cssnano", "version": "4.0.3", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "postcss-minify-font-values", "url": "https://github.com/cssnano/cssnano", "version": "4.0.2", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE"}, {"name": "postcss-minify-gradients", "url": "https://github.com/cssnano/cssnano", "version": "4.0.2", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "postcss-minify-params", "url": "https://github.com/cssnano/cssnano", "version": "4.0.2", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE"}, {"name": "postcss-minify-selectors", "url": "https://github.com/cssnano/cssnano", "version": "4.0.2", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "postcss-modules-extract-imports", "url": "https://github.com/css-modules/postcss-modules-extract-imports", "version": "3.0.0", "license": "ISC", "licenseUrl": "https://github.com/css-modules/postcss-modules-extract-imports/LICENSE"}, {"name": "postcss-modules-local-by-default", "url": "https://github.com/css-modules/postcss-modules-local-by-default", "version": "4.0.0", "license": "MIT", "licenseUrl": "https://github.com/css-modules/postcss-modules-local-by-default/LICENSE"}, {"name": "postcss-modules-scope", "url": "https://github.com/css-modules/postcss-modules-scope", "version": "3.0.0", "license": "ISC", "licenseUrl": "https://github.com/css-modules/postcss-modules-scope/LICENSE"}, {"name": "postcss-modules-values", "url": "https://github.com/css-modules/postcss-modules-values", "version": "4.0.0", "license": "ISC", "licenseUrl": "https://github.com/css-modules/postcss-modules-values/LICENSE"}, {"name": "postcss-nesting", "url": "https://github.com/csstools/postcss-plugins", "version": "10.1.9", "license": "CC0-1.0", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "postcss-normalize-charset", "url": "https://github.com/cssnano/cssnano", "version": "4.0.1", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE"}, {"name": "postcss-normalize-display-values", "url": "https://github.com/cssnano/cssnano", "version": "4.0.2", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "postcss-normalize-positions", "url": "https://github.com/cssnano/cssnano", "version": "4.0.2", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "postcss-normalize-repeat-style", "url": "https://github.com/cssnano/cssnano", "version": "4.0.2", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "postcss-normalize-string", "url": "https://github.com/cssnano/cssnano", "version": "4.0.2", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "postcss-normalize-timing-functions", "url": "https://github.com/cssnano/cssnano", "version": "4.0.2", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "postcss-normalize-unicode", "url": "https://github.com/cssnano/cssnano", "version": "4.0.1", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "postcss-normalize-url", "url": "https://github.com/cssnano/cssnano", "version": "4.0.1", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "postcss-normalize-whitespace", "url": "https://github.com/cssnano/cssnano", "version": "4.0.2", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "postcss-ordered-values", "url": "https://github.com/cssnano/cssnano", "version": "4.1.2", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "postcss-overflow-shorthand", "url": "https://github.com/csstools/postcss-plugins", "version": "3.0.3", "license": "CC0-1.0", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "postcss-page-break", "url": "https://github.com/shrpne/postcss-page-break", "version": "3.0.4", "license": "MIT", "licenseUrl": "https://github.com/shrpne/postcss-page-break/LICENSE"}, {"name": "postcss-place", "url": "https://github.com/csstools/postcss-plugins", "version": "7.0.4", "license": "CC0-1.0", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "postcss-preset-env", "url": "https://github.com/csstools/postcss-plugins", "version": "7.2.3", "license": "CC0-1.0", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "postcss-pseudo-class-any-link", "url": "https://github.com/csstools/postcss-plugins", "version": "7.1.5", "license": "CC0-1.0", "licenseUrl": "https://github.com/csstools/postcss-plugins/LICENSE.md"}, {"name": "postcss-reduce-initial", "url": "https://github.com/cssnano/cssnano", "version": "4.0.3", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "postcss-reduce-transforms", "url": "https://github.com/cssnano/cssnano", "version": "4.0.2", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "postcss-replace-overflow-wrap", "url": "https://github.com/MattDiMu/postcss-replace-overflow-wrap", "version": "4.0.0", "license": "MIT", "licenseUrl": "https://github.com/MattDiMu/postcss-replace-overflow-wrap/LICENSE"}, {"name": "postcss-selector-not", "url": "https://github.com/postcss/postcss-selector-not", "version": "5.0.0", "license": "MIT", "licenseUrl": "https://github.com/postcss/postcss-selector-not/LICENSE"}, {"name": "postcss-selector-parser", "url": "https://github.com/postcss/postcss-selector-parser", "version": "6.0.10", "license": "MIT", "licenseUrl": "https://github.com/postcss/postcss-selector-parser/LICENSE-MIT"}, {"name": "postcss-selector-parser", "url": "https://github.com/postcss/postcss-selector-parser", "version": "3.1.2", "license": "MIT", "licenseUrl": "https://github.com/postcss/postcss-selector-parser/LICENSE-MIT"}, {"name": "postcss-svgo", "url": "https://github.com/cssnano/cssnano", "version": "4.0.3", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "postcss-unique-selectors", "url": "https://github.com/cssnano/cssnano", "version": "4.0.1", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "postcss-value-parser", "url": "https://github.com/TrySound/postcss-value-parser", "version": "3.3.1", "license": "MIT", "licenseUrl": "https://github.com/TrySound/postcss-value-parser/LICENSE"}, {"name": "postcss-value-parser", "url": "https://github.com/TrySound/postcss-value-parser", "version": "4.2.0", "license": "MIT", "licenseUrl": "https://github.com/TrySound/postcss-value-parser/LICENSE"}, {"name": "prelude-ls", "url": "https://github.com/gkz/prelude-ls", "version": "1.1.2", "license": "MIT", "licenseUrl": "https://github.com/gkz/prelude-ls/LICENSE"}, {"name": "prelude-ls", "url": "https://github.com/gkz/prelude-ls", "version": "1.2.1", "license": "MIT", "licenseUrl": "https://github.com/gkz/prelude-ls/LICENSE"}, {"name": "pretty-bytes", "url": "https://github.com/sindresorhus/pretty-bytes", "version": "5.6.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/pretty-bytes/license"}, {"name": "pretty-format", "url": "https://github.com/facebook/jest", "version": "28.1.1", "license": "MIT", "licenseUrl": "https://github.com/facebook/jest/LICENSE"}, {"name": "process-nextick-args", "url": "https://github.com/calvinmetcalf/process-nextick-args", "version": "2.0.1", "license": "MIT", "licenseUrl": "https://github.com/calvinmetcalf/process-nextick-args/license.md"}, {"name": "promise-inflight", "url": "https://github.com/iarna/promise-inflight", "version": "1.0.1", "license": "ISC", "licenseUrl": "https://github.com/iarna/promise-inflight/LICENSE"}, {"name": "promise-retry", "url": "https://github.com/IndigoUnited/node-promise-retry", "version": "2.0.1", "license": "MIT", "licenseUrl": "https://github.com/IndigoUnited/node-promise-retry/LICENSE"}, {"name": "prop-types", "url": "https://github.com/facebook/prop-types", "version": "15.8.1", "license": "MIT", "licenseUrl": "https://github.com/facebook/prop-types/LICENSE"}, {"name": "protobuf-kotlin", "url": "https://developers.google.com/protocol-buffers", "version": "3.25.5", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://github.com/protocolbuffers/protobuf/blob/main/LICENSE"}, {"name": "proxy-addr", "url": "https://github.com/jshttp/proxy-addr", "version": "2.0.7", "license": "MIT", "licenseUrl": "https://github.com/jshttp/proxy-addr/LICENSE"}, {"name": "prr", "url": "https://github.com/rvagg/prr", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/rvagg/prr/LICENSE.md"}, {"name": "pty4j", "url": "https://github.com/JetBrains/pty4j", "version": "0.13.1", "license": "EPL 1.0", "licenseUrl": "https://github.com/JetBrains/pty4j/blob/master/LICENSE"}, {"name": "punycode", "url": "https://github.com/bestiejs/punycode.js", "version": "2.1.1", "license": "MIT", "licenseUrl": "https://github.com/bestiejs/punycode.js/LICENSE-MIT.txt"}, {"name": "punycode", "url": "https://github.com/bestiejs/punycode.js", "version": "1.4.1", "license": "MIT", "licenseUrl": "https://github.com/bestiejs/punycode.js/LICENSE-MIT.txt"}, {"name": "q", "url": "https://github.com/kriskowal/q", "version": "1.5.1", "license": "MIT", "licenseUrl": "https://github.com/kriskowal/q/LICENSE"}, {"name": "qdox-java-parser", "url": "https://github.com/paul-hammant/qdox", "version": "2.2.0", "license": "Apache 2.0", "licenseUrl": "https://github.com/paul-hammant/qdox/blob/master/LICENSE.txt"}, {"name": "qjobs", "url": "https://github.com/franck34/qjobs", "version": "1.2.0", "license": "MIT", "licenseUrl": "https://github.com/franck34/qjobs/LICENCE"}, {"name": "qs", "url": "https://github.com/ljharb/qs", "version": "6.10.3", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/ljharb/qs/LICENSE.md"}, {"name": "qs", "url": "https://github.com/ljharb/qs", "version": "6.12.1", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/ljharb/qs/LICENSE.md"}, {"name": "querystringify", "url": "https://github.com/unshiftio/querystringify", "version": "2.2.0", "license": "MIT", "licenseUrl": "https://github.com/unshiftio/querystringify/LICENSE"}, {"name": "queue-microtask", "url": "https://github.com/feross/queue-microtask", "version": "1.2.3", "license": "MIT", "licenseUrl": "https://github.com/feross/queue-microtask/LICENSE"}, {"name": "randombytes", "url": "https://github.com/crypto-browserify/randombytes", "version": "2.1.0", "license": "MIT", "licenseUrl": "https://github.com/crypto-browserify/randombytes/LICENSE"}, {"name": "range-parser", "url": "https://github.com/jshttp/range-parser", "version": "1.2.1", "license": "MIT", "licenseUrl": "https://github.com/jshttp/range-parser/LICENSE"}, {"name": "raw-body", "url": "https://github.com/stream-utils/raw-body", "version": "2.5.1", "license": "MIT", "licenseUrl": "https://github.com/stream-utils/raw-body/LICENSE"}, {"name": "raw-loader", "url": "https://github.com/webpack-contrib/raw-loader", "version": "4.0.2", "license": "MIT", "licenseUrl": "https://github.com/webpack-contrib/raw-loader/LICENSE"}, {"name": "rd Swing integration", "url": "https://github.com/JetBrains/rd/tree/master/rd-kt/rd-swing", "version": "2025.1.1", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/rd/blob/master/LICENSE"}, {"name": "rd core", "url": "https://github.com/JetBrains/rd/tree/master/rd-kt/rd-core", "version": "2025.1.1", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/rd/blob/master/LICENSE"}, {"name": "rd framework", "url": "https://github.com/JetBrains/rd/tree/master/rd-kt/rd-framework", "version": "2025.1.1", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/rd/blob/master/LICENSE"}, {"name": "rd generator", "url": "https://github.com/JetBrains/rd/tree/master/rd-kt/rd-gen", "version": "2025.1.1", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/rd/blob/master/LICENSE"}, {"name": "rd text buffers", "url": "https://github.com/JetBrains/rd/tree/master/rd-kt/rd-text", "version": "2025.1.1", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/rd/blob/master/LICENSE"}, {"name": "react", "url": "https://github.com/facebook/react", "version": "18.3.1", "license": "MIT", "licenseUrl": "https://github.com/facebook/react/LICENSE"}, {"name": "react", "url": "https://github.com/facebook/react", "version": "17.0.2", "license": "MIT", "licenseUrl": "https://github.com/facebook/react/LICENSE"}, {"name": "react-dom", "url": "https://github.com/facebook/react", "version": "17.0.2", "license": "MIT", "licenseUrl": "https://github.com/facebook/react/LICENSE"}, {"name": "react-dom", "url": "https://github.com/facebook/react", "version": "18.3.1", "license": "MIT", "licenseUrl": "https://github.com/facebook/react/LICENSE"}, {"name": "react-is", "url": "https://github.com/facebook/react", "version": "16.13.1", "license": "MIT", "licenseUrl": "https://github.com/facebook/react/LICENSE"}, {"name": "react-is", "url": "https://github.com/facebook/react", "version": "18.2.0", "license": "MIT", "licenseUrl": "https://github.com/facebook/react/LICENSE"}, {"name": "react-lifecycles-compat", "url": "https://github.com/reactjs/react-lifecycles-compat", "version": "3.0.4", "license": "MIT", "licenseUrl": "https://github.com/reactjs/react-lifecycles-compat/LICENSE.md"}, {"name": "react-popper", "url": "https://github.com/souporserious/react-popper", "version": "1.3.11", "license": "MIT", "licenseUrl": "https://github.com/souporserious/react-popper/LICENSE"}, {"name": "react-transition-group", "url": "https://github.com/reactjs/react-transition-group", "version": "2.9.0", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/reactjs/react-transition-group/LICENSE"}, {"name": "read-cache", "url": "https://github.com/TrySound/read-cache", "version": "1.0.0", "license": "MIT", "licenseUrl": "https://github.com/TrySound/read-cache/LICENSE"}, {"name": "read-installed", "url": "https://github.com/isaacs/read-installed", "version": "4.0.3", "license": "ISC", "licenseUrl": "https://github.com/isaacs/read-installed/LICENSE"}, {"name": "read-package-json", "url": "https://github.com/npm/read-package-json", "version": "2.1.2", "license": "ISC", "licenseUrl": "https://github.com/npm/read-package-json/LICENSE"}, {"name": "read-package-json-fast", "url": "https://github.com/npm/read-package-json-fast", "version": "2.0.3", "license": "ISC", "licenseUrl": "https://github.com/npm/read-package-json-fast/LICENSE"}, {"name": "readable-stream", "url": "https://github.com/isaacs/readable-stream", "version": "1.0.34", "license": "MIT", "licenseUrl": "https://github.com/isaacs/readable-stream/LICENSE"}, {"name": "readable-stream", "url": "https://github.com/nodejs/readable-stream", "version": "3.6.0", "license": "MIT", "licenseUrl": "https://github.com/nodejs/readable-stream/LICENSE"}, {"name": "readable-stream", "url": "https://github.com/nodejs/readable-stream", "version": "2.3.7", "license": "MIT", "licenseUrl": "https://github.com/nodejs/readable-stream/LICENSE"}, {"name": "readdir-scoped-modules", "url": "https://github.com/npm/readdir-scoped-modules", "version": "1.1.0", "license": "ISC", "licenseUrl": "https://github.com/npm/readdir-scoped-modules/LICENSE"}, {"name": "readdirp", "url": "https://github.com/paulmillr/readdirp", "version": "3.6.0", "license": "MIT", "licenseUrl": "https://github.com/paulmillr/readdirp/LICENSE"}, {"name": "reflect-metadata", "url": "https://github.com/rbuckton/reflect-metadata", "version": "0.1.13", "license": "Apache-2.0", "licenseUrl": "https://github.com/rbuckton/reflect-metadata/LICENSE"}, {"name": "regenerate", "url": "https://github.com/mathiasbynens/regenerate", "version": "1.4.2", "license": "MIT", "licenseUrl": "https://github.com/mathiasbynens/regenerate/LICENSE-MIT.txt"}, {"name": "regenerate-unicode-properties", "url": "https://github.com/mathiasbynens/regenerate-unicode-properties", "version": "10.0.1", "license": "MIT", "licenseUrl": "https://github.com/mathiasbynens/regenerate-unicode-properties/LICENSE-MIT.txt"}, {"name": "regenerate-unicode-properties", "url": "https://github.com/mathiasbynens/regenerate-unicode-properties", "version": "8.2.0", "license": "MIT", "licenseUrl": "https://github.com/mathiasbynens/regenerate-unicode-properties/LICENSE-MIT.txt"}, {"name": "regenerator-runtime", "url": "https://github.com/facebook/regenerator/tree/main/packages/runtime", "version": "0.13.11", "license": "MIT", "licenseUrl": "https://github.com/facebook/regenerator/tree/main/packages/runtime/LICENSE"}, {"name": "regenerator-runtime", "url": "https://github.com/facebook/regenerator/tree/main/packages/runtime", "version": "0.14.1", "license": "MIT", "licenseUrl": "https://github.com/facebook/regenerator/tree/main/packages/runtime/LICENSE"}, {"name": "regenerator-runtime", "url": "https://github.com/facebook/regenerator/tree/master/packages/runtime", "version": "0.13.9", "license": "MIT", "licenseUrl": "https://github.com/facebook/regenerator/tree/master/packages/runtime/LICENSE"}, {"name": "regenerator-transform", "url": "https://github.com/facebook/regenerator/tree/master/packages/transform", "version": "0.15.0", "license": "MIT", "licenseUrl": "https://github.com/facebook/regenerator/tree/master/packages/transform/LICENSE"}, {"name": "regex-parser", "url": "https://github.com/IonicaBizau/regex-parser.js", "version": "2.2.11", "license": "MIT", "licenseUrl": "https://github.com/IonicaBizau/regex-parser.js/LICENSE"}, {"name": "regexp.prototype.flags", "url": "https://github.com/es-shims/RegExp.prototype.flags", "version": "1.4.3", "license": "MIT", "licenseUrl": "https://github.com/es-shims/RegExp.prototype.flags/LICENSE"}, {"name": "regexpp", "url": "https://github.com/mysticatea/regexpp", "version": "3.2.0", "license": "MIT", "licenseUrl": "https://github.com/mysticatea/regexpp/LICENSE"}, {"name": "regexpu-core", "url": "https://github.com/mathiasbynens/regexpu-core", "version": "5.0.1", "license": "MIT", "licenseUrl": "https://github.com/mathiasbynens/regexpu-core/LICENSE-MIT.txt"}, {"name": "regexpu-core", "url": "https://github.com/mathiasbynens/regexpu-core", "version": "4.5.4", "license": "MIT", "licenseUrl": "https://github.com/mathiasbynens/regexpu-core/LICENSE-MIT.txt"}, {"name": "regjsgen", "url": "https://github.com/bnjmnt4n/regjsgen", "version": "0.6.0", "license": "MIT", "licenseUrl": "https://github.com/bnjmnt4n/regjsgen/LICENSE-MIT.txt"}, {"name": "regjsgen", "url": "https://github.com/bnjmnt4n/regjsgen", "version": "0.5.2", "license": "MIT", "licenseUrl": "https://github.com/bnjmnt4n/regjsgen/LICENSE-MIT.txt"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/jviereck/regjsparser", "version": "0.8.4", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/jviereck/regjsparser/LICENSE.BSD"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/jviereck/regjsparser", "version": "0.6.9", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/jviereck/regjsparser/LICENSE.BSD"}, {"name": "require-directory", "url": "https://github.com/troygoode/node-require-directory", "version": "2.1.1", "license": "MIT", "licenseUrl": "https://github.com/troygoode/node-require-directory/LICENSE"}, {"name": "require-from-string", "url": "https://github.com/floatdrop/require-from-string", "version": "2.0.2", "license": "MIT", "licenseUrl": "https://github.com/floatdrop/require-from-string/license"}, {"name": "requirejs", "url": "https://github.com/jrburke/r.js", "version": "2.3.6", "license": "MIT", "licenseUrl": "https://github.com/jrburke/r.js/README.md"}, {"name": "requires-port", "url": "https://github.com/unshiftio/requires-port", "version": "1.0.0", "license": "MIT", "licenseUrl": "https://github.com/unshiftio/requires-port/LICENSE"}, {"name": "resize-observer-polyfill", "url": "https://github.com/que-etc/resize-observer-polyfill", "version": "1.5.1", "license": "MIT", "licenseUrl": "https://github.com/que-etc/resize-observer-polyfill/LICENSE"}, {"name": "resolve", "url": "https://github.com/browserify/resolve", "version": "1.22.0", "license": "MIT", "licenseUrl": "https://github.com/browserify/resolve/LICENSE"}, {"name": "resolve", "url": "https://github.com/substack/node-resolve", "version": "0.6.3", "license": "MIT", "licenseUrl": "https://github.com/substack/node-resolve/LICENSE"}, {"name": "resolve-from", "url": "https://github.com/sindresorhus/resolve-from", "version": "4.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/resolve-from/license"}, {"name": "resolve-from", "url": "https://github.com/sindresorhus/resolve-from", "version": "5.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/resolve-from/license"}, {"name": "resolve-from", "url": "https://github.com/sindresorhus/resolve-from", "version": "3.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/resolve-from/license"}, {"name": "resolve-url-loader", "url": "https://github.com/bholloway/resolve-url-loader", "version": "5.0.0", "license": "MIT", "licenseUrl": "https://github.com/bholloway/resolve-url-loader/LICENSE"}, {"name": "restore-cursor", "url": "https://github.com/sindresorhus/restore-cursor", "version": "3.1.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/restore-cursor/license"}, {"name": "retry", "url": "https://github.com/tim-kos/node-retry", "version": "0.13.1", "license": "MIT", "licenseUrl": "https://github.com/tim-kos/node-retry/License"}, {"name": "retry", "url": "https://github.com/tim-kos/node-retry", "version": "0.12.0", "license": "MIT", "licenseUrl": "https://github.com/tim-kos/node-retry/License"}, {"name": "reusify", "url": "https://github.com/mcollina/reusify", "version": "1.0.4", "license": "MIT", "licenseUrl": "https://github.com/mcollina/reusify/LICENSE"}, {"name": "rfdc", "url": "https://github.com/davidmarkclements/rfdc", "version": "1.3.0", "license": "MIT", "licenseUrl": "https://github.com/davidmarkclements/rfdc/LICENSE"}, {"name": "rgb-regex", "url": "https://github.com/regexps/rgb-regex", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/regexps/rgb-regex/LICENSE.md"}, {"name": "rgba-regex", "url": "https://github.com/johnotander/rgba-regex", "version": "1.0.0", "license": "MIT", "licenseUrl": "https://github.com/johnotander/rgba-regex/LICENSE.md"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/isaacs/rimraf", "version": "3.0.2", "license": "ISC", "licenseUrl": "https://github.com/isaacs/rimraf/LICENSE"}, {"name": "rjsf/core", "url": "https://github.com/rjsf-team/react-jsonschema-form", "version": "5.18.3", "license": "Apache-2.0", "licenseUrl": "https://github.com/rjsf-team/react-jsonschema-form/LICENSE.md"}, {"name": "rjsf/core", "url": "https://github.com/rjsf-team/react-jsonschema-form", "version": "3.2.1", "license": "Apache-2.0", "licenseUrl": "https://github.com/rjsf-team/react-jsonschema-form/LICENSE.md"}, {"name": "rjsf/utils", "url": "https://github.com/rjsf-team/react-jsonschema-form", "version": "5.18.3", "license": "Apache-2.0", "licenseUrl": "https://github.com/rjsf-team/react-jsonschema-form/LICENSE.md"}, {"name": "robust-predicates", "url": "https://github.com/mourner/robust-predicates", "version": "3.0.2", "license": "Unlicense", "licenseUrl": "https://github.com/mourner/robust-predicates/LICENSE"}, {"name": "run-async", "url": "https://github.com/SBoudrias/run-async", "version": "2.4.1", "license": "MIT", "licenseUrl": "https://github.com/SBoudrias/run-async/LICENSE"}, {"name": "run-parallel", "url": "https://github.com/feross/run-parallel", "version": "1.2.0", "license": "MIT", "licenseUrl": "https://github.com/feross/run-parallel/LICENSE"}, {"name": "rw", "url": "https://github.com/mbostock/rw", "version": "1.3.3", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/mbostock/rw/LICENSE"}, {"name": "rxjs", "url": "https://github.com/reactivex/rxjs", "version": "7.5.5", "license": "Apache-2.0", "licenseUrl": "https://github.com/reactivex/rxjs/LICENSE.txt"}, {"name": "rxjs", "url": "https://github.com/reactivex/rxjs", "version": "6.6.7", "license": "Apache-2.0", "licenseUrl": "https://github.com/reactivex/rxjs/LICENSE.txt"}, {"name": "rxjs-for-await", "url": "https://github.com/benlesh/rxjs-for-await", "version": "0.0.2", "license": "MIT", "licenseUrl": "https://github.com/benlesh/rxjs-for-await/LICENSE"}, {"name": "sa-jdwp", "url": "https://github.com/JetBrains/jdk-sa-jdwp", "version": "1.24", "license": "GPL 2.0 + Classpath", "licenseUrl": "https://github.com/JetBrains/jdk-sa-jdwp/raw/master/LICENSE.txt"}, {"name": "sade", "url": "https://github.com/lukeed/sade", "version": "1.8.1", "license": "MIT", "licenseUrl": "https://github.com/lukeed/sade/license"}, {"name": "safe-buffer", "url": "https://github.com/feross/safe-buffer", "version": "5.1.2", "license": "MIT", "licenseUrl": "https://github.com/feross/safe-buffer/LICENSE"}, {"name": "safe-buffer", "url": "https://github.com/feross/safe-buffer", "version": "5.2.1", "license": "MIT", "licenseUrl": "https://github.com/feross/safe-buffer/LICENSE"}, {"name": "safer-buffer", "url": "https://github.com/ChALkeR/safer-buffer", "version": "2.1.2", "license": "MIT", "licenseUrl": "https://github.com/ChALkeR/safer-buffer/LICENSE"}, {"name": "sanitize-html", "url": "https://github.com/apostrophecms/sanitize-html", "version": "2.7.3", "license": "MIT", "licenseUrl": "https://github.com/apostrophecms/sanitize-html/LICENSE"}, {"name": "sass", "url": "https://github.com/sass/dart-sass", "version": "1.49.9", "license": "MIT", "licenseUrl": "https://github.com/sass/dart-sass/LICENSE"}, {"name": "sass-loader", "url": "https://github.com/webpack-contrib/sass-loader", "version": "12.4.0", "license": "MIT", "licenseUrl": "https://github.com/webpack-contrib/sass-loader/LICENSE"}, {"name": "sax", "url": "https://github.com/isaacs/sax-js", "version": "1.2.4", "license": "ISC", "licenseUrl": "https://github.com/isaacs/sax-js/LICENSE"}, {"name": "scheduler", "url": "https://github.com/facebook/react", "version": "0.20.2", "license": "MIT", "licenseUrl": "https://github.com/facebook/react/LICENSE"}, {"name": "scheduler", "url": "https://github.com/facebook/react", "version": "0.23.2", "license": "MIT", "licenseUrl": "https://github.com/facebook/react/LICENSE"}, {"name": "schema-utils", "url": "https://github.com/webpack/schema-utils", "version": "2.7.1", "license": "MIT", "licenseUrl": "https://github.com/webpack/schema-utils/LICENSE"}, {"name": "schema-utils", "url": "https://github.com/webpack/schema-utils", "version": "3.1.1", "license": "MIT", "licenseUrl": "https://github.com/webpack/schema-utils/LICENSE"}, {"name": "schema-utils", "url": "https://github.com/webpack/schema-utils", "version": "4.0.0", "license": "MIT", "licenseUrl": "https://github.com/webpack/schema-utils/LICENSE"}, {"name": "schematics/angular", "url": "https://github.com/angular/angular-cli", "version": "13.3.8", "license": "MIT", "licenseUrl": "https://github.com/angular/angular-cli/LICENSE"}, {"name": "select-hose", "url": "https://github.com/indutny/select-hose", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/indutny/select-hose/README.md"}, {"name": "selfsigned", "url": "https://github.com/jfromaniello/selfsigned", "version": "2.0.1", "license": "MIT", "licenseUrl": "https://github.com/jfromaniello/selfsigned/LICENSE"}, {"name": "semver", "url": "https://github.com/npm/node-semver", "version": "7.3.5", "license": "ISC", "licenseUrl": "https://github.com/npm/node-semver/LICENSE"}, {"name": "semver", "url": "https://github.com/npm/node-semver", "version": "7.3.4", "license": "ISC", "licenseUrl": "https://github.com/npm/node-semver/LICENSE"}, {"name": "semver", "url": "https://github.com/npm/node-semver", "version": "7.0.0", "license": "ISC", "licenseUrl": "https://github.com/npm/node-semver/LICENSE"}, {"name": "semver", "url": "https://github.com/npm/node-semver", "version": "5.7.1", "license": "ISC", "licenseUrl": "https://github.com/npm/node-semver/LICENSE"}, {"name": "semver", "url": "https://github.com/npm/node-semver", "version": "6.3.0", "license": "ISC", "licenseUrl": "https://github.com/npm/node-semver/LICENSE"}, {"name": "semver", "url": "https://github.com/npm/node-semver", "version": "7.5.4", "license": "ISC", "licenseUrl": "https://github.com/npm/node-semver/LICENSE"}, {"name": "send", "url": "https://github.com/pillarjs/send", "version": "0.18.0", "license": "MIT", "licenseUrl": "https://github.com/pillarjs/send/LICENSE"}, {"name": "serialize-javascript", "url": "https://github.com/yahoo/serialize-javascript", "version": "6.0.0", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/yahoo/serialize-javascript/LICENSE"}, {"name": "serve-index", "url": "https://github.com/expressjs/serve-index", "version": "1.9.1", "license": "MIT", "licenseUrl": "https://github.com/expressjs/serve-index/LICENSE"}, {"name": "serve-static", "url": "https://github.com/expressjs/serve-static", "version": "1.15.0", "license": "MIT", "licenseUrl": "https://github.com/expressjs/serve-static/LICENSE"}, {"name": "set-blocking", "url": "https://github.com/yargs/set-blocking", "version": "2.0.0", "license": "ISC", "licenseUrl": "https://github.com/yargs/set-blocking/LICENSE.txt"}, {"name": "set-function-length", "url": "https://github.com/ljharb/set-function-length", "version": "1.2.2", "license": "MIT", "licenseUrl": "https://github.com/ljharb/set-function-length/LICENSE"}, {"name": "setprot<PERSON>of", "url": "https://github.com/wesleytodd/setprototypeof", "version": "1.2.0", "license": "ISC", "licenseUrl": "https://github.com/wesleytodd/setprototypeof/LICENSE"}, {"name": "setprot<PERSON>of", "url": "https://github.com/wesleytodd/setprototypeof", "version": "1.1.0", "license": "ISC", "licenseUrl": "https://github.com/wesleytodd/setprototypeof/LICENSE"}, {"name": "shallow-clone", "url": "https://github.com/jonschlinkert/shallow-clone", "version": "3.0.1", "license": "MIT", "licenseUrl": "https://github.com/jonschlinkert/shallow-clone/LICENSE"}, {"name": "shallow-copy", "url": "https://github.com/substack/shallow-copy", "version": "0.0.1", "license": "MIT", "licenseUrl": "https://github.com/substack/shallow-copy/LICENSE"}, {"name": "shebang-command", "url": "https://github.com/kevva/shebang-command", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/kevva/shebang-command/license"}, {"name": "shebang-regex", "url": "https://github.com/sindresorhus/shebang-regex", "version": "3.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/shebang-regex/license"}, {"name": "side-channel", "url": "https://github.com/ljharb/side-channel", "version": "1.0.6", "license": "MIT", "licenseUrl": "https://github.com/ljharb/side-channel/LICENSE"}, {"name": "signal-exit", "url": "https://github.com/tapjs/signal-exit", "version": "3.0.7", "license": "ISC", "licenseUrl": "https://github.com/tapjs/signal-exit/LICENSE.txt"}, {"name": "simple-swizzle", "url": "https://github.com/qix-/node-simple-swizzle", "version": "0.2.2", "license": "MIT", "licenseUrl": "https://github.com/qix-/node-simple-swizzle/LICENSE"}, {"name": "sinclair/typebox", "url": "https://github.com/sinclairzx81/typebox", "version": "0.23.5", "license": "MIT", "licenseUrl": "https://github.com/sinclairzx81/typebox/license"}, {"name": "slash", "url": "https://github.com/sindresorhus/slash", "version": "4.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/slash/license"}, {"name": "slash", "url": "https://github.com/sindresorhus/slash", "version": "3.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/slash/license"}, {"name": "slf4j-api", "url": "https://slf4j.org/", "version": "2.0.13", "license": "MIT", "licenseUrl": "https://www.slf4j.org/license.html"}, {"name": "slf4j-jdk14", "url": "https://slf4j.org/", "version": "2.0.13", "license": "MIT", "licenseUrl": "https://www.slf4j.org/license.html"}, {"name": "slide", "url": "https://github.com/isaacs/slide-flow-control", "version": "1.1.6", "license": "ISC", "licenseUrl": "https://github.com/isaacs/slide-flow-control/LICENSE"}, {"name": "smart-buffer", "url": "https://github.com/JoshGlazebrook/smart-buffer", "version": "4.2.0", "license": "MIT", "licenseUrl": "https://github.com/JoshGlazebrook/smart-buffer/LICENSE"}, {"name": "snakeyaml-engine", "url": "https://bitbucket.org/snakeyaml/snakeyaml-engine/", "version": "2.9", "license": "Apache 2.0", "licenseUrl": "https://bitbucket.org/snakeyaml/snakeyaml-engine/src/master/LICENSE.txt"}, {"name": "socket.io", "url": "https://github.com/socketio/socket.io", "version": "4.5.1", "license": "MIT", "licenseUrl": "https://github.com/socketio/socket.io/LICENSE"}, {"name": "socket.io-adapter", "url": "https://github.com/socketio/socket.io-adapter", "version": "2.4.0", "license": "MIT", "licenseUrl": "https://github.com/socketio/socket.io-adapter/LICENSE"}, {"name": "socket.io-parser", "url": "https://github.com/socketio/socket.io-parser", "version": "4.0.4", "license": "MIT", "licenseUrl": "https://github.com/socketio/socket.io-parser/LICENSE"}, {"name": "sockjs", "url": "https://github.com/sockjs/sockjs-node", "version": "0.3.24", "license": "MIT", "licenseUrl": "https://github.com/sockjs/sockjs-node/LICENSE"}, {"name": "socks", "url": "https://github.com/Josh<PERSON>lazebrook/socks", "version": "2.6.2", "license": "MIT", "licenseUrl": "https://github.com/JoshGlazebrook/socks/LICENSE"}, {"name": "socks-proxy-agent", "url": "https://github.com/TooTallNate/node-socks-proxy-agent", "version": "6.2.1", "license": "MIT", "licenseUrl": "https://github.com/TooTallNate/node-socks-proxy-agent/README.md"}, {"name": "socks-proxy-agent", "url": "https://github.com/TooTallNate/node-socks-proxy-agent", "version": "7.0.0", "license": "MIT", "licenseUrl": "https://github.com/TooTallNate/node-socks-proxy-agent/README.md"}, {"name": "source-map", "url": "https://github.com/mozilla/source-map", "version": "0.7.3", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/mozilla/source-map/LICENSE"}, {"name": "source-map", "url": "https://github.com/mozilla/source-map", "version": "0.5.7", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/mozilla/source-map/LICENSE"}, {"name": "source-map", "url": "https://github.com/mozilla/source-map", "version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/mozilla/source-map/LICENSE"}, {"name": "source-map-js", "url": "https://github.com/7rulnik/source-map-js", "version": "1.0.2", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/7rulnik/source-map-js/LICENSE"}, {"name": "source-map-loader", "url": "https://github.com/webpack-contrib/source-map-loader", "version": "3.0.1", "license": "MIT", "licenseUrl": "https://github.com/webpack-contrib/source-map-loader/LICENSE"}, {"name": "source-map-resolve", "url": "https://github.com/lydell/source-map-resolve", "version": "0.6.0", "license": "MIT", "licenseUrl": "https://github.com/lydell/source-map-resolve/LICENSE"}, {"name": "source-map-support", "url": "https://github.com/evanw/node-source-map-support", "version": "0.5.21", "license": "MIT", "licenseUrl": "https://github.com/evanw/node-source-map-support/LICENSE.md"}, {"name": "sourcemap-codec", "url": "https://github.com/<PERSON>-<PERSON>/sourcemap-codec", "version": "1.4.8", "license": "MIT", "licenseUrl": "https://github.com/<PERSON>-<PERSON>/sourcemap-codec/LICENSE"}, {"name": "spdx-compare", "url": "https://github.com/kemitchell/spdx-compare.js", "version": "1.0.0", "license": "MIT", "licenseUrl": "https://github.com/kemitchell/spdx-compare.js/LICENSE.md"}, {"name": "spdx-correct", "url": "https://github.com/jslicense/spdx-correct.js", "version": "3.1.1", "license": "Apache-2.0", "licenseUrl": "https://github.com/jslicense/spdx-correct.js/LICENSE"}, {"name": "spdx-exceptions", "url": "https://github.com/kemitchell/spdx-exceptions.json", "version": "2.3.0", "license": "CC-BY-3.0", "licenseUrl": "https://github.com/kemitchell/spdx-exceptions.json/README.md"}, {"name": "spdx-expression-parse", "url": "https://github.com/jslicense/spdx-expression-parse.js", "version": "3.0.1", "license": "MIT", "licenseUrl": "https://github.com/jslicense/spdx-expression-parse.js/LICENSE"}, {"name": "spdx-license-ids", "url": "https://github.com/jslicense/spdx-license-ids", "version": "3.0.11", "license": "CC0-1.0", "licenseUrl": "https://github.com/jslicense/spdx-license-ids/README.md"}, {"name": "spdx-ranges", "url": "https://github.com/kemitchell/spdx-ranges.js", "version": "2.1.1", "license": "(MIT AND CC-BY-3.0)", "licenseUrl": "https://github.com/kemitchell/spdx-ranges.js/LICENSE.md"}, {"name": "spdx-satisfies", "url": "https://github.com/kemitchell/spdx-satisfies.js", "version": "4.0.1", "license": "MIT", "licenseUrl": "https://github.com/kemitchell/spdx-satisfies.js/LICENSE"}, {"name": "spdy", "url": "https://github.com/indutny/node-spdy", "version": "4.0.2", "license": "MIT", "licenseUrl": "https://github.com/indutny/node-spdy/README.md"}, {"name": "spdy-transport", "url": "https://github.com/spdy-http2/spdy-transport", "version": "3.0.0", "license": "MIT", "licenseUrl": "https://github.com/spdy-http2/spdy-transport/README.md"}, {"name": "speech-rule-engine", "url": "https://github.com/zorkow/speech-rule-engine", "version": "4.0.7", "license": "Apache-2.0", "licenseUrl": "https://github.com/zorkow/speech-rule-engine/LICENSE"}, {"name": "sprintf-js", "url": "https://github.com/alexei/sprintf.js", "version": "1.0.3", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/alexei/sprintf.js/LICENSE"}, {"name": "ssri", "url": "https://github.com/npm/ssri", "version": "8.0.1", "license": "ISC", "licenseUrl": "https://github.com/npm/ssri/LICENSE.md"}, {"name": "ssri", "url": "https://github.com/npm/ssri", "version": "9.0.1", "license": "ISC", "licenseUrl": "https://github.com/npm/ssri/LICENSE.md"}, {"name": "stable", "url": "https://github.com/Two-Screen/stable", "version": "0.1.8", "license": "MIT", "licenseUrl": "https://github.com/Two-Screen/stable/README.md"}, {"name": "stack-trace", "url": "https://github.com/felixge/node-stack-trace", "version": "0.0.9", "license": "MIT*", "licenseUrl": "https://github.com/felixge/node-stack-trace/License"}, {"name": "stack-utils", "url": "https://github.com/tapjs/stack-utils", "version": "2.0.5", "license": "MIT", "licenseUrl": "https://github.com/tapjs/stack-utils/license"}, {"name": "static-eval", "url": "https://github.com/browserify/static-eval", "version": "2.1.0", "license": "MIT", "licenseUrl": "https://github.com/browserify/static-eval/LICENSE"}, {"name": "statuses", "url": "https://github.com/jshttp/statuses", "version": "1.5.0", "license": "MIT", "licenseUrl": "https://github.com/jshttp/statuses/LICENSE"}, {"name": "statuses", "url": "https://github.com/jshttp/statuses", "version": "2.0.1", "license": "MIT", "licenseUrl": "https://github.com/jshttp/statuses/LICENSE"}, {"name": "stream-shift", "url": "https://github.com/mafintosh/stream-shift", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/mafintosh/stream-shift/LICENSE"}, {"name": "streamroller", "url": "https://github.com/nomiddlename/streamroller", "version": "3.1.1", "license": "MIT", "licenseUrl": "https://github.com/nomiddlename/streamroller/LICENSE"}, {"name": "string-width", "url": "https://github.com/sindresorhus/string-width", "version": "4.2.3", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/string-width/license"}, {"name": "string.prototype.trimend", "url": "https://github.com/es-shims/String.prototype.trimEnd", "version": "1.0.5", "license": "MIT", "licenseUrl": "https://github.com/es-shims/String.prototype.trimEnd/LICENSE"}, {"name": "string.prototype.trimstart", "url": "https://github.com/es-shims/String.prototype.trimStart", "version": "1.0.5", "license": "MIT", "licenseUrl": "https://github.com/es-shims/String.prototype.trimStart/LICENSE"}, {"name": "string_decoder", "url": "https://github.com/rvagg/string_decoder", "version": "0.10.31", "license": "MIT", "licenseUrl": "https://github.com/rvagg/string_decoder/LICENSE"}, {"name": "string_decoder", "url": "https://github.com/nodejs/string_decoder", "version": "1.1.1", "license": "MIT", "licenseUrl": "https://github.com/nodejs/string_decoder/LICENSE"}, {"name": "strip-ansi", "url": "https://github.com/chalk/strip-ansi", "version": "7.0.1", "license": "MIT", "licenseUrl": "https://github.com/chalk/strip-ansi/license"}, {"name": "strip-ansi", "url": "https://github.com/chalk/strip-ansi", "version": "6.0.1", "license": "MIT", "licenseUrl": "https://github.com/chalk/strip-ansi/license"}, {"name": "strip-bom", "url": "https://github.com/sindresorhus/strip-bom", "version": "3.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/strip-bom/license"}, {"name": "strip-final-newline", "url": "https://github.com/sindresorhus/strip-final-newline", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/strip-final-newline/license"}, {"name": "strip-json-comments", "url": "https://github.com/sindresorhus/strip-json-comments", "version": "3.1.1", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/strip-json-comments/license"}, {"name": "style-mod", "url": "https://github.com/marijnh/style-mod", "version": "4.1.2", "license": "MIT", "licenseUrl": "https://github.com/marijnh/style-mod/LICENSE"}, {"name": "stylehacks", "url": "https://github.com/cssnano/cssnano", "version": "4.0.3", "license": "MIT", "licenseUrl": "https://github.com/cssnano/cssnano/LICENSE-MIT"}, {"name": "stylis", "url": "https://github.com/thysultan/stylis.js", "version": "4.3.2", "license": "MIT", "licenseUrl": "https://github.com/thysultan/stylis.js/LICENSE"}, {"name": "stylus", "url": "https://github.com/stylus/stylus", "version": "0.56.0", "license": "MIT", "licenseUrl": "https://github.com/stylus/stylus/LICENSE"}, {"name": "stylus-loader", "url": "https://github.com/webpack-contrib/stylus-loader", "version": "6.2.0", "license": "MIT", "licenseUrl": "https://github.com/webpack-contrib/stylus-loader/LICENSE"}, {"name": "supports-color", "url": "https://github.com/chalk/supports-color", "version": "7.2.0", "license": "MIT", "licenseUrl": "https://github.com/chalk/supports-color/license"}, {"name": "supports-color", "url": "https://github.com/chalk/supports-color", "version": "5.5.0", "license": "MIT", "licenseUrl": "https://github.com/chalk/supports-color/license"}, {"name": "supports-color", "url": "https://github.com/chalk/supports-color", "version": "8.1.1", "license": "MIT", "licenseUrl": "https://github.com/chalk/supports-color/license"}, {"name": "supports-preserve-symlinks-flag", "url": "https://github.com/inspect-js/node-supports-preserve-symlinks-flag", "version": "1.0.0", "license": "MIT", "licenseUrl": "https://github.com/inspect-js/node-supports-preserve-symlinks-flag/LICENSE"}, {"name": "svg-url-loader", "url": "https://github.com/bhovhannes/svg-url-loader", "version": "7.1.1", "license": "MIT", "licenseUrl": "https://github.com/bhovhannes/svg-url-loader/LICENSE"}, {"name": "svgo", "url": "https://github.com/svg/svgo", "version": "1.3.2", "license": "MIT", "licenseUrl": "https://github.com/svg/svgo/LICENSE"}, {"name": "swingx", "url": "https://central.sonatype.com/artifact/org.swinglabs/swingx-core/1.6.2-2", "version": "1.6.2-2", "license": "LGPL 2.1", "licenseUrl": "https://www.gnu.org/licenses/old-licenses/lgpl-2.1.en.html"}, {"name": "symbol-observable", "url": "https://github.com/blesh/symbol-observable", "version": "4.0.0", "license": "MIT", "licenseUrl": "https://github.com/blesh/symbol-observable/license"}, {"name": "tabbable", "url": "https://github.com/focus-trap/tabbable", "version": "5.3.3", "license": "MIT", "licenseUrl": "https://github.com/focus-trap/tabbable/LICENSE"}, {"name": "tapable", "url": "https://github.com/webpack/tapable", "version": "2.2.1", "license": "MIT", "licenseUrl": "https://github.com/webpack/tapable/LICENSE"}, {"name": "tar", "url": "https://github.com/npm/node-tar", "version": "6.1.11", "license": "ISC", "licenseUrl": "https://github.com/npm/node-tar/LICENSE"}, {"name": "tar-stream", "url": "https://github.com/mafintosh/tar-stream", "version": "2.2.0", "license": "MIT", "licenseUrl": "https://github.com/mafintosh/tar-stream/LICENSE"}, {"name": "terser", "url": "https://github.com/terser/terser", "version": "5.11.0", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/terser/terser/LICENSE"}, {"name": "terser-webpack-plugin", "url": "https://github.com/webpack-contrib/terser-webpack-plugin", "version": "5.3.3", "license": "MIT", "licenseUrl": "https://github.com/webpack-contrib/terser-webpack-plugin/LICENSE"}, {"name": "test-exclude", "url": "https://github.com/istanbuljs/test-exclude", "version": "6.0.0", "license": "ISC", "licenseUrl": "https://github.com/istanbuljs/test-exclude/LICENSE.txt"}, {"name": "text-table", "url": "https://github.com/substack/text-table", "version": "0.2.0", "license": "MIT", "licenseUrl": "https://github.com/substack/text-table/LICENSE"}, {"name": "through", "url": "https://github.com/dominictarr/through", "version": "2.3.8", "license": "MIT", "licenseUrl": "https://github.com/dominictarr/through/LICENSE.APACHE2"}, {"name": "through2", "url": "https://github.com/rvagg/through2", "version": "2.0.5", "license": "MIT", "licenseUrl": "https://github.com/rvagg/through2/LICENSE.md"}, {"name": "through2", "url": "https://github.com/rvagg/through2", "version": "0.6.5", "license": "MIT", "licenseUrl": "https://github.com/rvagg/through2/LICENSE"}, {"name": "thunky", "url": "https://github.com/mafintosh/thunky", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/mafintosh/thunky/LICENSE"}, {"name": "timsort", "url": "https://github.com/mziccard/node-timsort", "version": "0.3.0", "license": "MIT", "licenseUrl": "https://github.com/mziccard/node-timsort/LICENSE.md"}, {"name": "tmp", "url": "https://github.com/raszi/node-tmp", "version": "0.0.33", "license": "MIT", "licenseUrl": "https://github.com/raszi/node-tmp/LICENSE"}, {"name": "tmp", "url": "https://github.com/raszi/node-tmp", "version": "0.2.1", "license": "MIT", "licenseUrl": "https://github.com/raszi/node-tmp/LICENSE"}, {"name": "to-fast-properties", "url": "https://github.com/sindresorhus/to-fast-properties", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/to-fast-properties/license"}, {"name": "to-regex-range", "url": "https://github.com/micromatch/to-regex-range", "version": "5.0.1", "license": "MIT", "licenseUrl": "https://github.com/micromatch/to-regex-range/LICENSE"}, {"name": "toidentifier", "url": "https://github.com/component/toidentifier", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/component/toidentifier/LICENSE"}, {"name": "tootallnate/once", "url": "https://github.com/TooTallNate/once", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/TooTallNate/once/LICENSE"}, {"name": "tootallnate/once", "url": "https://github.com/TooTallNate/once", "version": "1.1.2", "license": "MIT", "licenseUrl": "https://npmjs.org/package/@tootallnate/once@1.1.2"}, {"name": "tr46", "url": "https://github.com/Sebmaster/tr46.js", "version": "0.0.3", "license": "MIT", "licenseUrl": "https://npmjs.org/package/tr46@0.0.3"}, {"name": "tree-kill", "url": "https://github.com/pkrumins/node-tree-kill", "version": "1.2.2", "license": "MIT", "licenseUrl": "https://github.com/pkrumins/node-tree-kill/LICENSE"}, {"name": "treeify", "url": "https://github.com/notatestuser/treeify", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/notatestuser/treeify/LICENSE"}, {"name": "ts-dedent", "url": "https://github.com/tamino-martinius/node-ts-dedent", "version": "2.2.0", "license": "MIT", "licenseUrl": "https://github.com/tamino-martinius/node-ts-dedent/LICENSE"}, {"name": "ts-node", "url": "https://github.com/TypeStrong/ts-node", "version": "10.8.2", "license": "MIT", "licenseUrl": "https://github.com/TypeStrong/ts-node/LICENSE"}, {"name": "tsconfig-paths", "url": "https://github.com/dividab/tsconfig-paths", "version": "3.14.1", "license": "MIT", "licenseUrl": "https://github.com/dividab/tsconfig-paths/LICENSE"}, {"name": "tsconfig/node10", "url": "https://github.com/tsconfig/bases", "version": "1.0.9", "license": "MIT", "licenseUrl": "https://github.com/tsconfig/bases/LICENSE"}, {"name": "tsconfig/node12", "url": "https://github.com/tsconfig/bases", "version": "1.0.11", "license": "MIT", "licenseUrl": "https://github.com/tsconfig/bases/LICENSE"}, {"name": "tsconfig/node14", "url": "https://github.com/tsconfig/bases", "version": "1.0.3", "license": "MIT", "licenseUrl": "https://github.com/tsconfig/bases/LICENSE"}, {"name": "tsconfig/node16", "url": "https://github.com/tsconfig/bases", "version": "1.0.3", "license": "MIT", "licenseUrl": "https://github.com/tsconfig/bases/LICENSE"}, {"name": "tslib", "url": "https://github.com/Microsoft/tslib", "version": "1.14.1", "license": "0BSD", "licenseUrl": "https://github.com/Microsoft/tslib/LICENSE.txt"}, {"name": "tslib", "url": "https://github.com/Microsoft/tslib", "version": "2.3.1", "license": "0BSD", "licenseUrl": "https://github.com/Microsoft/tslib/LICENSE.txt"}, {"name": "tsu<PERSON>s", "url": "https://github.com/ajafff/tsutils", "version": "3.21.0", "license": "MIT", "licenseUrl": "https://github.com/ajafff/tsutils/LICENSE"}, {"name": "type-check", "url": "https://github.com/gkz/type-check", "version": "0.3.2", "license": "MIT", "licenseUrl": "https://github.com/gkz/type-check/LICENSE"}, {"name": "type-check", "url": "https://github.com/gkz/type-check", "version": "0.4.0", "license": "MIT", "licenseUrl": "https://github.com/gkz/type-check/LICENSE"}, {"name": "type-detect", "url": "https://github.com/chaijs/type-detect", "version": "4.0.8", "license": "MIT", "licenseUrl": "https://github.com/chaijs/type-detect/LICENSE"}, {"name": "type-fest", "url": "https://github.com/sindresorhus/type-fest", "version": "0.21.3", "license": "(MIT OR CC0-1.0)", "licenseUrl": "https://github.com/sindresorhus/type-fest/license"}, {"name": "type-fest", "url": "https://github.com/sindresorhus/type-fest", "version": "0.20.2", "license": "(MIT OR CC0-1.0)", "licenseUrl": "https://github.com/sindresorhus/type-fest/license"}, {"name": "type-is", "url": "https://github.com/jshttp/type-is", "version": "1.6.18", "license": "MIT", "licenseUrl": "https://github.com/jshttp/type-is/LICENSE"}, {"name": "typed-assert", "url": "https://github.com/elier<PERSON>nberg/typed-assert", "version": "1.0.9", "license": "MIT", "licenseUrl": "https://github.com/elierotenberg/typed-assert/README.md"}, {"name": "typedarray", "url": "https://github.com/substack/typedarray", "version": "0.0.6", "license": "MIT", "licenseUrl": "https://github.com/substack/typedarray/LICENSE"}, {"name": "types/backbone", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "1.4.14", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/body-parser", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "1.19.2", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/bonjour", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "3.5.10", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/chai", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "4.3.11", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/component-emitter", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "1.2.11", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/connect", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "3.4.35", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/connect-history-api-fallback", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "1.3.5", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/cookie", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "0.4.1", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/cors", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "2.8.12", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/d3-scale", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "4.0.8", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/d3-scale-chromatic", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "3.0.3", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/d3-time", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "3.0.3", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/debug", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "4.1.12", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/dom4", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "2.0.2", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/eslint", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "8.4.3", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/eslint-scope", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "3.7.3", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/estree", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "0.0.51", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/expect", "url": "https://github.com/facebook/jest", "version": "24.3.0", "license": "MIT", "licenseUrl": "https://github.com/facebook/jest/LICENSE"}, {"name": "types/express", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "4.17.13", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/express-serve-static-core", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "4.17.29", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/http-proxy", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "1.17.9", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/istanbul-lib-coverage", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "2.0.4", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/istanbul-lib-report", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "3.0.0", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/istanbul-reports", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "3.0.1", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/jasmine", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "3.6.11", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/jquery", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "3.5.29", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/json-schema", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "7.0.11", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/json5", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "0.0.29", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/README.md"}, {"name": "types/karma", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "6.3.3", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/lodash", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "4.14.182", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/mdast", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "3.0.15", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/mime", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "1.3.2", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/mocha", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "9.1.1", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/ms", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "0.7.34", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/node", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "12.20.55", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/parse-json", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "4.0.0", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/plotly.js", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "2.12.32", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/prop-types", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "15.7.12", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/q", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "1.5.5", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/qs", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "6.9.7", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/range-parser", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "1.2.4", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/react", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "18.3.1", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/react", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "17.0.80", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/retry", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "0.12.0", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/scheduler", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "0.16.8", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/semver", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "7.5.6", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/serve-index", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "1.9.1", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/serve-static", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "1.13.10", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/sizzle", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "2.3.8", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/sockjs", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "0.3.33", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/stack-utils", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "2.0.1", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/underscore", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "1.11.15", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/unist", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "2.0.10", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/uuid", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "8.3.4", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/ws", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "7.4.7", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/ws", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "8.5.3", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/yargs", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "17.0.10", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "types/yargs-parser", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped", "version": "21.0.0", "license": "MIT", "licenseUrl": "https://github.com/DefinitelyTyped/DefinitelyTyped/LICENSE"}, {"name": "typescript", "url": "https://github.com/Microsoft/TypeScript", "version": "4.6.4", "license": "Apache-2.0", "licenseUrl": "https://github.com/Microsoft/TypeScript/LICENSE.txt"}, {"name": "typescript-eslint/eslint-plugin", "url": "https://github.com/typescript-eslint/typescript-eslint", "version": "5.27.1", "license": "MIT", "licenseUrl": "https://github.com/typescript-eslint/typescript-eslint/LICENSE"}, {"name": "typescript-eslint/experimental-utils", "url": "https://github.com/typescript-eslint/typescript-eslint", "version": "5.27.1", "license": "MIT", "licenseUrl": "https://github.com/typescript-eslint/typescript-eslint/LICENSE"}, {"name": "typescript-eslint/parser", "url": "https://github.com/typescript-eslint/typescript-eslint", "version": "5.27.1", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/typescript-eslint/typescript-eslint/LICENSE"}, {"name": "typescript-eslint/scope-manager", "url": "https://github.com/typescript-eslint/typescript-eslint", "version": "5.27.1", "license": "MIT", "licenseUrl": "https://github.com/typescript-eslint/typescript-eslint/LICENSE"}, {"name": "typescript-eslint/type-utils", "url": "https://github.com/typescript-eslint/typescript-eslint", "version": "5.27.1", "license": "MIT", "licenseUrl": "https://github.com/typescript-eslint/typescript-eslint/LICENSE"}, {"name": "typescript-eslint/types", "url": "https://github.com/typescript-eslint/typescript-eslint", "version": "5.27.1", "license": "MIT", "licenseUrl": "https://github.com/typescript-eslint/typescript-eslint/LICENSE"}, {"name": "typescript-eslint/typescript-estree", "url": "https://github.com/typescript-eslint/typescript-eslint", "version": "5.27.1", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/typescript-eslint/typescript-eslint/LICENSE"}, {"name": "typescript-eslint/utils", "url": "https://github.com/typescript-eslint/typescript-eslint", "version": "5.27.1", "license": "MIT", "licenseUrl": "https://github.com/typescript-eslint/typescript-eslint/LICENSE"}, {"name": "typescript-eslint/visitor-keys", "url": "https://github.com/typescript-eslint/typescript-eslint", "version": "5.27.1", "license": "MIT", "licenseUrl": "https://github.com/typescript-eslint/typescript-eslint/LICENSE"}, {"name": "typestyle", "url": "https://github.com/typestyle/typestyle", "version": "2.3.0", "license": "MIT", "licenseUrl": "https://github.com/typestyle/typestyle/LICENSE"}, {"name": "ua-parser-js", "url": "https://github.com/faisalman/ua-parser-js", "version": "0.7.31", "license": "MIT", "licenseUrl": "https://github.com/faisalman/ua-parser-js/license.md"}, {"name": "unbox-primitive", "url": "https://github.com/ljharb/unbox-primitive", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/ljharb/unbox-primitive/LICENSE"}, {"name": "underscore", "url": "https://github.com/jashkenas/underscore", "version": "1.13.6", "license": "MIT", "licenseUrl": "https://github.com/jashkenas/underscore/LICENSE"}, {"name": "ungap/promise-all-settled", "url": "https://github.com/ungap/promise-all-settled", "version": "1.1.2", "license": "ISC", "licenseUrl": "https://github.com/ungap/promise-all-settled/LICENSE"}, {"name": "unicode-canonical-property-names-ecmascript", "url": "https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript/LICENSE-MIT.txt"}, {"name": "unicode-canonical-property-names-ecmascript", "url": "https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript", "version": "1.0.4", "license": "MIT", "licenseUrl": "https://github.com/mathiasbynens/unicode-canonical-property-names-ecmascript/LICENSE-MIT.txt"}, {"name": "unicode-match-property-ecmascript", "url": "https://github.com/mathiasbynens/unicode-match-property-ecmascript", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/mathiasbynens/unicode-match-property-ecmascript/LICENSE-MIT.txt"}, {"name": "unicode-match-property-ecmascript", "url": "https://github.com/mathiasbynens/unicode-match-property-ecmascript", "version": "1.0.4", "license": "MIT", "licenseUrl": "https://github.com/mathiasbynens/unicode-match-property-ecmascript/LICENSE-MIT.txt"}, {"name": "unicode-match-property-value-ecmascript", "url": "https://github.com/mathiasbynens/unicode-match-property-value-ecmascript", "version": "1.2.0", "license": "MIT", "licenseUrl": "https://github.com/mathiasbynens/unicode-match-property-value-ecmascript/LICENSE-MIT.txt"}, {"name": "unicode-match-property-value-ecmascript", "url": "https://github.com/mathiasbynens/unicode-match-property-value-ecmascript", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/mathiasbynens/unicode-match-property-value-ecmascript/LICENSE-MIT.txt"}, {"name": "unicode-property-aliases-ecmascript", "url": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript", "version": "1.1.0", "license": "MIT", "licenseUrl": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript/LICENSE-MIT.txt"}, {"name": "unicode-property-aliases-ecmascript", "url": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/mathiasbynens/unicode-property-aliases-ecmascript/LICENSE-MIT.txt"}, {"name": "uniq", "url": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/uniq", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/mi<PERSON><PERSON><PERSON><PERSON>/uniq/LICENSE"}, {"name": "uniqs", "url": "https://github.com/fgnass/uniqs", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/fgnass/uniqs/README.md"}, {"name": "unique-filename", "url": "https://github.com/iarna/unique-filename", "version": "1.1.1", "license": "ISC", "licenseUrl": "https://github.com/iarna/unique-filename/LICENSE"}, {"name": "unique-slug", "url": "https://github.com/iarna/unique-slug", "version": "2.0.2", "license": "ISC", "licenseUrl": "https://github.com/iarna/unique-slug/LICENSE"}, {"name": "unist-util-stringify-position", "url": "https://github.com/syntax-tree/unist-util-stringify-position", "version": "3.0.3", "license": "MIT", "licenseUrl": "https://github.com/syntax-tree/unist-util-stringify-position/license"}, {"name": "unit-api", "url": "https://github.com/unitsofmeasurement/unit-api", "version": "1.0", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://github.com/unitsofmeasurement/unit-api/blob/master/LICENSE"}, {"name": "universalify", "url": "https://github.com/RyanZim/universalify", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/RyanZim/universalify/LICENSE"}, {"name": "unpipe", "url": "https://github.com/stream-utils/unpipe", "version": "1.0.0", "license": "MIT", "licenseUrl": "https://github.com/stream-utils/unpipe/LICENSE"}, {"name": "unquote", "url": "https://github.com/lakenen/node-unquote", "version": "1.1.1", "license": "MIT", "licenseUrl": "https://github.com/lakenen/node-unquote/LICENSE"}, {"name": "uom-lib-common", "url": "https://github.com/unitsofmeasurement/uom-lib", "version": "1.1", "license": "BSD 3-<PERSON><PERSON>", "licenseUrl": "https://github.com/unitsofmeasurement/uom-lib/blob/master/LICENSE"}, {"name": "update-browserslist-db", "url": "https://github.com/browserslist/update-db", "version": "1.0.4", "license": "MIT", "licenseUrl": "https://github.com/browserslist/update-db/LICENSE"}, {"name": "uri-js", "url": "https://github.com/garycourt/uri-js", "version": "4.4.1", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/garycourt/uri-js/LICENSE"}, {"name": "url", "url": "https://github.com/defunctzombie/node-url", "version": "0.11.3", "license": "MIT", "licenseUrl": "https://github.com/defunctzombie/node-url/LICENSE"}, {"name": "url-parse", "url": "https://github.com/unshiftio/url-parse", "version": "1.5.10", "license": "MIT", "licenseUrl": "https://github.com/unshiftio/url-parse/LICENSE"}, {"name": "util-deprecate", "url": "https://github.com/TooTallNate/util-deprecate", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/TooTallNate/util-deprecate/LICENSE"}, {"name": "util-extend", "url": "https://github.com/isaacs/util-extend", "version": "1.0.3", "license": "MIT", "licenseUrl": "https://github.com/isaacs/util-extend/LICENSE"}, {"name": "util.promisify", "url": "https://github.com/ljharb/util.promisify", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/ljharb/util.promisify/LICENSE"}, {"name": "utils-merge", "url": "https://github.com/jaredhanson/utils-merge", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/jaredhanson/utils-merge/LICENSE"}, {"name": "uuid", "url": "https://github.com/uuidjs/uuid", "version": "9.0.1", "license": "MIT", "licenseUrl": "https://github.com/uuidjs/uuid/LICENSE.md"}, {"name": "uuid", "url": "https://github.com/uuidjs/uuid", "version": "8.3.2", "license": "MIT", "licenseUrl": "https://github.com/uuidjs/uuid/LICENSE.md"}, {"name": "uvu", "url": "https://github.com/lukeed/uvu", "version": "0.5.6", "license": "MIT", "licenseUrl": "https://github.com/lukeed/uvu/license"}, {"name": "v8-compile-cache", "url": "https://github.com/zertosh/v8-compile-cache", "version": "2.3.0", "license": "MIT", "licenseUrl": "https://github.com/zertosh/v8-compile-cache/LICENSE"}, {"name": "v8-compile-cache-lib", "url": "https://github.com/cspotcode/v8-compile-cache-lib", "version": "3.0.1", "license": "MIT", "licenseUrl": "https://github.com/cspotcode/v8-compile-cache-lib/LICENSE"}, {"name": "validate-npm-package-license", "url": "https://github.com/kemitchell/validate-npm-package-license.js", "version": "3.0.4", "license": "Apache-2.0", "licenseUrl": "https://github.com/kemitchell/validate-npm-package-license.js/LICENSE"}, {"name": "validate-npm-package-name", "url": "https://github.com/npm/validate-npm-package-name", "version": "3.0.0", "license": "ISC", "licenseUrl": "https://github.com/npm/validate-npm-package-name/LICENSE"}, {"name": "validate.io-array", "url": "https://github.com/validate-io/array", "version": "1.0.6", "license": "MIT", "licenseUrl": "https://github.com/validate-io/array/LICENSE"}, {"name": "validate.io-function", "url": "https://github.com/validate-io/function", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/validate-io/function/LICENSE"}, {"name": "validate.io-integer", "url": "https://github.com/validate-io/integer", "version": "1.0.5", "license": "MIT", "licenseUrl": "https://github.com/validate-io/integer/LICENSE"}, {"name": "validate.io-integer-array", "url": "https://github.com/validate-io/integer-array", "version": "1.0.0", "license": "MIT", "licenseUrl": "https://github.com/validate-io/integer-array/LICENSE"}, {"name": "validate.io-number", "url": "https://github.com/validate-io/number", "version": "1.0.3", "license": "MIT", "licenseUrl": "https://github.com/validate-io/number/LICENSE"}, {"name": "vary", "url": "https://github.com/jshttp/vary", "version": "1.1.2", "license": "MIT", "licenseUrl": "https://github.com/jshttp/vary/LICENSE"}, {"name": "vavr", "url": "https://vavr.io/", "version": "0.10.4", "license": "MIT", "licenseUrl": "https://github.com/vavr-io/vavr/blob/master/LICENSE"}, {"name": "vendors", "url": "https://github.com/wooorm/vendors", "version": "1.0.4", "license": "MIT", "licenseUrl": "https://github.com/wooorm/vendors/license"}, {"name": "void-elements", "url": "https://github.com/hemanth/void-elements", "version": "2.0.1", "license": "MIT", "licenseUrl": "https://github.com/hemanth/void-elements/LICENSE"}, {"name": "vscode-jsonrpc", "url": "https://github.com/Microsoft/vscode-languageserver-node", "version": "8.2.0", "license": "MIT", "licenseUrl": "https://github.com/Microsoft/vscode-languageserver-node/License.txt"}, {"name": "vscode-jsonrpc", "url": "https://github.com/Microsoft/vscode-languageserver-node", "version": "6.0.0", "license": "MIT", "licenseUrl": "https://github.com/Microsoft/vscode-languageserver-node/License.txt"}, {"name": "vscode-jupyter", "url": "https://github.com/microsoft/vscode-jupyter", "version": "2023.10.**********", "license": "MIT", "licenseUrl": "https://github.com/microsoft/vscode-jupyter/blob/main/LICENSE"}, {"name": "vscode-jupyter-ipywidgets", "url": "https://github.com/Microsoft/vscode-jupyter-ipywidgets", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/microsoft/vscode-jupyter-ipywidgets/blob/main/LICENSE"}, {"name": "vscode-languageserver-protocol", "url": "https://github.com/Microsoft/vscode-languageserver-node", "version": "3.17.5", "license": "MIT", "licenseUrl": "https://github.com/Microsoft/vscode-languageserver-node/License.txt"}, {"name": "vscode-languageserver-types", "url": "https://github.com/Microsoft/vscode-languageserver-node", "version": "3.17.5", "license": "MIT", "licenseUrl": "https://github.com/Microsoft/vscode-languageserver-node/License.txt"}, {"name": "vscode-ws-jsonrpc", "url": "https://github.com/TypeFox/monaco-languageclient", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/TypeFox/monaco-languageclient/License.txt"}, {"name": "w3c-keyname", "url": "https://github.com/marijnh/w3c-keyname", "version": "2.2.8", "license": "MIT", "licenseUrl": "https://github.com/marijnh/w3c-keyname/LICENSE"}, {"name": "warning", "url": "https://github.com/BerkeleyTrue/warning", "version": "4.0.3", "license": "MIT", "licenseUrl": "https://github.com/BerkeleyTrue/warning/LICENSE.md"}, {"name": "watchpack", "url": "https://github.com/webpack/watchpack", "version": "2.4.0", "license": "MIT", "licenseUrl": "https://github.com/webpack/watchpack/LICENSE"}, {"name": "wbuf", "url": "https://github.com/indutny/wbuf", "version": "1.7.3", "license": "MIT", "licenseUrl": "https://github.com/indutny/wbuf/README.md"}, {"name": "wcwidth", "url": "https://github.com/timoxley/wcwidth", "version": "1.0.1", "license": "MIT", "licenseUrl": "https://github.com/timoxley/wcwidth/LICENSE"}, {"name": "web-worker", "url": "https://github.com/developit/web-worker", "version": "1.3.0", "license": "Apache-2.0", "licenseUrl": "https://github.com/developit/web-worker/LICENSE"}, {"name": "webassemblyjs/ast", "url": "https://github.com/xtuc/webassemblyjs", "version": "1.11.1", "license": "MIT", "licenseUrl": "https://github.com/xtuc/webassemblyjs/LICENSE"}, {"name": "webassemblyjs/floating-point-hex-parser", "url": "https://github.com/xtuc/webassemblyjs", "version": "1.11.1", "license": "MIT", "licenseUrl": "https://github.com/xtuc/webassemblyjs/LICENSE"}, {"name": "webassemblyjs/helper-api-error", "url": "https://github.com/xtuc/webassemblyjs", "version": "1.11.1", "license": "MIT", "licenseUrl": "https://github.com/xtuc/webassemblyjs/LICENSE"}, {"name": "webassemblyjs/helper-buffer", "url": "https://github.com/xtuc/webassemblyjs", "version": "1.11.1", "license": "MIT", "licenseUrl": "https://github.com/xtuc/webassemblyjs/LICENSE"}, {"name": "webassemblyjs/helper-numbers", "url": "https://github.com/xtuc/webassemblyjs", "version": "1.11.1", "license": "MIT", "licenseUrl": "https://github.com/xtuc/webassemblyjs/LICENSE"}, {"name": "webassemblyjs/helper-wasm-bytecode", "url": "https://github.com/xtuc/webassemblyjs", "version": "1.11.1", "license": "MIT", "licenseUrl": "https://github.com/xtuc/webassemblyjs/LICENSE"}, {"name": "webassemblyjs/helper-wasm-section", "url": "https://github.com/xtuc/webassemblyjs", "version": "1.11.1", "license": "MIT", "licenseUrl": "https://github.com/xtuc/webassemblyjs/LICENSE"}, {"name": "webassemblyjs/ieee754", "url": "https://github.com/xtuc/webassemblyjs", "version": "1.11.1", "license": "MIT", "licenseUrl": "https://github.com/xtuc/webassemblyjs/LICENSE"}, {"name": "webassemblyjs/leb128", "url": "https://github.com/xtuc/webassemblyjs", "version": "1.11.1", "license": "Apache-2.0", "licenseUrl": "https://github.com/xtuc/webassemblyjs/LICENSE.txt"}, {"name": "webassemblyjs/utf8", "url": "https://github.com/xtuc/webassemblyjs", "version": "1.11.1", "license": "MIT", "licenseUrl": "https://github.com/xtuc/webassemblyjs/LICENSE"}, {"name": "webassemblyjs/wasm-edit", "url": "https://github.com/xtuc/webassemblyjs", "version": "1.11.1", "license": "MIT", "licenseUrl": "https://github.com/xtuc/webassemblyjs/LICENSE"}, {"name": "webassemblyjs/wasm-gen", "url": "https://github.com/xtuc/webassemblyjs", "version": "1.11.1", "license": "MIT", "licenseUrl": "https://github.com/xtuc/webassemblyjs/LICENSE"}, {"name": "webassemblyjs/wasm-opt", "url": "https://github.com/xtuc/webassemblyjs", "version": "1.11.1", "license": "MIT", "licenseUrl": "https://github.com/xtuc/webassemblyjs/LICENSE"}, {"name": "webassemblyjs/wasm-parser", "url": "https://github.com/xtuc/webassemblyjs", "version": "1.11.1", "license": "MIT", "licenseUrl": "https://github.com/xtuc/webassemblyjs/LICENSE"}, {"name": "webassemblyjs/wast-printer", "url": "https://github.com/xtuc/webassemblyjs", "version": "1.11.1", "license": "MIT", "licenseUrl": "https://github.com/xtuc/webassemblyjs/LICENSE"}, {"name": "webidl-conversions", "url": "https://github.com/jsdom/webidl-conversions", "version": "3.0.1", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/jsdom/webidl-conversions/LICENSE.md"}, {"name": "webpack", "url": "https://github.com/webpack/webpack", "version": "5.70.0", "license": "MIT", "licenseUrl": "https://github.com/webpack/webpack/LICENSE"}, {"name": "webpack-dev-middleware", "url": "https://github.com/webpack/webpack-dev-middleware", "version": "5.3.0", "license": "MIT", "licenseUrl": "https://github.com/webpack/webpack-dev-middleware/LICENSE"}, {"name": "webpack-dev-server", "url": "https://github.com/webpack/webpack-dev-server", "version": "4.7.3", "license": "MIT", "licenseUrl": "https://github.com/webpack/webpack-dev-server/LICENSE"}, {"name": "webpack-merge", "url": "https://github.com/survivejs/webpack-merge", "version": "5.8.0", "license": "MIT", "licenseUrl": "https://github.com/survivejs/webpack-merge/LICENSE"}, {"name": "webpack-sources", "url": "https://github.com/webpack/webpack-sources", "version": "3.2.3", "license": "MIT", "licenseUrl": "https://github.com/webpack/webpack-sources/LICENSE"}, {"name": "webpack-subresource-integrity", "url": "https://github.com/waysact/webpack-subresource-integrity", "version": "5.1.0", "license": "MIT", "licenseUrl": "https://github.com/waysact/webpack-subresource-integrity/LICENSE"}, {"name": "websocket-driver", "url": "https://github.com/faye/websocket-driver-node", "version": "0.7.4", "license": "Apache-2.0", "licenseUrl": "https://github.com/faye/websocket-driver-node/LICENSE.md"}, {"name": "websocket-extensions", "url": "https://github.com/faye/websocket-extensions-node", "version": "0.1.4", "license": "Apache-2.0", "licenseUrl": "https://github.com/faye/websocket-extensions-node/LICENSE.md"}, {"name": "whatwg-url", "url": "https://github.com/jsdom/whatwg-url", "version": "5.0.0", "license": "MIT", "licenseUrl": "https://github.com/jsdom/whatwg-url/LICENSE.txt"}, {"name": "which", "url": "https://github.com/isaacs/node-which", "version": "1.3.1", "license": "ISC", "licenseUrl": "https://github.com/isaacs/node-which/LICENSE"}, {"name": "which", "url": "https://github.com/isaacs/node-which", "version": "2.0.2", "license": "ISC", "licenseUrl": "https://github.com/isaacs/node-which/LICENSE"}, {"name": "which-boxed-primitive", "url": "https://github.com/inspect-js/which-boxed-primitive", "version": "1.0.2", "license": "MIT", "licenseUrl": "https://github.com/inspect-js/which-boxed-primitive/LICENSE"}, {"name": "wicked-good-xpath", "url": "https://github.com/google/wicked-good-xpath", "version": "1.3.0", "license": "MIT", "licenseUrl": "https://github.com/google/wicked-good-xpath/LICENSE"}, {"name": "wide-align", "url": "https://github.com/iarna/wide-align", "version": "1.1.5", "license": "ISC", "licenseUrl": "https://github.com/iarna/wide-align/LICENSE"}, {"name": "wildcard", "url": "https://github.com/<PERSON><PERSON><PERSON>/wildcard", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/<PERSON><PERSON><PERSON>/wildcard/README.md"}, {"name": "winp", "url": "https://github.com/jenkinsci/winp", "version": "1.30.1", "license": "MIT", "licenseUrl": "https://github.com/jenkinsci/winp/blob/master/LICENSE.txt"}, {"name": "word-wrap", "url": "https://github.com/jonschlinkert/word-wrap", "version": "1.2.3", "license": "MIT", "licenseUrl": "https://github.com/jonschlinkert/word-wrap/LICENSE"}, {"name": "workerpool", "url": "https://github.com/josdejong/workerpool", "version": "6.2.1", "license": "Apache-2.0", "licenseUrl": "https://github.com/josdejong/workerpool/LICENSE"}, {"name": "wrap-ansi", "url": "https://github.com/chalk/wrap-ansi", "version": "7.0.0", "license": "MIT", "licenseUrl": "https://github.com/chalk/wrap-ansi/license"}, {"name": "wrappy", "url": "https://github.com/npm/wrappy", "version": "1.0.2", "license": "ISC", "licenseUrl": "https://github.com/npm/wrappy/LICENSE"}, {"name": "ws", "url": "https://github.com/websockets/ws", "version": "7.5.8", "license": "MIT", "licenseUrl": "https://github.com/websockets/ws/LICENSE"}, {"name": "ws", "url": "https://github.com/websockets/ws", "version": "8.17.0", "license": "MIT", "licenseUrl": "https://github.com/websockets/ws/LICENSE"}, {"name": "ws", "url": "https://github.com/websockets/ws", "version": "8.2.3", "license": "MIT", "licenseUrl": "https://github.com/websockets/ws/LICENSE"}, {"name": "ws", "url": "https://github.com/websockets/ws", "version": "8.8.0", "license": "MIT", "licenseUrl": "https://github.com/websockets/ws/LICENSE"}, {"name": "xml-apis-ext", "url": "https://xerces.apache.org/xml-commons/components/external/", "version": "1.3.04", "license": "Apache 2.0", "licenseUrl": "https://svn.apache.org/viewvc/xerces/xml-commons/trunk/java/external/LICENSE?view=markup"}, {"name": "xml-resolver", "url": "https://xerces.apache.org/xml-commons/components/resolver/", "version": "1.2", "license": "Apache 2.0", "licenseUrl": "https://svn.apache.org/viewvc/xerces/xml-commons/trunk/LICENSE?view=markup"}, {"name": "xmldom-sre", "url": "https://github.com/zorkow/xmldom", "version": "0.1.31", "license": "MIT*", "licenseUrl": "https://github.com/zorkow/xmldom/LICENSE"}, {"name": "xtend", "url": "https://github.com/Raynos/xtend", "version": "2.2.0", "license": "MIT", "licenseUrl": "https://github.com/Raynos/xtend/LICENCE"}, {"name": "xtend", "url": "https://github.com/Raynos/xtend", "version": "4.0.2", "license": "MIT", "licenseUrl": "https://github.com/Raynos/xtend/LICENSE"}, {"name": "xtuc/ieee754", "url": "https://github.com/feross/ieee754", "version": "1.2.0", "license": "BSD-3-<PERSON><PERSON>", "licenseUrl": "https://github.com/feross/ieee754/LICENSE"}, {"name": "xtuc/long", "url": "https://github.com/dcodeIO/long.js", "version": "4.2.2", "license": "Apache-2.0", "licenseUrl": "https://github.com/dcodeIO/long.js/LICENSE"}, {"name": "y-codemirror", "url": "https://github.com/yjs/y-codemirror", "version": "3.0.1", "license": "MIT", "licenseUrl": "https://github.com/yjs/y-codemirror/LICENSE"}, {"name": "y-protocols", "url": "https://github.com/yjs/y-protocols", "version": "1.0.6", "license": "MIT", "licenseUrl": "https://github.com/yjs/y-protocols/LICENSE"}, {"name": "y18n", "url": "https://github.com/yargs/y18n", "version": "5.0.8", "license": "ISC", "licenseUrl": "https://github.com/yargs/y18n/LICENSE"}, {"name": "yallist", "url": "https://github.com/isaacs/yallist", "version": "4.0.0", "license": "ISC", "licenseUrl": "https://github.com/isaacs/yallist/LICENSE"}, {"name": "yaml", "url": "https://github.com/eemeli/yaml", "version": "1.10.2", "license": "ISC", "licenseUrl": "https://github.com/eemeli/yaml/LICENSE"}, {"name": "yargs", "url": "https://github.com/yargs/yargs", "version": "17.5.1", "license": "MIT", "licenseUrl": "https://github.com/yargs/yargs/LICENSE"}, {"name": "yargs", "url": "https://github.com/yargs/yargs", "version": "16.2.0", "license": "MIT", "licenseUrl": "https://github.com/yargs/yargs/LICENSE"}, {"name": "yargs-parser", "url": "https://github.com/yargs/yargs-parser", "version": "20.0.0", "license": "ISC", "licenseUrl": "https://github.com/yargs/yargs-parser/LICENSE.txt"}, {"name": "yargs-parser", "url": "https://github.com/yargs/yargs-parser", "version": "20.2.9", "license": "ISC", "licenseUrl": "https://github.com/yargs/yargs-parser/LICENSE.txt"}, {"name": "yargs-parser", "url": "https://github.com/yargs/yargs-parser", "version": "20.2.4", "license": "ISC", "licenseUrl": "https://github.com/yargs/yargs-parser/LICENSE.txt"}, {"name": "yargs-parser", "url": "https://github.com/yargs/yargs-parser", "version": "21.0.1", "license": "ISC", "licenseUrl": "https://github.com/yargs/yargs-parser/LICENSE.txt"}, {"name": "yargs-unparser", "url": "https://github.com/yargs/yargs-unparser", "version": "2.0.0", "license": "MIT", "licenseUrl": "https://github.com/yargs/yargs-unparser/LICENSE"}, {"name": "yarnpkg/lockfile", "url": "https://github.com/yarnpkg/yarn/blob/master/packages/lockfile", "version": "1.1.0", "license": "BSD-2-<PERSON><PERSON>", "licenseUrl": "https://github.com/yarnpkg/yarn/blob/master/packages/lockfile/README.md"}, {"name": "yjs", "url": "https://github.com/yjs/yjs", "version": "13.6.10", "license": "MIT", "licenseUrl": "https://github.com/yjs/yjs/LICENSE"}, {"name": "yn", "url": "https://github.com/sindresorhus/yn", "version": "3.1.1", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/yn/license"}, {"name": "yocto-queue", "url": "https://github.com/sindresorhus/yocto-queue", "version": "0.1.0", "license": "MIT", "licenseUrl": "https://github.com/sindresorhus/yocto-queue/license"}, {"name": "zip-signer", "url": "https://github.com/JetBrains/marketplace-zip-signer", "version": "0.1.24", "license": "Apache 2.0", "licenseUrl": "https://github.com/JetBrains/marketplace-zip-signer/blob/master/LICENSE"}, {"name": "zone.js", "url": "https://github.com/angular/angular", "version": "0.11.6", "license": "MIT", "licenseUrl": "https://github.com/angular/angular/LICENSE"}, {"name": "zstd-jni", "url": "https://github.com/luben/zstd-jni", "version": "1.5.6-9", "license": "BSD 2-<PERSON><PERSON>", "licenseUrl": "https://github.com/luben/zstd-jni/blob/master/LICENSE"}]