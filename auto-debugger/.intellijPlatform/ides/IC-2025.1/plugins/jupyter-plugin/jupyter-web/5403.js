"use strict";(self.webpackChunkjupyter_web=self.webpackChunkjupyter_web||[]).push([[5403],{15956:(L,g)=>{Object.defineProperty(g,"__esModule",{value:!0}),g.AbstractWrapper=void 0;var _=function(){function C(b,M){this.factory=b,this.node=M}return Object.defineProperty(C.prototype,"kind",{get:function(){return this.node.kind},enumerable:!1,configurable:!0}),C.prototype.wrap=function(b){return this.factory.wrap(b)},C}();g.AbstractWrapper=_},67556:function(L,g,_){var d,C=this&&this.__extends||(d=function(s,a){return(d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(t[e]=r[e])})(s,a)},function(s,a){if("function"!=typeof a&&null!==a)throw new TypeError("Class extends value "+String(a)+" is not a constructor or null");function t(){this.constructor=s}d(s,a),s.prototype=null===a?Object.create(a):(t.prototype=a.prototype,new t)}),b=this&&this.__read||function(d,s){var a="function"==typeof Symbol&&d[Symbol.iterator];if(!a)return d;var r,i,t=a.call(d),e=[];try{for(;(void 0===s||s-- >0)&&!(r=t.next()).done;)e.push(r.value)}catch(o){i={error:o}}finally{try{r&&!r.done&&(a=t.return)&&a.call(t)}finally{if(i)throw i.error}}return e},M=this&&this.__spreadArray||function(d,s,a){if(a||2===arguments.length)for(var e,t=0,r=s.length;t<r;t++)(e||!(t in s))&&(e||(e=Array.prototype.slice.call(s,0,t)),e[t]=s[t]);return d.concat(e||Array.prototype.slice.call(s))};Object.defineProperty(g,"__esModule",{value:!0}),g.AbstractWrapperFactory=void 0;var y=function(d){function s(){return null!==d&&d.apply(this,arguments)||this}return C(s,d),s.prototype.wrap=function(a){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];return this.create.apply(this,M([a.kind,a],b(t),!1))},s}(_(95272).AbstractFactory);g.AbstractWrapperFactory=y},6670:function(L,g,_){var l,C=this&&this.__extends||(l=function(u,h){return(l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,f){c.__proto__=f}||function(c,f){for(var p in f)Object.prototype.hasOwnProperty.call(f,p)&&(c[p]=f[p])})(u,h)},function(u,h){if("function"!=typeof h&&null!==h)throw new TypeError("Class extends value "+String(h)+" is not a constructor or null");function c(){this.constructor=u}l(u,h),u.prototype=null===h?Object.create(h):(c.prototype=h.prototype,new c)}),b=this&&this.__assign||function(){return b=Object.assign||function(l){for(var u,h=1,c=arguments.length;h<c;h++)for(var f in u=arguments[h])Object.prototype.hasOwnProperty.call(u,f)&&(l[f]=u[f]);return l},b.apply(this,arguments)},M=this&&this.__createBinding||(Object.create?function(l,u,h,c){void 0===c&&(c=h);var f=Object.getOwnPropertyDescriptor(u,h);(!f||("get"in f?!u.__esModule:f.writable||f.configurable))&&(f={enumerable:!0,get:function(){return u[h]}}),Object.defineProperty(l,c,f)}:function(l,u,h,c){void 0===c&&(c=h),l[c]=u[h]}),v=this&&this.__setModuleDefault||(Object.create?function(l,u){Object.defineProperty(l,"default",{enumerable:!0,value:u})}:function(l,u){l.default=u}),y=this&&this.__importStar||function(l){if(l&&l.__esModule)return l;var u={};if(null!=l)for(var h in l)"default"!==h&&Object.prototype.hasOwnProperty.call(l,h)&&M(u,l,h);return v(u,l),u},d=this&&this.__values||function(l){var u="function"==typeof Symbol&&Symbol.iterator,h=u&&l[u],c=0;if(h)return h.call(l);if(l&&"number"==typeof l.length)return{next:function(){return l&&c>=l.length&&(l=void 0),{value:l&&l[c++],done:!l}}};throw new TypeError(u?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(g,"__esModule",{value:!0}),g.CHTML=void 0;var s=_(22205),a=_(53873),t=_(74078),r=_(65125),e=_(30674),i=y(_(21108)),o=_(74267),n=function(l){function u(h){void 0===h&&(h=null);var c=l.call(this,h,t.CHTMLWrapperFactory,e.TeXFont)||this;return c.chtmlStyles=null,c.font.adaptiveCSS(c.options.adaptiveCSS),c.wrapperUsage=new r.Usage,c}return C(u,l),u.prototype.escaped=function(h,c){return this.setDocument(c),this.html("span",{},[this.text(h.math)])},u.prototype.styleSheet=function(h){if(this.chtmlStyles){if(this.options.adaptiveCSS){var c=new a.CssStyles;this.addWrapperStyles(c),this.updateFontStyles(c),this.adaptor.insertRules(this.chtmlStyles,c.getStyleRules())}return this.chtmlStyles}var f=this.chtmlStyles=l.prototype.styleSheet.call(this,h);return this.adaptor.setAttribute(f,"id",u.STYLESHEETID),this.wrapperUsage.update(),f},u.prototype.updateFontStyles=function(h){h.addStyles(this.font.updateStyles({}))},u.prototype.addWrapperStyles=function(h){var c,f;if(this.options.adaptiveCSS)try{for(var p=d(this.wrapperUsage.update()),m=p.next();!m.done;m=p.next()){var x=m.value,w=this.factory.getNodeClass(x);w&&this.addClassStyles(w,h)}}catch(j){c={error:j}}finally{try{m&&!m.done&&(f=p.return)&&f.call(p)}finally{if(c)throw c.error}}else l.prototype.addWrapperStyles.call(this,h)},u.prototype.addClassStyles=function(h,c){var f,p=h;p.autoStyle&&"unknown"!==p.kind&&c.addStyles(((f={})["mjx-"+p.kind]={display:"inline-block","text-align":"left"},f)),this.wrapperUsage.add(p.kind),l.prototype.addClassStyles.call(this,h,c)},u.prototype.processMath=function(h,c){this.factory.wrap(h).toCHTML(c)},u.prototype.clearCache=function(){this.cssStyles.clear(),this.font.clearCache(),this.wrapperUsage.clear(),this.chtmlStyles=null},u.prototype.reset=function(){this.clearCache()},u.prototype.unknownText=function(h,c,f){void 0===f&&(f=null);var p={},m=100/this.math.metrics.scale;if(100!==m&&(p["font-size"]=this.fixed(m,1)+"%",p.padding=i.em(75/m)+" 0 "+i.em(20/m)+" 0"),"-explicitFont"!==c){var x=(0,o.unicodeChars)(h);(1!==x.length||x[0]<119808||x[0]>120831)&&this.cssFontStyles(this.font.getCssFont(c),p)}if(null!==f){var w=this.math.metrics;p.width=Math.round(f*w.em*w.scale)+"px"}return this.html("mjx-utext",{variant:c,style:p},[this.text(h)])},u.prototype.measureTextNode=function(h){var c=this.adaptor,f=c.clone(h);c.setStyle(f,"font-family",c.getStyle(f,"font-family").replace(/MJXZERO, /g,""));var m=this.html("mjx-measure-text",{style:{position:"absolute","white-space":"nowrap"}},[f]);c.append(c.parent(this.math.start.node),this.container),c.append(this.container,m);var x=c.nodeSize(f,this.math.metrics.em)[0]/this.math.metrics.scale;return c.remove(this.container),c.remove(m),{w:x,h:.75,d:.2}},u.NAME="CHTML",u.OPTIONS=b(b({},s.CommonOutputJax.OPTIONS),{adaptiveCSS:!0,matchFontHeight:!0}),u.commonStyles={'mjx-container[jax="CHTML"]':{"line-height":0},'mjx-container [space="1"]':{"margin-left":".111em"},'mjx-container [space="2"]':{"margin-left":".167em"},'mjx-container [space="3"]':{"margin-left":".222em"},'mjx-container [space="4"]':{"margin-left":".278em"},'mjx-container [space="5"]':{"margin-left":".333em"},'mjx-container [rspace="1"]':{"margin-right":".111em"},'mjx-container [rspace="2"]':{"margin-right":".167em"},'mjx-container [rspace="3"]':{"margin-right":".222em"},'mjx-container [rspace="4"]':{"margin-right":".278em"},'mjx-container [rspace="5"]':{"margin-right":".333em"},'mjx-container [size="s"]':{"font-size":"70.7%"},'mjx-container [size="ss"]':{"font-size":"50%"},'mjx-container [size="Tn"]':{"font-size":"60%"},'mjx-container [size="sm"]':{"font-size":"85%"},'mjx-container [size="lg"]':{"font-size":"120%"},'mjx-container [size="Lg"]':{"font-size":"144%"},'mjx-container [size="LG"]':{"font-size":"173%"},'mjx-container [size="hg"]':{"font-size":"207%"},'mjx-container [size="HG"]':{"font-size":"249%"},'mjx-container [width="full"]':{width:"100%"},"mjx-box":{display:"inline-block"},"mjx-block":{display:"block"},"mjx-itable":{display:"inline-table"},"mjx-row":{display:"table-row"},"mjx-row > *":{display:"table-cell"},"mjx-mtext":{display:"inline-block"},"mjx-mstyle":{display:"inline-block"},"mjx-merror":{display:"inline-block",color:"red","background-color":"yellow"},"mjx-mphantom":{visibility:"hidden"},"_::-webkit-full-page-media, _:future, :root mjx-container":{"will-change":"opacity"}},u.STYLESHEETID="MJX-CHTML-styles",u}(s.CommonOutputJax);g.CHTML=n},33269:function(L,g,_){var C=this&&this.__createBinding||(Object.create?function(o,n,l,u){void 0===u&&(u=l);var h=Object.getOwnPropertyDescriptor(n,l);(!h||("get"in h?!n.__esModule:h.writable||h.configurable))&&(h={enumerable:!0,get:function(){return n[l]}}),Object.defineProperty(o,u,h)}:function(o,n,l,u){void 0===u&&(u=l),o[u]=n[l]}),b=this&&this.__setModuleDefault||(Object.create?function(o,n){Object.defineProperty(o,"default",{enumerable:!0,value:n})}:function(o,n){o.default=n}),M=this&&this.__importStar||function(o){if(o&&o.__esModule)return o;var n={};if(null!=o)for(var l in o)"default"!==l&&Object.prototype.hasOwnProperty.call(o,l)&&C(n,o,l);return b(n,o),n},v=this&&this.__exportStar||function(o,n){for(var l in o)"default"!==l&&!Object.prototype.hasOwnProperty.call(n,l)&&C(n,o,l)},y=this&&this.__read||function(o,n){var l="function"==typeof Symbol&&o[Symbol.iterator];if(!l)return o;var h,f,u=l.call(o),c=[];try{for(;(void 0===n||n-- >0)&&!(h=u.next()).done;)c.push(h.value)}catch(p){f={error:p}}finally{try{h&&!h.done&&(l=u.return)&&l.call(u)}finally{if(f)throw f.error}}return c};Object.defineProperty(g,"__esModule",{value:!0}),g.Arrow=g.DiagonalArrow=g.DiagonalStrike=g.Border2=g.Border=g.RenderElement=void 0;var d=M(_(21179));v(_(21179),g);g.RenderElement=function(o,n){return void 0===n&&(n=""),function(l,u){var h=l.adjustBorder(l.html("mjx-"+o));if(n){var c=l.getOffset(n);if(l.thickness!==d.THICKNESS||c){var f="translate".concat(n,"(").concat(l.em(l.thickness/2-c),")");l.adaptor.setStyle(h,"transform",f)}}l.adaptor.append(l.chtml,h)}};g.Border=function(o){return d.CommonBorder(function(n,l){n.adaptor.setStyle(l,"border-"+o,n.em(n.thickness)+" solid")})(o)};g.Border2=function(o,n,l){return d.CommonBorder2(function(u,h){var c=u.em(u.thickness)+" solid";u.adaptor.setStyle(h,"border-"+n,c),u.adaptor.setStyle(h,"border-"+l,c)})(o,n,l)};g.DiagonalStrike=function(o,n){return d.CommonDiagonalStrike(function(l){return function(u,h){var c=u.getBBox(),f=c.w,p=c.h,m=c.d,x=y(u.getArgMod(f,p+m),2),w=x[0],j=x[1],S=n*u.thickness/2,T=u.adjustBorder(u.html(l,{style:{width:u.em(j),transform:"rotate("+u.fixed(-n*w)+"rad) translateY("+S+"em)"}}));u.adaptor.append(u.chtml,T)}})(o)};g.DiagonalArrow=function(o){return d.CommonDiagonalArrow(function(n,l){n.adaptor.append(n.chtml,l)})(o)};g.Arrow=function(o){return d.CommonArrow(function(n,l){n.adaptor.append(n.chtml,l)})(o)}},10475:function(L,g,_){var s,i,C=this&&this.__extends||(i=function(o,n){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(l,u){l.__proto__=u}||function(l,u){for(var h in u)Object.prototype.hasOwnProperty.call(u,h)&&(l[h]=u[h])})(o,n)},function(o,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function l(){this.constructor=o}i(o,n),o.prototype=null===n?Object.create(n):(l.prototype=n.prototype,new l)}),b=this&&this.__createBinding||(Object.create?function(i,o,n,l){void 0===l&&(l=n);var u=Object.getOwnPropertyDescriptor(o,n);(!u||("get"in u?!o.__esModule:u.writable||u.configurable))&&(u={enumerable:!0,get:function(){return o[n]}}),Object.defineProperty(i,l,u)}:function(i,o,n,l){void 0===l&&(l=n),i[l]=o[n]}),M=this&&this.__setModuleDefault||(Object.create?function(i,o){Object.defineProperty(i,"default",{enumerable:!0,value:o})}:function(i,o){i.default=o}),v=this&&this.__importStar||function(i){if(i&&i.__esModule)return i;var o={};if(null!=i)for(var n in i)"default"!==n&&Object.prototype.hasOwnProperty.call(i,n)&&b(o,i,n);return M(o,i),o},y=this&&this.__values||function(i){var o="function"==typeof Symbol&&Symbol.iterator,n=o&&i[o],l=0;if(n)return n.call(i);if(i&&"number"==typeof i.length)return{next:function(){return i&&l>=i.length&&(i=void 0),{value:i&&i[l++],done:!i}}};throw new TypeError(o?"Object is not iterable.":"Symbol.iterator is not defined.")},d=this&&this.__read||function(i,o){var n="function"==typeof Symbol&&i[Symbol.iterator];if(!n)return i;var u,c,l=n.call(i),h=[];try{for(;(void 0===o||o-- >0)&&!(u=l.next()).done;)h.push(u.value)}catch(f){c={error:f}}finally{try{u&&!u.done&&(n=l.return)&&n.call(l)}finally{if(c)throw c.error}}return h};Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLWrapper=g.SPACE=g.FONTSIZE=void 0;var a=v(_(21108)),t=_(91907),r=_(13380);g.FONTSIZE={"70.7%":"s","70%":"s","50%":"ss","60%":"Tn","85%":"sm","120%":"lg","144%":"Lg","173%":"LG","207%":"hg","249%":"HG"},g.SPACE=((s={})[a.em(2/18)]="1",s[a.em(3/18)]="2",s[a.em(4/18)]="3",s[a.em(5/18)]="4",s[a.em(6/18)]="5",s);var e=function(i){function o(){var n=null!==i&&i.apply(this,arguments)||this;return n.chtml=null,n}return C(o,i),o.prototype.toCHTML=function(n){var l,u,h=this.standardCHTMLnode(n);try{for(var c=y(this.childNodes),f=c.next();!f.done;f=c.next()){f.value.toCHTML(h)}}catch(m){l={error:m}}finally{try{f&&!f.done&&(u=c.return)&&u.call(c)}finally{if(l)throw l.error}}},o.prototype.standardCHTMLnode=function(n){this.markUsed();var l=this.createCHTMLnode(n);return this.handleStyles(),this.handleVariant(),this.handleScale(),this.handleColor(),this.handleSpace(),this.handleAttributes(),this.handlePWidth(),l},o.prototype.markUsed=function(){this.jax.wrapperUsage.add(this.kind)},o.prototype.createCHTMLnode=function(n){var l=this.node.attributes.get("href");return l&&(n=this.adaptor.append(n,this.html("a",{href:l}))),this.chtml=this.adaptor.append(n,this.html("mjx-"+this.node.kind)),this.chtml},o.prototype.handleStyles=function(){if(this.styles){var n=this.styles.cssText;if(n){this.adaptor.setAttribute(this.chtml,"style",n);var l=this.styles.get("font-family");l&&this.adaptor.setStyle(this.chtml,"font-family","MJXZERO, "+l)}}},o.prototype.handleVariant=function(){this.node.isToken&&"-explicitFont"!==this.variant&&this.adaptor.setAttribute(this.chtml,"class",(this.font.getVariant(this.variant)||this.font.getVariant("normal")).classes)},o.prototype.handleScale=function(){this.setScale(this.chtml,this.bbox.rscale)},o.prototype.setScale=function(n,l){var u=Math.abs(l-1)<.001?1:l;if(n&&1!==u){var h=this.percent(u);g.FONTSIZE[h]?this.adaptor.setAttribute(n,"size",g.FONTSIZE[h]):this.adaptor.setStyle(n,"fontSize",h)}return n},o.prototype.handleSpace=function(){var n,l;try{for(var u=y([[this.bbox.L,"space","marginLeft"],[this.bbox.R,"rspace","marginRight"]]),h=u.next();!h.done;h=u.next()){var c=h.value,f=d(c,3),p=f[0],m=f[1],x=f[2];if(p){var w=this.em(p);g.SPACE[w]?this.adaptor.setAttribute(this.chtml,m,g.SPACE[w]):this.adaptor.setStyle(this.chtml,x,w)}}}catch(j){n={error:j}}finally{try{h&&!h.done&&(l=u.return)&&l.call(u)}finally{if(n)throw n.error}}},o.prototype.handleColor=function(){var n=this.node.attributes,l=n.getExplicit("mathcolor"),u=n.getExplicit("color"),h=n.getExplicit("mathbackground"),c=n.getExplicit("background");(l||u)&&this.adaptor.setStyle(this.chtml,"color",l||u),(h||c)&&this.adaptor.setStyle(this.chtml,"backgroundColor",h||c)},o.prototype.handleAttributes=function(){var n,l,u,h,c=this.node.attributes,f=c.getAllDefaults(),p=o.skipAttributes;try{for(var m=y(c.getExplicitNames()),x=m.next();!x.done;x=m.next()){var w=x.value;(!1===p[w]||!(w in f)&&!p[w]&&!this.adaptor.hasAttribute(this.chtml,w))&&this.adaptor.setAttribute(this.chtml,w,c.getExplicit(w))}}catch(B){n={error:B}}finally{try{x&&!x.done&&(l=m.return)&&l.call(m)}finally{if(n)throw n.error}}if(c.get("class")){var j=c.get("class").trim().split(/ +/);try{for(var S=y(j),T=S.next();!T.done;T=S.next()){var O=T.value;this.adaptor.addClass(this.chtml,O)}}catch(B){u={error:B}}finally{try{T&&!T.done&&(h=S.return)&&h.call(S)}finally{if(u)throw u.error}}}},o.prototype.handlePWidth=function(){this.bbox.pwidth&&(this.bbox.pwidth===r.BBox.fullWidth?this.adaptor.setAttribute(this.chtml,"width","full"):this.adaptor.setStyle(this.chtml,"width",this.bbox.pwidth))},o.prototype.setIndent=function(n,l,u){var h=this.adaptor;if("center"===l||"left"===l){var c=this.getBBox().L;h.setStyle(n,"margin-left",this.em(u+c))}if("center"===l||"right"===l){var f=this.getBBox().R;h.setStyle(n,"margin-right",this.em(-u+f))}},o.prototype.drawBBox=function(){var n=this.getBBox(),l=n.w,u=n.h,h=n.d,c=n.R,f=this.html("mjx-box",{style:{opacity:.25,"margin-left":this.em(-l-c)}},[this.html("mjx-box",{style:{height:this.em(u),width:this.em(l),"background-color":"red"}}),this.html("mjx-box",{style:{height:this.em(h),width:this.em(l),"margin-left":this.em(-l),"vertical-align":this.em(-h),"background-color":"green"}})]),p=this.chtml||this.parent.chtml,m=this.adaptor.getAttribute(p,"size");m&&this.adaptor.setAttribute(f,"size",m);var x=this.adaptor.getStyle(p,"fontSize");x&&this.adaptor.setStyle(f,"fontSize",x),this.adaptor.append(this.adaptor.parent(p),f),this.adaptor.setStyle(p,"backgroundColor","#FFEE00")},o.prototype.html=function(n,l,u){return void 0===l&&(l={}),void 0===u&&(u=[]),this.jax.html(n,l,u)},o.prototype.text=function(n){return this.jax.text(n)},o.prototype.char=function(n){return this.font.charSelector(n).substr(1)},o.kind="unknown",o.autoStyle=!0,o}(t.CommonWrapper);g.CHTMLWrapper=e},74078:function(L,g,_){var y,C=this&&this.__extends||(y=function(d,s){return(y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,t){a.__proto__=t}||function(a,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(a[r]=t[r])})(d,s)},function(d,s){if("function"!=typeof s&&null!==s)throw new TypeError("Class extends value "+String(s)+" is not a constructor or null");function a(){this.constructor=d}y(d,s),d.prototype=null===s?Object.create(s):(a.prototype=s.prototype,new a)});Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLWrapperFactory=void 0;var b=_(46340),M=_(70905),v=function(y){function d(){return null!==y&&y.apply(this,arguments)||this}return C(d,y),d.defaultNodes=M.CHTMLWrappers,d}(b.CommonWrapperFactory);g.CHTMLWrapperFactory=v},70905:(L,g,_)=>{var C;Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLWrappers=void 0;var b=_(10475),M=_(40067),v=_(80237),y=_(16327),d=_(62844),s=_(27102),a=_(89175),t=_(39786),r=_(8011),e=_(36645),i=_(74239),o=_(3535),n=_(68657),l=_(82512),u=_(29255),h=_(31201),c=_(81005),f=_(37801),p=_(64612),m=_(90714),x=_(90215),w=_(83874),j=_(37453),S=_(37690),T=_(8763),O=_(11836);g.CHTMLWrappers=((C={})[M.CHTMLmath.kind]=M.CHTMLmath,C[i.CHTMLmrow.kind]=i.CHTMLmrow,C[i.CHTMLinferredMrow.kind]=i.CHTMLinferredMrow,C[v.CHTMLmi.kind]=v.CHTMLmi,C[y.CHTMLmo.kind]=y.CHTMLmo,C[d.CHTMLmn.kind]=d.CHTMLmn,C[s.CHTMLms.kind]=s.CHTMLms,C[a.CHTMLmtext.kind]=a.CHTMLmtext,C[t.CHTMLmspace.kind]=t.CHTMLmspace,C[r.CHTMLmpadded.kind]=r.CHTMLmpadded,C[e.CHTMLmenclose.kind]=e.CHTMLmenclose,C[n.CHTMLmfrac.kind]=n.CHTMLmfrac,C[l.CHTMLmsqrt.kind]=l.CHTMLmsqrt,C[u.CHTMLmroot.kind]=u.CHTMLmroot,C[h.CHTMLmsub.kind]=h.CHTMLmsub,C[h.CHTMLmsup.kind]=h.CHTMLmsup,C[h.CHTMLmsubsup.kind]=h.CHTMLmsubsup,C[c.CHTMLmunder.kind]=c.CHTMLmunder,C[c.CHTMLmover.kind]=c.CHTMLmover,C[c.CHTMLmunderover.kind]=c.CHTMLmunderover,C[f.CHTMLmmultiscripts.kind]=f.CHTMLmmultiscripts,C[o.CHTMLmfenced.kind]=o.CHTMLmfenced,C[p.CHTMLmtable.kind]=p.CHTMLmtable,C[m.CHTMLmtr.kind]=m.CHTMLmtr,C[m.CHTMLmlabeledtr.kind]=m.CHTMLmlabeledtr,C[x.CHTMLmtd.kind]=x.CHTMLmtd,C[w.CHTMLmaction.kind]=w.CHTMLmaction,C[j.CHTMLmglyph.kind]=j.CHTMLmglyph,C[S.CHTMLsemantics.kind]=S.CHTMLsemantics,C[S.CHTMLannotation.kind]=S.CHTMLannotation,C[S.CHTMLannotationXML.kind]=S.CHTMLannotationXML,C[S.CHTMLxml.kind]=S.CHTMLxml,C[T.CHTMLTeXAtom.kind]=T.CHTMLTeXAtom,C[O.CHTMLTextNode.kind]=O.CHTMLTextNode,C[b.CHTMLWrapper.kind]=b.CHTMLWrapper,C)},8763:function(L,g,_){var s,C=this&&this.__extends||(s=function(a,t){return(s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,e){r.__proto__=e}||function(r,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(r[i]=e[i])})(a,t)},function(a,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=a}s(a,t),a.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLTeXAtom=void 0;var b=_(10475),M=_(36957),v=_(8252),y=_(91585),d=function(s){function a(){return null!==s&&s.apply(this,arguments)||this}return C(a,s),a.prototype.toCHTML=function(t){if(s.prototype.toCHTML.call(this,t),this.adaptor.setAttribute(this.chtml,"texclass",y.TEXCLASSNAMES[this.node.texClass]),this.node.texClass===y.TEXCLASS.VCENTER){var r=this.childNodes[0].getBBox(),e=r.h,n=(e+r.d)/2+this.font.params.axis_height-e;this.adaptor.setStyle(this.chtml,"verticalAlign",this.em(n))}},a.kind=v.TeXAtom.prototype.kind,a}((0,M.CommonTeXAtomMixin)(b.CHTMLWrapper));g.CHTMLTeXAtom=d},11836:function(L,g,_){var s,C=this&&this.__extends||(s=function(a,t){return(s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,e){r.__proto__=e}||function(r,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(r[i]=e[i])})(a,t)},function(a,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=a}s(a,t),a.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),b=this&&this.__values||function(s){var a="function"==typeof Symbol&&Symbol.iterator,t=a&&s[a],r=0;if(t)return t.call(s);if(s&&"number"==typeof s.length)return{next:function(){return s&&r>=s.length&&(s=void 0),{value:s&&s[r++],done:!s}}};throw new TypeError(a?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLTextNode=void 0;var M=_(91585),v=_(10475),d=function(s){function a(){return null!==s&&s.apply(this,arguments)||this}return C(a,s),a.prototype.toCHTML=function(t){var r,e;this.markUsed();var i=this.adaptor,o=this.parent.variant,n=this.node.getText();if(0!==n.length)if("-explicitFont"===o)i.append(t,this.jax.unknownText(n,o,this.getBBox().w));else{var l=this.remappedText(n,o);try{for(var u=b(l),h=u.next();!h.done;h=u.next()){var c=h.value,f=this.getVariantChar(o,c)[3],p=f.f?" TEX-"+f.f:"",m=f.unknown?this.jax.unknownText(String.fromCodePoint(c),o):this.html("mjx-c",{class:this.char(c)+p});i.append(t,m),!f.unknown&&this.font.charUsage.add([o,c])}}catch(x){r={error:x}}finally{try{h&&!h.done&&(e=u.return)&&e.call(u)}finally{if(r)throw r.error}}}},a.kind=M.TextNode.prototype.kind,a.autoStyle=!1,a.styles={"mjx-c":{display:"inline-block"},"mjx-utext":{display:"inline-block",padding:".75em 0 .2em 0"}},a}((0,_(32861).CommonTextNodeMixin)(v.CHTMLWrapper));g.CHTMLTextNode=d},83874:function(L,g,_){var s,C=this&&this.__extends||(s=function(a,t){return(s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,e){r.__proto__=e}||function(r,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(r[i]=e[i])})(a,t)},function(a,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=a}s(a,t),a.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)});Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLmaction=void 0;var b=_(10475),M=_(55309),v=_(55309),y=_(54466),d=function(s){function a(){return null!==s&&s.apply(this,arguments)||this}return C(a,s),a.prototype.toCHTML=function(t){var r=this.standardCHTMLnode(t);this.selected.toCHTML(r),this.action(this,this.data)},a.prototype.setEventHandler=function(t,r){this.chtml.addEventListener(t,r)},a.kind=y.MmlMaction.prototype.kind,a.styles={"mjx-maction":{position:"relative"},"mjx-maction > mjx-tool":{display:"none",position:"absolute",bottom:0,right:0,width:0,height:0,"z-index":500},"mjx-tool > mjx-tip":{display:"inline-block",padding:".2em",border:"1px solid #888","font-size":"70%","background-color":"#F8F8F8",color:"black","box-shadow":"2px 2px 5px #AAAAAA"},"mjx-maction[toggle]":{cursor:"pointer"},"mjx-status":{display:"block",position:"fixed",left:"1em",bottom:"1em","min-width":"25%",padding:".2em .4em",border:"1px solid #888","font-size":"90%","background-color":"#F8F8F8",color:"black"}},a.actions=new Map([["toggle",[function(t,r){t.adaptor.setAttribute(t.chtml,"toggle",t.node.attributes.get("selection"));var e=t.factory.jax.math,i=t.factory.jax.document,o=t.node;t.setEventHandler("click",function(n){e.end.node||(e.start.node=e.end.node=e.typesetRoot,e.start.n=e.end.n=0),o.nextToggleSelection(),e.rerender(i),n.stopPropagation()})},{}]],["tooltip",[function(t,r){var e=t.childNodes[1];if(e)if(e.node.isKind("mtext")){var i=e.node.getText();t.adaptor.setAttribute(t.chtml,"title",i)}else{var o=t.adaptor,n=o.append(t.chtml,t.html("mjx-tool",{style:{bottom:t.em(-t.dy),right:t.em(-t.dx)}},[t.html("mjx-tip")]));e.toCHTML(o.firstChild(n)),t.setEventHandler("mouseover",function(l){r.stopTimers(t,r);var u=setTimeout(function(){return o.setStyle(n,"display","block")},r.postDelay);r.hoverTimer.set(t,u),l.stopPropagation()}),t.setEventHandler("mouseout",function(l){r.stopTimers(t,r);var u=setTimeout(function(){return o.setStyle(n,"display","")},r.clearDelay);r.clearTimer.set(t,u),l.stopPropagation()})}},v.TooltipData]],["statusline",[function(t,r){var e=t.childNodes[1];if(e&&e.node.isKind("mtext")){var i=t.adaptor,o=e.node.getText();i.setAttribute(t.chtml,"statusline",o),t.setEventHandler("mouseover",function(n){if(null===r.status){var l=i.body(i.document);r.status=i.append(l,t.html("mjx-status",{},[t.text(o)]))}n.stopPropagation()}),t.setEventHandler("mouseout",function(n){r.status&&(i.remove(r.status),r.status=null),n.stopPropagation()})}},{status:null}]]]),a}((0,M.CommonMactionMixin)(b.CHTMLWrapper));g.CHTMLmaction=d},40067:function(L,g,_){var a,C=this&&this.__extends||(a=function(t,r){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,i){e.__proto__=i}||function(e,i){for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(e[o]=i[o])})(t,r)},function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function e(){this.constructor=t}a(t,r),t.prototype=null===r?Object.create(r):(e.prototype=r.prototype,new e)}),b=this&&this.__read||function(a,t){var r="function"==typeof Symbol&&a[Symbol.iterator];if(!r)return a;var i,n,e=r.call(a),o=[];try{for(;(void 0===t||t-- >0)&&!(i=e.next()).done;)o.push(i.value)}catch(l){n={error:l}}finally{try{i&&!i.done&&(r=e.return)&&r.call(e)}finally{if(n)throw n.error}}return o};Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLmath=void 0;var M=_(10475),v=_(79039),y=_(25021),d=_(13380),s=function(a){function t(){return null!==a&&a.apply(this,arguments)||this}return C(t,a),t.prototype.toCHTML=function(r){a.prototype.toCHTML.call(this,r);var e=this.chtml,i=this.adaptor;"block"===this.node.attributes.get("display")?(i.setAttribute(e,"display","true"),i.setAttribute(r,"display","true"),this.handleDisplay(r)):this.handleInline(r),i.addClass(e,"MJX-TEX")},t.prototype.handleDisplay=function(r){var e=this.adaptor,i=b(this.getAlignShift(),2),o=i[0],n=i[1];if("center"!==o&&e.setAttribute(r,"justify",o),this.bbox.pwidth===d.BBox.fullWidth){if(e.setAttribute(r,"width","full"),this.jax.table){var l=this.jax.table.getOuterBBox(),u=l.L,h=l.w,c=l.R;"right"===o?c=Math.max(c||-n,-n):"left"===o?u=Math.max(u||n,n):"center"===o&&(h+=2*Math.abs(n));var f=this.em(Math.max(0,u+h+c));e.setStyle(r,"min-width",f),e.setStyle(this.jax.table.chtml,"min-width",f)}}else this.setIndent(this.chtml,o,n)},t.prototype.handleInline=function(r){var e=this.adaptor,i=e.getStyle(this.chtml,"margin-right");i&&(e.setStyle(this.chtml,"margin-right",""),e.setStyle(r,"margin-right",i),e.setStyle(r,"width","0"))},t.prototype.setChildPWidths=function(r,e,i){return void 0===e&&(e=null),void 0===i&&(i=!0),!!this.parent&&a.prototype.setChildPWidths.call(this,r,e,i)},t.kind=y.MmlMath.prototype.kind,t.styles={"mjx-math":{"line-height":0,"text-align":"left","text-indent":0,"font-style":"normal","font-weight":"normal","font-size":"100%","font-size-adjust":"none","letter-spacing":"normal","border-collapse":"collapse","word-wrap":"normal","word-spacing":"normal","white-space":"nowrap",direction:"ltr",padding:"1px 0"},'mjx-container[jax="CHTML"][display="true"]':{display:"block","text-align":"center",margin:"1em 0"},'mjx-container[jax="CHTML"][display="true"][width="full"]':{display:"flex"},'mjx-container[jax="CHTML"][display="true"] mjx-math':{padding:0},'mjx-container[jax="CHTML"][justify="left"]':{"text-align":"left"},'mjx-container[jax="CHTML"][justify="right"]':{"text-align":"right"}},t}((0,v.CommonMathMixin)(M.CHTMLWrapper));g.CHTMLmath=s},36645:function(L,g,_){var l,C=this&&this.__extends||(l=function(u,h){return(l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(c,f){c.__proto__=f}||function(c,f){for(var p in f)Object.prototype.hasOwnProperty.call(f,p)&&(c[p]=f[p])})(u,h)},function(u,h){if("function"!=typeof h&&null!==h)throw new TypeError("Class extends value "+String(h)+" is not a constructor or null");function c(){this.constructor=u}l(u,h),u.prototype=null===h?Object.create(h):(c.prototype=h.prototype,new c)}),b=this&&this.__createBinding||(Object.create?function(l,u,h,c){void 0===c&&(c=h);var f=Object.getOwnPropertyDescriptor(u,h);(!f||("get"in f?!u.__esModule:f.writable||f.configurable))&&(f={enumerable:!0,get:function(){return u[h]}}),Object.defineProperty(l,c,f)}:function(l,u,h,c){void 0===c&&(c=h),l[c]=u[h]}),M=this&&this.__setModuleDefault||(Object.create?function(l,u){Object.defineProperty(l,"default",{enumerable:!0,value:u})}:function(l,u){l.default=u}),v=this&&this.__importStar||function(l){if(l&&l.__esModule)return l;var u={};if(null!=l)for(var h in l)"default"!==h&&Object.prototype.hasOwnProperty.call(l,h)&&b(u,l,h);return M(u,l),u},y=this&&this.__values||function(l){var u="function"==typeof Symbol&&Symbol.iterator,h=u&&l[u],c=0;if(h)return h.call(l);if(l&&"number"==typeof l.length)return{next:function(){return l&&c>=l.length&&(l=void 0),{value:l&&l[c++],done:!l}}};throw new TypeError(u?"Object is not iterable.":"Symbol.iterator is not defined.")},d=this&&this.__read||function(l,u){var h="function"==typeof Symbol&&l[Symbol.iterator];if(!h)return l;var f,m,c=h.call(l),p=[];try{for(;(void 0===u||u-- >0)&&!(f=c.next()).done;)p.push(f.value)}catch(x){m={error:x}}finally{try{f&&!f.done&&(h=c.return)&&h.call(c)}finally{if(m)throw m.error}}return p};Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLmenclose=void 0;var s=_(10475),a=_(5028),t=v(_(33269)),r=_(78658),e=_(21108);function i(l,u){return Math.atan2(l,u).toFixed(3).replace(/\.?0+$/,"")}var o=i(t.ARROWDX,t.ARROWY),n=function(l){function u(){return null!==l&&l.apply(this,arguments)||this}return C(u,l),u.prototype.toCHTML=function(h){var c,f,p,m,x=this.adaptor,w=this.standardCHTMLnode(h),j=x.append(w,this.html("mjx-box"));this.renderChild?this.renderChild(this,j):this.childNodes[0].toCHTML(j);try{for(var S=y(Object.keys(this.notations)),T=S.next();!T.done;T=S.next()){var O=T.value,B=this.notations[O];!B.renderChild&&B.renderer(this,j)}}catch(k){c={error:k}}finally{try{T&&!T.done&&(f=S.return)&&f.call(S)}finally{if(c)throw c.error}}var A=this.getPadding();try{for(var H=y(t.sideNames),P=H.next();!P.done;P=H.next()){var W=P.value,N=t.sideIndex[W];A[N]>0&&x.setStyle(j,"padding-"+W,this.em(A[N]))}}catch(k){p={error:k}}finally{try{P&&!P.done&&(m=H.return)&&m.call(H)}finally{if(p)throw p.error}}},u.prototype.arrow=function(h,c,f,p,m){void 0===p&&(p=""),void 0===m&&(m=0);var x=this.getBBox().w,w={width:this.em(h)};x!==h&&(w.left=this.em((x-h)/2)),c&&(w.transform="rotate("+this.fixed(c)+"rad)");var j=this.html("mjx-arrow",{style:w},[this.html("mjx-aline"),this.html("mjx-rthead"),this.html("mjx-rbhead")]);return f&&(this.adaptor.append(j,this.html("mjx-lthead")),this.adaptor.append(j,this.html("mjx-lbhead")),this.adaptor.setAttribute(j,"double","true")),this.adjustArrow(j,f),this.moveArrow(j,p,m),j},u.prototype.adjustArrow=function(h,c){var f=this,p=this.thickness,m=this.arrowhead;if(m.x!==t.ARROWX||m.y!==t.ARROWY||m.dx!==t.ARROWDX||p!==t.THICKNESS){var x=d([p*m.x,p*m.y].map(function(W){return f.em(W)}),2),w=x[0],j=x[1],S=i(m.dx,m.y),T=d(this.adaptor.childNodes(h),5),O=T[0],B=T[1],A=T[2],H=T[3],P=T[4];this.adjustHead(B,[j,"0","1px",w],S),this.adjustHead(A,["1px","0",j,w],"-"+S),this.adjustHead(H,[j,w,"1px","0"],"-"+S),this.adjustHead(P,["1px",w,j,"0"],S),this.adjustLine(O,p,m.x,c)}},u.prototype.adjustHead=function(h,c,f){h&&(this.adaptor.setStyle(h,"border-width",c.join(" ")),this.adaptor.setStyle(h,"transform","skewX("+f+"rad)"))},u.prototype.adjustLine=function(h,c,f,p){this.adaptor.setStyle(h,"borderTop",this.em(c)+" solid"),this.adaptor.setStyle(h,"top",this.em(-c/2)),this.adaptor.setStyle(h,"right",this.em(c*(f-1))),p&&this.adaptor.setStyle(h,"left",this.em(c*(f-1)))},u.prototype.moveArrow=function(h,c,f){if(f){var p=this.adaptor.getStyle(h,"transform");this.adaptor.setStyle(h,"transform","translate".concat(c,"(").concat(this.em(-f),")").concat(p?" "+p:""))}},u.prototype.adjustBorder=function(h){return this.thickness!==t.THICKNESS&&this.adaptor.setStyle(h,"borderWidth",this.em(this.thickness)),h},u.prototype.adjustThickness=function(h){return this.thickness!==t.THICKNESS&&this.adaptor.setStyle(h,"strokeWidth",this.fixed(this.thickness)),h},u.prototype.fixed=function(h,c){return void 0===c&&(c=3),Math.abs(h)<6e-4?"0":h.toFixed(c).replace(/\.?0+$/,"")},u.prototype.em=function(h){return l.prototype.em.call(this,h)},u.kind=r.MmlMenclose.prototype.kind,u.styles={"mjx-menclose":{position:"relative"},"mjx-menclose > mjx-dstrike":{display:"inline-block",left:0,top:0,position:"absolute","border-top":t.SOLID,"transform-origin":"top left"},"mjx-menclose > mjx-ustrike":{display:"inline-block",left:0,bottom:0,position:"absolute","border-top":t.SOLID,"transform-origin":"bottom left"},"mjx-menclose > mjx-hstrike":{"border-top":t.SOLID,position:"absolute",left:0,right:0,bottom:"50%",transform:"translateY("+(0,e.em)(t.THICKNESS/2)+")"},"mjx-menclose > mjx-vstrike":{"border-left":t.SOLID,position:"absolute",top:0,bottom:0,right:"50%",transform:"translateX("+(0,e.em)(t.THICKNESS/2)+")"},"mjx-menclose > mjx-rbox":{position:"absolute",top:0,bottom:0,right:0,left:0,border:t.SOLID,"border-radius":(0,e.em)(t.THICKNESS+t.PADDING)},"mjx-menclose > mjx-cbox":{position:"absolute",top:0,bottom:0,right:0,left:0,border:t.SOLID,"border-radius":"50%"},"mjx-menclose > mjx-arrow":{position:"absolute",left:0,bottom:"50%",height:0,width:0},"mjx-menclose > mjx-arrow > *":{display:"block",position:"absolute","transform-origin":"bottom","border-left":(0,e.em)(t.THICKNESS*t.ARROWX)+" solid","border-right":0,"box-sizing":"border-box"},"mjx-menclose > mjx-arrow > mjx-aline":{left:0,top:(0,e.em)(-t.THICKNESS/2),right:(0,e.em)(t.THICKNESS*(t.ARROWX-1)),height:0,"border-top":(0,e.em)(t.THICKNESS)+" solid","border-left":0},"mjx-menclose > mjx-arrow[double] > mjx-aline":{left:(0,e.em)(t.THICKNESS*(t.ARROWX-1)),height:0},"mjx-menclose > mjx-arrow > mjx-rthead":{transform:"skewX("+o+"rad)",right:0,bottom:"-1px","border-bottom":"1px solid transparent","border-top":(0,e.em)(t.THICKNESS*t.ARROWY)+" solid transparent"},"mjx-menclose > mjx-arrow > mjx-rbhead":{transform:"skewX(-"+o+"rad)","transform-origin":"top",right:0,top:"-1px","border-top":"1px solid transparent","border-bottom":(0,e.em)(t.THICKNESS*t.ARROWY)+" solid transparent"},"mjx-menclose > mjx-arrow > mjx-lthead":{transform:"skewX(-"+o+"rad)",left:0,bottom:"-1px","border-left":0,"border-right":(0,e.em)(t.THICKNESS*t.ARROWX)+" solid","border-bottom":"1px solid transparent","border-top":(0,e.em)(t.THICKNESS*t.ARROWY)+" solid transparent"},"mjx-menclose > mjx-arrow > mjx-lbhead":{transform:"skewX("+o+"rad)","transform-origin":"top",left:0,top:"-1px","border-left":0,"border-right":(0,e.em)(t.THICKNESS*t.ARROWX)+" solid","border-top":"1px solid transparent","border-bottom":(0,e.em)(t.THICKNESS*t.ARROWY)+" solid transparent"},"mjx-menclose > dbox":{position:"absolute",top:0,bottom:0,left:(0,e.em)(-1.5*t.PADDING),width:(0,e.em)(3*t.PADDING),border:(0,e.em)(t.THICKNESS)+" solid","border-radius":"50%","clip-path":"inset(0 0 0 "+(0,e.em)(1.5*t.PADDING)+")","box-sizing":"border-box"}},u.notations=new Map([t.Border("top"),t.Border("right"),t.Border("bottom"),t.Border("left"),t.Border2("actuarial","top","right"),t.Border2("madruwb","bottom","right"),t.DiagonalStrike("up",1),t.DiagonalStrike("down",-1),["horizontalstrike",{renderer:t.RenderElement("hstrike","Y"),bbox:function(h){return[0,h.padding,0,h.padding]}}],["verticalstrike",{renderer:t.RenderElement("vstrike","X"),bbox:function(h){return[h.padding,0,h.padding,0]}}],["box",{renderer:function(h,c){h.adaptor.setStyle(c,"border",h.em(h.thickness)+" solid")},bbox:t.fullBBox,border:t.fullBorder,remove:"left right top bottom"}],["roundedbox",{renderer:t.RenderElement("rbox"),bbox:t.fullBBox}],["circle",{renderer:t.RenderElement("cbox"),bbox:t.fullBBox}],["phasorangle",{renderer:function(h,c){var f=h.getBBox(),p=f.h,m=f.d,x=d(h.getArgMod(1.75*h.padding,p+m),2),w=x[0],j=x[1],S=h.thickness*Math.sin(w)*.9;h.adaptor.setStyle(c,"border-bottom",h.em(h.thickness)+" solid");var T=h.adjustBorder(h.html("mjx-ustrike",{style:{width:h.em(j),transform:"translateX("+h.em(S)+") rotate("+h.fixed(-w)+"rad)"}}));h.adaptor.append(h.chtml,T)},bbox:function(h){var c=h.padding/2,f=h.thickness;return[2*c,c,c+f,3*c+f]},border:function(h){return[0,0,h.thickness,0]},remove:"bottom"}],t.Arrow("up"),t.Arrow("down"),t.Arrow("left"),t.Arrow("right"),t.Arrow("updown"),t.Arrow("leftright"),t.DiagonalArrow("updiagonal"),t.DiagonalArrow("northeast"),t.DiagonalArrow("southeast"),t.DiagonalArrow("northwest"),t.DiagonalArrow("southwest"),t.DiagonalArrow("northeastsouthwest"),t.DiagonalArrow("northwestsoutheast"),["longdiv",{renderer:function(h,c){var f=h.adaptor;f.setStyle(c,"border-top",h.em(h.thickness)+" solid");var p=f.append(h.chtml,h.html("dbox")),m=h.thickness,x=h.padding;m!==t.THICKNESS&&f.setStyle(p,"border-width",h.em(m)),x!==t.PADDING&&(f.setStyle(p,"left",h.em(-1.5*x)),f.setStyle(p,"width",h.em(3*x)),f.setStyle(p,"clip-path","inset(0 0 0 "+h.em(1.5*x)+")"))},bbox:function(h){var c=h.padding,f=h.thickness;return[c+f,c,c,2*c+f/2]}}],["radical",{renderer:function(h,c){h.msqrt.toCHTML(c);var f=h.sqrtTRBL();h.adaptor.setStyle(h.msqrt.chtml,"margin",f.map(function(p){return h.em(-p)}).join(" "))},init:function(h){h.msqrt=h.createMsqrt(h.childNodes[0])},bbox:function(h){return h.sqrtTRBL()},renderChild:!0}]]),u}((0,a.CommonMencloseMixin)(s.CHTMLWrapper));g.CHTMLmenclose=n},3535:function(L,g,_){var d,C=this&&this.__extends||(d=function(s,a){return(d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(t[e]=r[e])})(s,a)},function(s,a){if("function"!=typeof a&&null!==a)throw new TypeError("Class extends value "+String(a)+" is not a constructor or null");function t(){this.constructor=s}d(s,a),s.prototype=null===a?Object.create(a):(t.prototype=a.prototype,new t)});Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLmfenced=void 0;var b=_(10475),M=_(56427),v=_(53691),y=function(d){function s(){return null!==d&&d.apply(this,arguments)||this}return C(s,d),s.prototype.toCHTML=function(a){var t=this.standardCHTMLnode(a);this.mrow.toCHTML(t)},s.kind=v.MmlMfenced.prototype.kind,s}((0,M.CommonMfencedMixin)(b.CHTMLWrapper));g.CHTMLmfenced=y},68657:function(L,g,_){var s,C=this&&this.__extends||(s=function(a,t){return(s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,e){r.__proto__=e}||function(r,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(r[i]=e[i])})(a,t)},function(a,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=a}s(a,t),a.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),b=this&&this.__assign||function(){return b=Object.assign||function(s){for(var a,t=1,r=arguments.length;t<r;t++)for(var e in a=arguments[t])Object.prototype.hasOwnProperty.call(a,e)&&(s[e]=a[e]);return s},b.apply(this,arguments)};Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLmfrac=void 0;var M=_(10475),v=_(1778),y=_(37170),d=function(s){function a(){return null!==s&&s.apply(this,arguments)||this}return C(a,s),a.prototype.toCHTML=function(t){this.standardCHTMLnode(t);var r=this.node.attributes.getList("linethickness","bevelled"),e=r.linethickness,i=r.bevelled,o=this.isDisplay();if(i)this.makeBevelled(o);else{var n=this.length2em(String(e),.06);0===n?this.makeAtop(o):this.makeFraction(o,n)}},a.prototype.makeFraction=function(t,r){var H,P,e=this.node.attributes.getList("numalign","denomalign"),i=e.numalign,o=e.denomalign,l=t?{type:"d"}:{},u=this.node.getProperty("withDelims")?b(b({},l),{delims:"true"}):b({},l),h="center"!==i?{align:i}:{},c="center"!==o?{align:o}:{},f=b({},l),p=b({},l),m=this.font.params;if(.06!==r){var x=m.axis_height,w=this.em(r),j=this.getTUV(t,r),S=j.T,T=j.u,O=j.v,B=(t?this.em(3*r):w)+" -.1em";l.style={height:w,"border-top":w+" solid",margin:B};var A=this.em(Math.max(0,T));p.style={height:A,"vertical-align":"-"+A},f.style={height:this.em(Math.max(0,O))},u.style={"vertical-align":this.em(x-S)}}this.adaptor.append(this.chtml,this.html("mjx-frac",u,[H=this.html("mjx-num",h,[this.html("mjx-nstrut",p)]),this.html("mjx-dbox",{},[this.html("mjx-dtable",{},[this.html("mjx-line",l),this.html("mjx-row",{},[P=this.html("mjx-den",c,[this.html("mjx-dstrut",f)])])])])])),this.childNodes[0].toCHTML(H),this.childNodes[1].toCHTML(P)},a.prototype.makeAtop=function(t){var m,x,r=this.node.attributes.getList("numalign","denomalign"),e=r.numalign,i=r.denomalign,n=t?{type:"d",atop:!0}:{atop:!0},l=this.node.getProperty("withDelims")?b(b({},n),{delims:!0}):b({},n),u="center"!==e?{align:e}:{},h="center"!==i?{align:i}:{},c=this.getUVQ(t),f=c.v,p=c.q;u.style={"padding-bottom":this.em(p)},l.style={"vertical-align":this.em(-f)},this.adaptor.append(this.chtml,this.html("mjx-frac",l,[m=this.html("mjx-num",u),x=this.html("mjx-den",h)])),this.childNodes[0].toCHTML(m),this.childNodes[1].toCHTML(x)},a.prototype.makeBevelled=function(t){var r=this.adaptor;r.setAttribute(this.chtml,"bevelled","ture");var e=r.append(this.chtml,this.html("mjx-num"));this.childNodes[0].toCHTML(e),this.bevel.toCHTML(this.chtml);var i=r.append(this.chtml,this.html("mjx-den"));this.childNodes[1].toCHTML(i);var o=this.getBevelData(t),n=o.u,l=o.v,u=o.delta,h=o.nbox,c=o.dbox;n&&r.setStyle(e,"verticalAlign",this.em(n/h.scale)),l&&r.setStyle(i,"verticalAlign",this.em(l/c.scale));var f=this.em(-u/2);r.setStyle(this.bevel.chtml,"marginLeft",f),r.setStyle(this.bevel.chtml,"marginRight",f)},a.kind=y.MmlMfrac.prototype.kind,a.styles={"mjx-frac":{display:"inline-block","vertical-align":"0.17em",padding:"0 .22em"},'mjx-frac[type="d"]':{"vertical-align":".04em"},"mjx-frac[delims]":{padding:"0 .1em"},"mjx-frac[atop]":{padding:"0 .12em"},"mjx-frac[atop][delims]":{padding:"0"},"mjx-dtable":{display:"inline-table",width:"100%"},"mjx-dtable > *":{"font-size":"2000%"},"mjx-dbox":{display:"block","font-size":"5%"},"mjx-num":{display:"block","text-align":"center"},"mjx-den":{display:"block","text-align":"center"},"mjx-mfrac[bevelled] > mjx-num":{display:"inline-block"},"mjx-mfrac[bevelled] > mjx-den":{display:"inline-block"},'mjx-den[align="right"], mjx-num[align="right"]':{"text-align":"right"},'mjx-den[align="left"], mjx-num[align="left"]':{"text-align":"left"},"mjx-nstrut":{display:"inline-block",height:".054em",width:0,"vertical-align":"-.054em"},'mjx-nstrut[type="d"]':{height:".217em","vertical-align":"-.217em"},"mjx-dstrut":{display:"inline-block",height:".505em",width:0},'mjx-dstrut[type="d"]':{height:".726em"},"mjx-line":{display:"block","box-sizing":"border-box","min-height":"1px",height:".06em","border-top":".06em solid",margin:".06em -.1em",overflow:"hidden"},'mjx-line[type="d"]':{margin:".18em -.1em"}},a}((0,v.CommonMfracMixin)(M.CHTMLWrapper));g.CHTMLmfrac=d},37453:function(L,g,_){var d,C=this&&this.__extends||(d=function(s,a){return(d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(t[e]=r[e])})(s,a)},function(s,a){if("function"!=typeof a&&null!==a)throw new TypeError("Class extends value "+String(a)+" is not a constructor or null");function t(){this.constructor=s}d(s,a),s.prototype=null===a?Object.create(a):(t.prototype=a.prototype,new t)});Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLmglyph=void 0;var b=_(10475),M=_(74682),v=_(45628),y=function(d){function s(){return null!==d&&d.apply(this,arguments)||this}return C(s,d),s.prototype.toCHTML=function(a){var t=this.standardCHTMLnode(a);if(this.charWrapper)this.charWrapper.toCHTML(t);else{var r=this.node.attributes.getList("src","alt"),e=r.src,i=r.alt,o={width:this.em(this.width),height:this.em(this.height)};this.valign&&(o.verticalAlign=this.em(this.valign));var n=this.html("img",{src:e,style:o,alt:i,title:i});this.adaptor.append(t,n)}},s.kind=v.MmlMglyph.prototype.kind,s.styles={"mjx-mglyph > img":{display:"inline-block",border:0,padding:0}},s}((0,M.CommonMglyphMixin)(b.CHTMLWrapper));g.CHTMLmglyph=y},80237:function(L,g,_){var d,C=this&&this.__extends||(d=function(s,a){return(d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(t[e]=r[e])})(s,a)},function(s,a){if("function"!=typeof a&&null!==a)throw new TypeError("Class extends value "+String(a)+" is not a constructor or null");function t(){this.constructor=s}d(s,a),s.prototype=null===a?Object.create(a):(t.prototype=a.prototype,new t)});Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLmi=void 0;var b=_(10475),M=_(49445),v=_(2862),y=function(d){function s(){return null!==d&&d.apply(this,arguments)||this}return C(s,d),s.kind=v.MmlMi.prototype.kind,s}((0,M.CommonMiMixin)(b.CHTMLWrapper));g.CHTMLmi=y},37801:function(L,g,_){var a,C=this&&this.__extends||(a=function(t,r){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,i){e.__proto__=i}||function(e,i){for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(e[o]=i[o])})(t,r)},function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function e(){this.constructor=t}a(t,r),t.prototype=null===r?Object.create(r):(e.prototype=r.prototype,new e)}),b=this&&this.__read||function(a,t){var r="function"==typeof Symbol&&a[Symbol.iterator];if(!r)return a;var i,n,e=r.call(a),o=[];try{for(;(void 0===t||t-- >0)&&!(i=e.next()).done;)o.push(i.value)}catch(l){n={error:l}}finally{try{i&&!i.done&&(r=e.return)&&r.call(e)}finally{if(n)throw n.error}}return o};Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLmmultiscripts=void 0;var M=_(31201),v=_(49079),y=_(93018),d=_(74267),s=function(a){function t(){return null!==a&&a.apply(this,arguments)||this}return C(t,a),t.prototype.toCHTML=function(r){var e=this.standardCHTMLnode(r),i=this.scriptData,o=this.node.getProperty("scriptalign")||"right left",n=b((0,d.split)(o+" "+o),2),l=n[0],u=n[1],h=this.combinePrePost(i.sub,i.psub),c=this.combinePrePost(i.sup,i.psup),f=b(this.getUVQ(h,c),2),p=f[0],m=f[1];if(i.numPrescripts){var x=this.addScripts(p,-m,!0,i.psub,i.psup,this.firstPrescript,i.numPrescripts);"right"!==l&&this.adaptor.setAttribute(x,"script-align",l)}if(this.childNodes[0].toCHTML(e),i.numScripts){x=this.addScripts(p,-m,!1,i.sub,i.sup,1,i.numScripts);"left"!==u&&this.adaptor.setAttribute(x,"script-align",u)}},t.prototype.addScripts=function(r,e,i,o,n,l,u){for(var h=this.adaptor,c=r-n.d+(e-o.h),f=r<0&&0===e?o.h+r:r,p=c>0?{style:{height:this.em(c)}}:{},m=f?{style:{"vertical-align":this.em(f)}}:{},x=this.html("mjx-row"),w=this.html("mjx-row",p),j=this.html("mjx-row"),S="mjx-"+(i?"pre":"")+"scripts",T=l+2*u;l<T;)this.childNodes[l++].toCHTML(h.append(j,this.html("mjx-cell"))),this.childNodes[l++].toCHTML(h.append(x,this.html("mjx-cell")));return h.append(this.chtml,this.html(S,m,[x,w,j]))},t.kind=y.MmlMmultiscripts.prototype.kind,t.styles={"mjx-prescripts":{display:"inline-table","padding-left":".05em"},"mjx-scripts":{display:"inline-table","padding-right":".05em"},"mjx-prescripts > mjx-row > mjx-cell":{"text-align":"right"},'[script-align="left"] > mjx-row > mjx-cell':{"text-align":"left"},'[script-align="center"] > mjx-row > mjx-cell':{"text-align":"center"},'[script-align="right"] > mjx-row > mjx-cell':{"text-align":"right"}},t}((0,v.CommonMmultiscriptsMixin)(M.CHTMLmsubsup));g.CHTMLmmultiscripts=s},62844:function(L,g,_){var d,C=this&&this.__extends||(d=function(s,a){return(d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(t[e]=r[e])})(s,a)},function(s,a){if("function"!=typeof a&&null!==a)throw new TypeError("Class extends value "+String(a)+" is not a constructor or null");function t(){this.constructor=s}d(s,a),s.prototype=null===a?Object.create(a):(t.prototype=a.prototype,new t)});Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLmn=void 0;var b=_(10475),M=_(50768),v=_(26974),y=function(d){function s(){return null!==d&&d.apply(this,arguments)||this}return C(s,d),s.kind=v.MmlMn.prototype.kind,s}((0,M.CommonMnMixin)(b.CHTMLWrapper));g.CHTMLmn=y},16327:function(L,g,_){var s,C=this&&this.__extends||(s=function(a,t){return(s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,e){r.__proto__=e}||function(r,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(r[i]=e[i])})(a,t)},function(a,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=a}s(a,t),a.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),b=this&&this.__values||function(s){var a="function"==typeof Symbol&&Symbol.iterator,t=a&&s[a],r=0;if(t)return t.call(s);if(s&&"number"==typeof s.length)return{next:function(){return s&&r>=s.length&&(s=void 0),{value:s&&s[r++],done:!s}}};throw new TypeError(a?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLmo=void 0;var M=_(10475),v=_(3362),y=_(4902),d=function(s){function a(){return null!==s&&s.apply(this,arguments)||this}return C(a,s),a.prototype.toCHTML=function(t){var r,e,i=this.node.attributes,o=i.get("symmetric")&&2!==this.stretch.dir,n=0!==this.stretch.dir;n&&null===this.size&&this.getStretchedVariant([]);var l=this.standardCHTMLnode(t);if(n&&this.size<0)this.stretchHTML(l);else{if(o||i.get("largeop")){var u=this.em(this.getCenterOffset());"0"!==u&&this.adaptor.setStyle(l,"verticalAlign",u)}this.node.getProperty("mathaccent")&&(this.adaptor.setStyle(l,"width","0"),this.adaptor.setStyle(l,"margin-left",this.em(this.getAccentOffset())));try{for(var h=b(this.childNodes),c=h.next();!c.done;c=h.next()){c.value.toCHTML(l)}}catch(p){r={error:p}}finally{try{c&&!c.done&&(e=h.return)&&e.call(h)}finally{if(r)throw r.error}}}},a.prototype.stretchHTML=function(t){var r=this.getText().codePointAt(0);this.font.delimUsage.add(r),this.childNodes[0].markUsed();var e=this.stretch,i=e.stretch,o=[];i[0]&&o.push(this.html("mjx-beg",{},[this.html("mjx-c")])),o.push(this.html("mjx-ext",{},[this.html("mjx-c")])),4===i.length&&o.push(this.html("mjx-mid",{},[this.html("mjx-c")]),this.html("mjx-ext",{},[this.html("mjx-c")])),i[2]&&o.push(this.html("mjx-end",{},[this.html("mjx-c")]));var n={},l=this.bbox,u=l.h,h=l.d,c=l.w;1===e.dir?(o.push(this.html("mjx-mark")),n.height=this.em(u+h),n.verticalAlign=this.em(-h)):n.width=this.em(c);var f=v.DirectionVH[e.dir],p={class:this.char(e.c||r),style:n},m=this.html("mjx-stretchy-"+f,p,o);this.adaptor.append(t,m)},a.kind=y.MmlMo.prototype.kind,a.styles={"mjx-stretchy-h":{display:"inline-table",width:"100%"},"mjx-stretchy-h > *":{display:"table-cell",width:0},"mjx-stretchy-h > * > mjx-c":{display:"inline-block",transform:"scalex(1.0000001)"},"mjx-stretchy-h > * > mjx-c::before":{display:"inline-block",width:"initial"},"mjx-stretchy-h > mjx-ext":{"/* IE */ overflow":"hidden","/* others */ overflow":"clip visible",width:"100%"},"mjx-stretchy-h > mjx-ext > mjx-c::before":{transform:"scalex(500)"},"mjx-stretchy-h > mjx-ext > mjx-c":{width:0},"mjx-stretchy-h > mjx-beg > mjx-c":{"margin-right":"-.1em"},"mjx-stretchy-h > mjx-end > mjx-c":{"margin-left":"-.1em"},"mjx-stretchy-v":{display:"inline-block"},"mjx-stretchy-v > *":{display:"block"},"mjx-stretchy-v > mjx-beg":{height:0},"mjx-stretchy-v > mjx-end > mjx-c":{display:"block"},"mjx-stretchy-v > * > mjx-c":{transform:"scaley(1.0000001)","transform-origin":"left center",overflow:"hidden"},"mjx-stretchy-v > mjx-ext":{display:"block",height:"100%","box-sizing":"border-box",border:"0px solid transparent","/* IE */ overflow":"hidden","/* others */ overflow":"visible clip"},"mjx-stretchy-v > mjx-ext > mjx-c::before":{width:"initial","box-sizing":"border-box"},"mjx-stretchy-v > mjx-ext > mjx-c":{transform:"scaleY(500) translateY(.075em)",overflow:"visible"},"mjx-mark":{display:"inline-block",height:"0px"}},a}((0,v.CommonMoMixin)(M.CHTMLWrapper));g.CHTMLmo=d},8011:function(L,g,_){var a,C=this&&this.__extends||(a=function(t,r){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,i){e.__proto__=i}||function(e,i){for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(e[o]=i[o])})(t,r)},function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function e(){this.constructor=t}a(t,r),t.prototype=null===r?Object.create(r):(e.prototype=r.prototype,new e)}),b=this&&this.__read||function(a,t){var r="function"==typeof Symbol&&a[Symbol.iterator];if(!r)return a;var i,n,e=r.call(a),o=[];try{for(;(void 0===t||t-- >0)&&!(i=e.next()).done;)o.push(i.value)}catch(l){n={error:l}}finally{try{i&&!i.done&&(r=e.return)&&r.call(e)}finally{if(n)throw n.error}}return o},M=this&&this.__values||function(a){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&a[t],e=0;if(r)return r.call(a);if(a&&"number"==typeof a.length)return{next:function(){return a&&e>=a.length&&(a=void 0),{value:a&&a[e++],done:!a}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLmpadded=void 0;var v=_(10475),y=_(16819),d=_(27374),s=function(a){function t(){return null!==a&&a.apply(this,arguments)||this}return C(t,a),t.prototype.toCHTML=function(r){var e,i,o=this.standardCHTMLnode(r),n=[],l={},u=b(this.getDimens(),9),h=u[2],c=u[3],f=u[4],p=u[5],m=u[6],x=u[7],w=u[8];if(p&&(l.width=this.em(h+p)),(c||f)&&(l.margin=this.em(c)+" 0 "+this.em(f)),m+w||x){l.position="relative";var j=this.html("mjx-rbox",{style:{left:this.em(m+w),top:this.em(-x),"max-width":l.width}});m+w&&this.childNodes[0].getBBox().pwidth&&(this.adaptor.setAttribute(j,"width","full"),this.adaptor.setStyle(j,"left",this.em(m))),n.push(j)}o=this.adaptor.append(o,this.html("mjx-block",{style:l},n));try{for(var S=M(this.childNodes),T=S.next();!T.done;T=S.next()){T.value.toCHTML(n[0]||o)}}catch(B){e={error:B}}finally{try{T&&!T.done&&(i=S.return)&&i.call(S)}finally{if(e)throw e.error}}},t.kind=d.MmlMpadded.prototype.kind,t.styles={"mjx-mpadded":{display:"inline-block"},"mjx-rbox":{display:"inline-block",position:"relative"}},t}((0,y.CommonMpaddedMixin)(v.CHTMLWrapper));g.CHTMLmpadded=s},29255:function(L,g,_){var s,C=this&&this.__extends||(s=function(a,t){return(s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,e){r.__proto__=e}||function(r,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(r[i]=e[i])})(a,t)},function(a,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=a}s(a,t),a.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),b=this&&this.__read||function(s,a){var t="function"==typeof Symbol&&s[Symbol.iterator];if(!t)return s;var e,o,r=t.call(s),i=[];try{for(;(void 0===a||a-- >0)&&!(e=r.next()).done;)i.push(e.value)}catch(n){o={error:n}}finally{try{e&&!e.done&&(t=r.return)&&t.call(r)}finally{if(o)throw o.error}}return i};Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLmroot=void 0;var M=_(82512),v=_(8242),y=_(12773),d=function(s){function a(){return null!==s&&s.apply(this,arguments)||this}return C(a,s),a.prototype.addRoot=function(t,r,e,i){r.toCHTML(t);var o=b(this.getRootDimens(e,i),3),n=o[0],l=o[1],u=o[2];this.adaptor.setStyle(t,"verticalAlign",this.em(l)),this.adaptor.setStyle(t,"width",this.em(n)),u&&this.adaptor.setStyle(this.adaptor.firstChild(t),"paddingLeft",this.em(u))},a.kind=y.MmlMroot.prototype.kind,a}((0,v.CommonMrootMixin)(M.CHTMLmsqrt));g.CHTMLmroot=d},74239:function(L,g,_){var t,C=this&&this.__extends||(t=function(r,e){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,o){i.__proto__=o}||function(i,o){for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(i[n]=o[n])})(r,e)},function(r,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),b=this&&this.__values||function(t){var r="function"==typeof Symbol&&Symbol.iterator,e=r&&t[r],i=0;if(e)return e.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&i>=t.length&&(t=void 0),{value:t&&t[i++],done:!t}}};throw new TypeError(r?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLinferredMrow=g.CHTMLmrow=void 0;var M=_(10475),v=_(30887),y=_(30887),d=_(84799),s=function(t){function r(){return null!==t&&t.apply(this,arguments)||this}return C(r,t),r.prototype.toCHTML=function(e){var i,o,n=this.node.isInferred?this.chtml=e:this.standardCHTMLnode(e),l=!1;try{for(var u=b(this.childNodes),h=u.next();!h.done;h=u.next()){var c=h.value;c.toCHTML(n),c.bbox.w<0&&(l=!0)}}catch(p){i={error:p}}finally{try{h&&!h.done&&(o=u.return)&&o.call(u)}finally{if(i)throw i.error}}if(l){var f=this.getBBox().w;f&&(this.adaptor.setStyle(n,"width",this.em(Math.max(0,f))),f<0&&this.adaptor.setStyle(n,"marginRight",this.em(f)))}},r.kind=d.MmlMrow.prototype.kind,r}((0,v.CommonMrowMixin)(M.CHTMLWrapper));g.CHTMLmrow=s;var a=function(t){function r(){return null!==t&&t.apply(this,arguments)||this}return C(r,t),r.kind=d.MmlInferredMrow.prototype.kind,r}((0,y.CommonInferredMrowMixin)(s));g.CHTMLinferredMrow=a},27102:function(L,g,_){var d,C=this&&this.__extends||(d=function(s,a){return(d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(t[e]=r[e])})(s,a)},function(s,a){if("function"!=typeof a&&null!==a)throw new TypeError("Class extends value "+String(a)+" is not a constructor or null");function t(){this.constructor=s}d(s,a),s.prototype=null===a?Object.create(a):(t.prototype=a.prototype,new t)});Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLms=void 0;var b=_(10475),M=_(93952),v=_(92294),y=function(d){function s(){return null!==d&&d.apply(this,arguments)||this}return C(s,d),s.kind=v.MmlMs.prototype.kind,s}((0,M.CommonMsMixin)(b.CHTMLWrapper));g.CHTMLms=y},39786:function(L,g,_){var d,C=this&&this.__extends||(d=function(s,a){return(d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(t[e]=r[e])})(s,a)},function(s,a){if("function"!=typeof a&&null!==a)throw new TypeError("Class extends value "+String(a)+" is not a constructor or null");function t(){this.constructor=s}d(s,a),s.prototype=null===a?Object.create(a):(t.prototype=a.prototype,new t)});Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLmspace=void 0;var b=_(10475),M=_(49697),v=_(88143),y=function(d){function s(){return null!==d&&d.apply(this,arguments)||this}return C(s,d),s.prototype.toCHTML=function(a){var t=this.standardCHTMLnode(a),r=this.getBBox(),e=r.w,i=r.h,o=r.d;e<0&&(this.adaptor.setStyle(t,"marginRight",this.em(e)),e=0),e&&this.adaptor.setStyle(t,"width",this.em(e)),(i=Math.max(0,i+o))&&this.adaptor.setStyle(t,"height",this.em(Math.max(0,i))),o&&this.adaptor.setStyle(t,"verticalAlign",this.em(-o))},s.kind=v.MmlMspace.prototype.kind,s}((0,M.CommonMspaceMixin)(b.CHTMLWrapper));g.CHTMLmspace=y},82512:function(L,g,_){var s,C=this&&this.__extends||(s=function(a,t){return(s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,e){r.__proto__=e}||function(r,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(r[i]=e[i])})(a,t)},function(a,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=a}s(a,t),a.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),b=this&&this.__read||function(s,a){var t="function"==typeof Symbol&&s[Symbol.iterator];if(!t)return s;var e,o,r=t.call(s),i=[];try{for(;(void 0===a||a-- >0)&&!(e=r.next()).done;)i.push(e.value)}catch(n){o={error:n}}finally{try{e&&!e.done&&(t=r.return)&&t.call(r)}finally{if(o)throw o.error}}return i};Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLmsqrt=void 0;var M=_(10475),v=_(72472),y=_(58730),d=function(s){function a(){return null!==s&&s.apply(this,arguments)||this}return C(a,s),a.prototype.toCHTML=function(t){var f,p,m,x,r=this.childNodes[this.surd],e=this.childNodes[this.base],i=r.getBBox(),o=e.getOuterBBox(),l=b(this.getPQ(i),2)[1],u=this.font.params.rule_thickness,h=o.h+l+u,c=this.standardCHTMLnode(t);null!=this.root&&(m=this.adaptor.append(c,this.html("mjx-root")),x=this.childNodes[this.root]);var w=this.adaptor.append(c,this.html("mjx-sqrt",{},[f=this.html("mjx-surd"),p=this.html("mjx-box",{style:{paddingTop:this.em(l)}})]));this.addRoot(m,x,i,h),r.toCHTML(f),e.toCHTML(p),r.size<0&&this.adaptor.addClass(w,"mjx-tall")},a.prototype.addRoot=function(t,r,e,i){},a.kind=y.MmlMsqrt.prototype.kind,a.styles={"mjx-root":{display:"inline-block","white-space":"nowrap"},"mjx-surd":{display:"inline-block","vertical-align":"top"},"mjx-sqrt":{display:"inline-block","padding-top":".07em"},"mjx-sqrt > mjx-box":{"border-top":".07em solid"},"mjx-sqrt.mjx-tall > mjx-box":{"padding-left":".3em","margin-left":"-.3em"}},a}((0,v.CommonMsqrtMixin)(M.CHTMLWrapper));g.CHTMLmsqrt=d},31201:function(L,g,_){var e,C=this&&this.__extends||(e=function(i,o){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,l){n.__proto__=l}||function(n,l){for(var u in l)Object.prototype.hasOwnProperty.call(l,u)&&(n[u]=l[u])})(i,o)},function(i,o){if("function"!=typeof o&&null!==o)throw new TypeError("Class extends value "+String(o)+" is not a constructor or null");function n(){this.constructor=i}e(i,o),i.prototype=null===o?Object.create(o):(n.prototype=o.prototype,new n)}),b=this&&this.__read||function(e,i){var o="function"==typeof Symbol&&e[Symbol.iterator];if(!o)return e;var l,h,n=o.call(e),u=[];try{for(;(void 0===i||i-- >0)&&!(l=n.next()).done;)u.push(l.value)}catch(c){h={error:c}}finally{try{l&&!l.done&&(o=n.return)&&o.call(n)}finally{if(h)throw h.error}}return u};Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLmsubsup=g.CHTMLmsup=g.CHTMLmsub=void 0;var M=_(80880),v=_(39934),y=_(39934),d=_(39934),s=_(37666),a=function(e){function i(){return null!==e&&e.apply(this,arguments)||this}return C(i,e),i.kind=s.MmlMsub.prototype.kind,i}((0,v.CommonMsubMixin)(M.CHTMLscriptbase));g.CHTMLmsub=a;var t=function(e){function i(){return null!==e&&e.apply(this,arguments)||this}return C(i,e),i.kind=s.MmlMsup.prototype.kind,i}((0,y.CommonMsupMixin)(M.CHTMLscriptbase));g.CHTMLmsup=t;var r=function(e){function i(){return null!==e&&e.apply(this,arguments)||this}return C(i,e),i.prototype.toCHTML=function(o){var n=this.adaptor,l=this.standardCHTMLnode(o),u=b([this.baseChild,this.supChild,this.subChild],3),h=u[0],c=u[1],f=u[2],p=b(this.getUVQ(),3),m=p[1],x=p[2],w={"vertical-align":this.em(m)};h.toCHTML(l);var j=n.append(l,this.html("mjx-script",{style:w}));c.toCHTML(j),n.append(j,this.html("mjx-spacer",{style:{"margin-top":this.em(x)}})),f.toCHTML(j);var S=this.getAdjustedIc();S&&n.setStyle(c.chtml,"marginLeft",this.em(S/c.bbox.rscale)),this.baseRemoveIc&&n.setStyle(j,"marginLeft",this.em(-this.baseIc))},i.kind=s.MmlMsubsup.prototype.kind,i.styles={"mjx-script":{display:"inline-block","padding-right":".05em","padding-left":".033em"},"mjx-script > mjx-spacer":{display:"block"}},i}((0,d.CommonMsubsupMixin)(M.CHTMLscriptbase));g.CHTMLmsubsup=r},64612:function(L,g,_){var t,C=this&&this.__extends||(t=function(r,e){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,o){i.__proto__=o}||function(i,o){for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(i[n]=o[n])})(r,e)},function(r,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),b=this&&this.__values||function(t){var r="function"==typeof Symbol&&Symbol.iterator,e=r&&t[r],i=0;if(e)return e.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&i>=t.length&&(t=void 0),{value:t&&t[i++],done:!t}}};throw new TypeError(r?"Object is not iterable.":"Symbol.iterator is not defined.")},M=this&&this.__read||function(t,r){var e="function"==typeof Symbol&&t[Symbol.iterator];if(!e)return t;var o,l,i=e.call(t),n=[];try{for(;(void 0===r||r-- >0)&&!(o=i.next()).done;)n.push(o.value)}catch(u){l={error:u}}finally{try{o&&!o.done&&(e=i.return)&&e.call(i)}finally{if(l)throw l.error}}return n};Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLmtable=void 0;var v=_(10475),y=_(5010),d=_(96305),s=_(74267),a=function(t){function r(e,i,o){void 0===o&&(o=null);var n=t.call(this,e,i,o)||this;return n.itable=n.html("mjx-itable"),n.labels=n.html("mjx-itable"),n}return C(r,t),r.prototype.getAlignShift=function(){var e=t.prototype.getAlignShift.call(this);return this.isTop||(e[1]=0),e},r.prototype.toCHTML=function(e){var i,o,n=this.standardCHTMLnode(e);this.adaptor.append(n,this.html("mjx-table",{},[this.itable]));try{for(var l=b(this.childNodes),u=l.next();!u.done;u=l.next()){u.value.toCHTML(this.itable)}}catch(c){i={error:c}}finally{try{u&&!u.done&&(o=l.return)&&o.call(l)}finally{if(i)throw i.error}}this.padRows(),this.handleColumnSpacing(),this.handleColumnLines(),this.handleColumnWidths(),this.handleRowSpacing(),this.handleRowLines(),this.handleRowHeights(),this.handleFrame(),this.handleWidth(),this.handleLabels(),this.handleAlign(),this.handleJustify(),this.shiftColor()},r.prototype.shiftColor=function(){var e=this.adaptor,i=e.getStyle(this.chtml,"backgroundColor");i&&(e.setStyle(this.chtml,"backgroundColor",""),e.setStyle(this.itable,"backgroundColor",i))},r.prototype.padRows=function(){var e,i,o=this.adaptor;try{for(var n=b(o.childNodes(this.itable)),l=n.next();!l.done;l=n.next())for(var u=l.value;o.childNodes(u).length<this.numCols;)o.append(u,this.html("mjx-mtd",{extra:!0}))}catch(h){e={error:h}}finally{try{l&&!l.done&&(i=n.return)&&i.call(n)}finally{if(e)throw e.error}}},r.prototype.handleColumnSpacing=function(){var e,i,o,n,l=this.childNodes[0]?1/this.childNodes[0].getBBox().rscale:1,u=this.getEmHalfSpacing(this.fSpace[0],this.cSpace,l),h=this.frame;try{for(var c=b(this.tableRows),f=c.next();!f.done;f=c.next()){var p=f.value,m=0;try{for(var x=(o=void 0,b(p.tableCells)),w=x.next();!w.done;w=x.next()){var j=w.value,S=u[m++],T=u[m],O=j?j.chtml:this.adaptor.childNodes(p.chtml)[m];(m>1&&"0.4em"!==S||h&&1===m)&&this.adaptor.setStyle(O,"paddingLeft",S),(m<this.numCols&&"0.4em"!==T||h&&m===this.numCols)&&this.adaptor.setStyle(O,"paddingRight",T)}}catch(B){o={error:B}}finally{try{w&&!w.done&&(n=x.return)&&n.call(x)}finally{if(o)throw o.error}}}}catch(B){e={error:B}}finally{try{f&&!f.done&&(i=c.return)&&i.call(c)}finally{if(e)throw e.error}}},r.prototype.handleColumnLines=function(){var e,i,o,n;if("none"!==this.node.attributes.get("columnlines")){var l=this.getColumnAttributes("columnlines");try{for(var u=b(this.childNodes),h=u.next();!h.done;h=u.next()){var c=h.value,f=0;try{for(var p=(o=void 0,b(this.adaptor.childNodes(c.chtml).slice(1))),m=p.next();!m.done;m=p.next()){var x=m.value,w=l[f++];"none"!==w&&this.adaptor.setStyle(x,"borderLeft",".07em "+w)}}catch(j){o={error:j}}finally{try{m&&!m.done&&(n=p.return)&&n.call(p)}finally{if(o)throw o.error}}}}catch(j){e={error:j}}finally{try{h&&!h.done&&(i=u.return)&&i.call(u)}finally{if(e)throw e.error}}}},r.prototype.handleColumnWidths=function(){var e,i,o,n;try{for(var l=b(this.childNodes),u=l.next();!u.done;u=l.next()){var h=u.value,c=0;try{for(var f=(o=void 0,b(this.adaptor.childNodes(h.chtml))),p=f.next();!p.done;p=f.next()){var m=p.value,x=this.cWidths[c++];if(null!==x){var w="number"==typeof x?this.em(x):x;this.adaptor.setStyle(m,"width",w),this.adaptor.setStyle(m,"maxWidth",w),this.adaptor.setStyle(m,"minWidth",w)}}}catch(j){o={error:j}}finally{try{p&&!p.done&&(n=f.return)&&n.call(f)}finally{if(o)throw o.error}}}}catch(j){e={error:j}}finally{try{u&&!u.done&&(i=l.return)&&i.call(l)}finally{if(e)throw e.error}}},r.prototype.handleRowSpacing=function(){var e,i,o,n,l=this.childNodes[0]?1/this.childNodes[0].getBBox().rscale:1,u=this.getEmHalfSpacing(this.fSpace[1],this.rSpace,l),h=this.frame,c=0;try{for(var f=b(this.childNodes),p=f.next();!p.done;p=f.next()){var m=p.value,x=u[c++],w=u[c];try{for(var j=(o=void 0,b(m.childNodes)),S=j.next();!S.done;S=j.next()){var T=S.value;(c>1&&"0.215em"!==x||h&&1===c)&&this.adaptor.setStyle(T.chtml,"paddingTop",x),(c<this.numRows&&"0.215em"!==w||h&&c===this.numRows)&&this.adaptor.setStyle(T.chtml,"paddingBottom",w)}}catch(O){o={error:O}}finally{try{S&&!S.done&&(n=j.return)&&n.call(j)}finally{if(o)throw o.error}}}}catch(O){e={error:O}}finally{try{p&&!p.done&&(i=f.return)&&i.call(f)}finally{if(e)throw e.error}}},r.prototype.handleRowLines=function(){var e,i,o,n;if("none"!==this.node.attributes.get("rowlines")){var l=this.getRowAttributes("rowlines"),u=0;try{for(var h=b(this.childNodes.slice(1)),c=h.next();!c.done;c=h.next()){var f=c.value,p=l[u++];if("none"!==p)try{for(var m=(o=void 0,b(this.adaptor.childNodes(f.chtml))),x=m.next();!x.done;x=m.next()){var w=x.value;this.adaptor.setStyle(w,"borderTop",".07em "+p)}}catch(j){o={error:j}}finally{try{x&&!x.done&&(n=m.return)&&n.call(m)}finally{if(o)throw o.error}}}}catch(j){e={error:j}}finally{try{c&&!c.done&&(i=h.return)&&i.call(h)}finally{if(e)throw e.error}}}},r.prototype.handleRowHeights=function(){this.node.attributes.get("equalrows")&&this.handleEqualRows()},r.prototype.handleEqualRows=function(){for(var e=this.getRowHalfSpacing(),i=this.getTableData(),o=i.H,n=i.D,l=i.NH,u=i.ND,h=this.getEqualRowHeight(),c=0;c<this.numRows;c++){var f=this.childNodes[c];this.setRowHeight(f,h+e[c]+e[c+1]+this.rLines[c]),h!==l[c]+u[c]&&this.setRowBaseline(f,h,(h-o[c]+n[c])/2)}},r.prototype.setRowHeight=function(e,i){this.adaptor.setStyle(e.chtml,"height",this.em(i))},r.prototype.setRowBaseline=function(e,i,o){var n,l,u=e.node.attributes.get("rowalign");try{for(var h=b(e.childNodes),c=h.next();!c.done;c=h.next()){var f=c.value;if(this.setCellBaseline(f,u,i,o))break}}catch(p){n={error:p}}finally{try{c&&!c.done&&(l=h.return)&&l.call(h)}finally{if(n)throw n.error}}},r.prototype.setCellBaseline=function(e,i,o,n){var l=e.node.attributes.get("rowalign");if("baseline"===l||"axis"===l){var u=this.adaptor,h=u.lastChild(e.chtml);u.setStyle(h,"height",this.em(o)),u.setStyle(h,"verticalAlign",this.em(-n));var c=e.parent;if(!(c.node.isKind("mlabeledtr")&&e===c.childNodes[0]||"baseline"!==i&&"axis"!==i))return!0}return!1},r.prototype.handleFrame=function(){this.frame&&this.fLine&&this.adaptor.setStyle(this.itable,"border",".07em "+this.node.attributes.get("frame"))},r.prototype.handleWidth=function(){var e=this.adaptor,i=this.getBBox(),o=i.w,n=i.L,l=i.R;e.setStyle(this.chtml,"minWidth",this.em(n+o+l));var u=this.node.attributes.get("width");if((0,s.isPercent)(u))e.setStyle(this.chtml,"width",""),e.setAttribute(this.chtml,"width","full");else if(!this.hasLabels){if("auto"===u)return;u=this.em(this.length2em(u)+2*this.fLine)}var h=e.firstChild(this.chtml);if(e.setStyle(h,"width",u),e.setStyle(h,"minWidth",this.em(o)),n||l){e.setStyle(this.chtml,"margin","");var c=this.node.attributes.get("data-width-includes-label")?"padding":"margin";n===l?e.setStyle(h,c,"0 "+this.em(l)):e.setStyle(h,c,"0 "+this.em(l)+" 0 "+this.em(n))}e.setAttribute(this.itable,"width","full")},r.prototype.handleAlign=function(){var e=M(this.getAlignmentRow(),2),i=e[0],o=e[1];if(null===o)"axis"!==i&&this.adaptor.setAttribute(this.chtml,"align",i);else{var n=this.getVerticalPosition(o,i);this.adaptor.setAttribute(this.chtml,"align","top"),this.adaptor.setStyle(this.chtml,"verticalAlign",this.em(n))}},r.prototype.handleJustify=function(){var e=this.getAlignShift()[0];"center"!==e&&this.adaptor.setAttribute(this.chtml,"justify",e)},r.prototype.handleLabels=function(){if(this.hasLabels){var e=this.labels,i=this.node.attributes,o=this.adaptor,n=i.get("side");o.setAttribute(this.chtml,"side",n),o.setAttribute(e,"align",n),o.setStyle(e,n,"0");var l=M(this.addLabelPadding(n),2),u=l[0],h=l[1];if(h){var c=o.firstChild(this.chtml);this.setIndent(c,u,h)}this.updateRowHeights(),this.addLabelSpacing()}},r.prototype.addLabelPadding=function(e){var i=M(this.getPadAlignShift(e),3),o=i[1],n=i[2],l={};if("right"===e&&!this.node.attributes.get("data-width-includes-label")){var u=this.node.attributes.get("width"),h=this.getBBox(),c=h.w,f=h.L,p=h.R;l.style={width:(0,s.isPercent)(u)?"calc("+u+" + "+this.em(f+p)+")":this.em(f+c+p)}}return this.adaptor.append(this.chtml,this.html("mjx-labels",l,[this.labels])),[o,n]},r.prototype.updateRowHeights=function(){for(var e=this.getTableData(),i=e.H,o=e.D,n=e.NH,l=e.ND,u=this.getRowHalfSpacing(),h=0;h<this.numRows;h++){var c=this.childNodes[h];this.setRowHeight(c,i[h]+o[h]+u[h]+u[h+1]+this.rLines[h]),i[h]!==n[h]||o[h]!==l[h]?this.setRowBaseline(c,i[h]+o[h],o[h]):c.node.isKind("mlabeledtr")&&this.setCellBaseline(c.childNodes[0],"",i[h]+o[h],o[h])}},r.prototype.addLabelSpacing=function(){for(var e=this.adaptor,i=this.node.attributes.get("equalrows"),o=this.getTableData(),n=o.H,l=o.D,u=i?this.getEqualRowHeight():0,h=this.getRowHalfSpacing(),c=this.fLine,f=e.firstChild(this.labels),p=0;p<this.numRows;p++){this.childNodes[p].node.isKind("mlabeledtr")?(c&&e.insert(this.html("mjx-mtr",{style:{height:this.em(c)}}),f),e.setStyle(f,"height",this.em((i?u:n[p]+l[p])+h[p]+h[p+1])),f=e.next(f),c=this.rLines[p]):c+=h[p]+(i?u:n[p]+l[p])+h[p+1]+this.rLines[p]}},r.kind=d.MmlMtable.prototype.kind,r.styles={"mjx-mtable":{"vertical-align":".25em","text-align":"center",position:"relative","box-sizing":"border-box","border-spacing":0,"border-collapse":"collapse"},'mjx-mstyle[size="s"] mjx-mtable':{"vertical-align":".354em"},"mjx-labels":{position:"absolute",left:0,top:0},"mjx-table":{display:"inline-block","vertical-align":"-.5ex","box-sizing":"border-box"},"mjx-table > mjx-itable":{"vertical-align":"middle","text-align":"left","box-sizing":"border-box"},"mjx-labels > mjx-itable":{position:"absolute",top:0},'mjx-mtable[justify="left"]':{"text-align":"left"},'mjx-mtable[justify="right"]':{"text-align":"right"},'mjx-mtable[justify="left"][side="left"]':{"padding-right":"0 ! important"},'mjx-mtable[justify="left"][side="right"]':{"padding-left":"0 ! important"},'mjx-mtable[justify="right"][side="left"]':{"padding-right":"0 ! important"},'mjx-mtable[justify="right"][side="right"]':{"padding-left":"0 ! important"},"mjx-mtable[align]":{"vertical-align":"baseline"},'mjx-mtable[align="top"] > mjx-table':{"vertical-align":"top"},'mjx-mtable[align="bottom"] > mjx-table':{"vertical-align":"bottom"},'mjx-mtable[side="right"] mjx-labels':{"min-width":"100%"}},r}((0,y.CommonMtableMixin)(v.CHTMLWrapper));g.CHTMLmtable=a},90215:function(L,g,_){var d,C=this&&this.__extends||(d=function(s,a){return(d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(t[e]=r[e])})(s,a)},function(s,a){if("function"!=typeof a&&null!==a)throw new TypeError("Class extends value "+String(a)+" is not a constructor or null");function t(){this.constructor=s}d(s,a),s.prototype=null===a?Object.create(a):(t.prototype=a.prototype,new t)});Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLmtd=void 0;var b=_(10475),M=_(96151),v=_(8880),y=function(d){function s(){return null!==d&&d.apply(this,arguments)||this}return C(s,d),s.prototype.toCHTML=function(a){d.prototype.toCHTML.call(this,a);var t=this.node.attributes.get("rowalign"),r=this.node.attributes.get("columnalign");t!==this.parent.node.attributes.get("rowalign")&&this.adaptor.setAttribute(this.chtml,"rowalign",t),"center"!==r&&("mlabeledtr"!==this.parent.kind||this!==this.parent.childNodes[0]||r!==this.parent.parent.node.attributes.get("side"))&&this.adaptor.setStyle(this.chtml,"textAlign",r),this.parent.parent.node.getProperty("useHeight")&&this.adaptor.append(this.chtml,this.html("mjx-tstrut"))},s.kind=v.MmlMtd.prototype.kind,s.styles={"mjx-mtd":{display:"table-cell","text-align":"center",padding:".215em .4em"},"mjx-mtd:first-child":{"padding-left":0},"mjx-mtd:last-child":{"padding-right":0},"mjx-mtable > * > mjx-itable > *:first-child > mjx-mtd":{"padding-top":0},"mjx-mtable > * > mjx-itable > *:last-child > mjx-mtd":{"padding-bottom":0},"mjx-tstrut":{display:"inline-block",height:"1em","vertical-align":"-.25em"},'mjx-labels[align="left"] > mjx-mtr > mjx-mtd':{"text-align":"left"},'mjx-labels[align="right"] > mjx-mtr > mjx-mtd':{"text-align":"right"},"mjx-mtd[extra]":{padding:0},'mjx-mtd[rowalign="top"]':{"vertical-align":"top"},'mjx-mtd[rowalign="center"]':{"vertical-align":"middle"},'mjx-mtd[rowalign="bottom"]':{"vertical-align":"bottom"},'mjx-mtd[rowalign="baseline"]':{"vertical-align":"baseline"},'mjx-mtd[rowalign="axis"]':{"vertical-align":".25em"}},s}((0,M.CommonMtdMixin)(b.CHTMLWrapper));g.CHTMLmtd=y},89175:function(L,g,_){var d,C=this&&this.__extends||(d=function(s,a){return(d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(t[e]=r[e])})(s,a)},function(s,a){if("function"!=typeof a&&null!==a)throw new TypeError("Class extends value "+String(a)+" is not a constructor or null");function t(){this.constructor=s}d(s,a),s.prototype=null===a?Object.create(a):(t.prototype=a.prototype,new t)});Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLmtext=void 0;var b=_(10475),M=_(30322),v=_(36242),y=function(d){function s(){return null!==d&&d.apply(this,arguments)||this}return C(s,d),s.kind=v.MmlMtext.prototype.kind,s}((0,M.CommonMtextMixin)(b.CHTMLWrapper));g.CHTMLmtext=y},90714:function(L,g,_){var a,C=this&&this.__extends||(a=function(t,r){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,i){e.__proto__=i}||function(e,i){for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(e[o]=i[o])})(t,r)},function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function e(){this.constructor=t}a(t,r),t.prototype=null===r?Object.create(r):(e.prototype=r.prototype,new e)});Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLmlabeledtr=g.CHTMLmtr=void 0;var b=_(10475),M=_(49078),v=_(49078),y=_(4181),d=function(a){function t(){return null!==a&&a.apply(this,arguments)||this}return C(t,a),t.prototype.toCHTML=function(r){a.prototype.toCHTML.call(this,r);var e=this.node.attributes.get("rowalign");"baseline"!==e&&this.adaptor.setAttribute(this.chtml,"rowalign",e)},t.kind=y.MmlMtr.prototype.kind,t.styles={"mjx-mtr":{display:"table-row"},'mjx-mtr[rowalign="top"] > mjx-mtd':{"vertical-align":"top"},'mjx-mtr[rowalign="center"] > mjx-mtd':{"vertical-align":"middle"},'mjx-mtr[rowalign="bottom"] > mjx-mtd':{"vertical-align":"bottom"},'mjx-mtr[rowalign="baseline"] > mjx-mtd':{"vertical-align":"baseline"},'mjx-mtr[rowalign="axis"] > mjx-mtd':{"vertical-align":".25em"}},t}((0,M.CommonMtrMixin)(b.CHTMLWrapper));g.CHTMLmtr=d;var s=function(a){function t(){return null!==a&&a.apply(this,arguments)||this}return C(t,a),t.prototype.toCHTML=function(r){a.prototype.toCHTML.call(this,r);var e=this.adaptor.firstChild(this.chtml);if(e){this.adaptor.remove(e);var i=this.node.attributes.get("rowalign"),o="baseline"!==i&&"axis"!==i?{rowalign:i}:{},n=this.html("mjx-mtr",o,[e]);this.adaptor.append(this.parent.labels,n)}},t.prototype.markUsed=function(){a.prototype.markUsed.call(this),this.jax.wrapperUsage.add(d.kind)},t.kind=y.MmlMlabeledtr.prototype.kind,t.styles={"mjx-mlabeledtr":{display:"table-row"},'mjx-mlabeledtr[rowalign="top"] > mjx-mtd':{"vertical-align":"top"},'mjx-mlabeledtr[rowalign="center"] > mjx-mtd':{"vertical-align":"middle"},'mjx-mlabeledtr[rowalign="bottom"] > mjx-mtd':{"vertical-align":"bottom"},'mjx-mlabeledtr[rowalign="baseline"] > mjx-mtd':{"vertical-align":"baseline"},'mjx-mlabeledtr[rowalign="axis"] > mjx-mtd':{"vertical-align":".25em"}},t}((0,v.CommonMlabeledtrMixin)(d));g.CHTMLmlabeledtr=s},81005:function(L,g,_){var r,C=this&&this.__extends||(r=function(e,i){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,n){o.__proto__=n}||function(o,n){for(var l in n)Object.prototype.hasOwnProperty.call(n,l)&&(o[l]=n[l])})(e,i)},function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function o(){this.constructor=e}r(e,i),e.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)});Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLmunderover=g.CHTMLmover=g.CHTMLmunder=void 0;var b=_(31201),M=_(55313),v=_(55313),y=_(55313),d=_(89617),s=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return C(e,r),e.prototype.toCHTML=function(i){if(this.hasMovableLimits())return r.prototype.toCHTML.call(this,i),void this.adaptor.setAttribute(this.chtml,"limits","false");this.chtml=this.standardCHTMLnode(i);var o=this.adaptor.append(this.adaptor.append(this.chtml,this.html("mjx-row")),this.html("mjx-base")),n=this.adaptor.append(this.adaptor.append(this.chtml,this.html("mjx-row")),this.html("mjx-under"));this.baseChild.toCHTML(o),this.scriptChild.toCHTML(n);var l=this.baseChild.getOuterBBox(),u=this.scriptChild.getOuterBBox(),h=this.getUnderKV(l,u)[0],c=this.isLineBelow?0:this.getDelta(!0);this.adaptor.setStyle(n,"paddingTop",this.em(h)),this.setDeltaW([o,n],this.getDeltaW([l,u],[0,-c])),this.adjustUnderDepth(n,u)},e.kind=d.MmlMunder.prototype.kind,e.styles={"mjx-over":{"text-align":"left"},'mjx-munder:not([limits="false"])':{display:"inline-table"},"mjx-munder > mjx-row":{"text-align":"left"},"mjx-under":{"padding-bottom":".1em"}},e}((0,M.CommonMunderMixin)(b.CHTMLmsub));g.CHTMLmunder=s;var a=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return C(e,r),e.prototype.toCHTML=function(i){if(this.hasMovableLimits())return r.prototype.toCHTML.call(this,i),void this.adaptor.setAttribute(this.chtml,"limits","false");this.chtml=this.standardCHTMLnode(i);var o=this.adaptor.append(this.chtml,this.html("mjx-over")),n=this.adaptor.append(this.chtml,this.html("mjx-base"));this.scriptChild.toCHTML(o),this.baseChild.toCHTML(n);var l=this.scriptChild.getOuterBBox(),u=this.baseChild.getOuterBBox();this.adjustBaseHeight(n,u);var h=this.getOverKU(u,l)[0],c=this.isLineAbove?0:this.getDelta();this.adaptor.setStyle(o,"paddingBottom",this.em(h)),this.setDeltaW([n,o],this.getDeltaW([u,l],[0,c])),this.adjustOverDepth(o,l)},e.kind=d.MmlMover.prototype.kind,e.styles={'mjx-mover:not([limits="false"])':{"padding-top":".1em"},'mjx-mover:not([limits="false"]) > *':{display:"block","text-align":"left"}},e}((0,v.CommonMoverMixin)(b.CHTMLmsup));g.CHTMLmover=a;var t=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return C(e,r),e.prototype.toCHTML=function(i){if(this.hasMovableLimits())return r.prototype.toCHTML.call(this,i),void this.adaptor.setAttribute(this.chtml,"limits","false");this.chtml=this.standardCHTMLnode(i);var o=this.adaptor.append(this.chtml,this.html("mjx-over")),n=this.adaptor.append(this.adaptor.append(this.chtml,this.html("mjx-box")),this.html("mjx-munder")),l=this.adaptor.append(this.adaptor.append(n,this.html("mjx-row")),this.html("mjx-base")),u=this.adaptor.append(this.adaptor.append(n,this.html("mjx-row")),this.html("mjx-under"));this.overChild.toCHTML(o),this.baseChild.toCHTML(l),this.underChild.toCHTML(u);var h=this.overChild.getOuterBBox(),c=this.baseChild.getOuterBBox(),f=this.underChild.getOuterBBox();this.adjustBaseHeight(l,c);var p=this.getOverKU(c,h)[0],m=this.getUnderKV(c,f)[0],x=this.getDelta();this.adaptor.setStyle(o,"paddingBottom",this.em(p)),this.adaptor.setStyle(u,"paddingTop",this.em(m)),this.setDeltaW([l,u,o],this.getDeltaW([c,f,h],[0,this.isLineBelow?0:-x,this.isLineAbove?0:x])),this.adjustOverDepth(o,h),this.adjustUnderDepth(u,f)},e.prototype.markUsed=function(){r.prototype.markUsed.call(this),this.jax.wrapperUsage.add(b.CHTMLmsubsup.kind)},e.kind=d.MmlMunderover.prototype.kind,e.styles={'mjx-munderover:not([limits="false"])':{"padding-top":".1em"},'mjx-munderover:not([limits="false"]) > *':{display:"block"}},e}((0,y.CommonMunderoverMixin)(b.CHTMLmsubsup));g.CHTMLmunderover=t},80880:function(L,g,_){var s,C=this&&this.__extends||(s=function(a,t){return(s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,e){r.__proto__=e}||function(r,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(r[i]=e[i])})(a,t)},function(a,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=a}s(a,t),a.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),b=this&&this.__read||function(s,a){var t="function"==typeof Symbol&&s[Symbol.iterator];if(!t)return s;var e,o,r=t.call(s),i=[];try{for(;(void 0===a||a-- >0)&&!(e=r.next()).done;)i.push(e.value)}catch(n){o={error:n}}finally{try{e&&!e.done&&(t=r.return)&&t.call(r)}finally{if(o)throw o.error}}return i},M=this&&this.__values||function(s){var a="function"==typeof Symbol&&Symbol.iterator,t=a&&s[a],r=0;if(t)return t.call(s);if(s&&"number"==typeof s.length)return{next:function(){return s&&r>=s.length&&(s=void 0),{value:s&&s[r++],done:!s}}};throw new TypeError(a?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLscriptbase=void 0;var v=_(10475),d=function(s){function a(){return null!==s&&s.apply(this,arguments)||this}return C(a,s),a.prototype.toCHTML=function(t){this.chtml=this.standardCHTMLnode(t);var r=b(this.getOffset(),2),e=r[0],i=r[1],o=e-(this.baseRemoveIc?this.baseIc:0),n={"vertical-align":this.em(i)};o&&(n["margin-left"]=this.em(o)),this.baseChild.toCHTML(this.chtml),this.scriptChild.toCHTML(this.adaptor.append(this.chtml,this.html("mjx-script",{style:n})))},a.prototype.setDeltaW=function(t,r){for(var e=0;e<r.length;e++)r[e]&&this.adaptor.setStyle(t[e],"paddingLeft",this.em(r[e]))},a.prototype.adjustOverDepth=function(t,r){r.d>=0||this.adaptor.setStyle(t,"marginBottom",this.em(r.d*r.rscale))},a.prototype.adjustUnderDepth=function(t,r){var e,i;if(!(r.d>=0)){var o=this.adaptor,n=this.em(r.d),l=this.html("mjx-box",{style:{"margin-bottom":n,"vertical-align":n}});try{for(var u=M(o.childNodes(o.firstChild(t))),h=u.next();!h.done;h=u.next()){var c=h.value;o.append(l,c)}}catch(f){e={error:f}}finally{try{h&&!h.done&&(i=u.return)&&i.call(u)}finally{if(e)throw e.error}}o.append(o.firstChild(t),l)}},a.prototype.adjustBaseHeight=function(t,r){if(this.node.attributes.get("accent")){var e=this.font.params.x_height*r.scale;r.h<e&&(this.adaptor.setStyle(t,"paddingTop",this.em(e-r.h)),r.h=e)}},a.kind="scriptbase",a}((0,_(77095).CommonScriptbaseMixin)(v.CHTMLWrapper));g.CHTMLscriptbase=d},37690:function(L,g,_){var r,C=this&&this.__extends||(r=function(e,i){return(r=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,n){o.__proto__=n}||function(o,n){for(var l in n)Object.prototype.hasOwnProperty.call(n,l)&&(o[l]=n[l])})(e,i)},function(e,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function o(){this.constructor=e}r(e,i),e.prototype=null===i?Object.create(i):(o.prototype=i.prototype,new o)});Object.defineProperty(g,"__esModule",{value:!0}),g.CHTMLxml=g.CHTMLannotationXML=g.CHTMLannotation=g.CHTMLsemantics=void 0;var b=_(10475),M=_(47463),v=_(1546),y=_(91585),d=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return C(e,r),e.prototype.toCHTML=function(i){var o=this.standardCHTMLnode(i);this.childNodes.length&&this.childNodes[0].toCHTML(o)},e.kind=v.MmlSemantics.prototype.kind,e}((0,M.CommonSemanticsMixin)(b.CHTMLWrapper));g.CHTMLsemantics=d;var s=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return C(e,r),e.prototype.toCHTML=function(i){r.prototype.toCHTML.call(this,i)},e.prototype.computeBBox=function(){return this.bbox},e.kind=v.MmlAnnotation.prototype.kind,e}(b.CHTMLWrapper);g.CHTMLannotation=s;var a=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return C(e,r),e.kind=v.MmlAnnotationXML.prototype.kind,e.styles={"mjx-annotation-xml":{"font-family":"initial","line-height":"normal"}},e}(b.CHTMLWrapper);g.CHTMLannotationXML=a;var t=function(r){function e(){return null!==r&&r.apply(this,arguments)||this}return C(e,r),e.prototype.toCHTML=function(i){this.chtml=this.adaptor.append(i,this.adaptor.clone(this.node.getXML()))},e.prototype.computeBBox=function(i,o){void 0===o&&(o=!1);var n=this.jax.measureXMLnode(this.node.getXML()),l=n.w,u=n.h,h=n.d;i.w=l,i.h=u,i.d=h},e.prototype.getStyles=function(){},e.prototype.getScale=function(){},e.prototype.getVariant=function(){},e.kind=y.XMLNode.prototype.kind,e.autoStyle=!1,e}(b.CHTMLWrapper);g.CHTMLxml=t},21179:function(L,g){var _=this&&this.__read||function(t,r){var e="function"==typeof Symbol&&t[Symbol.iterator];if(!e)return t;var o,l,i=e.call(t),n=[];try{for(;(void 0===r||r-- >0)&&!(o=i.next()).done;)n.push(o.value)}catch(u){l={error:u}}finally{try{o&&!o.done&&(e=i.return)&&e.call(i)}finally{if(l)throw l.error}}return n};Object.defineProperty(g,"__esModule",{value:!0}),g.CommonArrow=g.CommonDiagonalArrow=g.CommonDiagonalStrike=g.CommonBorder2=g.CommonBorder=g.arrowBBox=g.diagonalArrowDef=g.arrowDef=g.arrowBBoxW=g.arrowBBoxHD=g.arrowHead=g.fullBorder=g.fullPadding=g.fullBBox=g.sideNames=g.sideIndex=g.SOLID=g.PADDING=g.THICKNESS=g.ARROWY=g.ARROWDX=g.ARROWX=void 0,g.ARROWX=4,g.ARROWDX=1,g.ARROWY=2,g.THICKNESS=.067,g.PADDING=.2,g.SOLID=g.THICKNESS+"em solid",g.sideIndex={top:0,right:1,bottom:2,left:3},g.sideNames=Object.keys(g.sideIndex),g.fullBBox=function(t){return new Array(4).fill(t.thickness+t.padding)},g.fullPadding=function(t){return new Array(4).fill(t.padding)},g.fullBorder=function(t){return new Array(4).fill(t.thickness)};g.arrowHead=function(t){return Math.max(t.padding,t.thickness*(t.arrowhead.x+t.arrowhead.dx+1))};g.arrowBBoxHD=function(t,r){if(t.childNodes[0]){var e=t.childNodes[0].getBBox(),i=e.h,o=e.d;r[0]=r[2]=Math.max(0,t.thickness*t.arrowhead.y-(i+o)/2)}return r};g.arrowBBoxW=function(t,r){if(t.childNodes[0]){var e=t.childNodes[0].getBBox().w;r[1]=r[3]=Math.max(0,t.thickness*t.arrowhead.y-e/2)}return r},g.arrowDef={up:[-Math.PI/2,!1,!0,"verticalstrike"],down:[Math.PI/2,!1,!0,"verticakstrike"],right:[0,!1,!1,"horizontalstrike"],left:[Math.PI,!1,!1,"horizontalstrike"],updown:[Math.PI/2,!0,!0,"verticalstrike uparrow downarrow"],leftright:[0,!0,!1,"horizontalstrike leftarrow rightarrow"]},g.diagonalArrowDef={updiagonal:[-1,0,!1,"updiagonalstrike northeastarrow"],northeast:[-1,0,!1,"updiagonalstrike updiagonalarrow"],southeast:[1,0,!1,"downdiagonalstrike"],northwest:[1,Math.PI,!1,"downdiagonalstrike"],southwest:[-1,Math.PI,!1,"updiagonalstrike"],northeastsouthwest:[-1,0,!0,"updiagonalstrike northeastarrow updiagonalarrow southwestarrow"],northwestsoutheast:[1,0,!0,"downdiagonalstrike northwestarrow southeastarrow"]},g.arrowBBox={up:function(t){return(0,g.arrowBBoxW)(t,[(0,g.arrowHead)(t),0,t.padding,0])},down:function(t){return(0,g.arrowBBoxW)(t,[t.padding,0,(0,g.arrowHead)(t),0])},right:function(t){return(0,g.arrowBBoxHD)(t,[0,(0,g.arrowHead)(t),0,t.padding])},left:function(t){return(0,g.arrowBBoxHD)(t,[0,t.padding,0,(0,g.arrowHead)(t)])},updown:function(t){return(0,g.arrowBBoxW)(t,[(0,g.arrowHead)(t),0,(0,g.arrowHead)(t),0])},leftright:function(t){return(0,g.arrowBBoxHD)(t,[0,(0,g.arrowHead)(t),0,(0,g.arrowHead)(t)])}};g.CommonBorder=function(t){return function(r){var e=g.sideIndex[r];return[r,{renderer:t,bbox:function(i){var o=[0,0,0,0];return o[e]=i.thickness+i.padding,o},border:function(i){var o=[0,0,0,0];return o[e]=i.thickness,o}}]}};g.CommonBorder2=function(t){return function(r,e,i){var o=g.sideIndex[e],n=g.sideIndex[i];return[r,{renderer:t,bbox:function(l){var u=l.thickness+l.padding,h=[0,0,0,0];return h[o]=h[n]=u,h},border:function(l){var u=[0,0,0,0];return u[o]=u[n]=l.thickness,u},remove:e+" "+i}]}};g.CommonDiagonalStrike=function(t){return function(r){var e="mjx-"+r.charAt(0)+"strike";return[r+"diagonalstrike",{renderer:t(e),bbox:g.fullBBox}]}};g.CommonDiagonalArrow=function(t){return function(r){var e=_(g.diagonalArrowDef[r],4),i=e[0],o=e[1],n=e[2];return[r+"arrow",{renderer:function(u,h){var c=_(u.arrowAW(),2),f=c[0],p=c[1],m=u.arrow(p,i*(f-o),n);t(u,m)},bbox:function(u){var h=u.arrowData(),c=h.a,f=h.x,p=h.y,m=_([u.arrowhead.x,u.arrowhead.y,u.arrowhead.dx],3),x=m[0],w=m[1],j=m[2],S=_(u.getArgMod(x+j,w),2),T=S[0],O=S[1],B=p+(T>c?u.thickness*O*Math.sin(T-c):0),A=f+(T>Math.PI/2-c?u.thickness*O*Math.sin(T+c-Math.PI/2):0);return[B,A,B,A]},remove:e[3]}]}};g.CommonArrow=function(t){return function(r){var e=_(g.arrowDef[r],4),i=e[0],o=e[1],n=e[2],l=e[3];return[r+"arrow",{renderer:function(u,h){var c=u.getBBox(),f=c.w,p=c.h,m=c.d,x=_(n?[p+m,"X"]:[f,"Y"],2),w=x[0],j=x[1],S=u.getOffset(j),T=u.arrow(w,i,o,j,S);t(u,T)},bbox:g.arrowBBox[r],remove:l}]}}},22205:function(L,g,_){var i,C=this&&this.__extends||(i=function(o,n){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(l,u){l.__proto__=u}||function(l,u){for(var h in u)Object.prototype.hasOwnProperty.call(u,h)&&(l[h]=u[h])})(o,n)},function(o,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function l(){this.constructor=o}i(o,n),o.prototype=null===n?Object.create(n):(l.prototype=n.prototype,new l)}),b=this&&this.__assign||function(){return b=Object.assign||function(i){for(var o,n=1,l=arguments.length;n<l;n++)for(var u in o=arguments[n])Object.prototype.hasOwnProperty.call(o,u)&&(i[u]=o[u]);return i},b.apply(this,arguments)},M=this&&this.__read||function(i,o){var n="function"==typeof Symbol&&i[Symbol.iterator];if(!n)return i;var u,c,l=n.call(i),h=[];try{for(;(void 0===o||o-- >0)&&!(u=l.next()).done;)h.push(u.value)}catch(f){c={error:f}}finally{try{u&&!u.done&&(n=l.return)&&n.call(l)}finally{if(c)throw c.error}}return h},v=this&&this.__values||function(i){var o="function"==typeof Symbol&&Symbol.iterator,n=o&&i[o],l=0;if(n)return n.call(i);if(i&&"number"==typeof i.length)return{next:function(){return i&&l>=i.length&&(i=void 0),{value:i&&i[l++],done:!i}}};throw new TypeError(o?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(g,"__esModule",{value:!0}),g.CommonOutputJax=void 0;var y=_(12098),d=_(5520),s=_(80084),a=_(21108),t=_(27342),r=_(53873),e=function(i){function o(n,l,u){void 0===n&&(n=null),void 0===l&&(l=null),void 0===u&&(u=null);var h=this,c=M((0,s.separateOptions)(n,u.OPTIONS),2),f=c[0],p=c[1];return(h=i.call(this,f)||this).factory=h.options.wrapperFactory||new l,h.factory.jax=h,h.cssStyles=h.options.cssStyles||new r.CssStyles,h.font=h.options.font||new u(p),h.unknownCache=new Map,h}return C(o,i),o.prototype.typeset=function(n,l){this.setDocument(l);var u=this.createNode();return this.toDOM(n,u,l),u},o.prototype.createNode=function(){var n=this.constructor.NAME;return this.html("mjx-container",{class:"MathJax",jax:n})},o.prototype.setScale=function(n){var l=this.math.metrics.scale*this.options.scale;1!==l&&this.adaptor.setStyle(n,"fontSize",(0,a.percent)(l))},o.prototype.toDOM=function(n,l,u){void 0===u&&(u=null),this.setDocument(u),this.math=n,this.pxPerEm=n.metrics.ex/this.font.params.x_height,n.root.setTeXclass(null),this.setScale(l),this.nodeMap=new Map,this.container=l,this.processMath(n.root,l),this.nodeMap=null,this.executeFilters(this.postFilters,n,u,l)},o.prototype.getBBox=function(n,l){this.setDocument(l),this.math=n,n.root.setTeXclass(null),this.nodeMap=new Map;var u=this.factory.wrap(n.root).getOuterBBox();return this.nodeMap=null,u},o.prototype.getMetrics=function(n){var l,u;this.setDocument(n);var h=this.adaptor,c=this.getMetricMaps(n);try{for(var f=v(n.math),p=f.next();!p.done;p=f.next()){var m=p.value,x=h.parent(m.start.node);if(m.state()<d.STATE.METRICS&&x){var j=c[m.display?1:0].get(x),S=j.em,T=j.ex,O=j.containerWidth,B=j.lineWidth,A=j.scale,H=j.family;m.setMetrics(S,T,O,B,A),this.options.mtextInheritFont&&(m.outputData.mtextFamily=H),this.options.merrorInheritFont&&(m.outputData.merrorFamily=H),m.state(d.STATE.METRICS)}}}catch(P){l={error:P}}finally{try{p&&!p.done&&(u=f.return)&&u.call(f)}finally{if(l)throw l.error}}},o.prototype.getMetricsFor=function(n,l){var u=this.options.mtextInheritFont||this.options.merrorInheritFont,h=this.getTestElement(n,l),c=this.measureMetrics(h,u);return this.adaptor.remove(h),c},o.prototype.getMetricMaps=function(n){var l,u,h,c,f,p,m,x,w,j,S=this.adaptor,T=[new Map,new Map];try{for(var O=v(n.math),B=O.next();!B.done;B=O.next()){var A=B.value;if((H=S.parent(A.start.node))&&A.state()<d.STATE.METRICS){var P=T[A.display?1:0];P.has(H)||P.set(H,this.getTestElement(H,A.display))}}}catch(D){l={error:D}}finally{try{B&&!B.done&&(u=O.return)&&u.call(O)}finally{if(l)throw l.error}}var W=this.options.mtextInheritFont||this.options.merrorInheritFont,N=[new Map,new Map];try{for(var k=v(N.keys()),E=k.next();!E.done;E=k.next()){var R=E.value;try{for(var V=(f=void 0,v(T[R].keys())),I=V.next();!I.done;I=V.next()){var H=I.value;N[R].set(H,this.measureMetrics(T[R].get(H),W))}}catch(D){f={error:D}}finally{try{I&&!I.done&&(p=V.return)&&p.call(V)}finally{if(f)throw f.error}}}}catch(D){h={error:D}}finally{try{E&&!E.done&&(c=k.return)&&c.call(k)}finally{if(h)throw h.error}}try{for(var X=v(N.keys()),F=X.next();!F.done;F=X.next()){R=F.value;try{for(var U=(w=void 0,v(T[R].values())),z=U.next();!z.done;z=U.next()){H=z.value;S.remove(H)}}catch(K){w={error:K}}finally{try{z&&!z.done&&(j=U.return)&&j.call(U)}finally{if(w)throw w.error}}}}catch(D){m={error:D}}finally{try{F&&!F.done&&(x=X.return)&&x.call(X)}finally{if(m)throw m.error}}return N},o.prototype.getTestElement=function(n,l){var u=this.adaptor;if(!this.testInline){this.testInline=this.html("mjx-test",{style:{display:"inline-block",width:"100%","font-style":"normal","font-weight":"normal","font-size":"100%","font-size-adjust":"none","text-indent":0,"text-transform":"none","letter-spacing":"normal","word-spacing":"normal",overflow:"hidden",height:"1px","margin-right":"-1px"}},[this.html("mjx-left-box",{style:{display:"inline-block",width:0,float:"left"}}),this.html("mjx-ex-box",{style:{position:"absolute",overflow:"hidden",width:"1px",height:"60ex"}}),this.html("mjx-right-box",{style:{display:"inline-block",width:0,float:"right"}})]),this.testDisplay=u.clone(this.testInline),u.setStyle(this.testDisplay,"display","table"),u.setStyle(this.testDisplay,"margin-right",""),u.setStyle(u.firstChild(this.testDisplay),"display","none");var h=u.lastChild(this.testDisplay);u.setStyle(h,"display","table-cell"),u.setStyle(h,"width","10000em"),u.setStyle(h,"float","")}return u.append(n,u.clone(l?this.testDisplay:this.testInline))},o.prototype.measureMetrics=function(n,l){var u=this.adaptor,h=l?u.fontFamily(n):"",c=u.fontSize(n),f=M(u.nodeSize(u.childNode(n,1)),2),p=f[0],m=f[1],x=p?m/60:c*this.options.exFactor;return{em:c,ex:x,containerWidth:p?"table"===u.getStyle(n,"display")?u.nodeSize(u.lastChild(n))[0]-1:u.nodeBBox(u.lastChild(n)).left-u.nodeBBox(u.firstChild(n)).left-2:1e6,lineWidth:1e6,scale:Math.max(this.options.minScale,this.options.matchFontHeight?x/this.font.params.x_height/c:1),family:h}},o.prototype.styleSheet=function(n){var l,u;if(this.setDocument(n),this.cssStyles.clear(),this.cssStyles.addStyles(this.constructor.commonStyles),"getStyles"in n)try{for(var h=v(n.getStyles()),c=h.next();!c.done;c=h.next()){var f=c.value;this.cssStyles.addStyles(f)}}catch(m){l={error:m}}finally{try{c&&!c.done&&(u=h.return)&&u.call(h)}finally{if(l)throw l.error}}return this.addWrapperStyles(this.cssStyles),this.addFontStyles(this.cssStyles),this.html("style",{id:"MJX-styles"},[this.text("\n"+this.cssStyles.cssText+"\n")])},o.prototype.addFontStyles=function(n){n.addStyles(this.font.styles)},o.prototype.addWrapperStyles=function(n){var l,u;try{for(var h=v(this.factory.getKinds()),c=h.next();!c.done;c=h.next()){var f=c.value;this.addClassStyles(this.factory.getNodeClass(f),n)}}catch(p){l={error:p}}finally{try{c&&!c.done&&(u=h.return)&&u.call(h)}finally{if(l)throw l.error}}},o.prototype.addClassStyles=function(n,l){l.addStyles(n.styles)},o.prototype.setDocument=function(n){n&&(this.document=n,this.adaptor.document=n.document)},o.prototype.html=function(n,l,u,h){return void 0===l&&(l={}),void 0===u&&(u=[]),this.adaptor.node(n,l,u,h)},o.prototype.text=function(n){return this.adaptor.text(n)},o.prototype.fixed=function(n,l){return void 0===l&&(l=3),Math.abs(n)<6e-4?"0":n.toFixed(l).replace(/\.?0+$/,"")},o.prototype.measureText=function(n,l,u){void 0===u&&(u=["",!1,!1]);var h=this.unknownText(n,l);if("-explicitFont"===l){var c=this.cssFontStyles(u);this.adaptor.setAttributes(h,{style:c})}return this.measureTextNodeWithCache(h,n,l,u)},o.prototype.measureTextNodeWithCache=function(n,l,u,h){void 0===h&&(h=["",!1,!1]),"-explicitFont"===u&&(u=[h[0],h[1]?"T":"F",h[2]?"T":"F",""].join("-")),this.unknownCache.has(u)||this.unknownCache.set(u,new Map);var c=this.unknownCache.get(u),f=c.get(l);if(f)return f;var p=this.measureTextNode(n);return c.set(l,p),p},o.prototype.measureXMLnode=function(n){var l=this.adaptor,u=this.html("mjx-xml-block",{style:{display:"inline-block"}},[l.clone(n)]),h=this.html("mjx-baseline",{style:{display:"inline-block",width:0,height:0}}),f=this.html("mjx-measure-xml",{style:{position:"absolute",display:"inline-block","font-family":"initial","line-height":"normal"}},[h,u]);l.append(l.parent(this.math.start.node),this.container),l.append(this.container,f);var p=this.math.metrics.em*this.math.metrics.scale,m=l.nodeBBox(u),x=m.left,w=m.right,j=m.bottom,S=m.top,T=(w-x)/p,O=(l.nodeBBox(h).top-S)/p,B=(j-S)/p-O;return l.remove(this.container),l.remove(f),{w:T,h:O,d:B}},o.prototype.cssFontStyles=function(n,l){void 0===l&&(l={});var u=M(n,3),h=u[0],c=u[1],f=u[2];return l["font-family"]=this.font.getFamily(h),c&&(l["font-style"]="italic"),f&&(l["font-weight"]="bold"),l},o.prototype.getFontData=function(n){return n||(n=new t.Styles),[this.font.getFamily(n.get("font-family")),"italic"===n.get("font-style"),"bold"===n.get("font-weight")]},o.NAME="Common",o.OPTIONS=b(b({},y.AbstractOutputJax.OPTIONS),{scale:1,minScale:.5,mtextInheritFont:!1,merrorInheritFont:!1,mtextFont:"",merrorFont:"serif",mathmlSpacing:!1,skipAttributes:{},exFactor:.5,displayAlign:"center",displayIndent:"0",wrapperFactory:null,font:null,cssStyles:null}),o.commonStyles={},o}(y.AbstractOutputJax);g.CommonOutputJax=e},91907:function(L,g,_){var c,C=this&&this.__extends||(c=function(f,p){return(c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(m,x){m.__proto__=x}||function(m,x){for(var w in x)Object.prototype.hasOwnProperty.call(x,w)&&(m[w]=x[w])})(f,p)},function(f,p){if("function"!=typeof p&&null!==p)throw new TypeError("Class extends value "+String(p)+" is not a constructor or null");function m(){this.constructor=f}c(f,p),f.prototype=null===p?Object.create(p):(m.prototype=p.prototype,new m)}),b=this&&this.__createBinding||(Object.create?function(c,f,p,m){void 0===m&&(m=p);var x=Object.getOwnPropertyDescriptor(f,p);(!x||("get"in x?!f.__esModule:x.writable||x.configurable))&&(x={enumerable:!0,get:function(){return f[p]}}),Object.defineProperty(c,m,x)}:function(c,f,p,m){void 0===m&&(m=p),c[m]=f[p]}),M=this&&this.__setModuleDefault||(Object.create?function(c,f){Object.defineProperty(c,"default",{enumerable:!0,value:f})}:function(c,f){c.default=f}),v=this&&this.__importStar||function(c){if(c&&c.__esModule)return c;var f={};if(null!=c)for(var p in c)"default"!==p&&Object.prototype.hasOwnProperty.call(c,p)&&b(f,c,p);return M(f,c),f},y=this&&this.__values||function(c){var f="function"==typeof Symbol&&Symbol.iterator,p=f&&c[f],m=0;if(p)return p.call(c);if(c&&"number"==typeof c.length)return{next:function(){return c&&m>=c.length&&(c=void 0),{value:c&&c[m++],done:!c}}};throw new TypeError(f?"Object is not iterable.":"Symbol.iterator is not defined.")},d=this&&this.__read||function(c,f){var p="function"==typeof Symbol&&c[Symbol.iterator];if(!p)return c;var x,j,m=p.call(c),w=[];try{for(;(void 0===f||f-- >0)&&!(x=m.next()).done;)w.push(x.value)}catch(S){j={error:S}}finally{try{x&&!x.done&&(p=m.return)&&p.call(m)}finally{if(j)throw j.error}}return w},s=this&&this.__spreadArray||function(c,f,p){if(p||2===arguments.length)for(var w,m=0,x=f.length;m<x;m++)(w||!(m in f))&&(w||(w=Array.prototype.slice.call(f,0,m)),w[m]=f[m]);return c.concat(w||Array.prototype.slice.call(f))};Object.defineProperty(g,"__esModule",{value:!0}),g.CommonWrapper=void 0;var a=_(15956),t=_(91585),r=_(74267),e=v(_(21108)),i=_(27342),o=_(13380),n=_(91609),l=2/18;function u(c,f){return c?f<l?0:l:f}var h=function(c){function f(p,m,x){void 0===x&&(x=null);var w=c.call(this,p,m)||this;return w.parent=null,w.removedStyles=null,w.styles=null,w.variant="",w.bboxComputed=!1,w.stretch=n.NOSTRETCH,w.font=null,w.parent=x,w.font=p.jax.font,w.bbox=o.BBox.zero(),w.getStyles(),w.getVariant(),w.getScale(),w.getSpace(),w.childNodes=m.childNodes.map(function(j){var S=w.wrap(j);return S.bbox.pwidth&&(m.notParent||m.isKind("math"))&&(w.bbox.pwidth=o.BBox.fullWidth),S}),w}return C(f,c),Object.defineProperty(f.prototype,"jax",{get:function(){return this.factory.jax},enumerable:!1,configurable:!0}),Object.defineProperty(f.prototype,"adaptor",{get:function(){return this.factory.jax.adaptor},enumerable:!1,configurable:!0}),Object.defineProperty(f.prototype,"metrics",{get:function(){return this.factory.jax.math.metrics},enumerable:!1,configurable:!0}),Object.defineProperty(f.prototype,"fixesPWidth",{get:function(){return!this.node.notParent&&!this.node.isToken},enumerable:!1,configurable:!0}),f.prototype.wrap=function(p,m){void 0===m&&(m=null);var x=this.factory.wrap(p,m||this);return m&&m.childNodes.push(x),this.jax.nodeMap.set(p,x),x},f.prototype.getBBox=function(p){if(void 0===p&&(p=!0),this.bboxComputed)return this.bbox;var m=p?this.bbox:o.BBox.zero();return this.computeBBox(m),this.bboxComputed=p,m},f.prototype.getOuterBBox=function(p){var m,x;void 0===p&&(p=!0);var w=this.getBBox(p);if(!this.styles)return w;var j=new o.BBox;Object.assign(j,w);try{for(var S=y(o.BBox.StyleAdjust),T=S.next();!T.done;T=S.next()){var O=d(T.value,2),B=O[0],A=O[1],H=this.styles.get(B);H&&(j[A]+=this.length2em(H,1,j.rscale))}}catch(P){m={error:P}}finally{try{T&&!T.done&&(x=S.return)&&x.call(S)}finally{if(m)throw m.error}}return j},f.prototype.computeBBox=function(p,m){var x,w;void 0===m&&(m=!1),p.empty();try{for(var j=y(this.childNodes),S=j.next();!S.done;S=j.next()){var T=S.value;p.append(T.getOuterBBox())}}catch(O){x={error:O}}finally{try{S&&!S.done&&(w=j.return)&&w.call(j)}finally{if(x)throw x.error}}p.clean(),this.fixesPWidth&&this.setChildPWidths(m)&&this.computeBBox(p,!0)},f.prototype.setChildPWidths=function(p,m,x){var w,j;if(void 0===m&&(m=null),void 0===x&&(x=!0),p)return!1;x&&(this.bbox.pwidth="");var S=!1;try{for(var T=y(this.childNodes),O=T.next();!O.done;O=T.next()){var B=O.value,A=B.getOuterBBox();A.pwidth&&B.setChildPWidths(p,null===m?A.w:m,x)&&(S=!0)}}catch(H){w={error:H}}finally{try{O&&!O.done&&(j=T.return)&&j.call(T)}finally{if(w)throw w.error}}return S},f.prototype.invalidateBBox=function(){this.bboxComputed&&(this.bboxComputed=!1,this.parent&&this.parent.invalidateBBox())},f.prototype.copySkewIC=function(p){var m=this.childNodes[0];(null==m?void 0:m.bbox.sk)&&(p.sk=m.bbox.sk),(null==m?void 0:m.bbox.dx)&&(p.dx=m.bbox.dx);var x=this.childNodes[this.childNodes.length-1];(null==x?void 0:x.bbox.ic)&&(p.ic=x.bbox.ic,p.w+=p.ic)},f.prototype.getStyles=function(){var p=this.node.attributes.getExplicit("style");if(p)for(var m=this.styles=new i.Styles(p),x=0,w=f.removeStyles.length;x<w;x++){var j=f.removeStyles[x];m.get(j)&&(this.removedStyles||(this.removedStyles={}),this.removedStyles[j]=m.get(j),m.set(j,""))}},f.prototype.getVariant=function(){if(this.node.isToken){var p=this.node.attributes,m=p.get("mathvariant");if(!p.getExplicit("mathvariant")){var x=p.getList("fontfamily","fontweight","fontstyle");if(this.removedStyles){var w=this.removedStyles;w.fontFamily&&(x.family=w.fontFamily),w.fontWeight&&(x.weight=w.fontWeight),w.fontStyle&&(x.style=w.fontStyle)}x.fontfamily&&(x.family=x.fontfamily),x.fontweight&&(x.weight=x.fontweight),x.fontstyle&&(x.style=x.fontstyle),x.weight&&x.weight.match(/^\d+$/)&&(x.weight=parseInt(x.weight)>600?"bold":"normal"),x.family?m=this.explicitVariant(x.family,x.weight,x.style):(this.node.getProperty("variantForm")&&(m="-tex-variant"),m=(f.BOLDVARIANTS[x.weight]||{})[m]||m,m=(f.ITALICVARIANTS[x.style]||{})[m]||m)}this.variant=m}},f.prototype.explicitVariant=function(p,m,x){var w=this.styles;return w||(w=this.styles=new i.Styles),w.set("fontFamily",p),m&&w.set("fontWeight",m),x&&w.set("fontStyle",x),"-explicitFont"},f.prototype.getScale=function(){var p=1,m=this.parent,x=m?m.bbox.scale:1,w=this.node.attributes,j=Math.min(w.get("scriptlevel"),2),S=w.get("fontsize"),T=this.node.isToken||this.node.isKind("mstyle")?w.get("mathsize"):w.getInherited("mathsize");if(0!==j){p=Math.pow(w.get("scriptsizemultiplier"),j);var O=this.length2em(w.get("scriptminsize"),.8,1);p<O&&(p=O)}this.removedStyles&&this.removedStyles.fontSize&&!S&&(S=this.removedStyles.fontSize),S&&!w.getExplicit("mathsize")&&(T=S),"1"!==T&&(p*=this.length2em(T,1,1)),this.bbox.scale=p,this.bbox.rscale=p/x},f.prototype.getSpace=function(){var p=this.isTopEmbellished(),m=this.node.hasSpacingAttributes();this.jax.options.mathmlSpacing||m?p&&this.getMathMLSpacing():this.getTeXSpacing(p,m)},f.prototype.getMathMLSpacing=function(){var p=this.node.coreMO(),m=p.coreParent(),x=m.parent;if(x&&x.isKind("mrow")&&1!==x.childNodes.length){var w=p.attributes,j=w.get("scriptlevel")>0;this.bbox.L=w.isSet("lspace")?Math.max(0,this.length2em(w.get("lspace"))):u(j,p.lspace),this.bbox.R=w.isSet("rspace")?Math.max(0,this.length2em(w.get("rspace"))):u(j,p.rspace);var S=x.childIndex(m);if(0!==S){var T=x.childNodes[S-1];if(T.isEmbellished){var O=this.jax.nodeMap.get(T).getBBox();O.R&&(this.bbox.L=Math.max(0,this.bbox.L-O.R))}}}},f.prototype.getTeXSpacing=function(p,m){if(!m){var x=this.node.texSpacing();x&&(this.bbox.L=this.length2em(x))}if(p||m){var w=this.node.coreMO().attributes;w.isSet("lspace")&&(this.bbox.L=Math.max(0,this.length2em(w.get("lspace")))),w.isSet("rspace")&&(this.bbox.R=Math.max(0,this.length2em(w.get("rspace"))))}},f.prototype.isTopEmbellished=function(){return this.node.isEmbellished&&!(this.node.parent&&this.node.parent.isEmbellished)},f.prototype.core=function(){return this.jax.nodeMap.get(this.node.core())},f.prototype.coreMO=function(){return this.jax.nodeMap.get(this.node.coreMO())},f.prototype.getText=function(){var p,m,x="";if(this.node.isToken)try{for(var w=y(this.node.childNodes),j=w.next();!j.done;j=w.next()){var S=j.value;S instanceof t.TextNode&&(x+=S.getText())}}catch(T){p={error:T}}finally{try{j&&!j.done&&(m=w.return)&&m.call(w)}finally{if(p)throw p.error}}return x},f.prototype.canStretch=function(p){if(this.stretch=n.NOSTRETCH,this.node.isEmbellished){var m=this.core();m&&m.node!==this.node&&m.canStretch(p)&&(this.stretch=m.stretch)}return 0!==this.stretch.dir},f.prototype.getAlignShift=function(){var p,m=(p=this.node.attributes).getList.apply(p,s([],d(t.indentAttributes),!1)),x=m.indentalign,w=m.indentshift,j=m.indentalignfirst,S=m.indentshiftfirst;return"indentalign"!==j&&(x=j),"auto"===x&&(x=this.jax.options.displayAlign),"indentshift"!==S&&(w=S),"auto"===w&&(w=this.jax.options.displayIndent,"right"===x&&!w.match(/^\s*0[a-z]*\s*$/)&&(w=("-"+w.trim()).replace(/^--/,""))),[x,this.length2em(w,this.metrics.containerWidth)]},f.prototype.getAlignX=function(p,m,x){return"right"===x?p-(m.w+m.R)*m.rscale:"left"===x?m.L*m.rscale:(p-m.w*m.rscale)/2},f.prototype.getAlignY=function(p,m,x,w,j){return"top"===j?p-x:"bottom"===j?w-m:"center"===j?(p-x-(m-w))/2:0},f.prototype.getWrapWidth=function(p){return this.childNodes[p].getBBox().w},f.prototype.getChildAlign=function(p){return"left"},f.prototype.percent=function(p){return e.percent(p)},f.prototype.em=function(p){return e.em(p)},f.prototype.px=function(p,m){return void 0===m&&(m=-e.BIGDIMEN),e.px(p,m,this.metrics.em)},f.prototype.length2em=function(p,m,x){return void 0===m&&(m=1),void 0===x&&(x=null),null===x&&(x=this.bbox.scale),e.length2em(p,m,x,this.jax.pxPerEm)},f.prototype.unicodeChars=function(p,m){void 0===m&&(m=this.variant);var x=(0,r.unicodeChars)(p),w=this.font.getVariant(m);if(w&&w.chars){var j=w.chars;x=x.map(function(S){return((j[S]||[])[3]||{}).smp||S})}return x},f.prototype.remapChars=function(p){return p},f.prototype.mmlText=function(p){return this.node.factory.create("text").setText(p)},f.prototype.mmlNode=function(p,m,x){return void 0===m&&(m={}),void 0===x&&(x=[]),this.node.factory.create(p,m,x)},f.prototype.createMo=function(p){var m=this.node.factory,x=m.create("text").setText(p),w=m.create("mo",{stretchy:!0},[x]);w.inheritAttributesFrom(this.node);var j=this.wrap(w);return j.parent=this,j},f.prototype.getVariantChar=function(p,m){var x=this.font.getChar(p,m)||[0,0,0,{unknown:!0}];return 3===x.length&&(x[3]={}),x},f.kind="unknown",f.styles={},f.removeStyles=["fontSize","fontFamily","fontWeight","fontStyle","fontVariant","font"],f.skipAttributes={fontfamily:!0,fontsize:!0,fontweight:!0,fontstyle:!0,color:!0,background:!0,class:!0,href:!0,style:!0,xmlns:!0},f.BOLDVARIANTS={bold:{normal:"bold",italic:"bold-italic",fraktur:"bold-fraktur",script:"bold-script","sans-serif":"bold-sans-serif","sans-serif-italic":"sans-serif-bold-italic"},normal:{bold:"normal","bold-italic":"italic","bold-fraktur":"fraktur","bold-script":"script","bold-sans-serif":"sans-serif","sans-serif-bold-italic":"sans-serif-italic"}},f.ITALICVARIANTS={italic:{normal:"italic",bold:"bold-italic","sans-serif":"sans-serif-italic","bold-sans-serif":"sans-serif-bold-italic"},normal:{italic:"normal","bold-italic":"bold","sans-serif-italic":"sans-serif","sans-serif-bold-italic":"bold-sans-serif"}},f}(a.AbstractWrapper);g.CommonWrapper=h},46340:function(L,g,_){var v,C=this&&this.__extends||(v=function(y,d){return(v=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,a){s.__proto__=a}||function(s,a){for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(s[t]=a[t])})(y,d)},function(y,d){if("function"!=typeof d&&null!==d)throw new TypeError("Class extends value "+String(d)+" is not a constructor or null");function s(){this.constructor=y}v(y,d),y.prototype=null===d?Object.create(d):(s.prototype=d.prototype,new s)});Object.defineProperty(g,"__esModule",{value:!0}),g.CommonWrapperFactory=void 0;var M=function(v){function y(){var d=null!==v&&v.apply(this,arguments)||this;return d.jax=null,d}return C(y,v),Object.defineProperty(y.prototype,"Wrappers",{get:function(){return this.node},enumerable:!1,configurable:!0}),y.defaultNodes={},y}(_(67556).AbstractWrapperFactory);g.CommonWrapperFactory=M},36957:function(L,g,_){var v,C=this&&this.__extends||(v=function(y,d){return(v=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,a){s.__proto__=a}||function(s,a){for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(s[t]=a[t])})(y,d)},function(y,d){if("function"!=typeof d&&null!==d)throw new TypeError("Class extends value "+String(d)+" is not a constructor or null");function s(){this.constructor=y}v(y,d),y.prototype=null===d?Object.create(d):(s.prototype=d.prototype,new s)});Object.defineProperty(g,"__esModule",{value:!0}),g.CommonTeXAtomMixin=void 0;var b=_(91585);g.CommonTeXAtomMixin=function M(v){return function(y){function d(){return null!==y&&y.apply(this,arguments)||this}return C(d,y),d.prototype.computeBBox=function(s,a){if(void 0===a&&(a=!1),y.prototype.computeBBox.call(this,s,a),this.childNodes[0]&&this.childNodes[0].bbox.ic&&(s.ic=this.childNodes[0].bbox.ic),this.node.texClass===b.TEXCLASS.VCENTER){var t=s.h,i=(t+s.d)/2+this.font.params.axis_height-t;s.h+=i,s.d-=i}},d}(v)}},32861:function(L,g){var v,_=this&&this.__extends||(v=function(y,d){return(v=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,a){s.__proto__=a}||function(s,a){for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(s[t]=a[t])})(y,d)},function(y,d){if("function"!=typeof d&&null!==d)throw new TypeError("Class extends value "+String(d)+" is not a constructor or null");function s(){this.constructor=y}v(y,d),y.prototype=null===d?Object.create(d):(s.prototype=d.prototype,new s)}),C=this&&this.__values||function(v){var y="function"==typeof Symbol&&Symbol.iterator,d=y&&v[y],s=0;if(d)return d.call(v);if(v&&"number"==typeof v.length)return{next:function(){return v&&s>=v.length&&(v=void 0),{value:v&&v[s++],done:!v}}};throw new TypeError(y?"Object is not iterable.":"Symbol.iterator is not defined.")},b=this&&this.__read||function(v,y){var d="function"==typeof Symbol&&v[Symbol.iterator];if(!d)return v;var a,r,s=d.call(v),t=[];try{for(;(void 0===y||y-- >0)&&!(a=s.next()).done;)t.push(a.value)}catch(e){r={error:e}}finally{try{a&&!a.done&&(d=s.return)&&d.call(s)}finally{if(r)throw r.error}}return t};Object.defineProperty(g,"__esModule",{value:!0}),g.CommonTextNodeMixin=void 0,g.CommonTextNodeMixin=function M(v){return function(y){function d(){return null!==y&&y.apply(this,arguments)||this}return _(d,y),d.prototype.computeBBox=function(s,a){var t,r;void 0===a&&(a=!1);var e=this.parent.variant,i=this.node.getText();if("-explicitFont"===e){var o=this.jax.getFontData(this.parent.styles),n=this.jax.measureText(i,e,o),l=n.w,u=n.h,h=n.d;s.h=u,s.d=h,s.w=l}else{var c=this.remappedText(i,e);s.empty();try{for(var f=C(c),p=f.next();!p.done;p=f.next()){var m=p.value,x=b(this.getVariantChar(e,m),4),w=(u=x[0],h=x[1],l=x[2],x[3]);if(w.unknown){var j=this.jax.measureText(String.fromCodePoint(m),e);l=j.w,u=j.h,h=j.d}s.w+=l,u>s.h&&(s.h=u),h>s.d&&(s.d=h),s.ic=w.ic||0,s.sk=w.sk||0,s.dx=w.dx||0}}catch(S){t={error:S}}finally{try{p&&!p.done&&(r=f.return)&&r.call(f)}finally{if(t)throw t.error}}c.length>1&&(s.sk=0),s.clean()}},d.prototype.remappedText=function(s,a){var t=this.parent.stretch.c;return t?[t]:this.parent.remapChars(this.unicodeChars(s,a))},d.prototype.getStyles=function(){},d.prototype.getVariant=function(){},d.prototype.getScale=function(){},d.prototype.getSpace=function(){},d}(v)}},55309:function(L,g,_){var d,C=this&&this.__extends||(d=function(s,a){return(d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(t[e]=r[e])})(s,a)},function(s,a){if("function"!=typeof a&&null!==a)throw new TypeError("Class extends value "+String(a)+" is not a constructor or null");function t(){this.constructor=s}d(s,a),s.prototype=null===a?Object.create(a):(t.prototype=a.prototype,new t)}),b=this&&this.__read||function(d,s){var a="function"==typeof Symbol&&d[Symbol.iterator];if(!a)return d;var r,i,t=a.call(d),e=[];try{for(;(void 0===s||s-- >0)&&!(r=t.next()).done;)e.push(r.value)}catch(o){i={error:o}}finally{try{r&&!r.done&&(a=t.return)&&a.call(t)}finally{if(i)throw i.error}}return e},M=this&&this.__spreadArray||function(d,s,a){if(a||2===arguments.length)for(var e,t=0,r=s.length;t<r;t++)(e||!(t in s))&&(e||(e=Array.prototype.slice.call(s,0,t)),e[t]=s[t]);return d.concat(e||Array.prototype.slice.call(s))};Object.defineProperty(g,"__esModule",{value:!0}),g.CommonMactionMixin=g.TooltipData=void 0;var v=_(74267);g.TooltipData={dx:".2em",dy:".1em",postDelay:600,clearDelay:100,hoverTimer:new Map,clearTimer:new Map,stopTimers:function(d,s){s.clearTimer.has(d)&&(clearTimeout(s.clearTimer.get(d)),s.clearTimer.delete(d)),s.hoverTimer.has(d)&&(clearTimeout(s.hoverTimer.get(d)),s.hoverTimer.delete(d))}},g.CommonMactionMixin=function y(d){return function(s){function a(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var e=s.apply(this,M([],b(t),!1))||this,i=e.constructor.actions,o=e.node.attributes.get("actiontype"),n=b(i.get(o)||[function(h,c){},{}],2),l=n[0],u=n[1];return e.action=l,e.data=u,e.getParameters(),e}return C(a,s),Object.defineProperty(a.prototype,"selected",{get:function(){var t=this.node.attributes.get("selection"),r=Math.max(1,Math.min(this.childNodes.length,t))-1;return this.childNodes[r]||this.wrap(this.node.selected)},enumerable:!1,configurable:!0}),a.prototype.getParameters=function(){var t=this.node.attributes.get("data-offsets"),r=b((0,v.split)(t||""),2),e=r[0],i=r[1];this.dx=this.length2em(e||g.TooltipData.dx),this.dy=this.length2em(i||g.TooltipData.dy)},a.prototype.computeBBox=function(t,r){void 0===r&&(r=!1),t.updateFrom(this.selected.getOuterBBox()),this.selected.setChildPWidths(r)},a}(d)}},79039:function(L,g){var b,_=this&&this.__extends||(b=function(M,v){return(b=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(y,d){y.__proto__=d}||function(y,d){for(var s in d)Object.prototype.hasOwnProperty.call(d,s)&&(y[s]=d[s])})(M,v)},function(M,v){if("function"!=typeof v&&null!==v)throw new TypeError("Class extends value "+String(v)+" is not a constructor or null");function y(){this.constructor=M}b(M,v),M.prototype=null===v?Object.create(v):(y.prototype=v.prototype,new y)});Object.defineProperty(g,"__esModule",{value:!0}),g.CommonMathMixin=void 0,g.CommonMathMixin=function C(b){return function(M){function v(){return null!==M&&M.apply(this,arguments)||this}return _(v,M),v.prototype.getWrapWidth=function(y){return this.parent?this.getBBox().w:this.metrics.containerWidth/this.jax.pxPerEm},v}(b)}},5028:function(L,g,_){var e,C=this&&this.__extends||(e=function(i,o){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,l){n.__proto__=l}||function(n,l){for(var u in l)Object.prototype.hasOwnProperty.call(l,u)&&(n[u]=l[u])})(i,o)},function(i,o){if("function"!=typeof o&&null!==o)throw new TypeError("Class extends value "+String(o)+" is not a constructor or null");function n(){this.constructor=i}e(i,o),i.prototype=null===o?Object.create(o):(n.prototype=o.prototype,new n)}),b=this&&this.__createBinding||(Object.create?function(e,i,o,n){void 0===n&&(n=o);var l=Object.getOwnPropertyDescriptor(i,o);(!l||("get"in l?!i.__esModule:l.writable||l.configurable))&&(l={enumerable:!0,get:function(){return i[o]}}),Object.defineProperty(e,n,l)}:function(e,i,o,n){void 0===n&&(n=o),e[n]=i[o]}),M=this&&this.__setModuleDefault||(Object.create?function(e,i){Object.defineProperty(e,"default",{enumerable:!0,value:i})}:function(e,i){e.default=i}),v=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var i={};if(null!=e)for(var o in e)"default"!==o&&Object.prototype.hasOwnProperty.call(e,o)&&b(i,e,o);return M(i,e),i},y=this&&this.__read||function(e,i){var o="function"==typeof Symbol&&e[Symbol.iterator];if(!o)return e;var l,h,n=o.call(e),u=[];try{for(;(void 0===i||i-- >0)&&!(l=n.next()).done;)u.push(l.value)}catch(c){h={error:c}}finally{try{l&&!l.done&&(o=n.return)&&o.call(n)}finally{if(h)throw h.error}}return u},d=this&&this.__spreadArray||function(e,i,o){if(o||2===arguments.length)for(var u,n=0,l=i.length;n<l;n++)(u||!(n in i))&&(u||(u=Array.prototype.slice.call(i,0,n)),u[n]=i[n]);return e.concat(u||Array.prototype.slice.call(i))},s=this&&this.__values||function(e){var i="function"==typeof Symbol&&Symbol.iterator,o=i&&e[i],n=0;if(o)return o.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(i?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(g,"__esModule",{value:!0}),g.CommonMencloseMixin=void 0;var a=v(_(21179)),t=_(74267);g.CommonMencloseMixin=function r(e){return function(i){function o(){for(var n=[],l=0;l<arguments.length;l++)n[l]=arguments[l];var u=i.apply(this,d([],y(n),!1))||this;return u.notations={},u.renderChild=null,u.msqrt=null,u.padding=a.PADDING,u.thickness=a.THICKNESS,u.arrowhead={x:a.ARROWX,y:a.ARROWY,dx:a.ARROWDX},u.TRBL=[0,0,0,0],u.getParameters(),u.getNotations(),u.removeRedundantNotations(),u.initializeNotations(),u.TRBL=u.getBBoxExtenders(),u}return C(o,i),o.prototype.getParameters=function(){var n=this.node.attributes,l=n.get("data-padding");void 0!==l&&(this.padding=this.length2em(l,a.PADDING));var u=n.get("data-thickness");void 0!==u&&(this.thickness=this.length2em(u,a.THICKNESS));var h=n.get("data-arrowhead");if(void 0!==h){var c=y((0,t.split)(h),3),f=c[0],p=c[1],m=c[2];this.arrowhead={x:f?parseFloat(f):a.ARROWX,y:p?parseFloat(p):a.ARROWY,dx:m?parseFloat(m):a.ARROWDX}}},o.prototype.getNotations=function(){var n,l,u=this.constructor.notations;try{for(var h=s((0,t.split)(this.node.attributes.get("notation"))),c=h.next();!c.done;c=h.next()){var f=c.value,p=u.get(f);p&&(this.notations[f]=p,p.renderChild&&(this.renderChild=p.renderer))}}catch(m){n={error:m}}finally{try{c&&!c.done&&(l=h.return)&&l.call(h)}finally{if(n)throw n.error}}},o.prototype.removeRedundantNotations=function(){var n,l,u,h;try{for(var c=s(Object.keys(this.notations)),f=c.next();!f.done;f=c.next()){var p=f.value;if(this.notations[p]){var m=this.notations[p].remove||"";try{for(var x=(u=void 0,s(m.split(/ /))),w=x.next();!w.done;w=x.next()){var j=w.value;delete this.notations[j]}}catch(S){u={error:S}}finally{try{w&&!w.done&&(h=x.return)&&h.call(x)}finally{if(u)throw u.error}}}}}catch(S){n={error:S}}finally{try{f&&!f.done&&(l=c.return)&&l.call(c)}finally{if(n)throw n.error}}},o.prototype.initializeNotations=function(){var n,l;try{for(var u=s(Object.keys(this.notations)),h=u.next();!h.done;h=u.next()){var c=h.value,f=this.notations[c].init;f&&f(this)}}catch(p){n={error:p}}finally{try{h&&!h.done&&(l=u.return)&&l.call(u)}finally{if(n)throw n.error}}},o.prototype.computeBBox=function(n,l){void 0===l&&(l=!1);var u=y(this.TRBL,4),h=u[0],c=u[1],f=u[2],p=u[3],m=this.childNodes[0].getBBox();n.combine(m,p,0),n.h+=h,n.d+=f,n.w+=c,this.setChildPWidths(l)},o.prototype.getBBoxExtenders=function(){var n,l,u=[0,0,0,0];try{for(var h=s(Object.keys(this.notations)),c=h.next();!c.done;c=h.next()){var f=c.value;this.maximizeEntries(u,this.notations[f].bbox(this))}}catch(p){n={error:p}}finally{try{c&&!c.done&&(l=h.return)&&l.call(h)}finally{if(n)throw n.error}}return u},o.prototype.getPadding=function(){var n,l,u=this,h=[0,0,0,0];try{for(var c=s(Object.keys(this.notations)),f=c.next();!f.done;f=c.next()){var p=f.value,m=this.notations[p].border;m&&this.maximizeEntries(h,m(this))}}catch(x){n={error:x}}finally{try{f&&!f.done&&(l=c.return)&&l.call(c)}finally{if(n)throw n.error}}return[0,1,2,3].map(function(x){return u.TRBL[x]-h[x]})},o.prototype.maximizeEntries=function(n,l){for(var u=0;u<n.length;u++)n[u]<l[u]&&(n[u]=l[u])},o.prototype.getOffset=function(n){var l=y(this.TRBL,4),u=l[0],h=l[1],c=l[2],f=l[3],p=("X"===n?h-f:c-u)/2;return Math.abs(p)>.001?p:0},o.prototype.getArgMod=function(n,l){return[Math.atan2(l,n),Math.sqrt(n*n+l*l)]},o.prototype.arrow=function(n,l,u,h,c){return void 0===h&&(h=""),void 0===c&&(c=0),null},o.prototype.arrowData=function(){var n=y([this.padding,this.thickness],2),l=n[0],h=n[1]*(this.arrowhead.x+Math.max(1,this.arrowhead.dx)),c=this.childNodes[0].getBBox(),f=c.h,p=c.d,m=c.w,x=f+p,w=Math.sqrt(x*x+m*m),j=Math.max(l,h*m/w),S=Math.max(l,h*x/w),T=y(this.getArgMod(m+2*j,x+2*S),2);return{a:T[0],W:T[1],x:j,y:S}},o.prototype.arrowAW=function(){var n=this.childNodes[0].getBBox(),l=n.h,u=n.d,h=n.w,c=y(this.TRBL,4),f=c[0],p=c[1],m=c[2],x=c[3];return this.getArgMod(x+h+p,f+l+u+m)},o.prototype.createMsqrt=function(n){var u=this.node.factory.create("msqrt");u.inheritAttributesFrom(this.node),u.childNodes[0]=n.node;var h=this.wrap(u);return h.parent=this,h},o.prototype.sqrtTRBL=function(){var n=this.msqrt.getBBox(),l=this.msqrt.childNodes[0].getBBox();return[n.h-l.h,0,n.d-l.d,n.w-l.w]},o}(e)}},56427:function(L,g){var y,_=this&&this.__extends||(y=function(d,s){return(y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,t){a.__proto__=t}||function(a,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(a[r]=t[r])})(d,s)},function(d,s){if("function"!=typeof s&&null!==s)throw new TypeError("Class extends value "+String(s)+" is not a constructor or null");function a(){this.constructor=d}y(d,s),d.prototype=null===s?Object.create(s):(a.prototype=s.prototype,new a)}),C=this&&this.__read||function(y,d){var s="function"==typeof Symbol&&y[Symbol.iterator];if(!s)return y;var t,e,a=s.call(y),r=[];try{for(;(void 0===d||d-- >0)&&!(t=a.next()).done;)r.push(t.value)}catch(i){e={error:i}}finally{try{t&&!t.done&&(s=a.return)&&s.call(a)}finally{if(e)throw e.error}}return r},b=this&&this.__spreadArray||function(y,d,s){if(s||2===arguments.length)for(var r,a=0,t=d.length;a<t;a++)(r||!(a in d))&&(r||(r=Array.prototype.slice.call(d,0,a)),r[a]=d[a]);return y.concat(r||Array.prototype.slice.call(d))},M=this&&this.__values||function(y){var d="function"==typeof Symbol&&Symbol.iterator,s=d&&y[d],a=0;if(s)return s.call(y);if(y&&"number"==typeof y.length)return{next:function(){return y&&a>=y.length&&(y=void 0),{value:y&&y[a++],done:!y}}};throw new TypeError(d?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(g,"__esModule",{value:!0}),g.CommonMfencedMixin=void 0,g.CommonMfencedMixin=function v(y){return function(d){function s(){for(var a=[],t=0;t<arguments.length;t++)a[t]=arguments[t];var r=d.apply(this,b([],C(a),!1))||this;return r.mrow=null,r.createMrow(),r.addMrowChildren(),r}return _(s,d),s.prototype.createMrow=function(){var t=this.node.factory.create("inferredMrow");t.inheritAttributesFrom(this.node),this.mrow=this.wrap(t),this.mrow.parent=this},s.prototype.addMrowChildren=function(){var a,t,r=this.node,e=this.mrow;this.addMo(r.open),this.childNodes.length&&e.childNodes.push(this.childNodes[0]);var i=0;try{for(var o=M(this.childNodes.slice(1)),n=o.next();!n.done;n=o.next()){var l=n.value;this.addMo(r.separators[i++]),e.childNodes.push(l)}}catch(u){a={error:u}}finally{try{n&&!n.done&&(t=o.return)&&t.call(o)}finally{if(a)throw a.error}}this.addMo(r.close),e.stretchChildren()},s.prototype.addMo=function(a){if(a){var t=this.wrap(a);this.mrow.childNodes.push(t),t.parent=this.mrow}},s.prototype.computeBBox=function(a,t){void 0===t&&(t=!1),a.updateFrom(this.mrow.getOuterBBox()),this.setChildPWidths(t)},s}(y)}},1778:function(L,g){var v,_=this&&this.__extends||(v=function(y,d){return(v=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,a){s.__proto__=a}||function(s,a){for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(s[t]=a[t])})(y,d)},function(y,d){if("function"!=typeof d&&null!==d)throw new TypeError("Class extends value "+String(d)+" is not a constructor or null");function s(){this.constructor=y}v(y,d),y.prototype=null===d?Object.create(d):(s.prototype=d.prototype,new s)}),C=this&&this.__read||function(v,y){var d="function"==typeof Symbol&&v[Symbol.iterator];if(!d)return v;var a,r,s=d.call(v),t=[];try{for(;(void 0===y||y-- >0)&&!(a=s.next()).done;)t.push(a.value)}catch(e){r={error:e}}finally{try{a&&!a.done&&(d=s.return)&&d.call(s)}finally{if(r)throw r.error}}return t},b=this&&this.__spreadArray||function(v,y,d){if(d||2===arguments.length)for(var t,s=0,a=y.length;s<a;s++)(t||!(s in y))&&(t||(t=Array.prototype.slice.call(y,0,s)),t[s]=y[s]);return v.concat(t||Array.prototype.slice.call(y))};Object.defineProperty(g,"__esModule",{value:!0}),g.CommonMfracMixin=void 0,g.CommonMfracMixin=function M(v){return function(y){function d(){for(var s=[],a=0;a<arguments.length;a++)s[a]=arguments[a];var t=y.apply(this,b([],C(s),!1))||this;if(t.bevel=null,t.pad=t.node.getProperty("withDelims")?0:t.font.params.nulldelimiterspace,t.node.attributes.get("bevelled")){var r=t.getBevelData(t.isDisplay()).H,e=t.bevel=t.createMo("/");e.node.attributes.set("symmetric",!0),e.canStretch(1),e.getStretchedVariant([r],!0)}return t}return _(d,y),d.prototype.computeBBox=function(s,a){void 0===a&&(a=!1),s.empty();var t=this.node.attributes.getList("linethickness","bevelled"),r=t.linethickness,e=t.bevelled,i=this.isDisplay(),o=null;if(e)this.getBevelledBBox(s,i);else{var n=this.length2em(String(r),.06);o=-2*this.pad,0===n?this.getAtopBBox(s,i):(this.getFractionBBox(s,i,n),o-=.2),o+=s.w}s.clean(),this.setChildPWidths(a,o)},d.prototype.getFractionBBox=function(s,a,t){var r=this.childNodes[0].getOuterBBox(),e=this.childNodes[1].getOuterBBox(),o=this.font.params.axis_height,n=this.getTUV(a,t),l=n.T,u=n.u,h=n.v;s.combine(r,0,o+l+Math.max(r.d*r.rscale,u)),s.combine(e,0,o-l-Math.max(e.h*e.rscale,h)),s.w+=2*this.pad+.2},d.prototype.getTUV=function(s,a){var t=this.font.params,r=t.axis_height,e=(s?3.5:1.5)*a;return{T:(s?3.5:1.5)*a,u:(s?t.num1:t.num2)-r-e,v:(s?t.denom1:t.denom2)+r-e}},d.prototype.getAtopBBox=function(s,a){var t=this.getUVQ(a),r=t.u,e=t.v,i=t.nbox,o=t.dbox;s.combine(i,0,r),s.combine(o,0,-e),s.w+=2*this.pad},d.prototype.getUVQ=function(s){var a=this.childNodes[0].getOuterBBox(),t=this.childNodes[1].getOuterBBox(),r=this.font.params,e=C(s?[r.num1,r.denom1]:[r.num3,r.denom2],2),i=e[0],o=e[1],n=(s?7:3)*r.rule_thickness,l=i-a.d*a.scale-(t.h*t.scale-o);return l<n&&(i+=(n-l)/2,o+=(n-l)/2,l=n),{u:i,v:o,q:l,nbox:a,dbox:t}},d.prototype.getBevelledBBox=function(s,a){var t=this.getBevelData(a),r=t.u,e=t.v,i=t.delta,o=t.nbox,n=t.dbox,l=this.bevel.getOuterBBox();s.combine(o,0,r),s.combine(l,s.w-i/2,0),s.combine(n,s.w-i/2,e)},d.prototype.getBevelData=function(s){var a=this.childNodes[0].getOuterBBox(),t=this.childNodes[1].getOuterBBox(),r=s?.4:.15,e=Math.max(a.scale*(a.h+a.d),t.scale*(t.h+t.d))+2*r,i=this.font.params.axis_height;return{H:e,delta:r,u:a.scale*(a.d-a.h)/2+i+r,v:t.scale*(t.d-t.h)/2+i-r,nbox:a,dbox:t}},d.prototype.canStretch=function(s){return!1},d.prototype.isDisplay=function(){var s=this.node.attributes.getList("displaystyle","scriptlevel"),a=s.displaystyle,t=s.scriptlevel;return a&&0===t},d.prototype.getWrapWidth=function(s){var a=this.node.attributes;return a.get("bevelled")?this.childNodes[s].getOuterBBox().w:this.getBBox().w-(this.length2em(a.get("linethickness"))?.2:0)-2*this.pad},d.prototype.getChildAlign=function(s){var a=this.node.attributes;return a.get("bevelled")?"left":a.get(["numalign","denomalign"][s])},d}(v)}},74682:function(L,g){var v,_=this&&this.__extends||(v=function(y,d){return(v=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,a){s.__proto__=a}||function(s,a){for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(s[t]=a[t])})(y,d)},function(y,d){if("function"!=typeof d&&null!==d)throw new TypeError("Class extends value "+String(d)+" is not a constructor or null");function s(){this.constructor=y}v(y,d),y.prototype=null===d?Object.create(d):(s.prototype=d.prototype,new s)}),C=this&&this.__read||function(v,y){var d="function"==typeof Symbol&&v[Symbol.iterator];if(!d)return v;var a,r,s=d.call(v),t=[];try{for(;(void 0===y||y-- >0)&&!(a=s.next()).done;)t.push(a.value)}catch(e){r={error:e}}finally{try{a&&!a.done&&(d=s.return)&&d.call(s)}finally{if(r)throw r.error}}return t},b=this&&this.__spreadArray||function(v,y,d){if(d||2===arguments.length)for(var t,s=0,a=y.length;s<a;s++)(t||!(s in y))&&(t||(t=Array.prototype.slice.call(y,0,s)),t[s]=y[s]);return v.concat(t||Array.prototype.slice.call(y))};Object.defineProperty(g,"__esModule",{value:!0}),g.CommonMglyphMixin=void 0,g.CommonMglyphMixin=function M(v){return function(y){function d(){for(var s=[],a=0;a<arguments.length;a++)s[a]=arguments[a];var t=y.apply(this,b([],C(s),!1))||this;return t.getParameters(),t}return _(d,y),d.prototype.getParameters=function(){var s=this.node.attributes.getList("width","height","valign","src","index"),a=s.width,t=s.height,r=s.valign,e=s.src,i=s.index;if(e)this.width="auto"===a?1:this.length2em(a),this.height="auto"===t?1:this.length2em(t),this.valign=this.length2em(r||"0");else{var o=String.fromCodePoint(parseInt(i)),n=this.node.factory;this.charWrapper=this.wrap(n.create("text").setText(o)),this.charWrapper.parent=this}},d.prototype.computeBBox=function(s,a){void 0===a&&(a=!1),this.charWrapper?s.updateFrom(this.charWrapper.getBBox()):(s.w=this.width,s.h=this.height+this.valign,s.d=-this.valign)},d}(v)}},49445:function(L,g){var b,_=this&&this.__extends||(b=function(M,v){return(b=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(y,d){y.__proto__=d}||function(y,d){for(var s in d)Object.prototype.hasOwnProperty.call(d,s)&&(y[s]=d[s])})(M,v)},function(M,v){if("function"!=typeof v&&null!==v)throw new TypeError("Class extends value "+String(v)+" is not a constructor or null");function y(){this.constructor=M}b(M,v),M.prototype=null===v?Object.create(v):(y.prototype=v.prototype,new y)});Object.defineProperty(g,"__esModule",{value:!0}),g.CommonMiMixin=void 0,g.CommonMiMixin=function C(b){return function(M){function v(){return null!==M&&M.apply(this,arguments)||this}return _(v,M),v.prototype.computeBBox=function(y,d){void 0===d&&(d=!1),M.prototype.computeBBox.call(this,y),this.copySkewIC(y)},v}(b)}},49079:function(L,g,_){var s,C=this&&this.__extends||(s=function(a,t){return(s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,e){r.__proto__=e}||function(r,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(r[i]=e[i])})(a,t)},function(a,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=a}s(a,t),a.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),b=this&&this.__read||function(s,a){var t="function"==typeof Symbol&&s[Symbol.iterator];if(!t)return s;var e,o,r=t.call(s),i=[];try{for(;(void 0===a||a-- >0)&&!(e=r.next()).done;)i.push(e.value)}catch(n){o={error:n}}finally{try{e&&!e.done&&(t=r.return)&&t.call(r)}finally{if(o)throw o.error}}return i},M=this&&this.__spreadArray||function(s,a,t){if(t||2===arguments.length)for(var i,r=0,e=a.length;r<e;r++)(i||!(r in a))&&(i||(i=Array.prototype.slice.call(a,0,r)),i[r]=a[r]);return s.concat(i||Array.prototype.slice.call(a))},v=this&&this.__values||function(s){var a="function"==typeof Symbol&&Symbol.iterator,t=a&&s[a],r=0;if(t)return t.call(s);if(s&&"number"==typeof s.length)return{next:function(){return s&&r>=s.length&&(s=void 0),{value:s&&s[r++],done:!s}}};throw new TypeError(a?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(g,"__esModule",{value:!0}),g.CommonMmultiscriptsMixin=g.ScriptNames=g.NextScript=void 0;var y=_(13380);g.NextScript={base:"subList",subList:"supList",supList:"subList",psubList:"psupList",psupList:"psubList"},g.ScriptNames=["sup","sup","psup","psub"],g.CommonMmultiscriptsMixin=function d(s){return function(a){function t(){for(var r=[],e=0;e<arguments.length;e++)r[e]=arguments[e];var i=a.apply(this,M([],b(r),!1))||this;return i.scriptData=null,i.firstPrescript=0,i.getScriptData(),i}return C(t,a),t.prototype.combinePrePost=function(r,e){var i=new y.BBox(r);return i.combine(e,0,0),i},t.prototype.computeBBox=function(r,e){void 0===e&&(e=!1);var i=this.font.params.scriptspace,o=this.scriptData,n=this.combinePrePost(o.sub,o.psub),l=this.combinePrePost(o.sup,o.psup),u=b(this.getUVQ(n,l),2),h=u[0],c=u[1];if(r.empty(),o.numPrescripts&&(r.combine(o.psup,i,h),r.combine(o.psub,i,c)),r.append(o.base),o.numScripts){var f=r.w;r.combine(o.sup,f,h),r.combine(o.sub,f,c),r.w+=i}r.clean(),this.setChildPWidths(e)},t.prototype.getScriptData=function(){var r=this.scriptData={base:null,sub:y.BBox.empty(),sup:y.BBox.empty(),psub:y.BBox.empty(),psup:y.BBox.empty(),numPrescripts:0,numScripts:0},e=this.getScriptBBoxLists();this.combineBBoxLists(r.sub,r.sup,e.subList,e.supList),this.combineBBoxLists(r.psub,r.psup,e.psubList,e.psupList),r.base=e.base[0],r.numPrescripts=e.psubList.length,r.numScripts=e.subList.length},t.prototype.getScriptBBoxLists=function(){var r,e,i={base:[],subList:[],supList:[],psubList:[],psupList:[]},o="base";try{for(var n=v(this.childNodes),l=n.next();!l.done;l=n.next()){var u=l.value;u.node.isKind("mprescripts")?o="psubList":(i[o].push(u.getOuterBBox()),o=g.NextScript[o])}}catch(h){r={error:h}}finally{try{l&&!l.done&&(e=n.return)&&e.call(n)}finally{if(r)throw r.error}}return this.firstPrescript=i.subList.length+i.supList.length+2,this.padLists(i.subList,i.supList),this.padLists(i.psubList,i.psupList),i},t.prototype.padLists=function(r,e){r.length>e.length&&e.push(y.BBox.empty())},t.prototype.combineBBoxLists=function(r,e,i,o){for(var n=0;n<i.length;n++){var l=b(this.getScaledWHD(i[n]),3),u=l[0],h=l[1],c=l[2],f=b(this.getScaledWHD(o[n]),3),p=f[0],m=f[1],x=f[2],w=Math.max(u,p);r.w+=w,e.w+=w,h>r.h&&(r.h=h),c>r.d&&(r.d=c),m>e.h&&(e.h=m),x>e.d&&(e.d=x)}},t.prototype.getScaledWHD=function(r){var e=r.w,i=r.h,o=r.d,n=r.rscale;return[e*n,i*n,o*n]},t.prototype.getUVQ=function(r,e){var i;if(!this.UVQ){var o=b([0,0,0],3),n=o[0],l=o[1],u=o[2];0===r.h&&0===r.d?n=this.getU():0===e.h&&0===e.d?n=-this.getV():(n=(i=b(a.prototype.getUVQ.call(this,r,e),3))[0],l=i[1],u=i[2]),this.UVQ=[n,l,u]}return this.UVQ},t}(s)}},50768:function(L,g){var b,_=this&&this.__extends||(b=function(M,v){return(b=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(y,d){y.__proto__=d}||function(y,d){for(var s in d)Object.prototype.hasOwnProperty.call(d,s)&&(y[s]=d[s])})(M,v)},function(M,v){if("function"!=typeof v&&null!==v)throw new TypeError("Class extends value "+String(v)+" is not a constructor or null");function y(){this.constructor=M}b(M,v),M.prototype=null===v?Object.create(v):(y.prototype=v.prototype,new y)});Object.defineProperty(g,"__esModule",{value:!0}),g.CommonMnMixin=void 0,g.CommonMnMixin=function C(b){return function(M){function v(){return null!==M&&M.apply(this,arguments)||this}return _(v,M),v.prototype.remapChars=function(y){if(y.length){var d=this.font.getRemappedChar("mn",y[0]);if(d){var s=this.unicodeChars(d,this.variant);1===s.length?y[0]=s[0]:y=s.concat(y.slice(1))}}return y},v}(b)}},3362:function(L,g,_){var d,e,C=this&&this.__extends||(e=function(i,o){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,l){n.__proto__=l}||function(n,l){for(var u in l)Object.prototype.hasOwnProperty.call(l,u)&&(n[u]=l[u])})(i,o)},function(i,o){if("function"!=typeof o&&null!==o)throw new TypeError("Class extends value "+String(o)+" is not a constructor or null");function n(){this.constructor=i}e(i,o),i.prototype=null===o?Object.create(o):(n.prototype=o.prototype,new n)}),b=this&&this.__assign||function(){return b=Object.assign||function(e){for(var i,o=1,n=arguments.length;o<n;o++)for(var l in i=arguments[o])Object.prototype.hasOwnProperty.call(i,l)&&(e[l]=i[l]);return e},b.apply(this,arguments)},M=this&&this.__read||function(e,i){var o="function"==typeof Symbol&&e[Symbol.iterator];if(!o)return e;var l,h,n=o.call(e),u=[];try{for(;(void 0===i||i-- >0)&&!(l=n.next()).done;)u.push(l.value)}catch(c){h={error:c}}finally{try{l&&!l.done&&(o=n.return)&&o.call(n)}finally{if(h)throw h.error}}return u},v=this&&this.__spreadArray||function(e,i,o){if(o||2===arguments.length)for(var u,n=0,l=i.length;n<l;n++)(u||!(n in i))&&(u||(u=Array.prototype.slice.call(i,0,n)),u[n]=i[n]);return e.concat(u||Array.prototype.slice.call(i))},y=this&&this.__values||function(e){var i="function"==typeof Symbol&&Symbol.iterator,o=i&&e[i],n=0;if(o)return o.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&n>=e.length&&(e=void 0),{value:e&&e[n++],done:!e}}};throw new TypeError(i?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(g,"__esModule",{value:!0}),g.CommonMoMixin=g.DirectionVH=void 0;var s=_(13380),a=_(74267),t=_(91609);g.DirectionVH=((d={})[1]="v",d[2]="h",d),g.CommonMoMixin=function r(e){return function(i){function o(){for(var n=[],l=0;l<arguments.length;l++)n[l]=arguments[l];var u=i.apply(this,v([],M(n),!1))||this;return u.size=null,u.isAccent=u.node.isAccent,u}return C(o,i),o.prototype.computeBBox=function(n,l){if(void 0===l&&(l=!1),this.protoBBox(n),this.node.attributes.get("symmetric")&&2!==this.stretch.dir){var u=this.getCenterOffset(n);n.h+=u,n.d-=u}this.node.getProperty("mathaccent")&&(0===this.stretch.dir||this.size>=0)&&(n.w=0)},o.prototype.protoBBox=function(n){var l=0!==this.stretch.dir;l&&null===this.size&&this.getStretchedVariant([0]),!(l&&this.size<0)&&(i.prototype.computeBBox.call(this,n),this.copySkewIC(n))},o.prototype.getAccentOffset=function(){var n=s.BBox.empty();return this.protoBBox(n),-n.w/2},o.prototype.getCenterOffset=function(n){return void 0===n&&(n=null),n||(n=s.BBox.empty(),i.prototype.computeBBox.call(this,n)),(n.h+n.d)/2+this.font.params.axis_height-n.h},o.prototype.getVariant=function(){this.node.attributes.get("largeop")?this.variant=this.node.attributes.get("displaystyle")?"-largeop":"-smallop":this.node.attributes.getExplicit("mathvariant")||!1!==this.node.getProperty("pseudoscript")?i.prototype.getVariant.call(this):this.variant="-tex-variant"},o.prototype.canStretch=function(n){if(0!==this.stretch.dir)return this.stretch.dir===n;if(!this.node.attributes.get("stretchy"))return!1;var u=this.getText();if(1!==Array.from(u).length)return!1;var h=this.font.getDelimiter(u.codePointAt(0));return this.stretch=h&&h.dir===n?h:t.NOSTRETCH,0!==this.stretch.dir},o.prototype.getStretchedVariant=function(n,l){var u,h;if(void 0===l&&(l=!1),0!==this.stretch.dir){var c=this.getWH(n),f=this.getSize("minsize",0),p=this.getSize("maxsize",1/0),m=this.node.getProperty("mathaccent");c=Math.max(f,Math.min(p,c));var x=this.font.params.delimiterfactor/1e3,w=this.font.params.delimitershortfall,j=f||l?c:m?Math.min(c/x,c+w):Math.max(c*x,c-w),S=this.stretch,T=S.c||this.getText().codePointAt(0),O=0;if(S.sizes)try{for(var B=y(S.sizes),A=B.next();!A.done;A=B.next()){if(A.value>=j)return m&&O&&O--,this.variant=this.font.getSizeVariant(T,O),this.size=O,void(S.schar&&S.schar[O]&&(this.stretch=b(b({},this.stretch),{c:S.schar[O]})));O++}}catch(P){u={error:P}}finally{try{A&&!A.done&&(h=B.return)&&h.call(B)}finally{if(u)throw u.error}}S.stretch?(this.size=-1,this.invalidateBBox(),this.getStretchBBox(n,this.checkExtendedHeight(c,S),S)):(this.variant=this.font.getSizeVariant(T,O-1),this.size=O-1)}},o.prototype.getSize=function(n,l){var u=this.node.attributes;return u.isSet(n)&&(l=this.length2em(u.get(n),1,1)),l},o.prototype.getWH=function(n){if(0===n.length)return 0;if(1===n.length)return n[0];var l=M(n,2),u=l[0],h=l[1],c=this.font.params.axis_height;return this.node.attributes.get("symmetric")?2*Math.max(u-c,h+c):u+h},o.prototype.getStretchBBox=function(n,l,u){var h;u.hasOwnProperty("min")&&u.min>l&&(l=u.min);var c=M(u.HDW,3),f=c[0],p=c[1],m=c[2];1===this.stretch.dir?(f=(h=M(this.getBaseline(n,l,u),2))[0],p=h[1]):m=l,this.bbox.h=f,this.bbox.d=p,this.bbox.w=m},o.prototype.getBaseline=function(n,l,u){var h=2===n.length&&n[0]+n[1]===l,c=this.node.attributes.get("symmetric"),f=M(h?n:[l,0],2),p=f[0],m=f[1],x=M([p+m,0],2),w=x[0],j=x[1];if(c){var S=this.font.params.axis_height;h&&(w=2*Math.max(p-S,m+S)),j=w/2-S}else if(h)j=m;else{var T=M(u.HDW||[.75,.25],2),O=T[0],B=T[1];j=B*(w/(O+B))}return[w-j,j]},o.prototype.checkExtendedHeight=function(n,l){if(l.fullExt){var u=M(l.fullExt,2),h=u[0],c=u[1];n=c+Math.ceil(Math.max(0,n-c)/h)*h}return n},o.prototype.remapChars=function(n){var l=this.node.getProperty("primes");if(l)return(0,a.unicodeChars)(l);if(1===n.length){var u=this.node.coreParent().parent,c=this.isAccent&&!u.isKind("mrow")?"accent":"mo",f=this.font.getRemappedChar(c,n[0]);f&&(n=this.unicodeChars(f,this.variant))}return n},o}(e)}},16819:function(L,g){var M,_=this&&this.__extends||(M=function(v,y){return(M=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,s){d.__proto__=s}||function(d,s){for(var a in s)Object.prototype.hasOwnProperty.call(s,a)&&(d[a]=s[a])})(v,y)},function(v,y){if("function"!=typeof y&&null!==y)throw new TypeError("Class extends value "+String(y)+" is not a constructor or null");function d(){this.constructor=v}M(v,y),v.prototype=null===y?Object.create(y):(d.prototype=y.prototype,new d)}),C=this&&this.__read||function(M,v){var y="function"==typeof Symbol&&M[Symbol.iterator];if(!y)return M;var s,t,d=y.call(M),a=[];try{for(;(void 0===v||v-- >0)&&!(s=d.next()).done;)a.push(s.value)}catch(r){t={error:r}}finally{try{s&&!s.done&&(y=d.return)&&y.call(d)}finally{if(t)throw t.error}}return a};Object.defineProperty(g,"__esModule",{value:!0}),g.CommonMpaddedMixin=void 0,g.CommonMpaddedMixin=function b(M){return function(v){function y(){return null!==v&&v.apply(this,arguments)||this}return _(y,v),y.prototype.getDimens=function(){var d=this.node.attributes.getList("width","height","depth","lspace","voffset"),s=this.childNodes[0].getBBox(),a=s.w,t=s.h,r=s.d,e=a,i=t,o=r,n=0,l=0,u=0;""!==d.width&&(a=this.dimen(d.width,s,"w",0)),""!==d.height&&(t=this.dimen(d.height,s,"h",0)),""!==d.depth&&(r=this.dimen(d.depth,s,"d",0)),""!==d.voffset&&(l=this.dimen(d.voffset,s)),""!==d.lspace&&(n=this.dimen(d.lspace,s));var h=this.node.attributes.get("data-align");return h&&(u=this.getAlignX(a,s,h)),[i,o,e,t-i,r-o,a-e,n,l,u]},y.prototype.dimen=function(d,s,a,t){void 0===a&&(a=""),void 0===t&&(t=null);var r=(d=String(d)).match(/width|height|depth/),e=r?s[r[0].charAt(0)]:a?s[a]:0,i=this.length2em(d,e)||0;return d.match(/^[-+]/)&&a&&(i+=e),null!=t&&(i=Math.max(t,i)),i},y.prototype.computeBBox=function(d,s){void 0===s&&(s=!1);var a=C(this.getDimens(),6),t=a[0],r=a[1],e=a[2],i=a[3],o=a[4],n=a[5];d.w=e+n,d.h=t+i,d.d=r+o,this.setChildPWidths(s,d.w)},y.prototype.getWrapWidth=function(d){return this.getBBox().w},y.prototype.getChildAlign=function(d){return this.node.attributes.get("data-align")||"left"},y}(M)}},8242:function(L,g){var b,_=this&&this.__extends||(b=function(M,v){return(b=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(y,d){y.__proto__=d}||function(y,d){for(var s in d)Object.prototype.hasOwnProperty.call(d,s)&&(y[s]=d[s])})(M,v)},function(M,v){if("function"!=typeof v&&null!==v)throw new TypeError("Class extends value "+String(v)+" is not a constructor or null");function y(){this.constructor=M}b(M,v),M.prototype=null===v?Object.create(v):(y.prototype=v.prototype,new y)});Object.defineProperty(g,"__esModule",{value:!0}),g.CommonMrootMixin=void 0,g.CommonMrootMixin=function C(b){return function(M){function v(){return null!==M&&M.apply(this,arguments)||this}return _(v,M),Object.defineProperty(v.prototype,"surd",{get:function(){return 2},enumerable:!1,configurable:!0}),Object.defineProperty(v.prototype,"root",{get:function(){return 1},enumerable:!1,configurable:!0}),v.prototype.combineRootBBox=function(y,d,s){var a=this.childNodes[this.root].getOuterBBox(),t=this.getRootDimens(d,s)[1];y.combine(a,0,t)},v.prototype.getRootDimens=function(y,d){var s=this.childNodes[this.surd],a=this.childNodes[this.root].getOuterBBox(),t=(s.size<0?.5:.6)*y.w,r=a.w,e=a.rscale,i=Math.max(r,t/e),o=Math.max(0,i-r);return[i*e-t,this.rootHeight(a,y,s.size,d),o]},v.prototype.rootHeight=function(y,d,s,a){var t=d.h+d.d;return(s<0?1.9:.55*t)-(t-a)+Math.max(0,y.d*y.rscale)},v}(b)}},30887:function(L,g,_){var a,C=this&&this.__extends||(a=function(t,r){return(a=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,i){e.__proto__=i}||function(e,i){for(var o in i)Object.prototype.hasOwnProperty.call(i,o)&&(e[o]=i[o])})(t,r)},function(t,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function e(){this.constructor=t}a(t,r),t.prototype=null===r?Object.create(r):(e.prototype=r.prototype,new e)}),b=this&&this.__read||function(a,t){var r="function"==typeof Symbol&&a[Symbol.iterator];if(!r)return a;var i,n,e=r.call(a),o=[];try{for(;(void 0===t||t-- >0)&&!(i=e.next()).done;)o.push(i.value)}catch(l){n={error:l}}finally{try{i&&!i.done&&(r=e.return)&&r.call(e)}finally{if(n)throw n.error}}return o},M=this&&this.__spreadArray||function(a,t,r){if(r||2===arguments.length)for(var o,e=0,i=t.length;e<i;e++)(o||!(e in t))&&(o||(o=Array.prototype.slice.call(t,0,e)),o[e]=t[e]);return a.concat(o||Array.prototype.slice.call(t))},v=this&&this.__values||function(a){var t="function"==typeof Symbol&&Symbol.iterator,r=t&&a[t],e=0;if(r)return r.call(a);if(a&&"number"==typeof a.length)return{next:function(){return a&&e>=a.length&&(a=void 0),{value:a&&a[e++],done:!a}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(g,"__esModule",{value:!0}),g.CommonInferredMrowMixin=g.CommonMrowMixin=void 0;var y=_(13380);g.CommonMrowMixin=function d(a){return function(t){function r(){for(var e,i,o=[],n=0;n<arguments.length;n++)o[n]=arguments[n];var l=t.apply(this,M([],b(o),!1))||this;l.stretchChildren();try{for(var u=v(l.childNodes),h=u.next();!h.done;h=u.next()){var c=h.value;if(c.bbox.pwidth){l.bbox.pwidth=y.BBox.fullWidth;break}}}catch(f){e={error:f}}finally{try{h&&!h.done&&(i=u.return)&&i.call(u)}finally{if(e)throw e.error}}return l}return C(r,t),Object.defineProperty(r.prototype,"fixesPWidth",{get:function(){return!1},enumerable:!1,configurable:!0}),r.prototype.stretchChildren=function(){var e,i,o,n,l,u,h=[];try{for(var c=v(this.childNodes),f=c.next();!f.done;f=c.next()){(p=f.value).canStretch(1)&&h.push(p)}}catch(E){e={error:E}}finally{try{f&&!f.done&&(i=c.return)&&i.call(c)}finally{if(e)throw e.error}}var m=h.length,x=this.childNodes.length;if(m&&x>1){var w=0,j=0,S=m>1&&m===x;try{for(var T=v(this.childNodes),O=T.next();!O.done;O=T.next()){var B=0===(p=O.value).stretch.dir;if(S||B){var A=p.getOuterBBox(B),H=A.h,P=A.d,W=A.rscale;(H*=W)>w&&(w=H),(P*=W)>j&&(j=P)}}}catch(E){o={error:E}}finally{try{O&&!O.done&&(n=T.return)&&n.call(T)}finally{if(o)throw o.error}}try{for(var N=v(h),k=N.next();!k.done;k=N.next()){var p;(p=k.value).coreMO().getStretchedVariant([w,j])}}catch(E){l={error:E}}finally{try{k&&!k.done&&(u=N.return)&&u.call(N)}finally{if(l)throw l.error}}}},r}(a)},g.CommonInferredMrowMixin=function s(a){return function(t){function r(){return null!==t&&t.apply(this,arguments)||this}return C(r,t),r.prototype.getScale=function(){this.bbox.scale=this.parent.bbox.scale,this.bbox.rscale=1},r}(a)}},93952:function(L,g){var v,_=this&&this.__extends||(v=function(y,d){return(v=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,a){s.__proto__=a}||function(s,a){for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(s[t]=a[t])})(y,d)},function(y,d){if("function"!=typeof d&&null!==d)throw new TypeError("Class extends value "+String(d)+" is not a constructor or null");function s(){this.constructor=y}v(y,d),y.prototype=null===d?Object.create(d):(s.prototype=d.prototype,new s)}),C=this&&this.__read||function(v,y){var d="function"==typeof Symbol&&v[Symbol.iterator];if(!d)return v;var a,r,s=d.call(v),t=[];try{for(;(void 0===y||y-- >0)&&!(a=s.next()).done;)t.push(a.value)}catch(e){r={error:e}}finally{try{a&&!a.done&&(d=s.return)&&d.call(s)}finally{if(r)throw r.error}}return t},b=this&&this.__spreadArray||function(v,y,d){if(d||2===arguments.length)for(var t,s=0,a=y.length;s<a;s++)(t||!(s in y))&&(t||(t=Array.prototype.slice.call(y,0,s)),t[s]=y[s]);return v.concat(t||Array.prototype.slice.call(y))};Object.defineProperty(g,"__esModule",{value:!0}),g.CommonMsMixin=void 0,g.CommonMsMixin=function M(v){return function(y){function d(){for(var s=[],a=0;a<arguments.length;a++)s[a]=arguments[a];var t=y.apply(this,b([],C(s),!1))||this,r=t.node.attributes,e=r.getList("lquote","rquote");return"monospace"!==t.variant&&(!r.isSet("lquote")&&'"'===e.lquote&&(e.lquote="\u201c"),!r.isSet("rquote")&&'"'===e.rquote&&(e.rquote="\u201d")),t.childNodes.unshift(t.createText(e.lquote)),t.childNodes.push(t.createText(e.rquote)),t}return _(d,y),d.prototype.createText=function(s){var a=this.wrap(this.mmlText(s));return a.parent=this,a},d}(v)}},49697:function(L,g){var b,_=this&&this.__extends||(b=function(M,v){return(b=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(y,d){y.__proto__=d}||function(y,d){for(var s in d)Object.prototype.hasOwnProperty.call(d,s)&&(y[s]=d[s])})(M,v)},function(M,v){if("function"!=typeof v&&null!==v)throw new TypeError("Class extends value "+String(v)+" is not a constructor or null");function y(){this.constructor=M}b(M,v),M.prototype=null===v?Object.create(v):(y.prototype=v.prototype,new y)});Object.defineProperty(g,"__esModule",{value:!0}),g.CommonMspaceMixin=void 0,g.CommonMspaceMixin=function C(b){return function(M){function v(){return null!==M&&M.apply(this,arguments)||this}return _(v,M),v.prototype.computeBBox=function(y,d){void 0===d&&(d=!1);var s=this.node.attributes;y.w=this.length2em(s.get("width"),0),y.h=this.length2em(s.get("height"),0),y.d=this.length2em(s.get("depth"),0)},v.prototype.handleVariant=function(){},v}(b)}},72472:function(L,g,_){var d,C=this&&this.__extends||(d=function(s,a){return(d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(t[e]=r[e])})(s,a)},function(s,a){if("function"!=typeof a&&null!==a)throw new TypeError("Class extends value "+String(a)+" is not a constructor or null");function t(){this.constructor=s}d(s,a),s.prototype=null===a?Object.create(a):(t.prototype=a.prototype,new t)}),b=this&&this.__read||function(d,s){var a="function"==typeof Symbol&&d[Symbol.iterator];if(!a)return d;var r,i,t=a.call(d),e=[];try{for(;(void 0===s||s-- >0)&&!(r=t.next()).done;)e.push(r.value)}catch(o){i={error:o}}finally{try{r&&!r.done&&(a=t.return)&&a.call(t)}finally{if(i)throw i.error}}return e},M=this&&this.__spreadArray||function(d,s,a){if(a||2===arguments.length)for(var e,t=0,r=s.length;t<r;t++)(e||!(t in s))&&(e||(e=Array.prototype.slice.call(s,0,t)),e[t]=s[t]);return d.concat(e||Array.prototype.slice.call(s))};Object.defineProperty(g,"__esModule",{value:!0}),g.CommonMsqrtMixin=void 0;var v=_(13380);g.CommonMsqrtMixin=function y(d){return function(s){function a(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var e=s.apply(this,M([],b(t),!1))||this,i=e.createMo("\u221a");i.canStretch(1);var o=e.childNodes[e.base].getOuterBBox(),n=o.h,l=o.d,u=e.font.params.rule_thickness,h=e.node.attributes.get("displaystyle")?e.font.params.x_height:u;return e.surdH=n+l+2*u+h/4,i.getStretchedVariant([e.surdH-l,l],!0),e}return C(a,s),Object.defineProperty(a.prototype,"base",{get:function(){return 0},enumerable:!1,configurable:!0}),Object.defineProperty(a.prototype,"surd",{get:function(){return 1},enumerable:!1,configurable:!0}),Object.defineProperty(a.prototype,"root",{get:function(){return null},enumerable:!1,configurable:!0}),a.prototype.createMo=function(t){var r=s.prototype.createMo.call(this,t);return this.childNodes.push(r),r},a.prototype.computeBBox=function(t,r){void 0===r&&(r=!1);var e=this.childNodes[this.surd].getBBox(),i=new v.BBox(this.childNodes[this.base].getOuterBBox()),o=this.getPQ(e)[1],n=this.font.params.rule_thickness,l=i.h+o+n,h=b(this.getRootDimens(e,l),1)[0];t.h=l+n,this.combineRootBBox(t,e,l),t.combine(e,h,l-e.h),t.combine(i,h+e.w,0),t.clean(),this.setChildPWidths(r)},a.prototype.combineRootBBox=function(t,r,e){},a.prototype.getPQ=function(t){var r=this.font.params.rule_thickness,e=this.node.attributes.get("displaystyle")?this.font.params.x_height:r;return[e,t.h+t.d>this.surdH?(t.h+t.d-(this.surdH-2*r-e/2))/2:r+e/4]},a.prototype.getRootDimens=function(t,r){return[0,0,0,0]},a}(d)}},39934:function(L,g){var y,_=this&&this.__extends||(y=function(d,s){return(y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,t){a.__proto__=t}||function(a,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(a[r]=t[r])})(d,s)},function(d,s){if("function"!=typeof s&&null!==s)throw new TypeError("Class extends value "+String(s)+" is not a constructor or null");function a(){this.constructor=d}y(d,s),d.prototype=null===s?Object.create(s):(a.prototype=s.prototype,new a)}),C=this&&this.__read||function(y,d){var s="function"==typeof Symbol&&y[Symbol.iterator];if(!s)return y;var t,e,a=s.call(y),r=[];try{for(;(void 0===d||d-- >0)&&!(t=a.next()).done;)r.push(t.value)}catch(i){e={error:i}}finally{try{t&&!t.done&&(s=a.return)&&s.call(a)}finally{if(e)throw e.error}}return r};Object.defineProperty(g,"__esModule",{value:!0}),g.CommonMsubsupMixin=g.CommonMsupMixin=g.CommonMsubMixin=void 0,g.CommonMsubMixin=function b(y){var d;return d=function(s){function a(){return null!==s&&s.apply(this,arguments)||this}return _(a,s),Object.defineProperty(a.prototype,"scriptChild",{get:function(){return this.childNodes[this.node.sub]},enumerable:!1,configurable:!0}),a.prototype.getOffset=function(){return[0,-this.getV()]},a}(y),d.useIC=!1,d},g.CommonMsupMixin=function M(y){return function(d){function s(){return null!==d&&d.apply(this,arguments)||this}return _(s,d),Object.defineProperty(s.prototype,"scriptChild",{get:function(){return this.childNodes[this.node.sup]},enumerable:!1,configurable:!0}),s.prototype.getOffset=function(){return[this.getAdjustedIc()-(this.baseRemoveIc?0:this.baseIc),this.getU()]},s}(y)},g.CommonMsubsupMixin=function v(y){var d;return d=function(s){function a(){var t=null!==s&&s.apply(this,arguments)||this;return t.UVQ=null,t}return _(a,s),Object.defineProperty(a.prototype,"subChild",{get:function(){return this.childNodes[this.node.sub]},enumerable:!1,configurable:!0}),Object.defineProperty(a.prototype,"supChild",{get:function(){return this.childNodes[this.node.sup]},enumerable:!1,configurable:!0}),a.prototype.computeBBox=function(t,r){void 0===r&&(r=!1);var e=this.baseChild.getOuterBBox(),i=C([this.subChild.getOuterBBox(),this.supChild.getOuterBBox()],2),o=i[0],n=i[1];t.empty(),t.append(e);var l=this.getBaseWidth(),u=this.getAdjustedIc(),h=C(this.getUVQ(),2),c=h[0],f=h[1];t.combine(o,l,f),t.combine(n,l+u,c),t.w+=this.font.params.scriptspace,t.clean(),this.setChildPWidths(r)},a.prototype.getUVQ=function(t,r){void 0===t&&(t=this.subChild.getOuterBBox()),void 0===r&&(r=this.supChild.getOuterBBox());var e=this.baseCore.getOuterBBox();if(this.UVQ)return this.UVQ;var i=this.font.params,o=3*i.rule_thickness,n=this.length2em(this.node.attributes.get("subscriptshift"),i.sub2),l=this.baseCharZero(e.d*this.baseScale+i.sub_drop*t.rscale),u=C([this.getU(),Math.max(l,n)],2),h=u[0],c=u[1],f=h-r.d*r.rscale-(t.h*t.rscale-c);if(f<o){c+=o-f;var p=.8*i.x_height-(h-r.d*r.rscale);p>0&&(h+=p,c-=p)}return h=Math.max(this.length2em(this.node.attributes.get("superscriptshift"),h),h),c=Math.max(this.length2em(this.node.attributes.get("subscriptshift"),c),c),f=h-r.d*r.rscale-(t.h*t.rscale-c),this.UVQ=[h,-c,f],this.UVQ},a}(y),d.useIC=!1,d}},5010:function(L,g,_){var t,C=this&&this.__extends||(t=function(r,e){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,o){i.__proto__=o}||function(i,o){for(var n in o)Object.prototype.hasOwnProperty.call(o,n)&&(i[n]=o[n])})(r,e)},function(r,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}),b=this&&this.__read||function(t,r){var e="function"==typeof Symbol&&t[Symbol.iterator];if(!e)return t;var o,l,i=e.call(t),n=[];try{for(;(void 0===r||r-- >0)&&!(o=i.next()).done;)n.push(o.value)}catch(u){l={error:u}}finally{try{o&&!o.done&&(e=i.return)&&e.call(i)}finally{if(l)throw l.error}}return n},M=this&&this.__spreadArray||function(t,r,e){if(e||2===arguments.length)for(var n,i=0,o=r.length;i<o;i++)(n||!(i in r))&&(n||(n=Array.prototype.slice.call(r,0,i)),n[i]=r[i]);return t.concat(n||Array.prototype.slice.call(r))},v=this&&this.__values||function(t){var r="function"==typeof Symbol&&Symbol.iterator,e=r&&t[r],i=0;if(e)return e.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&i>=t.length&&(t=void 0),{value:t&&t[i++],done:!t}}};throw new TypeError(r?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(g,"__esModule",{value:!0}),g.CommonMtableMixin=void 0;var y=_(13380),d=_(74267),s=_(89711);g.CommonMtableMixin=function a(t){return function(r){function e(){for(var i=[],o=0;o<arguments.length;o++)i[o]=arguments[o];var n=r.apply(this,M([],b(i),!1))||this;n.numCols=0,n.numRows=0,n.data=null,n.pwidthCells=[],n.pWidth=0,n.numCols=(0,s.max)(n.tableRows.map(function(u){return u.numCells})),n.numRows=n.childNodes.length,n.hasLabels=n.childNodes.reduce(function(u,h){return u||h.node.isKind("mlabeledtr")},!1),n.findContainer(),n.isTop=!n.container||n.container.node.isKind("math")&&!n.container.parent,n.isTop&&(n.jax.table=n),n.getPercentageWidth();var l=n.node.attributes;return n.frame="none"!==l.get("frame"),n.fLine=n.frame&&l.get("frame")?.07:0,n.fSpace=n.frame?n.convertLengths(n.getAttributeArray("framespacing")):[0,0],n.cSpace=n.convertLengths(n.getColumnAttributes("columnspacing")),n.rSpace=n.convertLengths(n.getRowAttributes("rowspacing")),n.cLines=n.getColumnAttributes("columnlines").map(function(u){return"none"===u?0:.07}),n.rLines=n.getRowAttributes("rowlines").map(function(u){return"none"===u?0:.07}),n.cWidths=n.getColumnWidths(),n.stretchRows(),n.stretchColumns(),n}return C(e,r),Object.defineProperty(e.prototype,"tableRows",{get:function(){return this.childNodes},enumerable:!1,configurable:!0}),e.prototype.findContainer=function(){for(var i=this,o=i.parent;o&&(o.node.notParent||o.node.isKind("mrow"));)i=o,o=o.parent;this.container=o,this.containerI=i.node.childPosition()},e.prototype.getPercentageWidth=function(){if(this.hasLabels)this.bbox.pwidth=y.BBox.fullWidth;else{var i=this.node.attributes.get("width");(0,d.isPercent)(i)&&(this.bbox.pwidth=i)}},e.prototype.stretchRows=function(){for(var i=this.node.attributes.get("equalrows"),o=i?this.getEqualRowHeight():0,n=i?this.getTableData():{H:[0],D:[0]},l=n.H,u=n.D,h=this.tableRows,c=0;c<this.numRows;c++){var f=i?[(o+l[c]-u[c])/2,(o-l[c]+u[c])/2]:null;h[c].stretchChildren(f)}},e.prototype.stretchColumns=function(){for(var i=0;i<this.numCols;i++){var o="number"==typeof this.cWidths[i]?this.cWidths[i]:null;this.stretchColumn(i,o)}},e.prototype.stretchColumn=function(i,o){var n,l,u,h,c,f,p=[];try{for(var m=v(this.tableRows),x=m.next();!x.done;x=m.next()){if(j=x.value.getChild(i))0===(S=j.childNodes[0]).stretch.dir&&S.canStretch(2)&&p.push(S)}}catch(E){n={error:E}}finally{try{x&&!x.done&&(l=m.return)&&l.call(m)}finally{if(n)throw n.error}}var T=p.length,O=this.childNodes.length;if(T&&O>1){if(null===o){o=0;var B=T>1&&T===O;try{for(var A=v(this.tableRows),H=A.next();!H.done;H=A.next()){var j;if(j=H.value.getChild(i)){var P=0===(S=j.childNodes[0]).stretch.dir;if(B||P){var W=S.getBBox(P).w;W>o&&(o=W)}}}}catch(E){u={error:E}}finally{try{H&&!H.done&&(h=A.return)&&h.call(A)}finally{if(u)throw u.error}}}try{for(var N=v(p),k=N.next();!k.done;k=N.next()){var S;(S=k.value).coreMO().getStretchedVariant([o])}}catch(E){c={error:E}}finally{try{k&&!k.done&&(f=N.return)&&f.call(N)}finally{if(c)throw c.error}}}},e.prototype.getTableData=function(){if(this.data)return this.data;for(var i=new Array(this.numRows).fill(0),o=new Array(this.numRows).fill(0),n=new Array(this.numCols).fill(0),l=new Array(this.numRows),u=new Array(this.numRows),h=[0],c=this.tableRows,f=0;f<c.length;f++){for(var p=0,m=c[f],x=m.node.attributes.get("rowalign"),w=0;w<m.numCells;w++){var j=m.getChild(w);p=this.updateHDW(j,w,f,x,i,o,n,p),this.recordPWidthCell(j,w)}l[f]=i[f],u[f]=o[f],m.labeled&&(p=this.updateHDW(m.childNodes[0],0,f,x,i,o,h,p)),this.extendHD(f,i,o,p),this.extendHD(f,l,u,p)}var S=h[0];return this.data={H:i,D:o,W:n,NH:l,ND:u,L:S},this.data},e.prototype.updateHDW=function(i,o,n,l,u,h,c,f){var p=i.getBBox(),m=p.h,x=p.d,w=p.w,j=i.parent.bbox.rscale;1!==i.parent.bbox.rscale&&(m*=j,x*=j,w*=j),this.node.getProperty("useHeight")&&(m<.75&&(m=.75),x<.25&&(x=.25));var S=0;return"baseline"!==(l=i.node.attributes.get("rowalign")||l)&&"axis"!==l&&(S=m+x,m=x=0),m>u[n]&&(u[n]=m),x>h[n]&&(h[n]=x),S>f&&(f=S),c&&w>c[o]&&(c[o]=w),f},e.prototype.extendHD=function(i,o,n,l){var u=(l-(o[i]+n[i]))/2;u<1e-5||(o[i]+=u,n[i]+=u)},e.prototype.recordPWidthCell=function(i,o){i.childNodes[0]&&i.childNodes[0].getBBox().pwidth&&this.pwidthCells.push([i,o])},e.prototype.computeBBox=function(i,o){void 0===o&&(o=!1);var h,c,n=this.getTableData(),l=n.H,u=n.D;if(this.node.attributes.get("equalrows")){var f=this.getEqualRowHeight();h=(0,s.sum)([].concat(this.rLines,this.rSpace))+f*this.numRows}else h=(0,s.sum)(l.concat(u,this.rLines,this.rSpace));h+=2*(this.fLine+this.fSpace[1]);var p=this.getComputedWidths();c=(0,s.sum)(p.concat(this.cLines,this.cSpace))+2*(this.fLine+this.fSpace[0]);var m=this.node.attributes.get("width");"auto"!==m&&(c=Math.max(this.length2em(m,0)+2*this.fLine,c));var x=b(this.getBBoxHD(h),2),w=x[0],j=x[1];i.h=w,i.d=j,i.w=c;var S=b(this.getBBoxLR(),2),T=S[0],O=S[1];i.L=T,i.R=O,(0,d.isPercent)(m)||this.setColumnPWidths()},e.prototype.setChildPWidths=function(i,o,n){var l=this.node.attributes.get("width");if(!(0,d.isPercent)(l))return!1;this.hasLabels||(this.bbox.pwidth="",this.container.bbox.pwidth="");var u=this.bbox,h=u.w,c=u.L,f=u.R,p=this.node.attributes.get("data-width-includes-label"),m=Math.max(h,this.length2em(l,Math.max(o,c+h+f)))-(p?c+f:0),x=this.node.attributes.get("equalcolumns")?Array(this.numCols).fill(this.percent(1/Math.max(1,this.numCols))):this.getColumnAttributes("columnwidth",0);this.cWidths=this.getColumnWidthsFixed(x,m);var w=this.getComputedWidths();return this.pWidth=(0,s.sum)(w.concat(this.cLines,this.cSpace))+2*(this.fLine+this.fSpace[0]),this.isTop&&(this.bbox.w=this.pWidth),this.setColumnPWidths(),this.pWidth!==h&&this.parent.invalidateBBox(),this.pWidth!==h},e.prototype.setColumnPWidths=function(){var i,o,n=this.cWidths;try{for(var l=v(this.pwidthCells),u=l.next();!u.done;u=l.next()){var h=b(u.value,2),c=h[0],f=h[1];c.setChildPWidths(!1,n[f])&&(c.invalidateBBox(),c.getBBox())}}catch(p){i={error:p}}finally{try{u&&!u.done&&(o=l.return)&&o.call(l)}finally{if(i)throw i.error}}},e.prototype.getBBoxHD=function(i){var o=b(this.getAlignmentRow(),2),n=o[0],l=o[1];if(null===l){var u=this.font.params.axis_height,h=i/2;return{top:[0,i],center:[h,h],bottom:[i,0],baseline:[h,h],axis:[h+u,h-u]}[n]||[h,h]}var f=this.getVerticalPosition(l,n);return[f,i-f]},e.prototype.getBBoxLR=function(){if(this.hasLabels){var i=this.node.attributes,o=i.get("side"),n=b(this.getPadAlignShift(o),2),l=n[0],u=n[1],h=this.hasLabels&&!!i.get("data-width-includes-label");return h&&this.frame&&this.fSpace[0]&&(l-=this.fSpace[0]),"center"!==u||h?"left"===o?[l,0]:[0,l]:[l,l]}return[0,0]},e.prototype.getPadAlignShift=function(i){var l=this.getTableData().L+this.length2em(this.node.attributes.get("minlabelspacing")),u=b(null==this.styles?["",""]:[this.styles.get("padding-left"),this.styles.get("padding-right")],2),h=u[0],c=u[1];(h||c)&&(l=Math.max(l,this.length2em(h||"0"),this.length2em(c||"0")));var f=b(this.getAlignShift(),2),p=f[0],m=f[1];return p===i&&(m="left"===i?Math.max(l,m)-l:Math.min(-l,m)+l),[l,p,m]},e.prototype.getAlignShift=function(){return this.isTop?r.prototype.getAlignShift.call(this):[this.container.getChildAlign(this.containerI),0]},e.prototype.getWidth=function(){return this.pWidth||this.getBBox().w},e.prototype.getEqualRowHeight=function(){var i=this.getTableData(),o=i.H,n=i.D,l=Array.from(o.keys()).map(function(u){return o[u]+n[u]});return Math.max.apply(Math,l)},e.prototype.getComputedWidths=function(){var i=this,o=this.getTableData().W,n=Array.from(o.keys()).map(function(l){return"number"==typeof i.cWidths[l]?i.cWidths[l]:o[l]});return this.node.attributes.get("equalcolumns")&&(n=Array(n.length).fill((0,s.max)(n))),n},e.prototype.getColumnWidths=function(){var i=this.node.attributes.get("width");if(this.node.attributes.get("equalcolumns"))return this.getEqualColumns(i);var o=this.getColumnAttributes("columnwidth",0);return"auto"===i?this.getColumnWidthsAuto(o):(0,d.isPercent)(i)?this.getColumnWidthsPercent(o):this.getColumnWidthsFixed(o,this.length2em(i))},e.prototype.getEqualColumns=function(i){var n,o=Math.max(1,this.numCols);if("auto"===i){var l=this.getTableData().W;n=(0,s.max)(l)}else if((0,d.isPercent)(i))n=this.percent(1/o);else{var u=(0,s.sum)([].concat(this.cLines,this.cSpace))+2*this.fSpace[0];n=Math.max(0,this.length2em(i)-u)/o}return Array(this.numCols).fill(n)},e.prototype.getColumnWidthsAuto=function(i){var o=this;return i.map(function(n){return"auto"===n||"fit"===n?null:(0,d.isPercent)(n)?n:o.length2em(n)})},e.prototype.getColumnWidthsPercent=function(i){var o=this,n=i.indexOf("fit")>=0,l=(n?this.getTableData():{W:null}).W;return Array.from(i.keys()).map(function(u){var h=i[u];return"fit"===h?null:"auto"===h?n?l[u]:null:(0,d.isPercent)(h)?h:o.length2em(h)})},e.prototype.getColumnWidthsFixed=function(i,o){var n=this,l=Array.from(i.keys()),u=l.filter(function(w){return"fit"===i[w]}),h=l.filter(function(w){return"auto"===i[w]}),c=u.length||h.length,f=(c?this.getTableData():{W:null}).W,p=o-(0,s.sum)([].concat(this.cLines,this.cSpace))-2*this.fSpace[0],m=p;l.forEach(function(w){var j=i[w];m-="fit"===j||"auto"===j?f[w]:n.length2em(j,p)});var x=c&&m>0?m/c:0;return l.map(function(w){var j=i[w];return"fit"===j?f[w]+x:"auto"===j?f[w]+(0===u.length?x:0):n.length2em(j,p)})},e.prototype.getVerticalPosition=function(i,o){for(var n=this.node.attributes.get("equalrows"),l=this.getTableData(),u=l.H,h=l.D,c=n?this.getEqualRowHeight():0,f=this.getRowHalfSpacing(),p=this.fLine,m=0;m<i;m++)p+=f[m]+(n?c:u[m]+h[m])+f[m+1]+this.rLines[m];var x=b(n?[(c+u[i]-h[i])/2,(c-u[i]+h[i])/2]:[u[i],h[i]],2),w=x[0],j=x[1];return p+={top:0,center:f[i]+(w+j)/2,bottom:f[i]+w+j+f[i+1],baseline:f[i]+w,axis:f[i]+w-.25}[o]||0},e.prototype.getEmHalfSpacing=function(i,o,n){void 0===n&&(n=1);var l=this.em(i*n),u=this.addEm(o,2/n);return u.unshift(l),u.push(l),u},e.prototype.getRowHalfSpacing=function(){var i=this.rSpace.map(function(o){return o/2});return i.unshift(this.fSpace[1]),i.push(this.fSpace[1]),i},e.prototype.getColumnHalfSpacing=function(){var i=this.cSpace.map(function(o){return o/2});return i.unshift(this.fSpace[0]),i.push(this.fSpace[0]),i},e.prototype.getAlignmentRow=function(){var i=b((0,d.split)(this.node.attributes.get("align")),2),o=i[0],n=i[1];if(null==n)return[o,null];var l=parseInt(n);return l<0&&(l+=this.numRows+1),[o,l<1||l>this.numRows?null:l-1]},e.prototype.getColumnAttributes=function(i,o){void 0===o&&(o=1);var n=this.numCols-o,l=this.getAttributeArray(i);if(0===l.length)return null;for(;l.length<n;)l.push(l[l.length-1]);return l.length>n&&l.splice(n),l},e.prototype.getRowAttributes=function(i,o){void 0===o&&(o=1);var n=this.numRows-o,l=this.getAttributeArray(i);if(0===l.length)return null;for(;l.length<n;)l.push(l[l.length-1]);return l.length>n&&l.splice(n),l},e.prototype.getAttributeArray=function(i){var o=this.node.attributes.get(i);return o?(0,d.split)(o):[this.node.attributes.getDefault(i)]},e.prototype.addEm=function(i,o){var n=this;return void 0===o&&(o=1),i?i.map(function(l){return n.em(l/o)}):null},e.prototype.convertLengths=function(i){var o=this;return i?i.map(function(n){return o.length2em(n)}):null},e}(t)}},96151:function(L,g){var b,_=this&&this.__extends||(b=function(M,v){return(b=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(y,d){y.__proto__=d}||function(y,d){for(var s in d)Object.prototype.hasOwnProperty.call(d,s)&&(y[s]=d[s])})(M,v)},function(M,v){if("function"!=typeof v&&null!==v)throw new TypeError("Class extends value "+String(v)+" is not a constructor or null");function y(){this.constructor=M}b(M,v),M.prototype=null===v?Object.create(v):(y.prototype=v.prototype,new y)});Object.defineProperty(g,"__esModule",{value:!0}),g.CommonMtdMixin=void 0,g.CommonMtdMixin=function C(b){return function(M){function v(){return null!==M&&M.apply(this,arguments)||this}return _(v,M),Object.defineProperty(v.prototype,"fixesPWidth",{get:function(){return!1},enumerable:!1,configurable:!0}),v.prototype.invalidateBBox=function(){this.bboxComputed=!1},v.prototype.getWrapWidth=function(y){var d=this.parent.parent,s=this.parent,a=this.node.childPosition()-(s.labeled?1:0);return"number"==typeof d.cWidths[a]?d.cWidths[a]:d.getTableData().W[a]},v.prototype.getChildAlign=function(y){return this.node.attributes.get("columnalign")},v}(b)}},30322:function(L,g){var b,_=this&&this.__extends||(b=function(M,v){return(b=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(y,d){y.__proto__=d}||function(y,d){for(var s in d)Object.prototype.hasOwnProperty.call(d,s)&&(y[s]=d[s])})(M,v)},function(M,v){if("function"!=typeof v&&null!==v)throw new TypeError("Class extends value "+String(v)+" is not a constructor or null");function y(){this.constructor=M}b(M,v),M.prototype=null===v?Object.create(v):(y.prototype=v.prototype,new y)});Object.defineProperty(g,"__esModule",{value:!0}),g.CommonMtextMixin=void 0,g.CommonMtextMixin=function C(b){var M;return M=function(v){function y(){return null!==v&&v.apply(this,arguments)||this}return _(y,v),y.prototype.getVariant=function(){var d=this.jax.options,s=this.jax.math.outputData,a=(!!s.merrorFamily||!!d.merrorFont)&&this.node.Parent.isKind("merror");if(s.mtextFamily||d.mtextFont||a){var t=this.node.attributes.get("mathvariant"),r=this.constructor.INHERITFONTS[t]||this.jax.font.getCssFont(t),e=r[0]||(a?s.merrorFamily||d.merrorFont:s.mtextFamily||d.mtextFont);this.variant=this.explicitVariant(e,r[2]?"bold":"",r[1]?"italic":"")}else v.prototype.getVariant.call(this)},y}(b),M.INHERITFONTS={normal:["",!1,!1],bold:["",!1,!0],italic:["",!0,!1],"bold-italic":["",!0,!0]},M}},49078:function(L,g){var v,_=this&&this.__extends||(v=function(y,d){return(v=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(s,a){s.__proto__=a}||function(s,a){for(var t in a)Object.prototype.hasOwnProperty.call(a,t)&&(s[t]=a[t])})(y,d)},function(y,d){if("function"!=typeof d&&null!==d)throw new TypeError("Class extends value "+String(d)+" is not a constructor or null");function s(){this.constructor=y}v(y,d),y.prototype=null===d?Object.create(d):(s.prototype=d.prototype,new s)}),C=this&&this.__values||function(v){var y="function"==typeof Symbol&&Symbol.iterator,d=y&&v[y],s=0;if(d)return d.call(v);if(v&&"number"==typeof v.length)return{next:function(){return v&&s>=v.length&&(v=void 0),{value:v&&v[s++],done:!v}}};throw new TypeError(y?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(g,"__esModule",{value:!0}),g.CommonMlabeledtrMixin=g.CommonMtrMixin=void 0,g.CommonMtrMixin=function b(v){return function(y){function d(){return null!==y&&y.apply(this,arguments)||this}return _(d,y),Object.defineProperty(d.prototype,"fixesPWidth",{get:function(){return!1},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"numCells",{get:function(){return this.childNodes.length},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"labeled",{get:function(){return!1},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"tableCells",{get:function(){return this.childNodes},enumerable:!1,configurable:!0}),d.prototype.getChild=function(s){return this.childNodes[s]},d.prototype.getChildBBoxes=function(){return this.childNodes.map(function(s){return s.getBBox()})},d.prototype.stretchChildren=function(s){var a,t,r,e,i,o;void 0===s&&(s=null);var n=[],l=this.labeled?this.childNodes.slice(1):this.childNodes;try{for(var u=C(l),h=u.next();!h.done;h=u.next()){(f=h.value.childNodes[0]).canStretch(1)&&n.push(f)}}catch(N){a={error:N}}finally{try{h&&!h.done&&(t=u.return)&&t.call(u)}finally{if(a)throw a.error}}var p=n.length,m=this.childNodes.length;if(p&&m>1){if(null===s){var x=0,w=0,j=p>1&&p===m;try{for(var S=C(l),T=S.next();!T.done;T=S.next()){var O=0===(f=T.value.childNodes[0]).stretch.dir;if(j||O){var B=f.getBBox(O),A=B.h,H=B.d;A>x&&(x=A),H>w&&(w=H)}}}catch(N){r={error:N}}finally{try{T&&!T.done&&(e=S.return)&&e.call(S)}finally{if(r)throw r.error}}s=[x,w]}try{for(var P=C(n),W=P.next();!W.done;W=P.next()){var f;(f=W.value).coreMO().getStretchedVariant(s)}}catch(N){i={error:N}}finally{try{W&&!W.done&&(o=P.return)&&o.call(P)}finally{if(i)throw i.error}}}},d}(v)},g.CommonMlabeledtrMixin=function M(v){return function(y){function d(){return null!==y&&y.apply(this,arguments)||this}return _(d,y),Object.defineProperty(d.prototype,"numCells",{get:function(){return Math.max(0,this.childNodes.length-1)},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"labeled",{get:function(){return!0},enumerable:!1,configurable:!0}),Object.defineProperty(d.prototype,"tableCells",{get:function(){return this.childNodes.slice(1)},enumerable:!1,configurable:!0}),d.prototype.getChild=function(s){return this.childNodes[s+1]},d.prototype.getChildBBoxes=function(){return this.childNodes.slice(1).map(function(s){return s.getBBox()})},d}(v)}},55313:function(L,g){var d,_=this&&this.__extends||(d=function(s,a){return(d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(t[e]=r[e])})(s,a)},function(s,a){if("function"!=typeof a&&null!==a)throw new TypeError("Class extends value "+String(a)+" is not a constructor or null");function t(){this.constructor=s}d(s,a),s.prototype=null===a?Object.create(a):(t.prototype=a.prototype,new t)}),C=this&&this.__read||function(d,s){var a="function"==typeof Symbol&&d[Symbol.iterator];if(!a)return d;var r,i,t=a.call(d),e=[];try{for(;(void 0===s||s-- >0)&&!(r=t.next()).done;)e.push(r.value)}catch(o){i={error:o}}finally{try{r&&!r.done&&(a=t.return)&&a.call(t)}finally{if(i)throw i.error}}return e},b=this&&this.__spreadArray||function(d,s,a){if(a||2===arguments.length)for(var e,t=0,r=s.length;t<r;t++)(e||!(t in s))&&(e||(e=Array.prototype.slice.call(s,0,t)),e[t]=s[t]);return d.concat(e||Array.prototype.slice.call(s))};Object.defineProperty(g,"__esModule",{value:!0}),g.CommonMunderoverMixin=g.CommonMoverMixin=g.CommonMunderMixin=void 0,g.CommonMunderMixin=function M(d){return function(s){function a(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var e=s.apply(this,b([],C(t),!1))||this;return e.stretchChildren(),e}return _(a,s),Object.defineProperty(a.prototype,"scriptChild",{get:function(){return this.childNodes[this.node.under]},enumerable:!1,configurable:!0}),a.prototype.computeBBox=function(t,r){if(void 0===r&&(r=!1),this.hasMovableLimits())s.prototype.computeBBox.call(this,t,r);else{t.empty();var e=this.baseChild.getOuterBBox(),i=this.scriptChild.getOuterBBox(),o=this.getUnderKV(e,i)[1],n=this.isLineBelow?0:this.getDelta(!0),l=C(this.getDeltaW([e,i],[0,-n]),2),u=l[0],h=l[1];t.combine(e,u,0),t.combine(i,h,o),t.d+=this.font.params.big_op_spacing5,t.clean(),this.setChildPWidths(r)}},a}(d)},g.CommonMoverMixin=function v(d){return function(s){function a(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var e=s.apply(this,b([],C(t),!1))||this;return e.stretchChildren(),e}return _(a,s),Object.defineProperty(a.prototype,"scriptChild",{get:function(){return this.childNodes[this.node.over]},enumerable:!1,configurable:!0}),a.prototype.computeBBox=function(t){if(this.hasMovableLimits())s.prototype.computeBBox.call(this,t);else{t.empty();var r=this.baseChild.getOuterBBox(),e=this.scriptChild.getOuterBBox();this.node.attributes.get("accent")&&(r.h=Math.max(r.h,this.font.params.x_height*r.scale));var i=this.getOverKU(r,e)[1],o=this.isLineAbove?0:this.getDelta(),n=C(this.getDeltaW([r,e],[0,o]),2),l=n[0],u=n[1];t.combine(r,l,0),t.combine(e,u,i),t.h+=this.font.params.big_op_spacing5,t.clean()}},a}(d)},g.CommonMunderoverMixin=function y(d){return function(s){function a(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var e=s.apply(this,b([],C(t),!1))||this;return e.stretchChildren(),e}return _(a,s),Object.defineProperty(a.prototype,"underChild",{get:function(){return this.childNodes[this.node.under]},enumerable:!1,configurable:!0}),Object.defineProperty(a.prototype,"overChild",{get:function(){return this.childNodes[this.node.over]},enumerable:!1,configurable:!0}),Object.defineProperty(a.prototype,"subChild",{get:function(){return this.underChild},enumerable:!1,configurable:!0}),Object.defineProperty(a.prototype,"supChild",{get:function(){return this.overChild},enumerable:!1,configurable:!0}),a.prototype.computeBBox=function(t){if(this.hasMovableLimits())s.prototype.computeBBox.call(this,t);else{t.empty();var r=this.overChild.getOuterBBox(),e=this.baseChild.getOuterBBox(),i=this.underChild.getOuterBBox();this.node.attributes.get("accent")&&(e.h=Math.max(e.h,this.font.params.x_height*e.scale));var o=this.getOverKU(e,r)[1],n=this.getUnderKV(e,i)[1],l=this.getDelta(),u=C(this.getDeltaW([e,i,r],[0,this.isLineBelow?0:-l,this.isLineAbove?0:l]),3),h=u[0],c=u[1],f=u[2];t.combine(e,h,0),t.combine(r,f,o),t.combine(i,c,n);var p=this.font.params.big_op_spacing5;t.h+=p,t.d+=p,t.clean()}},a}(d)}},77095:function(L,g,_){var s,C=this&&this.__extends||(s=function(a,t){return(s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,e){r.__proto__=e}||function(r,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(r[i]=e[i])})(a,t)},function(a,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=a}s(a,t),a.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),b=this&&this.__read||function(s,a){var t="function"==typeof Symbol&&s[Symbol.iterator];if(!t)return s;var e,o,r=t.call(s),i=[];try{for(;(void 0===a||a-- >0)&&!(e=r.next()).done;)i.push(e.value)}catch(n){o={error:n}}finally{try{e&&!e.done&&(t=r.return)&&t.call(r)}finally{if(o)throw o.error}}return i},M=this&&this.__spreadArray||function(s,a,t){if(t||2===arguments.length)for(var i,r=0,e=a.length;r<e;r++)(i||!(r in a))&&(i||(i=Array.prototype.slice.call(a,0,r)),i[r]=a[r]);return s.concat(i||Array.prototype.slice.call(a))},v=this&&this.__values||function(s){var a="function"==typeof Symbol&&Symbol.iterator,t=a&&s[a],r=0;if(t)return t.call(s);if(s&&"number"==typeof s.length)return{next:function(){return s&&r>=s.length&&(s=void 0),{value:s&&s[r++],done:!s}}};throw new TypeError(a?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(g,"__esModule",{value:!0}),g.CommonScriptbaseMixin=void 0;var y=_(91585);g.CommonScriptbaseMixin=function d(s){var a;return a=function(t){function r(){for(var e=[],i=0;i<arguments.length;i++)e[i]=arguments[i];var o=t.apply(this,M([],b(e),!1))||this;o.baseScale=1,o.baseIc=0,o.baseRemoveIc=!1,o.baseIsChar=!1,o.baseHasAccentOver=null,o.baseHasAccentUnder=null,o.isLineAbove=!1,o.isLineBelow=!1,o.isMathAccent=!1;var n=o.baseCore=o.getBaseCore();return n&&(o.setBaseAccentsFor(n),o.baseScale=o.getBaseScale(),o.baseIc=o.getBaseIc(),o.baseIsChar=o.isCharBase(),o.isMathAccent=o.baseIsChar&&o.scriptChild&&!!o.scriptChild.coreMO().node.getProperty("mathaccent"),o.checkLineAccents(),o.baseRemoveIc=!o.isLineAbove&&!o.isLineBelow&&(!o.constructor.useIC||o.isMathAccent)),o}return C(r,t),Object.defineProperty(r.prototype,"baseChild",{get:function(){return this.childNodes[this.node.base]},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"scriptChild",{get:function(){return this.childNodes[1]},enumerable:!1,configurable:!0}),r.prototype.getBaseCore=function(){for(var e=this.getSemanticBase()||this.childNodes[0];e&&(1===e.childNodes.length&&(e.node.isKind("mrow")||e.node.isKind("TeXAtom")&&e.node.texClass!==y.TEXCLASS.VCENTER||e.node.isKind("mstyle")||e.node.isKind("mpadded")||e.node.isKind("mphantom")||e.node.isKind("semantics"))||e.node.isKind("munderover")&&e.isMathAccent);)this.setBaseAccentsFor(e),e=e.childNodes[0];return e||(this.baseHasAccentOver=this.baseHasAccentUnder=!1),e||this.childNodes[0]},r.prototype.setBaseAccentsFor=function(e){e.node.isKind("munderover")&&(null===this.baseHasAccentOver&&(this.baseHasAccentOver=!!e.node.attributes.get("accent")),null===this.baseHasAccentUnder&&(this.baseHasAccentUnder=!!e.node.attributes.get("accentunder")))},r.prototype.getSemanticBase=function(){var e=this.node.attributes.getExplicit("data-semantic-fencepointer");return this.getBaseFence(this.baseChild,e)},r.prototype.getBaseFence=function(e,i){var o,n;if(!e||!e.node.attributes||!i)return null;if(e.node.attributes.getExplicit("data-semantic-id")===i)return e;try{for(var l=v(e.childNodes),u=l.next();!u.done;u=l.next()){var h=u.value,c=this.getBaseFence(h,i);if(c)return c}}catch(f){o={error:f}}finally{try{u&&!u.done&&(n=l.return)&&n.call(l)}finally{if(o)throw o.error}}return null},r.prototype.getBaseScale=function(){for(var e=this.baseCore,i=1;e&&e!==this;){i*=e.getOuterBBox().rscale,e=e.parent}return i},r.prototype.getBaseIc=function(){return this.baseCore.getOuterBBox().ic*this.baseScale},r.prototype.getAdjustedIc=function(){var e=this.baseCore.getOuterBBox();return(e.ic?1.05*e.ic+.05:0)*this.baseScale},r.prototype.isCharBase=function(){var e=this.baseCore;return(e.node.isKind("mo")&&null===e.size||e.node.isKind("mi")||e.node.isKind("mn"))&&1===e.bbox.rscale&&1===Array.from(e.getText()).length},r.prototype.checkLineAccents=function(){if(this.node.isKind("munderover"))if(this.node.isKind("mover"))this.isLineAbove=this.isLineAccent(this.scriptChild);else if(this.node.isKind("munder"))this.isLineBelow=this.isLineAccent(this.scriptChild);else{this.isLineAbove=this.isLineAccent(this.overChild),this.isLineBelow=this.isLineAccent(this.underChild)}},r.prototype.isLineAccent=function(e){var i=e.coreMO().node;return i.isToken&&"\u2015"===i.getText()},r.prototype.getBaseWidth=function(){var e=this.baseChild.getOuterBBox();return e.w*e.rscale-(this.baseRemoveIc?this.baseIc:0)+this.font.params.extra_ic},r.prototype.computeBBox=function(e,i){void 0===i&&(i=!1);var o=this.getBaseWidth(),n=b(this.getOffset(),2),l=n[0],u=n[1];e.append(this.baseChild.getOuterBBox()),e.combine(this.scriptChild.getOuterBBox(),o+l,u),e.w+=this.font.params.scriptspace,e.clean(),this.setChildPWidths(i)},r.prototype.getOffset=function(){return[0,0]},r.prototype.baseCharZero=function(e){var i=!!this.baseCore.node.attributes.get("largeop"),o=this.baseScale;return this.baseIsChar&&!i&&1===o?0:e},r.prototype.getV=function(){var e=this.baseCore.getOuterBBox(),i=this.scriptChild.getOuterBBox(),o=this.font.params,n=this.length2em(this.node.attributes.get("subscriptshift"),o.sub1);return Math.max(this.baseCharZero(e.d*this.baseScale+o.sub_drop*i.rscale),n,i.h*i.rscale-.8*o.x_height)},r.prototype.getU=function(){var e=this.baseCore.getOuterBBox(),i=this.scriptChild.getOuterBBox(),o=this.font.params,n=this.node.attributes.getList("displaystyle","superscriptshift"),u=this.node.getProperty("texprimestyle")?o.sup3:n.displaystyle?o.sup1:o.sup2,h=this.length2em(n.superscriptshift,u);return Math.max(this.baseCharZero(e.h*this.baseScale-o.sup_drop*i.rscale),h,i.d*i.rscale+1/4*o.x_height)},r.prototype.hasMovableLimits=function(){var e=this.node.attributes.get("displaystyle"),i=this.baseChild.coreMO().node;return!e&&!!i.attributes.get("movablelimits")},r.prototype.getOverKU=function(e,i){var o=this.node.attributes.get("accent"),n=this.font.params,l=i.d*i.rscale,u=n.rule_thickness*n.separation_factor,h=this.baseHasAccentOver?u:0,c=this.isLineAbove?3*n.rule_thickness:u,f=(o?c:Math.max(n.big_op_spacing1,n.big_op_spacing3-Math.max(0,l)))-h;return[f,e.h*e.rscale+f+l]},r.prototype.getUnderKV=function(e,i){var o=this.node.attributes.get("accentunder"),n=this.font.params,l=i.h*i.rscale,u=n.rule_thickness*n.separation_factor,h=this.baseHasAccentUnder?u:0,c=this.isLineBelow?3*n.rule_thickness:u,f=(o?c:Math.max(n.big_op_spacing2,n.big_op_spacing4-l))-h;return[f,-(e.d*e.rscale+f+l)]},r.prototype.getDeltaW=function(e,i){var o,n,l,u;void 0===i&&(i=[0,0,0]);var h=this.node.attributes.get("align"),c=e.map(function(O){return O.w*O.rscale});c[0]-=this.baseRemoveIc&&!this.baseCore.node.attributes.get("largeop")?this.baseIc:0;var f=Math.max.apply(Math,M([],b(c),!1)),p=[],m=0;try{for(var x=v(c.keys()),w=x.next();!w.done;w=x.next()){var j=w.value;p[j]=("center"===h?(f-c[j])/2:"right"===h?f-c[j]:0)+i[j],p[j]<m&&(m=-p[j])}}catch(O){o={error:O}}finally{try{w&&!w.done&&(n=x.return)&&n.call(x)}finally{if(o)throw o.error}}if(m)try{for(var S=v(p.keys()),T=S.next();!T.done;T=S.next()){j=T.value;p[j]+=m}}catch(O){l={error:O}}finally{try{T&&!T.done&&(u=S.return)&&u.call(S)}finally{if(l)throw l.error}}return[1,2].map(function(O){return p[O]+=e[O]?e[O].dx*e[0].scale:0}),p},r.prototype.getDelta=function(e){void 0===e&&(e=!1);var i=this.node.attributes.get("accent"),o=this.baseCore.getOuterBBox(),n=o.sk,l=o.ic;return((i&&!e?n:0)+this.font.skewIcFactor*l)*this.baseScale},r.prototype.stretchChildren=function(){var e,i,o,n,l,u,h=[];try{for(var c=v(this.childNodes),f=c.next();!f.done;f=c.next()){(p=f.value).canStretch(2)&&h.push(p)}}catch(N){e={error:N}}finally{try{f&&!f.done&&(i=c.return)&&i.call(c)}finally{if(e)throw e.error}}var m=h.length,x=this.childNodes.length;if(m&&x>1){var w=0,j=m>1&&m===x;try{for(var S=v(this.childNodes),T=S.next();!T.done;T=S.next()){var O=0===(p=T.value).stretch.dir;if(j||O){var B=p.getOuterBBox(O),A=B.w,H=B.rscale;A*H>w&&(w=A*H)}}}catch(N){o={error:N}}finally{try{T&&!T.done&&(n=S.return)&&n.call(S)}finally{if(o)throw o.error}}try{for(var P=v(h),W=P.next();!W.done;W=P.next()){var p;(p=W.value).coreMO().getStretchedVariant([w/p.bbox.rscale])}}catch(N){l={error:N}}finally{try{W&&!W.done&&(u=P.return)&&u.call(P)}finally{if(l)throw l.error}}}},r}(s),a.useIC=!0,a}},47463:function(L,g){var b,_=this&&this.__extends||(b=function(M,v){return(b=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(y,d){y.__proto__=d}||function(y,d){for(var s in d)Object.prototype.hasOwnProperty.call(d,s)&&(y[s]=d[s])})(M,v)},function(M,v){if("function"!=typeof v&&null!==v)throw new TypeError("Class extends value "+String(v)+" is not a constructor or null");function y(){this.constructor=M}b(M,v),M.prototype=null===v?Object.create(v):(y.prototype=v.prototype,new y)});Object.defineProperty(g,"__esModule",{value:!0}),g.CommonSemanticsMixin=void 0,g.CommonSemanticsMixin=function C(b){return function(M){function v(){return null!==M&&M.apply(this,arguments)||this}return _(v,M),v.prototype.computeBBox=function(y,d){if(void 0===d&&(d=!1),this.childNodes.length){var s=this.childNodes[0].getBBox(),a=s.w,t=s.h,r=s.d;y.w=a,y.h=t,y.d=r}},v}(b)}},13380:(L,g,_)=>{Object.defineProperty(g,"__esModule",{value:!0}),g.BBox=void 0;var C=_(21108),b=function(){function M(v){void 0===v&&(v={w:0,h:-C.BIGDIMEN,d:-C.BIGDIMEN}),this.w=v.w||0,this.h="h"in v?v.h:-C.BIGDIMEN,this.d="d"in v?v.d:-C.BIGDIMEN,this.L=this.R=this.ic=this.sk=this.dx=0,this.scale=this.rscale=1,this.pwidth=""}return M.zero=function(){return new M({h:0,d:0,w:0})},M.empty=function(){return new M},M.prototype.empty=function(){return this.w=0,this.h=this.d=-C.BIGDIMEN,this},M.prototype.clean=function(){this.w===-C.BIGDIMEN&&(this.w=0),this.h===-C.BIGDIMEN&&(this.h=0),this.d===-C.BIGDIMEN&&(this.d=0)},M.prototype.rescale=function(v){this.w*=v,this.h*=v,this.d*=v},M.prototype.combine=function(v,y,d){void 0===y&&(y=0),void 0===d&&(d=0);var s=v.rscale,a=y+s*(v.w+v.L+v.R),t=d+s*v.h,r=s*v.d-d;a>this.w&&(this.w=a),t>this.h&&(this.h=t),r>this.d&&(this.d=r)},M.prototype.append=function(v){var y=v.rscale;this.w+=y*(v.w+v.L+v.R),y*v.h>this.h&&(this.h=y*v.h),y*v.d>this.d&&(this.d=y*v.d)},M.prototype.updateFrom=function(v){this.h=v.h,this.d=v.d,this.w=v.w,v.pwidth&&(this.pwidth=v.pwidth)},M.fullWidth="100%",M.StyleAdjust=[["borderTopWidth","h"],["borderRightWidth","w"],["borderBottomWidth","d"],["borderLeftWidth","w",0],["paddingTop","h"],["paddingRight","w"],["paddingBottom","d"],["paddingLeft","w",0]],M}();g.BBox=b},55418:function(L,g,_){var s,C=this&&this.__extends||(s=function(a,t){return(s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,e){r.__proto__=e}||function(r,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(r[i]=e[i])})(a,t)},function(a,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=a}s(a,t),a.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}),b=this&&this.__values||function(s){var a="function"==typeof Symbol&&Symbol.iterator,t=a&&s[a],r=0;if(t)return t.call(s);if(s&&"number"==typeof s.length)return{next:function(){return s&&r>=s.length&&(s=void 0),{value:s&&s[r++],done:!s}}};throw new TypeError(a?"Object is not iterable.":"Symbol.iterator is not defined.")},M=this&&this.__read||function(s,a){var t="function"==typeof Symbol&&s[Symbol.iterator];if(!t)return s;var e,o,r=t.call(s),i=[];try{for(;(void 0===a||a-- >0)&&!(e=r.next()).done;)i.push(e.value)}catch(n){o={error:n}}finally{try{e&&!e.done&&(t=r.return)&&t.call(r)}finally{if(o)throw o.error}}return i},v=this&&this.__spreadArray||function(s,a,t){if(t||2===arguments.length)for(var i,r=0,e=a.length;r<e;r++)(i||!(r in a))&&(i||(i=Array.prototype.slice.call(a,0,r)),i[r]=a[r]);return s.concat(i||Array.prototype.slice.call(a))};Object.defineProperty(g,"__esModule",{value:!0}),g.FunctionList=void 0;var d=function(s){function a(){return null!==s&&s.apply(this,arguments)||this}return C(a,s),a.prototype.execute=function(){for(var t,r,e=[],i=0;i<arguments.length;i++)e[i]=arguments[i];try{for(var o=b(this),n=o.next();!n.done;n=o.next()){var l=n.value,u=l.item.apply(l,v([],M(e),!1));if(!1===u)return!1}}catch(h){t={error:h}}finally{try{n&&!n.done&&(r=o.return)&&r.call(o)}finally{if(t)throw t.error}}return!0},a.prototype.asyncExecute=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var e=-1,i=this.items;return new Promise(function(o,n){!function l(){for(var u;++e<i.length;){var h=(u=i[e]).item.apply(u,v([],M(t),!1));if(h instanceof Promise)return void h.then(l).catch(function(c){return n(c)});if(!1===h)return void o(!1)}o(!0)}()})},a}(_(63670).PrioritizedList);g.FunctionList=d},63670:(L,g)=>{Object.defineProperty(g,"__esModule",{value:!0}),g.PrioritizedList=void 0;var _=function(){function C(){this.items=[],this.items=[]}return C.prototype[Symbol.iterator]=function(){var b=0,M=this.items;return{next:function(){return{value:M[b++],done:b>M.length}}}},C.prototype.add=function(b,M){void 0===M&&(M=C.DEFAULTPRIORITY);var v=this.items.length;do{v--}while(v>=0&&M<this.items[v].priority);return this.items.splice(v+1,0,{item:b,priority:M}),b},C.prototype.remove=function(b){var M=this.items.length;do{M--}while(M>=0&&this.items[M].item!==b);M>=0&&this.items.splice(M,1)},C.DEFAULTPRIORITY=5,C}();g.PrioritizedList=_},53873:function(L,g){var _=this&&this.__values||function(b){var M="function"==typeof Symbol&&Symbol.iterator,v=M&&b[M],y=0;if(v)return v.call(b);if(b&&"number"==typeof b.length)return{next:function(){return b&&y>=b.length&&(b=void 0),{value:b&&b[y++],done:!b}}};throw new TypeError(M?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(g,"__esModule",{value:!0}),g.CssStyles=void 0;var C=function(){function b(M){void 0===M&&(M=null),this.styles={},this.addStyles(M)}return Object.defineProperty(b.prototype,"cssText",{get:function(){return this.getStyleString()},enumerable:!1,configurable:!0}),b.prototype.addStyles=function(M){var v,y;if(M)try{for(var d=_(Object.keys(M)),s=d.next();!s.done;s=d.next()){var a=s.value;this.styles[a]||(this.styles[a]={}),Object.assign(this.styles[a],M[a])}}catch(t){v={error:t}}finally{try{s&&!s.done&&(y=d.return)&&y.call(d)}finally{if(v)throw v.error}}},b.prototype.removeStyles=function(){for(var M,v,y=[],d=0;d<arguments.length;d++)y[d]=arguments[d];try{for(var s=_(y),a=s.next();!a.done;a=s.next()){var t=a.value;delete this.styles[t]}}catch(r){M={error:r}}finally{try{a&&!a.done&&(v=s.return)&&v.call(s)}finally{if(M)throw M.error}}},b.prototype.clear=function(){this.styles={}},b.prototype.getStyleString=function(){return this.getStyleRules().join("\n\n")},b.prototype.getStyleRules=function(){var M,v,y=Object.keys(this.styles),d=new Array(y.length),s=0;try{for(var a=_(y),t=a.next();!t.done;t=a.next()){var r=t.value;d[s++]=r+" {\n"+this.getStyleDefString(this.styles[r])+"\n}"}}catch(e){M={error:e}}finally{try{t&&!t.done&&(v=a.return)&&v.call(a)}finally{if(M)throw M.error}}return d},b.prototype.getStyleDefString=function(M){var v,y,d=Object.keys(M),s=new Array(d.length),a=0;try{for(var t=_(d),r=t.next();!r.done;r=t.next()){var e=r.value;s[a++]="  "+e+": "+M[e]+";"}}catch(i){v={error:i}}finally{try{r&&!r.done&&(y=t.return)&&y.call(t)}finally{if(v)throw v.error}}return s.join("\n")},b}();g.CssStyles=C},27342:function(L,g){var _=this&&this.__values||function(c){var f="function"==typeof Symbol&&Symbol.iterator,p=f&&c[f],m=0;if(p)return p.call(c);if(c&&"number"==typeof c.length)return{next:function(){return c&&m>=c.length&&(c=void 0),{value:c&&c[m++],done:!c}}};throw new TypeError(f?"Object is not iterable.":"Symbol.iterator is not defined.")},C=this&&this.__read||function(c,f){var p="function"==typeof Symbol&&c[Symbol.iterator];if(!p)return c;var x,j,m=p.call(c),w=[];try{for(;(void 0===f||f-- >0)&&!(x=m.next()).done;)w.push(x.value)}catch(S){j={error:S}}finally{try{x&&!x.done&&(p=m.return)&&p.call(m)}finally{if(j)throw j.error}}return w},b=this&&this.__spreadArray||function(c,f,p){if(p||2===arguments.length)for(var w,m=0,x=f.length;m<x;m++)(w||!(m in f))&&(w||(w=Array.prototype.slice.call(f,0,m)),w[m]=f[m]);return c.concat(w||Array.prototype.slice.call(f))};Object.defineProperty(g,"__esModule",{value:!0}),g.Styles=void 0;var M=["top","right","bottom","left"],v=["width","style","color"];function y(c){for(var f=c.split(/((?:'[^']*'|"[^"]*"|,[\s\n]|[^\s\n])*)/g),p=[];f.length>1;)f.shift(),p.push(f.shift());return p}function d(c){var f,p,m=y(this.styles[c]);0===m.length&&m.push(""),1===m.length&&m.push(m[0]),2===m.length&&m.push(m[0]),3===m.length&&m.push(m[1]);try{for(var x=_(h.connect[c].children),w=x.next();!w.done;w=x.next()){var j=w.value;this.setStyle(this.childName(c,j),m.shift())}}catch(S){f={error:S}}finally{try{w&&!w.done&&(p=x.return)&&p.call(x)}finally{if(f)throw f.error}}}function s(c){var f,p,m=h.connect[c].children,x=[];try{for(var w=_(m),j=w.next();!j.done;j=w.next()){var S=j.value,T=this.styles[c+"-"+S];if(!T)return void delete this.styles[c];x.push(T)}}catch(O){f={error:O}}finally{try{j&&!j.done&&(p=w.return)&&p.call(w)}finally{if(f)throw f.error}}x[3]===x[1]&&(x.pop(),x[2]===x[0]&&(x.pop(),x[1]===x[0]&&x.pop())),this.styles[c]=x.join(" ")}function a(c){var f,p;try{for(var m=_(h.connect[c].children),x=m.next();!x.done;x=m.next()){var w=x.value;this.setStyle(this.childName(c,w),this.styles[c])}}catch(j){f={error:j}}finally{try{x&&!x.done&&(p=m.return)&&p.call(m)}finally{if(f)throw f.error}}}function t(c){var f,p,m=b([],C(h.connect[c].children),!1),x=this.styles[this.childName(c,m.shift())];try{for(var w=_(m),j=w.next();!j.done;j=w.next()){var S=j.value;if(this.styles[this.childName(c,S)]!==x)return void delete this.styles[c]}}catch(T){f={error:T}}finally{try{j&&!j.done&&(p=w.return)&&p.call(w)}finally{if(f)throw f.error}}this.styles[c]=x}var r_width=/^(?:[\d.]+(?:[a-z]+)|thin|medium|thick|inherit|initial|unset)$/,r_style=/^(?:none|hidden|dotted|dashed|solid|double|groove|ridge|inset|outset|inherit|initial|unset)$/;function e(c){var f,p,m,x,w={width:"",style:"",color:""};try{for(var j=_(y(this.styles[c])),S=j.next();!S.done;S=j.next()){var T=S.value;T.match(r_width)&&""===w.width?w.width=T:T.match(r_style)&&""===w.style?w.style=T:w.color=T}}catch(H){f={error:H}}finally{try{S&&!S.done&&(p=j.return)&&p.call(j)}finally{if(f)throw f.error}}try{for(var O=_(h.connect[c].children),B=O.next();!B.done;B=O.next()){var A=B.value;this.setStyle(this.childName(c,A),w[A])}}catch(H){m={error:H}}finally{try{B&&!B.done&&(x=O.return)&&x.call(O)}finally{if(m)throw m.error}}}function i(c){var f,p,m=[];try{for(var x=_(h.connect[c].children),w=x.next();!w.done;w=x.next()){var j=w.value,S=this.styles[this.childName(c,j)];S&&m.push(S)}}catch(T){f={error:T}}finally{try{w&&!w.done&&(p=x.return)&&p.call(x)}finally{if(f)throw f.error}}m.length?this.styles[c]=m.join(" "):delete this.styles[c]}var o={style:/^(?:normal|italic|oblique|inherit|initial|unset)$/,variant:new RegExp("^(?:"+["normal|none","inherit|initial|unset","common-ligatures|no-common-ligatures","discretionary-ligatures|no-discretionary-ligatures","historical-ligatures|no-historical-ligatures","contextual|no-contextual","(?:stylistic|character-variant|swash|ornaments|annotation)\\([^)]*\\)","small-caps|all-small-caps|petite-caps|all-petite-caps|unicase|titling-caps","lining-nums|oldstyle-nums|proportional-nums|tabular-nums","diagonal-fractions|stacked-fractions","ordinal|slashed-zero","jis78|jis83|jis90|jis04|simplified|traditional","full-width|proportional-width","ruby"].join("|")+")$"),weight:/^(?:normal|bold|bolder|lighter|[1-9]00|inherit|initial|unset)$/,stretch:new RegExp("^(?:"+["normal","(?:(?:ultra|extra|semi)-)?condensed","(?:(?:semi|extra|ulta)-)?expanded","inherit|initial|unset"].join("|")+")$"),size:new RegExp("^(?:"+["xx-small|x-small|small|medium|large|x-large|xx-large|larger|smaller","[d.]+%|[d.]+[a-z]+","inherit|initial|unset"].join("|")+")(?:/(?:normal|[d.+](?:%|[a-z]+)?))?$")};function n(c){var f,p,m,x,w=y(this.styles[c]),j={style:"",variant:[],weight:"",stretch:"",size:"",family:"","line-height":""};try{for(var S=_(w),T=S.next();!T.done;T=S.next()){var O=T.value;j.family=O;try{for(var B=(m=void 0,_(Object.keys(o))),A=B.next();!A.done;A=B.next()){var H=A.value;if((Array.isArray(j[H])||""===j[H])&&O.match(o[H]))if("size"===H){var P=C(O.split(/\//),2),W=P[0],N=P[1];j[H]=W,N&&(j["line-height"]=N)}else""===j.size&&(Array.isArray(j[H])?j[H].push(O):j[H]=O)}}catch(k){m={error:k}}finally{try{A&&!A.done&&(x=B.return)&&x.call(B)}finally{if(m)throw m.error}}}}catch(k){f={error:k}}finally{try{T&&!T.done&&(p=S.return)&&p.call(S)}finally{if(f)throw f.error}}(function l(c,f){var p,m;try{for(var x=_(h.connect[c].children),w=x.next();!w.done;w=x.next()){var j=w.value,S=this.childName(c,j);if(Array.isArray(f[j])){var T=f[j];T.length&&(this.styles[S]=T.join(" "))}else""!==f[j]&&(this.styles[S]=f[j])}}catch(O){p={error:O}}finally{try{w&&!w.done&&(m=x.return)&&m.call(x)}finally{if(p)throw p.error}}})(c,j),delete this.styles[c]}function u(c){}var h=function(){function c(f){void 0===f&&(f=""),this.parse(f)}return Object.defineProperty(c.prototype,"cssText",{get:function(){var f,p,m=[];try{for(var x=_(Object.keys(this.styles)),w=x.next();!w.done;w=x.next()){var j=w.value,S=this.parentName(j);this.styles[S]||m.push(j+": "+this.styles[j]+";")}}catch(T){f={error:T}}finally{try{w&&!w.done&&(p=x.return)&&p.call(x)}finally{if(f)throw f.error}}return m.join(" ")},enumerable:!1,configurable:!0}),c.prototype.set=function(f,p){for(f=this.normalizeName(f),this.setStyle(f,p),c.connect[f]&&!c.connect[f].combine&&(this.combineChildren(f),delete this.styles[f]);f.match(/-/)&&(f=this.parentName(f),c.connect[f]);)c.connect[f].combine.call(this,f)},c.prototype.get=function(f){return f=this.normalizeName(f),this.styles.hasOwnProperty(f)?this.styles[f]:""},c.prototype.setStyle=function(f,p){this.styles[f]=p,c.connect[f]&&c.connect[f].children&&c.connect[f].split.call(this,f),""===p&&delete this.styles[f]},c.prototype.combineChildren=function(f){var p,m,x=this.parentName(f);try{for(var w=_(c.connect[f].children),j=w.next();!j.done;j=w.next()){var S=j.value,T=this.childName(x,S);c.connect[T].combine.call(this,T)}}catch(O){p={error:O}}finally{try{j&&!j.done&&(m=w.return)&&m.call(w)}finally{if(p)throw p.error}}},c.prototype.parentName=function(f){var p=f.replace(/-[^-]*$/,"");return f===p?"":p},c.prototype.childName=function(f,p){return p.match(/-/)?p:(c.connect[f]&&!c.connect[f].combine&&(p+=f.replace(/.*-/,"-"),f=this.parentName(f)),f+"-"+p)},c.prototype.normalizeName=function(f){return f.replace(/[A-Z]/g,function(p){return"-"+p.toLowerCase()})},c.prototype.parse=function(f){void 0===f&&(f="");var p=this.constructor.pattern;this.styles={};for(var m=f.replace(p.comment,"").split(p.style);m.length>1;){var x=C(m.splice(0,3),3),w=x[0],j=x[1],S=x[2];if(w.match(/[^\s\n]/))return;this.set(j,S)}},c.pattern={style:/([-a-z]+)[\s\n]*:[\s\n]*((?:'[^']*'|"[^"]*"|\n|.)*?)[\s\n]*(?:;|$)/g,comment:/\/\*[^]*?\*\//g},c.connect={padding:{children:M,split:d,combine:s},border:{children:M,split:a,combine:t},"border-top":{children:v,split:e,combine:i},"border-right":{children:v,split:e,combine:i},"border-bottom":{children:v,split:e,combine:i},"border-left":{children:v,split:e,combine:i},"border-width":{children:M,split:d,combine:null},"border-style":{children:M,split:d,combine:null},"border-color":{children:M,split:d,combine:null},font:{children:["style","variant","weight","stretch","line-height","size","family"],split:n,combine:u}},c}();g.Styles=h},89711:(L,g)=>{Object.defineProperty(g,"__esModule",{value:!0}),g.max=g.sum=void 0,g.sum=function _(b){return b.reduce(function(M,v){return M+v},0)},g.max=function C(b){return b.reduce(function(M,v){return Math.max(M,v)},0)}}}]);