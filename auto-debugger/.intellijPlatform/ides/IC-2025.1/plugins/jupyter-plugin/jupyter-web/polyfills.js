"use strict";(self.webpackChunkjupyter_web=self.webpackChunkjupyter_web||[]).push([[6429],{7435:(be,Re,Ce)=>{Ce(37091);const ue=window;ue.global=window,ue.process={env:{},argv:[]}},37091:function(be,Re,Ce){var ye,Le,ue=this&&this.__spreadArray||function(le,fe,De){if(De||2===arguments.length)for(var ve,Te=0,Ve=fe.length;Te<Ve;Te++)(ve||!(Te in fe))&&(ve||(ve=Array.prototype.slice.call(fe,0,Te)),ve[Te]=fe[Te]);return le.concat(ve||Array.prototype.slice.call(fe))};ye=function(){!function(e){var r=e.performance;function t(v){r&&r.mark&&r.mark(v)}function n(v,o){r&&r.measure&&r.measure(v,o)}t("Zone");var u=e.__Zone_symbol_prefix||"__zone_symbol__";function c(v){return u+v}var f=!0===e[c("forceDuplicateZoneCheck")];if(e.Zone){if(f||"function"!=typeof e.Zone.__symbol__)throw new Error("Zone already loaded.");return e.Zone}var d=function(){function v(o,a){this._parent=o,this._name=a?a.name||"unnamed":"<root>",this._properties=a&&a.properties||{},this._zoneDelegate=new T(this,this._parent&&this._parent._zoneDelegate,a)}return v.assertZonePatched=function(){if(e.Promise!==S.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")},Object.defineProperty(v,"root",{get:function(){for(var o=v.current;o.parent;)o=o.parent;return o},enumerable:!1,configurable:!0}),Object.defineProperty(v,"current",{get:function(){return W.zone},enumerable:!1,configurable:!0}),Object.defineProperty(v,"currentTask",{get:function(){return ae},enumerable:!1,configurable:!0}),v.__load_patch=function(o,a,i){if(void 0===i&&(i=!1),S.hasOwnProperty(o)){if(!i&&f)throw Error("Already loaded patch: "+o)}else if(!e["__Zone_disable_"+o]){var w="Zone:"+o;t(w),S[o]=a(e,v,X),n(w,w)}},Object.defineProperty(v.prototype,"parent",{get:function(){return this._parent},enumerable:!1,configurable:!0}),Object.defineProperty(v.prototype,"name",{get:function(){return this._name},enumerable:!1,configurable:!0}),v.prototype.get=function(o){var a=this.getZoneWith(o);if(a)return a._properties[o]},v.prototype.getZoneWith=function(o){for(var a=this;a;){if(a._properties.hasOwnProperty(o))return a;a=a._parent}return null},v.prototype.fork=function(o){if(!o)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,o)},v.prototype.wrap=function(o,a){if("function"!=typeof o)throw new Error("Expecting function got: "+o);var i=this._zoneDelegate.intercept(this,o,a),w=this;return function(){return w.runGuarded(i,this,arguments,a)}},v.prototype.run=function(o,a,i,w){W={parent:W,zone:this};try{return this._zoneDelegate.invoke(this,o,a,i,w)}finally{W=W.parent}},v.prototype.runGuarded=function(o,a,i,w){void 0===a&&(a=null),W={parent:W,zone:this};try{try{return this._zoneDelegate.invoke(this,o,a,i,w)}catch(Y){if(this._zoneDelegate.handleError(this,Y))throw Y}}finally{W=W.parent}},v.prototype.runTask=function(o,a,i){if(o.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(o.zone||b).name+"; Execution: "+this.name+")");if(o.state!==U||o.type!==N&&o.type!==O){var w=o.state!=B;w&&o._transitionTo(B,F),o.runCount++;var Y=ae;ae=o,W={parent:W,zone:this};try{o.type==O&&o.data&&!o.data.isPeriodic&&(o.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,o,a,i)}catch(se){if(this._zoneDelegate.handleError(this,se))throw se}}finally{o.state!==U&&o.state!==z&&(o.type==N||o.data&&o.data.isPeriodic?w&&o._transitionTo(F,B):(o.runCount=0,this._updateTaskCount(o,-1),w&&o._transitionTo(U,B,U))),W=W.parent,ae=Y}}},v.prototype.scheduleTask=function(o){if(o.zone&&o.zone!==this)for(var a=this;a;){if(a===o.zone)throw Error("can not reschedule task to ".concat(this.name," which is descendants of the original zone ").concat(o.zone.name));a=a.parent}o._transitionTo(x,U);var i=[];o._zoneDelegates=i,o._zone=this;try{o=this._zoneDelegate.scheduleTask(this,o)}catch(w){throw o._transitionTo(z,x,U),this._zoneDelegate.handleError(this,w),w}return o._zoneDelegates===i&&this._updateTaskCount(o,1),o.state==x&&o._transitionTo(F,x),o},v.prototype.scheduleMicroTask=function(o,a,i,w){return this.scheduleTask(new p(Z,o,a,i,w,void 0))},v.prototype.scheduleMacroTask=function(o,a,i,w,Y){return this.scheduleTask(new p(O,o,a,i,w,Y))},v.prototype.scheduleEventTask=function(o,a,i,w,Y){return this.scheduleTask(new p(N,o,a,i,w,Y))},v.prototype.cancelTask=function(o){if(o.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(o.zone||b).name+"; Execution: "+this.name+")");o._transitionTo(k,F,B);try{this._zoneDelegate.cancelTask(this,o)}catch(a){throw o._transitionTo(z,k),this._zoneDelegate.handleError(this,a),a}return this._updateTaskCount(o,-1),o._transitionTo(U,k),o.runCount=0,o},v.prototype._updateTaskCount=function(o,a){var i=o._zoneDelegates;-1==a&&(o._zoneDelegates=null);for(var w=0;w<i.length;w++)i[w]._updateTaskCount(o.type,a)},v}();d.__symbol__=c;var $,E={name:"",onHasTask:function(v,o,a,i){return v.hasTask(a,i)},onScheduleTask:function(v,o,a,i){return v.scheduleTask(a,i)},onInvokeTask:function(v,o,a,i,w,Y){return v.invokeTask(a,i,w,Y)},onCancelTask:function(v,o,a,i){return v.cancelTask(a,i)}},T=function(){function v(o,a,i){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this.zone=o,this._parentDelegate=a,this._forkZS=i&&(i&&i.onFork?i:a._forkZS),this._forkDlgt=i&&(i.onFork?a:a._forkDlgt),this._forkCurrZone=i&&(i.onFork?this.zone:a._forkCurrZone),this._interceptZS=i&&(i.onIntercept?i:a._interceptZS),this._interceptDlgt=i&&(i.onIntercept?a:a._interceptDlgt),this._interceptCurrZone=i&&(i.onIntercept?this.zone:a._interceptCurrZone),this._invokeZS=i&&(i.onInvoke?i:a._invokeZS),this._invokeDlgt=i&&(i.onInvoke?a:a._invokeDlgt),this._invokeCurrZone=i&&(i.onInvoke?this.zone:a._invokeCurrZone),this._handleErrorZS=i&&(i.onHandleError?i:a._handleErrorZS),this._handleErrorDlgt=i&&(i.onHandleError?a:a._handleErrorDlgt),this._handleErrorCurrZone=i&&(i.onHandleError?this.zone:a._handleErrorCurrZone),this._scheduleTaskZS=i&&(i.onScheduleTask?i:a._scheduleTaskZS),this._scheduleTaskDlgt=i&&(i.onScheduleTask?a:a._scheduleTaskDlgt),this._scheduleTaskCurrZone=i&&(i.onScheduleTask?this.zone:a._scheduleTaskCurrZone),this._invokeTaskZS=i&&(i.onInvokeTask?i:a._invokeTaskZS),this._invokeTaskDlgt=i&&(i.onInvokeTask?a:a._invokeTaskDlgt),this._invokeTaskCurrZone=i&&(i.onInvokeTask?this.zone:a._invokeTaskCurrZone),this._cancelTaskZS=i&&(i.onCancelTask?i:a._cancelTaskZS),this._cancelTaskDlgt=i&&(i.onCancelTask?a:a._cancelTaskDlgt),this._cancelTaskCurrZone=i&&(i.onCancelTask?this.zone:a._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;var w=i&&i.onHasTask,Y=a&&a._hasTaskZS;(w||Y)&&(this._hasTaskZS=w?i:E,this._hasTaskDlgt=a,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=o,i.onScheduleTask||(this._scheduleTaskZS=E,this._scheduleTaskDlgt=a,this._scheduleTaskCurrZone=this.zone),i.onInvokeTask||(this._invokeTaskZS=E,this._invokeTaskDlgt=a,this._invokeTaskCurrZone=this.zone),i.onCancelTask||(this._cancelTaskZS=E,this._cancelTaskDlgt=a,this._cancelTaskCurrZone=this.zone))}return v.prototype.fork=function(o,a){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,o,a):new d(o,a)},v.prototype.intercept=function(o,a,i){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,o,a,i):a},v.prototype.invoke=function(o,a,i,w,Y){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,o,a,i,w,Y):a.apply(i,w)},v.prototype.handleError=function(o,a){return!this._handleErrorZS||this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,o,a)},v.prototype.scheduleTask=function(o,a){var i=a;if(this._scheduleTaskZS)this._hasTaskZS&&i._zoneDelegates.push(this._hasTaskDlgtOwner),(i=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,o,a))||(i=a);else if(a.scheduleFn)a.scheduleFn(a);else{if(a.type!=Z)throw new Error("Task is missing scheduleFn.");J(a)}return i},v.prototype.invokeTask=function(o,a,i,w){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,o,a,i,w):a.callback.apply(i,w)},v.prototype.cancelTask=function(o,a){var i;if(this._cancelTaskZS)i=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,o,a);else{if(!a.cancelFn)throw Error("Task is not cancelable");i=a.cancelFn(a)}return i},v.prototype.hasTask=function(o,a){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,o,a)}catch(i){this.handleError(o,i)}},v.prototype._updateTaskCount=function(o,a){var i=this._taskCounts,w=i[o],Y=i[o]=w+a;if(Y<0)throw new Error("More tasks executed then were scheduled.");if(0==w||0==Y){var se={microTask:i.microTask>0,macroTask:i.macroTask>0,eventTask:i.eventTask>0,change:o};this.hasTask(this.zone,se)}},v}(),p=function(){function v(o,a,i,w,Y,se){if(this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state="notScheduled",this.type=o,this.source=a,this.data=w,this.scheduleFn=Y,this.cancelFn=se,!i)throw new Error("callback is not defined");this.callback=i;var l=this;o===N&&w&&w.useG?this.invoke=v.invokeTask:this.invoke=function(){return v.invokeTask.call(e,l,this,arguments)}}return v.invokeTask=function(o,a,i){o||(o=this),Q++;try{return o.runCount++,o.zone.runTask(o,a,i)}finally{1==Q&&A(),Q--}},Object.defineProperty(v.prototype,"zone",{get:function(){return this._zone},enumerable:!1,configurable:!0}),Object.defineProperty(v.prototype,"state",{get:function(){return this._state},enumerable:!1,configurable:!0}),v.prototype.cancelScheduleRequest=function(){this._transitionTo(U,x)},v.prototype._transitionTo=function(o,a,i){if(this._state!==a&&this._state!==i)throw new Error("".concat(this.type," '").concat(this.source,"': can not transition to '").concat(o,"', expecting state '").concat(a,"'").concat(i?" or '"+i+"'":"",", was '").concat(this._state,"'."));this._state=o,o==U&&(this._zoneDelegates=null)},v.prototype.toString=function(){return this.data&&typeof this.data.handleId<"u"?this.data.handleId.toString():Object.prototype.toString.call(this)},v.prototype.toJSON=function(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}},v}(),m=c("setTimeout"),g=c("Promise"),C=c("then"),D=[],H=!1;function V(v){if($||e[g]&&($=e[g].resolve(0)),$){var o=$[C];o||(o=$.then),o.call($,v)}else e[m](v,0)}function J(v){0===Q&&0===D.length&&V(A),v&&D.push(v)}function A(){if(!H){for(H=!0;D.length;){var v=D;D=[];for(var o=0;o<v.length;o++){var a=v[o];try{a.zone.runTask(a,null,null)}catch(i){X.onUnhandledError(i)}}}X.microtaskDrainDone(),H=!1}}var b={name:"NO ZONE"},U="notScheduled",x="scheduling",F="scheduled",B="running",k="canceling",z="unknown",Z="microTask",O="macroTask",N="eventTask",S={},X={symbol:c,currentZoneFrame:function(){return W},onUnhandledError:q,microtaskDrainDone:q,scheduleMicroTask:J,showUncaughtError:function(){return!d[c("ignoreConsoleErrorUncaughtError")]},patchEventTarget:function(){return[]},patchOnProperties:q,patchMethod:function(){return q},bindArguments:function(){return[]},patchThen:function(){return q},patchMacroTask:function(){return q},patchEventPrototype:function(){return q},isIEOrEdge:function(){return!1},getGlobalObjects:function(){},ObjectDefineProperty:function(){return q},ObjectGetOwnPropertyDescriptor:function(){},ObjectCreate:function(){},ArraySlice:function(){return[]},patchClass:function(){return q},wrapWithCurrentZone:function(){return q},filterProperties:function(){return[]},attachOriginToPatched:function(){return q},_redefineProperty:function(){return q},patchCallbacks:function(){return q},nativeScheduleMicroTask:V},W={parent:null,zone:new d(null,null)},ae=null,Q=0;function q(){}n("Zone","Zone"),e.Zone=d}(typeof window<"u"&&window||typeof self<"u"&&self||global);var le=Object.getOwnPropertyDescriptor,fe=Object.defineProperty,De=Object.getPrototypeOf,Te=Object.create,Ve=Array.prototype.slice,ve="addEventListener",Ue="removeEventListener",We=Zone.__symbol__(ve),ze=Zone.__symbol__(Ue),he="true",de="false",Ze=Zone.__symbol__("");function Xe(e,r){return Zone.current.wrap(e,r)}function qe(e,r,t,n,u){return Zone.current.scheduleMacroTask(e,r,t,n,u)}var G=Zone.__symbol__,Ne=typeof window<"u",ke=Ne?window:void 0,te=Ne&&ke||"object"==typeof self&&self||global,wr="removeAttribute";function Ye(e,r){for(var t=e.length-1;t>=0;t--)"function"==typeof e[t]&&(e[t]=Xe(e[t],r+"_"+t));return e}function Pr(e,r){for(var t=e.constructor.name,n=function(c){var T,p,f=r[c],d=e[f];if(d){if(!rr(le(e,f)))return"continue";e[f]=(p=function(){return T.apply(this,Ye(arguments,t+"."+f))},pe(p,T=d),p)}},u=0;u<r.length;u++)n(u)}function rr(e){return!e||!1!==e.writable&&!("function"==typeof e.get&&typeof e.set>"u")}var tr=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope,Ae=!("nw"in te)&&typeof te.process<"u"&&"[object process]"==={}.toString.call(te.process),Ke=!Ae&&!tr&&!(!Ne||!ke.HTMLElement),nr=typeof te.process<"u"&&"[object process]"==={}.toString.call(te.process)&&!tr&&!(!Ne||!ke.HTMLElement),je={},or=function(e){if(e=e||te.event){var r=je[e.type];r||(r=je[e.type]=G("ON_PROPERTY"+e.type));var u,t=this||e.target||te,n=t[r];if(Ke&&t===ke&&"error"===e.type){var c=e;!0===(u=n&&n.call(this,c.message,c.filename,c.lineno,c.colno,c.error))&&e.preventDefault()}else null!=(u=n&&n.apply(this,arguments))&&!u&&e.preventDefault();return u}};function ar(e,r,t){var n=le(e,r);if(!n&&t&&le(t,r)&&(n={enumerable:!0,configurable:!0}),n&&n.configurable){var c=G("on"+r+"patched");if(!e.hasOwnProperty(c)||!e[c]){delete n.writable,delete n.value;var f=n.get,d=n.set,E=r.slice(2),T=je[E];T||(T=je[E]=G("ON_PROPERTY"+E)),n.set=function(p){var m=this;!m&&e===te&&(m=te),m&&("function"==typeof m[T]&&m.removeEventListener(E,or),d&&d.call(m,null),m[T]=p,"function"==typeof p&&m.addEventListener(E,or,!1))},n.get=function(){var p=this;if(!p&&e===te&&(p=te),!p)return null;var m=p[T];if(m)return m;if(f){var g=f.call(this);if(g)return n.set.call(this,g),"function"==typeof p[wr]&&p.removeAttribute(r),g}return null},fe(e,r,n),e[c]=!0}}}function ir(e,r,t){if(r)for(var n=0;n<r.length;n++)ar(e,"on"+r[n],t);else{var u=[];for(var c in e)"on"==c.slice(0,2)&&u.push(c);for(var f=0;f<u.length;f++)ar(e,u[f],t)}}var ce=G("originalInstance");function Me(e){var r=te[e];if(r){te[G(e)]=r,te[e]=function(){var u=Ye(arguments,e);switch(u.length){case 0:this[ce]=new r;break;case 1:this[ce]=new r(u[0]);break;case 2:this[ce]=new r(u[0],u[1]);break;case 3:this[ce]=new r(u[0],u[1],u[2]);break;case 4:this[ce]=new r(u[0],u[1],u[2],u[3]);break;default:throw new Error("Arg list too long.")}},pe(te[e],r);var n,t=new r(function(){});for(n in t)"XMLHttpRequest"===e&&"responseBlob"===n||function(u){"function"==typeof t[u]?te[e].prototype[u]=function(){return this[ce][u].apply(this[ce],arguments)}:fe(te[e].prototype,u,{set:function(c){"function"==typeof c?(this[ce][u]=Xe(c,e+"."+u),pe(this[ce][u],c)):this[ce][u]=c},get:function(){return this[ce][u]}})}(n);for(n in r)"prototype"!==n&&r.hasOwnProperty(n)&&(te[e][n]=r[n])}}function _e(e,r,t){for(var n=e;n&&!n.hasOwnProperty(r);)n=De(n);!n&&e[r]&&(n=e);var u=G(r),c=null;if(n&&(!(c=n[u])||!n.hasOwnProperty(u))&&(c=n[u]=n[r],rr(n&&le(n,r)))){var d=t(c,u,r);n[r]=function(){return d(this,arguments)},pe(n[r],c)}return c}function Or(e,r,t){var n=null;function u(c){var f=c.data;return f.args[f.cbIdx]=function(){c.invoke.apply(this,arguments)},n.apply(f.target,f.args),c}n=_e(e,r,function(c){return function(f,d){var E=t(f,d);return E.cbIdx>=0&&"function"==typeof d[E.cbIdx]?qe(E.name,d[E.cbIdx],E,u):c.apply(f,d)}})}function pe(e,r){e[G("OriginalDelegate")]=r}var ur=!1,Je=!1;function Sr(){try{var e=ke.navigator.userAgent;if(-1!==e.indexOf("MSIE ")||-1!==e.indexOf("Trident/"))return!0}catch{}return!1}function Rr(){if(ur)return Je;ur=!0;try{var e=ke.navigator.userAgent;(-1!==e.indexOf("MSIE ")||-1!==e.indexOf("Trident/")||-1!==e.indexOf("Edge/"))&&(Je=!0)}catch{}return Je}Zone.__load_patch("ZoneAwarePromise",function(e,r,t){var n=Object.getOwnPropertyDescriptor,u=Object.defineProperty;function c(l){return l&&l.toString===Object.prototype.toString?(l.constructor&&l.constructor.name||"")+": "+JSON.stringify(l):l?l.toString():Object.prototype.toString.call(l)}var f=t.symbol,d=[],E=!0===e[f("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")],T=f("Promise"),p=f("then"),m="__creationTrace__";t.onUnhandledError=function(l){if(t.showUncaughtError()){var _=l&&l.rejection;_?console.error("Unhandled Promise rejection:",_ instanceof Error?_.message:_,"; Zone:",l.zone.name,"; Task:",l.task&&l.task.source,"; Value:",_,_ instanceof Error?_.stack:void 0):console.error(l)}},t.microtaskDrainDone=function(){for(var l=function(){var _=d.shift();try{_.zone.runGuarded(function(){throw _.throwOriginal?_.rejection:_})}catch(h){C(h)}};d.length;)l()};var g=f("unhandledPromiseRejectionHandler");function C(l){t.onUnhandledError(l);try{var _=r[g];"function"==typeof _&&_.call(this,l)}catch{}}function D(l){return l&&l.then}function H(l){return l}function $(l){return a.reject(l)}var V=f("state"),J=f("value"),A=f("finally"),b=f("parentPromiseValue"),U=f("parentPromiseState"),x="Promise.then",F=null,B=!0,k=!1,z=0;function Z(l,_){return function(h){try{X(l,_,h)}catch(s){X(l,!1,s)}}}var O=function(){var l=!1;return function(h){return function(){l||(l=!0,h.apply(null,arguments))}}},N="Promise resolved with itself",S=f("currentTaskTrace");function X(l,_,h){var s=O();if(l===h)throw new TypeError(N);if(l[V]===F){var y=null;try{("object"==typeof h||"function"==typeof h)&&(y=h&&h.then)}catch(L){return s(function(){X(l,!1,L)})(),l}if(_!==k&&h instanceof a&&h.hasOwnProperty(V)&&h.hasOwnProperty(J)&&h[V]!==F)ae(h),X(l,h[V],h[J]);else if(_!==k&&"function"==typeof y)try{y.call(h,s(Z(l,_)),s(Z(l,!1)))}catch(L){s(function(){X(l,!1,L)})()}else{l[V]=_;var R=l[J];if(l[J]=h,l[A]===A&&_===B&&(l[V]=l[U],l[J]=l[b]),_===k&&h instanceof Error){var P=r.currentTask&&r.currentTask.data&&r.currentTask.data[m];P&&u(h,S,{configurable:!0,enumerable:!1,writable:!0,value:P})}for(var M=0;M<R.length;)Q(l,R[M++],R[M++],R[M++],R[M++]);if(0==R.length&&_==k){l[V]=z;var I=h;try{throw new Error("Uncaught (in promise): "+c(h)+(h&&h.stack?"\n"+h.stack:""))}catch(L){I=L}E&&(I.throwOriginal=!0),I.rejection=h,I.promise=l,I.zone=r.current,I.task=r.currentTask,d.push(I),t.scheduleMicroTask()}}}return l}var W=f("rejectionHandledHandler");function ae(l){if(l[V]===z){try{var _=r[W];_&&"function"==typeof _&&_.call(this,{rejection:l[J],promise:l})}catch{}l[V]=k;for(var h=0;h<d.length;h++)l===d[h].promise&&d.splice(h,1)}}function Q(l,_,h,s,y){ae(l);var R=l[V],P=R?"function"==typeof s?s:H:"function"==typeof y?y:$;_.scheduleMicroTask(x,function(){try{var M=l[J],I=!!h&&A===h[A];I&&(h[b]=M,h[U]=R);var L=_.run(P,void 0,I&&P!==$&&P!==H?[]:[M]);X(h,!0,L)}catch(j){X(h,!1,j)}},h)}var q="function ZoneAwarePromise() { [native code] }",v=function(){},o=e.AggregateError,a=function(){function l(_){var h=this;if(!(h instanceof l))throw new Error("Must be an instanceof Promise.");h[V]=F,h[J]=[];try{var s=O();_&&_(s(Z(h,B)),s(Z(h,k)))}catch(y){X(h,!1,y)}}return l.toString=function(){return q},l.resolve=function(_){return X(new this(null),B,_)},l.reject=function(_){return X(new this(null),k,_)},l.any=function(_){if(!_||"function"!=typeof _[Symbol.iterator])return Promise.reject(new o([],"All promises were rejected"));var h=[],s=0;try{for(var y=0,R=_;y<R.length;y++){var P=R[y];s++,h.push(l.resolve(P))}}catch{return Promise.reject(new o([],"All promises were rejected"))}if(0===s)return Promise.reject(new o([],"All promises were rejected"));var M=!1,I=[];return new l(function(L,j){for(var K=0;K<h.length;K++)h[K].then(function(oe){M||(M=!0,L(oe))},function(oe){I.push(oe),0==--s&&(M=!0,j(new o(I,"All promises were rejected")))})})},l.race=function(_){var h,s,y=new this(function(j,K){h=j,s=K});function R(j){h(j)}function P(j){s(j)}for(var M=0,I=_;M<I.length;M++){var L=I[M];D(L)||(L=this.resolve(L)),L.then(R,P)}return y},l.all=function(_){return l.allWithCallback(_)},l.allSettled=function(_){return(this&&this.prototype instanceof l?this:l).allWithCallback(_,{thenCallback:function(s){return{status:"fulfilled",value:s}},errorCallback:function(s){return{status:"rejected",reason:s}}})},l.allWithCallback=function(_,h){for(var s,y,R=new this(function(re,ne){s=re,y=ne}),P=2,M=0,I=[],L=function(re){D(re)||(re=j.resolve(re));var ne=M;try{re.then(function(ee){I[ne]=h?h.thenCallback(ee):ee,0==--P&&s(I)},function(ee){h?(I[ne]=h.errorCallback(ee),0==--P&&s(I)):y(ee)})}catch(ee){y(ee)}P++,M++},j=this,K=0,oe=_;K<oe.length;K++)L(oe[K]);return 0==(P-=2)&&s(I),R},Object.defineProperty(l.prototype,Symbol.toStringTag,{get:function(){return"Promise"},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,Symbol.species,{get:function(){return l},enumerable:!1,configurable:!0}),l.prototype.then=function(_,h){var s,y=null===(s=this.constructor)||void 0===s?void 0:s[Symbol.species];(!y||"function"!=typeof y)&&(y=this.constructor||l);var R=new y(v),P=r.current;return this[V]==F?this[J].push(P,R,_,h):Q(this,P,R,_,h),R},l.prototype.catch=function(_){return this.then(null,_)},l.prototype.finally=function(_){var h,s=null===(h=this.constructor)||void 0===h?void 0:h[Symbol.species];(!s||"function"!=typeof s)&&(s=l);var y=new s(v);y[A]=A;var R=r.current;return this[V]==F?this[J].push(R,y,_,_):Q(this,R,y,_,_),y},l}();a.resolve=a.resolve,a.reject=a.reject,a.race=a.race,a.all=a.all;var i=e[T]=e.Promise;e.Promise=a;var w=f("thenPatched");function Y(l){var _=l.prototype,h=n(_,"then");if(!h||!1!==h.writable&&h.configurable){var s=_.then;_[p]=s,l.prototype.then=function(y,R){var P=this;return new a(function(I,L){s.call(P,I,L)}).then(y,R)},l[w]=!0}}function se(l){return function(_,h){var s=l.apply(_,h);if(s instanceof a)return s;var y=s.constructor;return y[w]||Y(y),s}}return t.patchThen=Y,i&&(Y(i),_e(e,"fetch",function(l){return se(l)})),Promise[r.__symbol__("uncaughtPromiseErrors")]=d,a}),Zone.__load_patch("toString",function(e){var r=Function.prototype.toString,t=G("OriginalDelegate"),n=G("Promise"),u=G("Error"),c=function(){if("function"==typeof this){var T=this[t];if(T)return"function"==typeof T?r.call(T):Object.prototype.toString.call(T);if(this===Promise){var p=e[n];if(p)return r.call(p)}if(this===Error){var m=e[u];if(m)return r.call(m)}}return r.call(this)};c[t]=r,Function.prototype.toString=c;var f=Object.prototype.toString,d="[object Promise]";Object.prototype.toString=function(){return"function"==typeof Promise&&this instanceof Promise?d:f.call(this)}});var we=!1;if(typeof window<"u")try{var He=Object.defineProperty({},"passive",{get:function(){we=!0}});window.addEventListener("test",He,He),window.removeEventListener("test",He,He)}catch{we=!1}var xe,Fe,_r,pr,me,Cr={useG:!0},ie={},cr={},sr=new RegExp("^"+Ze+"(\\w+)(true|false)$"),lr=G("propagationStopped");function fr(e,r){var t=(r?r(e):e)+de,n=(r?r(e):e)+he,u=Ze+t,c=Ze+n;ie[e]={},ie[e][de]=u,ie[e][he]=c}function Dr(e,r,t,n){var u=n&&n.add||ve,c=n&&n.rm||Ue,f=n&&n.listeners||"eventListeners",d=n&&n.rmAll||"removeAllListeners",E=G(u),T="."+u+":",p="prependListener",m="."+p+":",g=function(A,b,U){if(!A.isRemoved){var F,x=A.callback;"object"==typeof x&&x.handleEvent&&(A.callback=function(z){return x.handleEvent(z)},A.originalDelegate=x);try{A.invoke(A,b,[U])}catch(z){F=z}var B=A.options;if(B&&"object"==typeof B&&B.once){var k=A.originalDelegate?A.originalDelegate:A.callback;b[c].call(b,U.type,k,B)}return F}};function C(A,b,U){if(b=b||e.event){var x=A||b.target||e,F=x[ie[b.type][U?he:de]];if(F){var B=[];if(1===F.length)(k=g(F[0],x,b))&&B.push(k);else for(var z=F.slice(),Z=0;Z<z.length&&(!b||!0!==b[lr]);Z++){var k;(k=g(z[Z],x,b))&&B.push(k)}if(1===B.length)throw B[0];var O=function(N){var S=B[N];r.nativeScheduleMicroTask(function(){throw S})};for(Z=0;Z<B.length;Z++)O(Z)}}}var D=function(A){return C(this,A,!1)},H=function(A){return C(this,A,!0)};function $(A,b){if(!A)return!1;var U=!0;b&&void 0!==b.useG&&(U=b.useG);var x=b&&b.vh,F=!0;b&&void 0!==b.chkDup&&(F=b.chkDup);var B=!1;b&&void 0!==b.rt&&(B=b.rt);for(var k=A;k&&!k.hasOwnProperty(u);)k=De(k);if(!k&&A[u]&&(k=A),!k||k[E])return!1;var W,z=b&&b.eventNameToString,Z={},O=k[E]=k[u],N=k[G(c)]=k[c],S=k[G(f)]=k[f],X=k[G(d)]=k[d];function ae(s,y){return!we&&"object"==typeof s&&s?!!s.capture:we&&y?"boolean"==typeof s?{capture:s,passive:!0}:s?"object"==typeof s&&!1!==s.passive?Object.assign(Object.assign({},s),{passive:!0}):s:{passive:!0}:s}b&&b.prepend&&(W=k[G(b.prepend)]=k[b.prepend]);var o=function(s){return W.call(Z.target,Z.eventName,s.invoke,Z.options)},i=U?function(s){if(!Z.isExisting)return O.call(Z.target,Z.eventName,Z.capture?H:D,Z.options)}:function(s){return O.call(Z.target,Z.eventName,s.invoke,Z.options)},w=U?function(s){if(!s.isRemoved){var y=ie[s.eventName],R=void 0;y&&(R=y[s.capture?he:de]);var P=R&&s.target[R];if(P)for(var M=0;M<P.length;M++)if(P[M]===s){P.splice(M,1),s.isRemoved=!0,0===P.length&&(s.allRemoved=!0,s.target[R]=null);break}}if(s.allRemoved)return N.call(s.target,s.eventName,s.capture?H:D,s.options)}:function(s){return N.call(s.target,s.eventName,s.invoke,s.options)},Y=function(s,y){var R=typeof y;return"function"===R&&s.callback===y||"object"===R&&s.originalDelegate===y},se=b&&b.diff?b.diff:Y,l=Zone[G("UNPATCHED_EVENTS")],_=e[G("PASSIVE_EVENTS")],h=function(s,y,R,P,M,I){return void 0===M&&(M=!1),void 0===I&&(I=!1),function(){var L=this||e,j=arguments[0];b&&b.transferEventName&&(j=b.transferEventName(j));var K=arguments[1];if(!K)return s.apply(this,arguments);if(Ae&&"uncaughtException"===j)return s.apply(this,arguments);var oe=!1;if("function"!=typeof K){if(!K.handleEvent)return s.apply(this,arguments);oe=!0}if(!x||x(s,K,L,arguments)){var Ee=we&&!!_&&-1!==_.indexOf(j),re=ae(arguments[2],Ee);if(l)for(var ne=0;ne<l.length;ne++)if(j===l[ne])return Ee?s.call(L,j,K,re):s.apply(this,arguments);var ee=!!re&&("boolean"==typeof re||re.capture),Oe=!(!re||"object"!=typeof re)&&re.once,$r=Zone.current,er=ie[j];er||(fr(j,z),er=ie[j]);var gr=er[ee?he:de],Se=L[gr],mr=!1;if(Se){if(mr=!0,F)for(ne=0;ne<Se.length;ne++)if(se(Se[ne],K))return}else Se=L[gr]=[];var Ge,br=L.constructor.name,kr=cr[br];kr&&(Ge=kr[j]),Ge||(Ge=br+y+(z?z(j):j)),Z.options=re,Oe&&(Z.options.once=!1),Z.target=L,Z.capture=ee,Z.eventName=j,Z.isExisting=mr;var Ie=U?Cr:void 0;Ie&&(Ie.taskData=Z);var ge=$r.scheduleEventTask(Ge,K,Ie,R,P);if(Z.target=null,Ie&&(Ie.taskData=null),Oe&&(re.once=!0),!we&&"boolean"==typeof ge.options||(ge.options=re),ge.target=L,ge.capture=ee,ge.eventName=j,oe&&(ge.originalDelegate=K),I?Se.unshift(ge):Se.push(ge),M)return L}}};return k[u]=h(O,T,i,w,B),W&&(k[p]=h(W,m,o,w,B,!0)),k[c]=function(){var s=this||e,y=arguments[0];b&&b.transferEventName&&(y=b.transferEventName(y));var R=arguments[2],P=!!R&&("boolean"==typeof R||R.capture),M=arguments[1];if(!M)return N.apply(this,arguments);if(!x||x(N,M,s,arguments)){var L,I=ie[y];I&&(L=I[P?he:de]);var j=L&&s[L];if(j)for(var K=0;K<j.length;K++){var oe=j[K];if(se(oe,M))return j.splice(K,1),oe.isRemoved=!0,0===j.length&&(oe.allRemoved=!0,s[L]=null,"string"==typeof y)&&(s[Ze+"ON_PROPERTY"+y]=null),oe.zone.cancelTask(oe),B?s:void 0}return N.apply(this,arguments)}},k[f]=function(){var s=this||e,y=arguments[0];b&&b.transferEventName&&(y=b.transferEventName(y));for(var R=[],P=vr(s,z?z(y):y),M=0;M<P.length;M++){var I=P[M],L=I.originalDelegate?I.originalDelegate:I.callback;R.push(L)}return R},k[d]=function(){var s=this||e,y=arguments[0];if(y){b&&b.transferEventName&&(y=b.transferEventName(y));var j=ie[y];if(j){var K=j[de],oe=j[he],Ee=s[K],re=s[oe];if(Ee)for(var ne=Ee.slice(),P=0;P<ne.length;P++){var Oe=(ee=ne[P]).originalDelegate?ee.originalDelegate:ee.callback;this[c].call(this,y,Oe,ee.options)}if(re)for(ne=re.slice(),P=0;P<ne.length;P++){var ee;Oe=(ee=ne[P]).originalDelegate?ee.originalDelegate:ee.callback,this[c].call(this,y,Oe,ee.options)}}}else{var R=Object.keys(s);for(P=0;P<R.length;P++){var M=R[P],I=sr.exec(M),L=I&&I[1];L&&"removeListener"!==L&&this[d].call(this,L)}this[d].call(this,"removeListener")}if(B)return this},pe(k[u],O),pe(k[c],N),X&&pe(k[d],X),S&&pe(k[f],S),!0}for(var V=[],J=0;J<t.length;J++)V[J]=$(t[J],n);return V}function vr(e,r){if(!r){var t=[];for(var n in e){var u=sr.exec(n),c=u&&u[1];if(c&&(!r||c===r)){var f=e[n];if(f)for(var d=0;d<f.length;d++)t.push(f[d])}}return t}var E=ie[r];E||(fr(r),E=ie[r]);var T=e[E[de]],p=e[E[he]];return T?p?T.concat(p):T.slice():p?p.slice():[]}function Zr(e,r){var t=e.Event;t&&t.prototype&&r.patchMethod(t.prototype,"stopImmediatePropagation",function(n){return function(u,c){u[lr]=!0,n&&n.apply(u,c)}})}function Mr(e,r,t,n,u){var c=Zone.__symbol__(n);if(!r[c]){var f=r[c]=r[n];r[n]=function(d,E,T){return E&&E.prototype&&u.forEach(function(p){var m="".concat(t,".").concat(n,"::")+p,g=E.prototype;try{if(g.hasOwnProperty(p)){var C=e.ObjectGetOwnPropertyDescriptor(g,p);C&&C.value?(C.value=e.wrapWithCurrentZone(C.value,m),e._redefineProperty(E.prototype,p,C)):g[p]&&(g[p]=e.wrapWithCurrentZone(g[p],m))}else g[p]&&(g[p]=e.wrapWithCurrentZone(g[p],m))}catch{}}),f.call(r,d,E,T)},e.attachOriginToPatched(r[n],f)}}function hr(e,r,t){if(!t||0===t.length)return r;var n=t.filter(function(c){return c.target===e});if(!n||0===n.length)return r;var u=n[0].ignoreProperties;return r.filter(function(c){return-1===u.indexOf(c)})}function dr(e,r,t,n){e&&ir(e,hr(e,r,t),n)}function Qe(e){return Object.getOwnPropertyNames(e).filter(function(r){return r.startsWith("on")&&r.length>2}).map(function(r){return r.substring(2)})}function Ir(e,r){if((!Ae||nr)&&!Zone[e.symbol("patchEvents")]){var t=r.__Zone_ignore_on_properties,n=[];if(Ke){var u=window;n=n.concat(["Document","SVGElement","Element","HTMLElement","HTMLBodyElement","HTMLMediaElement","HTMLFrameSetElement","HTMLFrameElement","HTMLIFrameElement","HTMLMarqueeElement","Worker"]);var c=Sr()?[{target:u,ignoreProperties:["error"]}]:[];dr(u,Qe(u),t&&t.concat(c),De(u))}n=n.concat(["XMLHttpRequest","XMLHttpRequestEventTarget","IDBIndex","IDBRequest","IDBOpenDBRequest","IDBDatabase","IDBTransaction","IDBCursor","WebSocket"]);for(var f=0;f<n.length;f++){var d=r[n[f]];d&&d.prototype&&dr(d.prototype,Qe(d.prototype),t)}}}function Lr(){xe=Zone.__symbol__,Fe=Object[xe("defineProperty")]=Object.defineProperty,_r=Object[xe("getOwnPropertyDescriptor")]=Object.getOwnPropertyDescriptor,pr=Object.create,me=xe("unconfigurables"),Object.defineProperty=function(e,r,t){if(Er(e,r))throw new TypeError("Cannot assign to read only property '"+r+"' of "+e);var n=t.configurable;return"prototype"!==r&&(t=$e(e,r,t)),yr(e,r,t,n)},Object.defineProperties=function(e,r){Object.keys(r).forEach(function(f){Object.defineProperty(e,f,r[f])});for(var t=0,n=Object.getOwnPropertySymbols(r);t<n.length;t++){var u=n[t],c=Object.getOwnPropertyDescriptor(r,u);(null==c?void 0:c.enumerable)&&Object.defineProperty(e,u,r[u])}return e},Object.create=function(e,r){return"object"==typeof r&&!Object.isFrozen(r)&&Object.keys(r).forEach(function(t){r[t]=$e(e,t,r[t])}),pr(e,r)},Object.getOwnPropertyDescriptor=function(e,r){var t=_r(e,r);return t&&Er(e,r)&&(t.configurable=!1),t}}function Nr(e,r,t){var n=t.configurable;return yr(e,r,t=$e(e,r,t),n)}function Er(e,r){return e&&e[me]&&e[me][r]}function $e(e,r,t){return Object.isFrozen(t)||(t.configurable=!0),t.configurable||(!e[me]&&!Object.isFrozen(e)&&Fe(e,me,{writable:!0,value:{}}),e[me]&&(e[me][r]=!0)),t}function yr(e,r,t,n){try{return Fe(e,r,t)}catch(f){if(!t.configurable)throw f;typeof n>"u"?delete t.configurable:t.configurable=n;try{return Fe(e,r,t)}catch(d){var u=!1;if(("createdCallback"===r||"attachedCallback"===r||"detachedCallback"===r||"attributeChangedCallback"===r)&&(u=!0),!u)throw d;var c=null;try{c=JSON.stringify(t)}catch{c=t.toString()}console.log("Attempting to configure '".concat(r,"' with descriptor '").concat(c,"' on object '").concat(e,"' and got error, giving up: ").concat(d))}}}function Ar(e,r){var t=r.getGlobalObjects(),n=t.eventNames,u=t.globalSources,c=t.zoneSymbolEventNames,f=t.TRUE_STR,d=t.FALSE_STR,E=t.ZONE_SYMBOL_PREFIX,T="Anchor,Area,Audio,BR,Base,BaseFont,Body,Button,Canvas,Content,DList,Directory,Div,Embed,FieldSet,Font,Form,Frame,FrameSet,HR,Head,Heading,Html,IFrame,Image,Input,Keygen,LI,Label,Legend,Link,Map,Marquee,Media,Menu,Meta,Meter,Mod,OList,Object,OptGroup,Option,Output,Paragraph,Pre,Progress,Quote,Script,Select,Source,Span,Style,TableCaption,TableCell,TableCol,Table,TableRow,TableSection,TextArea,Title,Track,UList,Unknown,Video",p="ApplicationCache,EventSource,FileReader,InputMethodContext,MediaController,MessagePort,Node,Performance,SVGElementInstance,SharedWorker,TextTrack,TextTrackCue,TextTrackList,WebKitNamedFlow,Window,Worker,WorkerGlobalScope,XMLHttpRequest,XMLHttpRequestEventTarget,XMLHttpRequestUpload,IDBRequest,IDBOpenDBRequest,IDBDatabase,IDBTransaction,IDBCursor,DBIndex,WebSocket".split(","),m="EventTarget",g=[],C=e.wtf,D=T.split(",");C?g=D.map(function(Q){return"HTML"+Q+"Element"}).concat(p):e[m]?g.push(m):g=p;for(var H=e.__Zone_disable_IE_check||!1,$=e.__Zone_enable_cross_context_check||!1,V=r.isIEOrEdge(),J=".addEventListener:",A="[object FunctionWrapper]",b="function __BROWSERTOOLS_CONSOLE_SAFEFUNC() { [native code] }",U={MSPointerCancel:"pointercancel",MSPointerDown:"pointerdown",MSPointerEnter:"pointerenter",MSPointerHover:"pointerhover",MSPointerLeave:"pointerleave",MSPointerMove:"pointermove",MSPointerOut:"pointerout",MSPointerOver:"pointerover",MSPointerUp:"pointerup"},x=0;x<n.length;x++){var z=E+((F=n[x])+d),Z=E+(F+f);c[F]={},c[F][d]=z,c[F][f]=Z}for(x=0;x<D.length;x++)for(var O=D[x],N=u[O]={},S=0;S<n.length;S++){var F;N[F=n[S]]=O+J+F}var X=function(Q,q,v,o){if(!H&&V)if($)try{if((a=q.toString())===A||a==b)return Q.apply(v,o),!1}catch{return Q.apply(v,o),!1}else{var a;if((a=q.toString())===A||a==b)return Q.apply(v,o),!1}else if($)try{q.toString()}catch{return Q.apply(v,o),!1}return!0},W=[];for(x=0;x<g.length;x++){var ae=e[g[x]];W.push(ae&&ae.prototype)}return r.patchEventTarget(e,r,W,{vh:X,transferEventName:function(Q){return U[Q]||Q}}),Zone[r.symbol("patchEventTarget")]=!!e[m],!0}function jr(e,r){var t=e.getGlobalObjects(),n=t.ADD_EVENT_LISTENER_STR,u=t.REMOVE_EVENT_LISTENER_STR,c=r.WebSocket;r.EventTarget||e.patchEventTarget(r,e,[c.prototype]),r.WebSocket=function(E,T){var m,g,p=arguments.length>1?new c(E,T):new c(E),C=e.ObjectGetOwnPropertyDescriptor(p,"onmessage");return C&&!1===C.configurable?(m=e.ObjectCreate(p),g=p,[n,u,"send","close"].forEach(function(D){m[D]=function(){var H=e.ArraySlice.call(arguments);if(D===n||D===u){var $=H.length>0?H[0]:void 0;if($){var V=Zone.__symbol__("ON_PROPERTY"+$);p[V]=m[V]}}return p[D].apply(p,H)}})):m=p,e.patchOnProperties(m,["close","error","message","open"],g),m};var f=r.WebSocket;for(var d in c)f[d]=c[d]}function Hr(e,r){var t=e.getGlobalObjects(),n=t.isNode,u=t.isMix;if((!n||u)&&!xr(e,r)){var c=typeof WebSocket<"u";qr(e),e.patchClass("XMLHttpRequest"),c&&jr(e,r),Zone[e.symbol("patchEvents")]=!0}}function xr(e,r){var t=e.getGlobalObjects(),n=t.isBrowser,u=t.isMix;if((n||u)&&!e.ObjectGetOwnPropertyDescriptor(HTMLElement.prototype,"onclick")&&typeof Element<"u"){var c=e.ObjectGetOwnPropertyDescriptor(Element.prototype,"onclick");if(c&&!c.configurable)return!1;if(c){e.ObjectDefineProperty(Element.prototype,"onclick",{enumerable:!0,configurable:!0,get:function(){return!0}});var d=!!document.createElement("div").onclick;return e.ObjectDefineProperty(Element.prototype,"onclick",c),d}}var E=r.XMLHttpRequest;if(!E)return!1;var T="onreadystatechange",p=E.prototype,m=e.ObjectGetOwnPropertyDescriptor(p,T);if(m)return e.ObjectDefineProperty(p,T,{enumerable:!0,configurable:!0,get:function(){return!0}}),d=!!(g=new E).onreadystatechange,e.ObjectDefineProperty(p,T,m||{}),d;var C=e.symbol("fake");e.ObjectDefineProperty(p,T,{enumerable:!0,configurable:!0,get:function(){return this[C]},set:function(V){this[C]=V}});var g,D=function(){};return(g=new E).onreadystatechange=D,d=g[C]===D,g.onreadystatechange=null,d}Zone.__load_patch("util",function(e,r,t){var n=Qe(e);t.patchOnProperties=ir,t.patchMethod=_e,t.bindArguments=Ye,t.patchMacroTask=Or;var u=r.__symbol__("BLACK_LISTED_EVENTS"),c=r.__symbol__("UNPATCHED_EVENTS");e[c]&&(e[u]=e[c]),e[u]&&(r[u]=r[c]=e[u]),t.patchEventPrototype=Zr,t.patchEventTarget=Dr,t.isIEOrEdge=Rr,t.ObjectDefineProperty=fe,t.ObjectGetOwnPropertyDescriptor=le,t.ObjectCreate=Te,t.ArraySlice=Ve,t.patchClass=Me,t.wrapWithCurrentZone=Xe,t.filterProperties=hr,t.attachOriginToPatched=pe,t._redefineProperty=Object.defineProperty,t.patchCallbacks=Mr,t.getGlobalObjects=function(){return{globalSources:cr,zoneSymbolEventNames:ie,eventNames:n,isBrowser:Ke,isMix:nr,isNode:Ae,TRUE_STR:he,FALSE_STR:de,ZONE_SYMBOL_PREFIX:Ze,ADD_EVENT_LISTENER_STR:ve,REMOVE_EVENT_LISTENER_STR:Ue}}});var Br=["afterscriptexecute","beforescriptexecute","DOMContentLoaded","freeze","fullscreenchange","mozfullscreenchange","webkitfullscreenchange","msfullscreenchange","fullscreenerror","mozfullscreenerror","webkitfullscreenerror","msfullscreenerror","readystatechange","visibilitychange","resume"],Gr=["absolutedeviceorientation","afterinput","afterprint","appinstalled","beforeinstallprompt","beforeprint","beforeunload","devicelight","devicemotion","deviceorientation","deviceorientationabsolute","deviceproximity","hashchange","languagechange","message","mozbeforepaint","offline","online","paint","pageshow","pagehide","popstate","rejectionhandled","storage","unhandledrejection","unload","userproximity","vrdisplayconnected","vrdisplaydisconnected","vrdisplaypresentchange"],Vr=["beforecopy","beforecut","beforepaste","copy","cut","paste","dragstart","loadend","animationstart","search","transitionrun","transitionstart","webkitanimationend","webkitanimationiteration","webkitanimationstart","webkittransitionend"],Ur=["activate","afterupdate","ariarequest","beforeactivate","beforedeactivate","beforeeditfocus","beforeupdate","cellchange","controlselect","dataavailable","datasetchanged","datasetcomplete","errorupdate","filterchange","layoutcomplete","losecapture","move","moveend","movestart","propertychange","resizeend","resizestart","rowenter","rowexit","rowsdelete","rowsinserted","command","compassneedscalibration","deactivate","help","mscontentzoom","msmanipulationstatechanged","msgesturechange","msgesturedoubletap","msgestureend","msgesturehold","msgesturestart","msgesturetap","msgotpointercapture","msinertiastart","mslostpointercapture","mspointercancel","mspointerdown","mspointerenter","mspointerhover","mspointerleave","mspointermove","mspointerout","mspointerover","mspointerup","pointerout","mssitemodejumplistitemremoved","msthumbnailclick","stop","storagecommit"],Wr=["webglcontextrestored","webglcontextlost","webglcontextcreationerror"],zr=["autocomplete","autocompleteerror"],Xr=["toggle"],Tr=ue(ue(ue(ue(ue(ue(ue(ue([],["abort","animationcancel","animationend","animationiteration","auxclick","beforeinput","blur","cancel","canplay","canplaythrough","change","compositionstart","compositionupdate","compositionend","cuechange","click","close","contextmenu","curechange","dblclick","drag","dragend","dragenter","dragexit","dragleave","dragover","drop","durationchange","emptied","ended","error","focus","focusin","focusout","gotpointercapture","input","invalid","keydown","keypress","keyup","load","loadstart","loadeddata","loadedmetadata","lostpointercapture","mousedown","mouseenter","mouseleave","mousemove","mouseout","mouseover","mouseup","mousewheel","orientationchange","pause","play","playing","pointercancel","pointerdown","pointerenter","pointerleave","pointerlockchange","mozpointerlockchange","webkitpointerlockerchange","pointerlockerror","mozpointerlockerror","webkitpointerlockerror","pointermove","pointout","pointerover","pointerup","progress","ratechange","reset","resize","scroll","seeked","seeking","select","selectionchange","selectstart","show","sort","stalled","submit","suspend","timeupdate","volumechange","touchcancel","touchmove","touchstart","touchend","transitioncancel","transitionend","waiting","wheel"],!0),Wr,!0),zr,!0),Xr,!0),Br,!0),Gr,!0),Vr,!0),Ur,!0);function qr(e){for(var r=e.symbol("unbound"),t=function(u){var c=Tr[u],f="on"+c;self.addEventListener(c,function(d){var T,p,E=d.target;for(p=E?E.constructor.name+"."+f:"unknown."+f;E;)E[f]&&!E[f][r]&&((T=e.wrapWithCurrentZone(E[f],p))[r]=E[f],E[f]=T),E=E.parentElement},!0)},n=0;n<Tr.length;n++)t(n)}function Yr(e,r){var t=r.getGlobalObjects(),n=t.isBrowser,u=t.isMix;if((n||u)&&"registerElement"in e.document){var c=["createdCallback","attachedCallback","detachedCallback","attributeChangedCallback"];r.patchCallbacks(r,document,"Document","registerElement",c)}}!function(e){var r=e.__Zone_symbol_prefix||"__zone_symbol__";function t(n){return r+n}e[t("legacyPatch")]=function(){var n=e.Zone;n.__load_patch("defineProperty",function(u,c,f){f._redefineProperty=Nr,Lr()}),n.__load_patch("registerElement",function(u,c,f){Yr(u,f)}),n.__load_patch("EventTargetLegacy",function(u,c,f){Ar(u,f),Hr(f,u)})}}(typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{});var Be=G("zoneTask");function Pe(e,r,t,n){var u=null,c=null;t+=n;var f={};function d(T){var p=T.data;return p.args[0]=function(){return T.invoke.apply(this,arguments)},p.handleId=u.apply(e,p.args),T}function E(T){return c.call(e,T.data.handleId)}u=_e(e,r+=n,function(T){return function(p,m){if("function"==typeof m[0]){var g={isPeriodic:"Interval"===n,delay:"Timeout"===n||"Interval"===n?m[1]||0:void 0,args:m},C=m[0];m[0]=function(){try{return C.apply(this,arguments)}finally{g.isPeriodic||("number"==typeof g.handleId?delete f[g.handleId]:g.handleId&&(g.handleId[Be]=null))}};var D=qe(r,m[0],g,d,E);if(!D)return D;var H=D.data.handleId;return"number"==typeof H?f[H]=D:H&&(H[Be]=D),H&&H.ref&&H.unref&&"function"==typeof H.ref&&"function"==typeof H.unref&&(D.ref=H.ref.bind(H),D.unref=H.unref.bind(H)),"number"==typeof H||H?H:D}return T.apply(e,m)}}),c=_e(e,t,function(T){return function(p,m){var C,g=m[0];"number"==typeof g?C=f[g]:(C=g&&g[Be])||(C=g),C&&"string"==typeof C.type?"notScheduled"!==C.state&&(C.cancelFn&&C.data.isPeriodic||0===C.runCount)&&("number"==typeof g?delete f[g]:g&&(g[Be]=null),C.zone.cancelTask(C)):T.apply(e,m)}})}function Kr(e,r){var t=r.getGlobalObjects(),n=t.isBrowser,u=t.isMix;if((n||u)&&e.customElements&&"customElements"in e){var c=["connectedCallback","disconnectedCallback","adoptedCallback","attributeChangedCallback"];r.patchCallbacks(r,e.customElements,"customElements","define",c)}}function Jr(e,r){if(!Zone[r.symbol("patchEventTarget")]){for(var t=r.getGlobalObjects(),n=t.eventNames,u=t.zoneSymbolEventNames,c=t.TRUE_STR,f=t.FALSE_STR,d=t.ZONE_SYMBOL_PREFIX,E=0;E<n.length;E++){var T=n[E],g=d+(T+f),C=d+(T+c);u[T]={},u[T][f]=g,u[T][c]=C}var D=e.EventTarget;if(D&&D.prototype)return r.patchEventTarget(e,r,[D&&D.prototype]),!0}}function Qr(e,r){r.patchEventPrototype(e,r)}Zone.__load_patch("legacy",function(e){var r=e[Zone.__symbol__("legacyPatch")];r&&r()}),Zone.__load_patch("queueMicrotask",function(e,r,t){t.patchMethod(e,"queueMicrotask",function(n){return function(u,c){r.current.scheduleMicroTask("queueMicrotask",c[0])}})}),Zone.__load_patch("timers",function(e){var r="set",t="clear";Pe(e,r,t,"Timeout"),Pe(e,r,t,"Interval"),Pe(e,r,t,"Immediate")}),Zone.__load_patch("requestAnimationFrame",function(e){Pe(e,"request","cancel","AnimationFrame"),Pe(e,"mozRequest","mozCancel","AnimationFrame"),Pe(e,"webkitRequest","webkitCancel","AnimationFrame")}),Zone.__load_patch("blocking",function(e,r){for(var t=["alert","prompt","confirm"],n=0;n<t.length;n++){var u=t[n];_e(e,u,function(c,f,d){return function(E,T){return r.current.run(c,e,T,d)}})}}),Zone.__load_patch("EventTarget",function(e,r,t){Qr(e,t),Jr(e,t);var n=e.XMLHttpRequestEventTarget;n&&n.prototype&&t.patchEventTarget(e,t,[n.prototype])}),Zone.__load_patch("MutationObserver",function(e,r,t){Me("MutationObserver"),Me("WebKitMutationObserver")}),Zone.__load_patch("IntersectionObserver",function(e,r,t){Me("IntersectionObserver")}),Zone.__load_patch("FileReader",function(e,r,t){Me("FileReader")}),Zone.__load_patch("on_property",function(e,r,t){Ir(t,e)}),Zone.__load_patch("customElements",function(e,r,t){Kr(e,t)}),Zone.__load_patch("XHR",function(e,r){E(e);var t=G("xhrTask"),n=G("xhrSync"),u=G("xhrListener"),c=G("xhrScheduled"),f=G("xhrURL"),d=G("xhrErrorBeforeScheduled");function E(T){var p=T.XMLHttpRequest;if(p){var m=p.prototype,C=m[We],D=m[ze];if(!C){var H=T.XMLHttpRequestEventTarget;if(H){var $=H.prototype;C=$[We],D=$[ze]}}var V="readystatechange",J="scheduled",x=_e(m,"open",function(){return function(O,N){return O[n]=0==N[2],O[f]=N[1],x.apply(O,N)}}),F="XMLHttpRequest.send",B=G("fetchTaskAborting"),k=G("fetchTaskScheduling"),z=_e(m,"send",function(){return function(O,N){if(!0===r.current[k]||O[n])return z.apply(O,N);var S={target:O,url:O[f],isPeriodic:!1,args:N,aborted:!1},X=qe(F,b,S,A,U);O&&!0===O[d]&&!S.aborted&&X.state===J&&X.invoke()}}),Z=_e(m,"abort",function(){return function(O,N){var S=g(O);if(S&&"string"==typeof S.type){if(null==S.cancelFn||S.data&&S.data.aborted)return;S.zone.cancelTask(S)}else if(!0===r.current[B])return Z.apply(O,N)}})}function g(O){return O[t]}function A(O){var N=O.data,S=N.target;S[c]=!1,S[d]=!1;var X=S[u];C||(C=S[We],D=S[ze]),X&&D.call(S,V,X);var W=S[u]=function(){if(S.readyState===S.DONE)if(!N.aborted&&S[c]&&O.state===J){var Q=S[r.__symbol__("loadfalse")];if(0!==S.status&&Q&&Q.length>0){var q=O.invoke;O.invoke=function(){for(var v=S[r.__symbol__("loadfalse")],o=0;o<v.length;o++)v[o]===O&&v.splice(o,1);!N.aborted&&O.state===J&&q.call(O)},Q.push(O)}else O.invoke()}else!N.aborted&&!1===S[c]&&(S[d]=!0)};return C.call(S,V,W),S[t]||(S[t]=O),z.apply(S,N.args),S[c]=!0,O}function b(){}function U(O){var N=O.data;return N.aborted=!0,Z.apply(N.target,N.args)}}}),Zone.__load_patch("geolocation",function(e){e.navigator&&e.navigator.geolocation&&Pr(e.navigator.geolocation,["getCurrentPosition","watchPosition"])}),Zone.__load_patch("PromiseRejectionEvent",function(e,r){function t(n){return function(u){vr(e,n).forEach(function(f){var d=e.PromiseRejectionEvent;if(d){var E=new d(n,{promise:u.promise,reason:u.rejection});f.invoke(E)}})}}e.PromiseRejectionEvent&&(r[G("unhandledPromiseRejectionHandler")]=t("unhandledrejection"),r[G("rejectionHandledHandler")]=t("rejectionhandled"))})},void 0!==(Le="function"==typeof ye?ye.call(Re,Ce,Re,be):ye)&&(be.exports=Le)}},be=>{var ye;ye=7435,be(be.s=ye)}]);