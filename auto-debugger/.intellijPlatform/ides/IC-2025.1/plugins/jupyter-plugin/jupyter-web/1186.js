"use strict";(self.webpackChunkjupyter_web=self.webpackChunkjupyter_web||[]).push([[1186],{91186:(B,m,v)=>{Object.defineProperty(m,"__esModule",{value:!0}),m.AllPackages=void 0,v(80677),v(36928),v(22124),v(16529),v(23525),v(46174),v(26134),v(58218),v(43313),v(31541),v(88254),v(89112),v(25951),v(52830),v(25886),v(94822),v(88687),v(93846),v(83190),v(59548),v(41461),v(30203),v(20196),v(3861),v(97370),v(31097),v(23792),v(90108),v(85966),v(78740),v(13601),v(34531),v(14961),typeof MathJax<"u"&&MathJax.loader&&MathJax.loader.preLoad("[tex]/action","[tex]/ams","[tex]/amscd","[tex]/bbox","[tex]/boldsymbol","[tex]/braket","[tex]/bussproofs","[tex]/cancel","[tex]/cases","[tex]/centernot","[tex]/color","[tex]/colorv2","[tex]/colortbl","[tex]/empheq","[tex]/enclose","[tex]/extpfeil","[tex]/gensymb","[tex]/html","[tex]/mathtools","[tex]/mhchem","[tex]/newcommand","[tex]/noerrors","[tex]/noundefined","[tex]/physics","[tex]/upgreek","[tex]/unicode","[tex]/verb","[tex]/configmacros","[tex]/tagformat","[tex]/textcomp","[tex]/textmacros","[tex]/setoptions"),m.AllPackages=["base","action","ams","amscd","bbox","boldsymbol","braket","bussproofs","cancel","cases","centernot","color","colortbl","empheq","enclose","extpfeil","gensymb","html","mathtools","mhchem","newcommand","noerrors","noundefined","upgreek","unicode","verb","configmacros","tagformat","textcomp","textmacros"]},36928:function(B,m,v){var w=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(m,"__esModule",{value:!0}),m.ActionConfiguration=m.ActionMethods=void 0;var y=v(8890),M=w(v(55571)),x=v(82652),t=w(v(2158));m.ActionMethods={},m.ActionMethods.Macro=t.default.Macro,m.ActionMethods.Toggle=function(e,n){for(var l,c=[];"\\endtoggle"!==(l=e.GetArgument(n));)c.push(new M.default(l,e.stack.env,e.configuration).mml());e.Push(e.create("node","maction",c,{actiontype:"toggle"}))},m.ActionMethods.Mathtip=function(e,n){var c=e.ParseArg(n),l=e.ParseArg(n);e.Push(e.create("node","maction",[c,l],{actiontype:"tooltip"}))},new x.CommandMap("action-macros",{toggle:"Toggle",mathtip:"Mathtip",texttip:["Macro","\\mathtip{#1}{\\text{#2}}",2]},m.ActionMethods),m.ActionConfiguration=y.Configuration.create("action",{handler:{macro:["action-macros"]}})},22124:function(B,m,v){var y,d,w=this&&this.__extends||(d=function(o,i){return(d=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(f,h){f.__proto__=h}||function(f,h){for(var a in h)Object.prototype.hasOwnProperty.call(h,a)&&(f[a]=h[a])})(o,i)},function(o,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function f(){this.constructor=o}d(o,i),o.prototype=null===i?Object.create(i):(f.prototype=i.prototype,new f)});Object.defineProperty(m,"__esModule",{value:!0}),m.AmsConfiguration=m.AmsTags=void 0;var M=v(8890),x=v(66866),t=v(22450),e=v(86800);v(36155);var n=v(82652),c=function(d){function o(){return null!==d&&d.apply(this,arguments)||this}return w(o,d),o}(t.AbstractTags);m.AmsTags=c;m.AmsConfiguration=M.Configuration.create("ams",{handler:{character:["AMSmath-operatorLetter"],delimiter:["AMSsymbols-delimiter","AMSmath-delimiter"],macro:["AMSsymbols-mathchar0mi","AMSsymbols-mathchar0mo","AMSsymbols-delimiter","AMSsymbols-macros","AMSmath-mathchar0mo","AMSmath-macros","AMSmath-delimiter"],environment:["AMSmath-environment"]},items:(y={},y[x.MultlineItem.prototype.kind]=x.MultlineItem,y[x.FlalignItem.prototype.kind]=x.FlalignItem,y),tags:{ams:c},init:function(d){new n.CommandMap(e.NEW_OPS,{},{}),d.append(M.Configuration.local({handler:{macro:[e.NEW_OPS]},priority:-1}))},config:function(d,o){o.parseOptions.options.multlineWidth&&(o.parseOptions.options.ams.multlineWidth=o.parseOptions.options.multlineWidth),delete o.parseOptions.options.multlineWidth},options:{multlineWidth:"",ams:{multlineWidth:"100%",multlineIndent:"1em"}}})},66866:function(B,m,v){var o,w=this&&this.__extends||(o=function(i,f){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(h,a){h.__proto__=a}||function(h,a){for(var g in a)Object.prototype.hasOwnProperty.call(a,g)&&(h[g]=a[g])})(i,f)},function(i,f){if("function"!=typeof f&&null!==f)throw new TypeError("Class extends value "+String(f)+" is not a constructor or null");function h(){this.constructor=i}o(i,f),i.prototype=null===f?Object.create(f):(h.prototype=f.prototype,new h)}),y=this&&this.__assign||function(){return y=Object.assign||function(o){for(var i,f=1,h=arguments.length;f<h;f++)for(var a in i=arguments[f])Object.prototype.hasOwnProperty.call(i,a)&&(o[a]=i[a]);return o},y.apply(this,arguments)},M=this&&this.__importDefault||function(o){return o&&o.__esModule?o:{default:o}};Object.defineProperty(m,"__esModule",{value:!0}),m.FlalignItem=m.MultlineItem=void 0;var x=v(33012),t=M(v(39194)),e=M(v(60909)),n=M(v(6257)),c=v(60742),l=function(o){function i(f){for(var h=[],a=1;a<arguments.length;a++)h[a-1]=arguments[a];var g=o.call(this,f)||this;return g.factory.configuration.tags.start("multline",!0,h[0]),g}return w(i,o),Object.defineProperty(i.prototype,"kind",{get:function(){return"multline"},enumerable:!1,configurable:!0}),i.prototype.EndEntry=function(){this.table.length&&t.default.fixInitialMO(this.factory.configuration,this.nodes);var f=this.getProperty("shove"),h=this.create("node","mtd",this.nodes,f?{columnalign:f}:{});this.setProperty("shove",null),this.row.push(h),this.Clear()},i.prototype.EndRow=function(){if(1!==this.row.length)throw new n.default("MultlineRowsOneCol","The rows within the %1 environment must have exactly one column","multline");var f=this.create("node","mtr",this.row);this.table.push(f),this.row=[]},i.prototype.EndTable=function(){if(o.prototype.EndTable.call(this),this.table.length){var f=this.table.length-1,h=-1;e.default.getAttribute(e.default.getChildren(this.table[0])[0],"columnalign")||e.default.setAttribute(e.default.getChildren(this.table[0])[0],"columnalign",c.TexConstant.Align.LEFT),e.default.getAttribute(e.default.getChildren(this.table[f])[0],"columnalign")||e.default.setAttribute(e.default.getChildren(this.table[f])[0],"columnalign",c.TexConstant.Align.RIGHT);var a=this.factory.configuration.tags.getTag();if(a){h=this.arraydef.side===c.TexConstant.Align.LEFT?0:this.table.length-1;var g=this.table[h],r=this.create("node","mlabeledtr",[a].concat(e.default.getChildren(g)));e.default.copyAttributes(g,r),this.table[h]=r}}this.factory.configuration.tags.end()},i}(x.ArrayItem);m.MultlineItem=l;var d=function(o){function i(f,h,a,g,r){var u=o.call(this,f)||this;return u.name=h,u.numbered=a,u.padded=g,u.center=r,u.factory.configuration.tags.start(h,a,a),u}return w(i,o),Object.defineProperty(i.prototype,"kind",{get:function(){return"flalign"},enumerable:!1,configurable:!0}),i.prototype.EndEntry=function(){o.prototype.EndEntry.call(this);var f=this.getProperty("xalignat");if(f&&this.row.length>f)throw new n.default("XalignOverflow","Extra %1 in row of %2","&",this.name)},i.prototype.EndRow=function(){for(var f,h=this.row,a=this.getProperty("xalignat");h.length<a;)h.push(this.create("node","mtd"));for(this.row=[],this.padded&&this.row.push(this.create("node","mtd"));f=h.shift();)this.row.push(f),(f=h.shift())&&this.row.push(f),(h.length||this.padded)&&this.row.push(this.create("node","mtd"));this.row.length>this.maxrow&&(this.maxrow=this.row.length),o.prototype.EndRow.call(this);var g=this.table[this.table.length-1];if(this.getProperty("zeroWidthLabel")&&g.isKind("mlabeledtr")){var r=e.default.getChildren(g)[0],u=this.factory.configuration.options.tagSide,p=y({width:0},"right"===u?{lspace:"-1width"}:{}),A=this.create("node","mpadded",e.default.getChildren(r),p);r.setChildren([A])}},i.prototype.EndTable=function(){(o.prototype.EndTable.call(this),this.center&&this.maxrow<=2)&&(delete this.arraydef.width,delete this.global.indentalign)},i}(x.EqnArrayItem);m.FlalignItem=d},36155:function(B,m,v){var w=this&&this.__createBinding||(Object.create?function(i,f,h,a){void 0===a&&(a=h);var g=Object.getOwnPropertyDescriptor(f,h);(!g||("get"in g?!f.__esModule:g.writable||g.configurable))&&(g={enumerable:!0,get:function(){return f[h]}}),Object.defineProperty(i,a,g)}:function(i,f,h,a){void 0===a&&(a=h),i[a]=f[h]}),y=this&&this.__setModuleDefault||(Object.create?function(i,f){Object.defineProperty(i,"default",{enumerable:!0,value:f})}:function(i,f){i.default=f}),M=this&&this.__importStar||function(i){if(i&&i.__esModule)return i;var f={};if(null!=i)for(var h in i)"default"!==h&&Object.prototype.hasOwnProperty.call(i,h)&&w(f,i,h);return y(f,i),f},x=this&&this.__importDefault||function(i){return i&&i.__esModule?i:{default:i}};Object.defineProperty(m,"__esModule",{value:!0});var t=v(86800),e=M(v(82652)),n=v(60742),c=x(v(70004)),l=x(v(39194)),d=v(91585),o=v(21108);new e.CharacterMap("AMSmath-mathchar0mo",c.default.mathchar0mo,{iiiint:["\u2a0c",{texClass:d.TEXCLASS.OP}]}),new e.RegExpMap("AMSmath-operatorLetter",t.AmsMethods.operatorLetter,/[-*]/i),new e.CommandMap("AMSmath-macros",{mathring:["Accent","02DA"],nobreakspace:"Tilde",negmedspace:["Spacer",o.MATHSPACE.negativemediummathspace],negthickspace:["Spacer",o.MATHSPACE.negativethickmathspace],idotsint:["MultiIntegral","\\int\\cdots\\int"],dddot:["Accent","20DB"],ddddot:["Accent","20DC"],sideset:"SideSet",boxed:["Macro","\\fbox{$\\displaystyle{#1}$}",1],tag:"HandleTag",notag:"HandleNoTag",eqref:["HandleRef",!0],substack:["Macro","\\begin{subarray}{c}#1\\end{subarray}",1],injlim:["NamedOp","inj&thinsp;lim"],projlim:["NamedOp","proj&thinsp;lim"],varliminf:["Macro","\\mathop{\\underline{\\mmlToken{mi}{lim}}}"],varlimsup:["Macro","\\mathop{\\overline{\\mmlToken{mi}{lim}}}"],varinjlim:["Macro","\\mathop{\\underrightarrow{\\mmlToken{mi}{lim}}}"],varprojlim:["Macro","\\mathop{\\underleftarrow{\\mmlToken{mi}{lim}}}"],DeclareMathOperator:"HandleDeclareOp",operatorname:"HandleOperatorName",genfrac:"Genfrac",frac:["Genfrac","","","",""],tfrac:["Genfrac","","","","1"],dfrac:["Genfrac","","","","0"],binom:["Genfrac","(",")","0",""],tbinom:["Genfrac","(",")","0","1"],dbinom:["Genfrac","(",")","0","0"],cfrac:"CFrac",shoveleft:["HandleShove",n.TexConstant.Align.LEFT],shoveright:["HandleShove",n.TexConstant.Align.RIGHT],xrightarrow:["xArrow",8594,5,10],xleftarrow:["xArrow",8592,10,5]},t.AmsMethods),new e.EnvironmentMap("AMSmath-environment",c.default.environment,{"equation*":["Equation",null,!1],"eqnarray*":["EqnArray",null,!1,!0,"rcl",l.default.cols(0,o.MATHSPACE.thickmathspace),".5em"],align:["EqnArray",null,!0,!0,"rl",l.default.cols(0,2)],"align*":["EqnArray",null,!1,!0,"rl",l.default.cols(0,2)],multline:["Multline",null,!0],"multline*":["Multline",null,!1],split:["EqnArray",null,!1,!1,"rl",l.default.cols(0)],gather:["EqnArray",null,!0,!0,"c"],"gather*":["EqnArray",null,!1,!0,"c"],alignat:["AlignAt",null,!0,!0],"alignat*":["AlignAt",null,!1,!0],alignedat:["AlignAt",null,!1,!1],aligned:["AmsEqnArray",null,null,null,"rl",l.default.cols(0,2),".5em","D"],gathered:["AmsEqnArray",null,null,null,"c",null,".5em","D"],xalignat:["XalignAt",null,!0,!0],"xalignat*":["XalignAt",null,!1,!0],xxalignat:["XalignAt",null,!1,!1],flalign:["FlalignArray",null,!0,!1,!0,"rlc","auto auto fit"],"flalign*":["FlalignArray",null,!1,!1,!0,"rlc","auto auto fit"],subarray:["Array",null,null,null,null,l.default.cols(0),"0.1em","S",1],smallmatrix:["Array",null,null,null,"c",l.default.cols(1/3),".2em","S",1],matrix:["Array",null,null,null,"c"],pmatrix:["Array",null,"(",")","c"],bmatrix:["Array",null,"[","]","c"],Bmatrix:["Array",null,"\\{","\\}","c"],vmatrix:["Array",null,"\\vert","\\vert","c"],Vmatrix:["Array",null,"\\Vert","\\Vert","c"],cases:["Array",null,"\\{",".","ll",null,".2em","T"]},t.AmsMethods),new e.DelimiterMap("AMSmath-delimiter",c.default.delimiter,{"\\lvert":["|",{texClass:d.TEXCLASS.OPEN}],"\\rvert":["|",{texClass:d.TEXCLASS.CLOSE}],"\\lVert":["\u2016",{texClass:d.TEXCLASS.OPEN}],"\\rVert":["\u2016",{texClass:d.TEXCLASS.CLOSE}]}),new e.CharacterMap("AMSsymbols-mathchar0mi",c.default.mathchar0mi,{digamma:"\u03dd",varkappa:"\u03f0",varGamma:["\u0393",{mathvariant:n.TexConstant.Variant.ITALIC}],varDelta:["\u0394",{mathvariant:n.TexConstant.Variant.ITALIC}],varTheta:["\u0398",{mathvariant:n.TexConstant.Variant.ITALIC}],varLambda:["\u039b",{mathvariant:n.TexConstant.Variant.ITALIC}],varXi:["\u039e",{mathvariant:n.TexConstant.Variant.ITALIC}],varPi:["\u03a0",{mathvariant:n.TexConstant.Variant.ITALIC}],varSigma:["\u03a3",{mathvariant:n.TexConstant.Variant.ITALIC}],varUpsilon:["\u03a5",{mathvariant:n.TexConstant.Variant.ITALIC}],varPhi:["\u03a6",{mathvariant:n.TexConstant.Variant.ITALIC}],varPsi:["\u03a8",{mathvariant:n.TexConstant.Variant.ITALIC}],varOmega:["\u03a9",{mathvariant:n.TexConstant.Variant.ITALIC}],beth:"\u2136",gimel:"\u2137",daleth:"\u2138",backprime:["\u2035",{variantForm:!0}],hslash:"\u210f",varnothing:["\u2205",{variantForm:!0}],blacktriangle:"\u25b4",triangledown:["\u25bd",{variantForm:!0}],blacktriangledown:"\u25be",square:"\u25fb",Box:"\u25fb",blacksquare:"\u25fc",lozenge:"\u25ca",Diamond:"\u25ca",blacklozenge:"\u29eb",circledS:["\u24c8",{mathvariant:n.TexConstant.Variant.NORMAL}],bigstar:"\u2605",sphericalangle:"\u2222",measuredangle:"\u2221",nexists:"\u2204",complement:"\u2201",mho:"\u2127",eth:["\xf0",{mathvariant:n.TexConstant.Variant.NORMAL}],Finv:"\u2132",diagup:"\u2571",Game:"\u2141",diagdown:"\u2572",Bbbk:["k",{mathvariant:n.TexConstant.Variant.DOUBLESTRUCK}],yen:"\xa5",circledR:"\xae",checkmark:"\u2713",maltese:"\u2720"}),new e.CharacterMap("AMSsymbols-mathchar0mo",c.default.mathchar0mo,{dotplus:"\u2214",ltimes:"\u22c9",smallsetminus:["\u2216",{variantForm:!0}],rtimes:"\u22ca",Cap:"\u22d2",doublecap:"\u22d2",leftthreetimes:"\u22cb",Cup:"\u22d3",doublecup:"\u22d3",rightthreetimes:"\u22cc",barwedge:"\u22bc",curlywedge:"\u22cf",veebar:"\u22bb",curlyvee:"\u22ce",doublebarwedge:"\u2a5e",boxminus:"\u229f",circleddash:"\u229d",boxtimes:"\u22a0",circledast:"\u229b",boxdot:"\u22a1",circledcirc:"\u229a",boxplus:"\u229e",centerdot:["\u22c5",{variantForm:!0}],divideontimes:"\u22c7",intercal:"\u22ba",leqq:"\u2266",geqq:"\u2267",leqslant:"\u2a7d",geqslant:"\u2a7e",eqslantless:"\u2a95",eqslantgtr:"\u2a96",lesssim:"\u2272",gtrsim:"\u2273",lessapprox:"\u2a85",gtrapprox:"\u2a86",approxeq:"\u224a",lessdot:"\u22d6",gtrdot:"\u22d7",lll:"\u22d8",llless:"\u22d8",ggg:"\u22d9",gggtr:"\u22d9",lessgtr:"\u2276",gtrless:"\u2277",lesseqgtr:"\u22da",gtreqless:"\u22db",lesseqqgtr:"\u2a8b",gtreqqless:"\u2a8c",doteqdot:"\u2251",Doteq:"\u2251",eqcirc:"\u2256",risingdotseq:"\u2253",circeq:"\u2257",fallingdotseq:"\u2252",triangleq:"\u225c",backsim:"\u223d",thicksim:["\u223c",{variantForm:!0}],backsimeq:"\u22cd",thickapprox:["\u2248",{variantForm:!0}],subseteqq:"\u2ac5",supseteqq:"\u2ac6",Subset:"\u22d0",Supset:"\u22d1",sqsubset:"\u228f",sqsupset:"\u2290",preccurlyeq:"\u227c",succcurlyeq:"\u227d",curlyeqprec:"\u22de",curlyeqsucc:"\u22df",precsim:"\u227e",succsim:"\u227f",precapprox:"\u2ab7",succapprox:"\u2ab8",vartriangleleft:"\u22b2",lhd:"\u22b2",vartriangleright:"\u22b3",rhd:"\u22b3",trianglelefteq:"\u22b4",unlhd:"\u22b4",trianglerighteq:"\u22b5",unrhd:"\u22b5",vDash:["\u22a8",{variantForm:!0}],Vdash:"\u22a9",Vvdash:"\u22aa",smallsmile:["\u2323",{variantForm:!0}],shortmid:["\u2223",{variantForm:!0}],smallfrown:["\u2322",{variantForm:!0}],shortparallel:["\u2225",{variantForm:!0}],bumpeq:"\u224f",between:"\u226c",Bumpeq:"\u224e",pitchfork:"\u22d4",varpropto:["\u221d",{variantForm:!0}],backepsilon:"\u220d",blacktriangleleft:"\u25c2",blacktriangleright:"\u25b8",therefore:"\u2234",because:"\u2235",eqsim:"\u2242",vartriangle:["\u25b3",{variantForm:!0}],Join:"\u22c8",nless:"\u226e",ngtr:"\u226f",nleq:"\u2270",ngeq:"\u2271",nleqslant:["\u2a87",{variantForm:!0}],ngeqslant:["\u2a88",{variantForm:!0}],nleqq:["\u2270",{variantForm:!0}],ngeqq:["\u2271",{variantForm:!0}],lneq:"\u2a87",gneq:"\u2a88",lneqq:"\u2268",gneqq:"\u2269",lvertneqq:["\u2268",{variantForm:!0}],gvertneqq:["\u2269",{variantForm:!0}],lnsim:"\u22e6",gnsim:"\u22e7",lnapprox:"\u2a89",gnapprox:"\u2a8a",nprec:"\u2280",nsucc:"\u2281",npreceq:["\u22e0",{variantForm:!0}],nsucceq:["\u22e1",{variantForm:!0}],precneqq:"\u2ab5",succneqq:"\u2ab6",precnsim:"\u22e8",succnsim:"\u22e9",precnapprox:"\u2ab9",succnapprox:"\u2aba",nsim:"\u2241",ncong:"\u2247",nshortmid:["\u2224",{variantForm:!0}],nshortparallel:["\u2226",{variantForm:!0}],nmid:"\u2224",nparallel:"\u2226",nvdash:"\u22ac",nvDash:"\u22ad",nVdash:"\u22ae",nVDash:"\u22af",ntriangleleft:"\u22ea",ntriangleright:"\u22eb",ntrianglelefteq:"\u22ec",ntrianglerighteq:"\u22ed",nsubseteq:"\u2288",nsupseteq:"\u2289",nsubseteqq:["\u2288",{variantForm:!0}],nsupseteqq:["\u2289",{variantForm:!0}],subsetneq:"\u228a",supsetneq:"\u228b",varsubsetneq:["\u228a",{variantForm:!0}],varsupsetneq:["\u228b",{variantForm:!0}],subsetneqq:"\u2acb",supsetneqq:"\u2acc",varsubsetneqq:["\u2acb",{variantForm:!0}],varsupsetneqq:["\u2acc",{variantForm:!0}],leftleftarrows:"\u21c7",rightrightarrows:"\u21c9",leftrightarrows:"\u21c6",rightleftarrows:"\u21c4",Lleftarrow:"\u21da",Rrightarrow:"\u21db",twoheadleftarrow:"\u219e",twoheadrightarrow:"\u21a0",leftarrowtail:"\u21a2",rightarrowtail:"\u21a3",looparrowleft:"\u21ab",looparrowright:"\u21ac",leftrightharpoons:"\u21cb",rightleftharpoons:["\u21cc",{variantForm:!0}],curvearrowleft:"\u21b6",curvearrowright:"\u21b7",circlearrowleft:"\u21ba",circlearrowright:"\u21bb",Lsh:"\u21b0",Rsh:"\u21b1",upuparrows:"\u21c8",downdownarrows:"\u21ca",upharpoonleft:"\u21bf",upharpoonright:"\u21be",downharpoonleft:"\u21c3",restriction:"\u21be",multimap:"\u22b8",downharpoonright:"\u21c2",leftrightsquigarrow:"\u21ad",rightsquigarrow:"\u21dd",leadsto:"\u21dd",dashrightarrow:"\u21e2",dashleftarrow:"\u21e0",nleftarrow:"\u219a",nrightarrow:"\u219b",nLeftarrow:"\u21cd",nRightarrow:"\u21cf",nleftrightarrow:"\u21ae",nLeftrightarrow:"\u21ce"}),new e.DelimiterMap("AMSsymbols-delimiter",c.default.delimiter,{"\\ulcorner":"\u231c","\\urcorner":"\u231d","\\llcorner":"\u231e","\\lrcorner":"\u231f"}),new e.CommandMap("AMSsymbols-macros",{implies:["Macro","\\;\\Longrightarrow\\;"],impliedby:["Macro","\\;\\Longleftarrow\\;"]},t.AmsMethods)},86800:function(B,m,v){var w=this&&this.__assign||function(){return w=Object.assign||function(a){for(var g,r=1,u=arguments.length;r<u;r++)for(var p in g=arguments[r])Object.prototype.hasOwnProperty.call(g,p)&&(a[p]=g[p]);return a},w.apply(this,arguments)},y=this&&this.__read||function(a,g){var r="function"==typeof Symbol&&a[Symbol.iterator];if(!r)return a;var p,C,u=r.call(a),A=[];try{for(;(void 0===g||g-- >0)&&!(p=u.next()).done;)A.push(p.value)}catch(P){C={error:P}}finally{try{p&&!p.done&&(r=u.return)&&r.call(u)}finally{if(C)throw C.error}}return A},M=this&&this.__importDefault||function(a){return a&&a.__esModule?a:{default:a}};Object.defineProperty(m,"__esModule",{value:!0}),m.NEW_OPS=m.AmsMethods=void 0;var x=M(v(39194)),t=M(v(70004)),e=M(v(60909)),n=v(60742),c=M(v(55571)),l=M(v(6257)),d=v(95889),o=M(v(2158)),i=v(91585);function f(a){if(!a||a.isInferred&&0===a.childNodes.length)return[null,null];if(a.isKind("msubsup")&&h(a))return[a,null];var g=e.default.getChildAt(a,0);return a.isInferred&&g&&h(g)?(a.childNodes.splice(0,1),[g,a]):[null,a]}function h(a){var g=a.childNodes[0];return g&&g.isKind("mi")&&""===g.getText()}m.AmsMethods={},m.AmsMethods.AmsEqnArray=function(a,g,r,u,p,A,C){var P=a.GetBrackets("\\begin{"+g.getName()+"}"),s=o.default.EqnArray(a,g,r,u,p,A,C);return x.default.setArrayAlign(s,P)},m.AmsMethods.AlignAt=function(a,g,r,u){var A,C,p=g.getName(),P="",s=[];if(u||(C=a.GetBrackets("\\begin{"+p+"}")),(A=a.GetArgument("\\begin{"+p+"}")).match(/[^0-9]/))throw new l.default("PositiveIntegerArg","Argument to %1 must me a positive integer","\\begin{"+p+"}");for(var _=parseInt(A,10);_>0;)P+="rl",s.push("0em 0em"),_--;var S=s.join(" ");if(u)return m.AmsMethods.EqnArray(a,g,r,u,P,S);var b=m.AmsMethods.EqnArray(a,g,r,u,P,S);return x.default.setArrayAlign(b,C)},m.AmsMethods.Multline=function(a,g,r){a.Push(g),x.default.checkEqnEnv(a);var u=a.itemFactory.create("multline",r,a.stack);return u.arraydef={displaystyle:!0,rowspacing:".5em",columnspacing:"100%",width:a.options.ams.multlineWidth,side:a.options.tagSide,minlabelspacing:a.options.tagIndent,framespacing:a.options.ams.multlineIndent+" 0",frame:"","data-width-includes-label":!0},u},m.AmsMethods.XalignAt=function(a,g,r,u){var p=a.GetArgument("\\begin{"+g.getName()+"}");if(p.match(/[^0-9]/))throw new l.default("PositiveIntegerArg","Argument to %1 must me a positive integer","\\begin{"+g.getName()+"}");var A=u?"crl":"rlc",C=u?"fit auto auto":"auto auto fit",P=m.AmsMethods.FlalignArray(a,g,r,u,!1,A,C,!0);return P.setProperty("xalignat",2*parseInt(p)),P},m.AmsMethods.FlalignArray=function(a,g,r,u,p,A,C,P){void 0===P&&(P=!1),a.Push(g),x.default.checkEqnEnv(a),A=A.split("").join(" ").replace(/r/g,"right").replace(/l/g,"left").replace(/c/g,"center");var s=a.itemFactory.create("flalign",g.getName(),r,u,p,a.stack);return s.arraydef={width:"100%",displaystyle:!0,columnalign:A,columnspacing:"0em",columnwidth:C,rowspacing:"3pt",side:a.options.tagSide,minlabelspacing:P?"0":a.options.tagIndent,"data-width-includes-label":!0},s.setProperty("zeroWidthLabel",P),s},m.NEW_OPS="ams-declare-ops",m.AmsMethods.HandleDeclareOp=function(a,g){var r=a.GetStar()?"*":"",u=x.default.trimSpaces(a.GetArgument(g));"\\"===u.charAt(0)&&(u=u.substr(1));var p=a.GetArgument(g);a.configuration.handlers.retrieve(m.NEW_OPS).add(u,new d.Macro(u,m.AmsMethods.Macro,["\\operatorname".concat(r,"{").concat(p,"}")]))},m.AmsMethods.HandleOperatorName=function(a,g){var r=a.GetStar(),u=x.default.trimSpaces(a.GetArgument(g)),p=new c.default(u,w(w({},a.stack.env),{font:n.TexConstant.Variant.NORMAL,multiLetterIdentifiers:/^[-*a-z]+/i,operatorLetters:!0}),a.configuration).mml();if(p.isKind("mi")||(p=a.create("node","TeXAtom",[p])),e.default.setProperties(p,{movesupsub:r,movablelimits:!0,texClass:i.TEXCLASS.OP}),!r){var A=a.GetNext(),C=a.i;"\\"===A&&++a.i&&"limits"!==a.GetCS()&&(a.i=C)}a.Push(p)},m.AmsMethods.SideSet=function(a,g){var r=y(f(a.ParseArg(g)),2),u=r[0],p=r[1],A=y(f(a.ParseArg(g)),2),C=A[0],P=A[1],s=a.ParseArg(g),_=s;u&&(p?u.replaceChild(a.create("node","mphantom",[a.create("node","mpadded",[x.default.copyNode(s,a)],{width:0})]),e.default.getChildAt(u,0)):(_=a.create("node","mmultiscripts",[s]),C&&e.default.appendChildren(_,[e.default.getChildAt(C,1)||a.create("node","none"),e.default.getChildAt(C,2)||a.create("node","none")]),e.default.setProperty(_,"scriptalign","left"),e.default.appendChildren(_,[a.create("node","mprescripts"),e.default.getChildAt(u,1)||a.create("node","none"),e.default.getChildAt(u,2)||a.create("node","none")]))),C&&_===s&&(C.replaceChild(s,e.default.getChildAt(C,0)),_=C);var S=a.create("node","TeXAtom",[],{texClass:i.TEXCLASS.OP,movesupsub:!0,movablelimits:!0});p&&(u&&S.appendChild(u),S.appendChild(p)),S.appendChild(_),P&&S.appendChild(P),a.Push(S)},m.AmsMethods.operatorLetter=function(a,g){return!!a.stack.env.operatorLetters&&t.default.variable(a,g)},m.AmsMethods.MultiIntegral=function(a,g,r){var u=a.GetNext();if("\\"===u){var p=a.i;u=a.GetArgument(g),a.i=p,"\\limits"===u&&(r="\\idotsint"===g?"\\!\\!\\mathop{\\,\\,"+r+"}":"\\!\\!\\!\\mathop{\\,\\,\\,"+r+"}")}a.string=r+" "+a.string.slice(a.i),a.i=0},m.AmsMethods.xArrow=function(a,g,r,u,p){var A={width:"+"+x.default.Em((u+p)/18),lspace:x.default.Em(u/18)},C=a.GetBrackets(g),P=a.ParseArg(g),s=a.create("node","mspace",[],{depth:".25em"}),_=a.create("token","mo",{stretchy:!0,texClass:i.TEXCLASS.REL},String.fromCodePoint(r));_=a.create("node","mstyle",[_],{scriptlevel:0});var S=a.create("node","munderover",[_]),b=a.create("node","mpadded",[P,s],A);if(e.default.setAttribute(b,"voffset","-.2em"),e.default.setAttribute(b,"height","-.2em"),e.default.setChild(S,S.over,b),C){var O=new c.default(C,a.stack.env,a.configuration).mml(),T=a.create("node","mspace",[],{height:".75em"});b=a.create("node","mpadded",[O,T],A),e.default.setAttribute(b,"voffset",".15em"),e.default.setAttribute(b,"depth","-.15em"),e.default.setChild(S,S.under,b)}e.default.setProperty(S,"subsupOK",!0),a.Push(S)},m.AmsMethods.HandleShove=function(a,g,r){var u=a.stack.Top();if("multline"!==u.kind)throw new l.default("CommandOnlyAllowedInEnv","%1 only allowed in %2 environment",a.currentCS,"multline");if(u.Size())throw new l.default("CommandAtTheBeginingOfLine","%1 must come at the beginning of the line",a.currentCS);u.setProperty("shove",r)},m.AmsMethods.CFrac=function(a,g){var r=x.default.trimSpaces(a.GetBrackets(g,"")),u=a.GetArgument(g),p=a.GetArgument(g),A={l:n.TexConstant.Align.LEFT,r:n.TexConstant.Align.RIGHT,"":""},C=new c.default("\\strut\\textstyle{"+u+"}",a.stack.env,a.configuration).mml(),P=new c.default("\\strut\\textstyle{"+p+"}",a.stack.env,a.configuration).mml(),s=a.create("node","mfrac",[C,P]);if(null==(r=A[r]))throw new l.default("IllegalAlign","Illegal alignment specified in %1",a.currentCS);r&&e.default.setProperties(s,{numalign:r,denomalign:r}),a.Push(s)},m.AmsMethods.Genfrac=function(a,g,r,u,p,A){null==r&&(r=a.GetDelimiterArg(g)),null==u&&(u=a.GetDelimiterArg(g)),null==p&&(p=a.GetArgument(g)),null==A&&(A=x.default.trimSpaces(a.GetArgument(g)));var C=a.ParseArg(g),P=a.ParseArg(g),s=a.create("node","mfrac",[C,P]);if(""!==p&&e.default.setAttribute(s,"linethickness",p),(r||u)&&(e.default.setProperty(s,"withDelims",!0),s=x.default.fixedFence(a.configuration,r,s,u)),""!==A){var _=parseInt(A,10),S=["D","T","S","SS"][_];if(null==S)throw new l.default("BadMathStyleFor","Bad math style for %1",a.currentCS);s=a.create("node","mstyle",[s]),"D"===S?e.default.setProperties(s,{displaystyle:!0,scriptlevel:0}):e.default.setProperties(s,{displaystyle:!1,scriptlevel:_-1})}a.Push(s)},m.AmsMethods.HandleTag=function(a,g){if(!a.tags.currentTag.taggable&&a.tags.env)throw new l.default("CommandNotAllowedInEnv","%1 not allowed in %2 environment",a.currentCS,a.tags.env);if(a.tags.currentTag.tag)throw new l.default("MultipleCommand","Multiple %1",a.currentCS);var r=a.GetStar(),u=x.default.trimSpaces(a.GetArgument(g));a.tags.tag(u,r)},m.AmsMethods.HandleNoTag=o.default.HandleNoTag,m.AmsMethods.HandleRef=o.default.HandleRef,m.AmsMethods.Macro=o.default.Macro,m.AmsMethods.Accent=o.default.Accent,m.AmsMethods.Tilde=o.default.Tilde,m.AmsMethods.Array=o.default.Array,m.AmsMethods.Spacer=o.default.Spacer,m.AmsMethods.NamedOp=o.default.NamedOp,m.AmsMethods.EqnArray=o.default.EqnArray,m.AmsMethods.Equation=o.default.Equation},16529:(B,m,v)=>{Object.defineProperty(m,"__esModule",{value:!0}),m.AmsCdConfiguration=void 0;var w=v(8890);v(71346),m.AmsCdConfiguration=w.Configuration.create("amscd",{handler:{character:["amscd_special"],macro:["amscd_macros"],environment:["amscd_environment"]},options:{amscd:{colspace:"5pt",rowspace:"5pt",harrowsize:"2.75em",varrowsize:"1.75em",hideHorizontalLabels:!1}}})},71346:function(B,m,v){var w=this&&this.__createBinding||(Object.create?function(c,l,d,o){void 0===o&&(o=d);var i=Object.getOwnPropertyDescriptor(l,d);(!i||("get"in i?!l.__esModule:i.writable||i.configurable))&&(i={enumerable:!0,get:function(){return l[d]}}),Object.defineProperty(c,o,i)}:function(c,l,d,o){void 0===o&&(o=d),c[o]=l[d]}),y=this&&this.__setModuleDefault||(Object.create?function(c,l){Object.defineProperty(c,"default",{enumerable:!0,value:l})}:function(c,l){c.default=l}),M=this&&this.__importStar||function(c){if(c&&c.__esModule)return c;var l={};if(null!=c)for(var d in c)"default"!==d&&Object.prototype.hasOwnProperty.call(c,d)&&w(l,c,d);return y(l,c),l},x=this&&this.__importDefault||function(c){return c&&c.__esModule?c:{default:c}};Object.defineProperty(m,"__esModule",{value:!0});var t=M(v(82652)),e=x(v(70004)),n=x(v(31542));new t.EnvironmentMap("amscd_environment",e.default.environment,{CD:"CD"},n.default),new t.CommandMap("amscd_macros",{minCDarrowwidth:"minCDarrowwidth",minCDarrowheight:"minCDarrowheight"},n.default),new t.MacroMap("amscd_special",{"@":"arrow"},n.default)},31542:function(B,m,v){var w=this&&this.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(m,"__esModule",{value:!0});var y=w(v(55571)),M=v(80677),x=v(91585),t=w(v(60909)),e={CD:function(n,c){n.Push(c);var l=n.itemFactory.create("array"),d=n.configuration.options.amscd;return l.setProperties({minw:n.stack.env.CD_minw||d.harrowsize,minh:n.stack.env.CD_minh||d.varrowsize}),l.arraydef={columnalign:"center",columnspacing:d.colspace,rowspacing:d.rowspace,displaystyle:!0},l},arrow:function(n,c){var l=n.string.charAt(n.i);if(!l.match(/[><VA.|=]/))return(0,M.Other)(n,c);n.i++;var d=n.stack.Top();(!d.isKind("array")||d.Size())&&(e.cell(n,c),d=n.stack.Top());for(var o=d,i=o.table.length%2==1,f=(o.row.length+(i?0:1))%2;f;)e.cell(n,c),f--;var h,a={minsize:o.getProperty("minw"),stretchy:!0},g={minsize:o.getProperty("minh"),stretchy:!0,symmetric:!0,lspace:0,rspace:0};if("."!==l)if("|"===l)h=n.create("token","mo",g,"\u2225");else if("="===l)h=n.create("token","mo",a,"=");else{var r={">":"\u2192","<":"\u2190",V:"\u2193",A:"\u2191"}[l],u=n.GetUpTo(c+l,l),p=n.GetUpTo(c+l,l);if(">"===l||"<"===l){if(h=n.create("token","mo",a,r),u||(u="\\kern "+o.getProperty("minw")),u||p){var A={width:"+.67em",lspace:".33em"};if(h=n.create("node","munderover",[h]),u){var C=new y.default(u,n.stack.env,n.configuration).mml(),P=n.create("node","mpadded",[C],A);t.default.setAttribute(P,"voffset",".1em"),t.default.setChild(h,h.over,P)}if(p){var s=new y.default(p,n.stack.env,n.configuration).mml();t.default.setChild(h,h.under,n.create("node","mpadded",[s],A))}n.configuration.options.amscd.hideHorizontalLabels&&(h=n.create("node","mpadded",h,{depth:0,height:".67em"}))}}else{var _=n.create("token","mo",g,r);h=_,(u||p)&&(h=n.create("node","mrow"),u&&t.default.appendChildren(h,[new y.default("\\scriptstyle\\llap{"+u+"}",n.stack.env,n.configuration).mml()]),_.texClass=x.TEXCLASS.ORD,t.default.appendChildren(h,[_]),p&&t.default.appendChildren(h,[new y.default("\\scriptstyle\\rlap{"+p+"}",n.stack.env,n.configuration).mml()]))}}h&&n.Push(h),e.cell(n,c)},cell:function(n,c){var l=n.stack.Top();(l.table||[]).length%2==0&&0===(l.row||[]).length&&n.Push(n.create("node","mpadded",[],{height:"8.5pt",depth:"2pt"})),n.Push(n.itemFactory.create("cell").setProperties({isEntry:!0,name:c}))},minCDarrowwidth:function(n,c){n.stack.env.CD_minw=n.GetDimen(c)},minCDarrowheight:function(n,c){n.stack.env.CD_minh=n.GetDimen(c)}};m.default=e},23525:function(B,m,v){var w=this&&this.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(m,"__esModule",{value:!0}),m.BboxConfiguration=m.BboxMethods=void 0;var y=v(8890),M=v(82652),x=w(v(6257));m.BboxMethods={},m.BboxMethods.BBox=function(n,c){for(var i,f,h,l=n.GetBrackets(c,""),d=n.ParseArg(c),o=l.split(/,/),a=0,g=o.length;a<g;a++){var r=o[a].trim(),u=r.match(/^(\.\d+|\d+(\.\d*)?)(pt|em|ex|mu|px|in|cm|mm)$/);if(u){if(i)throw new x.default("MultipleBBoxProperty","%1 specified twice in %2","Padding",c);var p=e(u[1]+u[3]);p&&(i={height:"+"+p,depth:"+"+p,lspace:p,width:"+"+2*parseInt(u[1],10)+u[3]})}else if(r.match(/^([a-z0-9]+|\#[0-9a-f]{6}|\#[0-9a-f]{3})$/i)){if(f)throw new x.default("MultipleBBoxProperty","%1 specified twice in %2","Background",c);f=r}else if(r.match(/^[-a-z]+:/i)){if(h)throw new x.default("MultipleBBoxProperty","%1 specified twice in %2","Style",c);h=t(r)}else if(""!==r)throw new x.default("InvalidBBoxProperty",'"%1" doesn\'t look like a color, a padding dimension, or a style',r)}i&&(d=n.create("node","mpadded",[d],i)),(f||h)&&(i={},f&&Object.assign(i,{mathbackground:f}),h&&Object.assign(i,{style:h}),d=n.create("node","mstyle",[d],i)),n.Push(d)};var t=function(n){return n},e=function(n){return n};new M.CommandMap("bbox",{bbox:"BBox"},m.BboxMethods),m.BboxConfiguration=y.Configuration.create("bbox",{handler:{macro:["bbox"]}})},46174:function(B,m,v){var w=this&&this.__values||function(o){var i="function"==typeof Symbol&&Symbol.iterator,f=i&&o[i],h=0;if(f)return f.call(o);if(o&&"number"==typeof o.length)return{next:function(){return o&&h>=o.length&&(o=void 0),{value:o&&o[h++],done:!o}}};throw new TypeError(i?"Object is not iterable.":"Symbol.iterator is not defined.")},y=this&&this.__importDefault||function(o){return o&&o.__esModule?o:{default:o}};Object.defineProperty(m,"__esModule",{value:!0}),m.BoldsymbolConfiguration=m.rewriteBoldTokens=m.createBoldToken=m.BoldsymbolMethods=void 0;var M=v(8890),x=y(v(60909)),t=v(60742),e=v(82652),n=v(42023),c={};function l(o,i,f,h){var a=n.NodeFactory.createToken(o,i,f,h);return"mtext"!==i&&o.configuration.parser.stack.env.boldsymbol&&(x.default.setProperty(a,"fixBold",!0),o.configuration.addNode("fixBold",a)),a}function d(o){var i,f;try{for(var h=w(o.data.getList("fixBold")),a=h.next();!a.done;a=h.next()){var g=a.value;if(x.default.getProperty(g,"fixBold")){var r=x.default.getAttribute(g,"mathvariant");null==r?x.default.setAttribute(g,"mathvariant",t.TexConstant.Variant.BOLD):x.default.setAttribute(g,"mathvariant",c[r]||r),x.default.removeProperties(g,"fixBold")}}}catch(u){i={error:u}}finally{try{a&&!a.done&&(f=h.return)&&f.call(h)}finally{if(i)throw i.error}}}c[t.TexConstant.Variant.NORMAL]=t.TexConstant.Variant.BOLD,c[t.TexConstant.Variant.ITALIC]=t.TexConstant.Variant.BOLDITALIC,c[t.TexConstant.Variant.FRAKTUR]=t.TexConstant.Variant.BOLDFRAKTUR,c[t.TexConstant.Variant.SCRIPT]=t.TexConstant.Variant.BOLDSCRIPT,c[t.TexConstant.Variant.SANSSERIF]=t.TexConstant.Variant.BOLDSANSSERIF,c["-tex-calligraphic"]="-tex-bold-calligraphic",c["-tex-oldstyle"]="-tex-bold-oldstyle",c["-tex-mathit"]=t.TexConstant.Variant.BOLDITALIC,m.BoldsymbolMethods={},m.BoldsymbolMethods.Boldsymbol=function(o,i){var f=o.stack.env.boldsymbol;o.stack.env.boldsymbol=!0;var h=o.ParseArg(i);o.stack.env.boldsymbol=f,o.Push(h)},new e.CommandMap("boldsymbol",{boldsymbol:"Boldsymbol"},m.BoldsymbolMethods),m.createBoldToken=l,m.rewriteBoldTokens=d,m.BoldsymbolConfiguration=M.Configuration.create("boldsymbol",{handler:{macro:["boldsymbol"]},nodes:{token:l},postprocessors:[d]})},26134:(B,m,v)=>{var w;Object.defineProperty(m,"__esModule",{value:!0}),m.BraketConfiguration=void 0;var y=v(8890),M=v(3285);v(70702),m.BraketConfiguration=y.Configuration.create("braket",{handler:{character:["Braket-characters"],macro:["Braket-macros"]},items:(w={},w[M.BraketItem.prototype.kind]=M.BraketItem,w)})},3285:function(B,m,v){var n,w=this&&this.__extends||(n=function(c,l){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,o){d.__proto__=o}||function(d,o){for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(d[i]=o[i])})(c,l)},function(c,l){if("function"!=typeof l&&null!==l)throw new TypeError("Class extends value "+String(l)+" is not a constructor or null");function d(){this.constructor=c}n(c,l),c.prototype=null===l?Object.create(l):(d.prototype=l.prototype,new d)}),y=this&&this.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(m,"__esModule",{value:!0}),m.BraketItem=void 0;var M=v(90117),x=v(91585),t=y(v(39194)),e=function(n){function c(){return null!==n&&n.apply(this,arguments)||this}return w(c,n),Object.defineProperty(c.prototype,"kind",{get:function(){return"braket"},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),c.prototype.checkItem=function(l){return l.isKind("close")?[[this.factory.create("mml",this.toMml())],!0]:l.isKind("mml")?(this.Push(l.toMml()),this.getProperty("single")?[[this.toMml()],!0]:M.BaseItem.fail):n.prototype.checkItem.call(this,l)},c.prototype.toMml=function(){var l=n.prototype.toMml.call(this),d=this.getProperty("open"),o=this.getProperty("close");if(this.getProperty("stretchy"))return t.default.fenced(this.factory.configuration,d,l,o);var i={fence:!0,stretchy:!1,symmetric:!0,texClass:x.TEXCLASS.OPEN},f=this.create("token","mo",i,d);i.texClass=x.TEXCLASS.CLOSE;var h=this.create("token","mo",i,o);return this.create("node","mrow",[f,l,h],{open:d,close:o,texClass:x.TEXCLASS.INNER})},c}(M.BaseItem);m.BraketItem=e},70702:function(B,m,v){var w=this&&this.__importDefault||function(x){return x&&x.__esModule?x:{default:x}};Object.defineProperty(m,"__esModule",{value:!0});var y=v(82652),M=w(v(69407));new y.CommandMap("Braket-macros",{bra:["Macro","{\\langle {#1} \\vert}",1],ket:["Macro","{\\vert {#1} \\rangle}",1],braket:["Braket","\u27e8","\u27e9",!1,1/0],set:["Braket","{","}",!1,1],Bra:["Macro","{\\left\\langle {#1} \\right\\vert}",1],Ket:["Macro","{\\left\\vert {#1} \\right\\rangle}",1],Braket:["Braket","\u27e8","\u27e9",!0,1/0],Set:["Braket","{","}",!0,1],ketbra:["Macro","{\\vert {#1} \\rangle\\langle {#2} \\vert}",2],Ketbra:["Macro","{\\left\\vert {#1} \\right\\rangle\\left\\langle {#2} \\right\\vert}",2],"|":"Bar"},M.default),new y.MacroMap("Braket-characters",{"|":"Bar"},M.default)},69407:function(B,m,v){var w=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(m,"__esModule",{value:!0});var y=w(v(2158)),M=v(91585),x=w(v(6257)),t={};t.Macro=y.default.Macro,t.Braket=function(e,n,c,l,d,o){var i=e.GetNext();if(""===i)throw new x.default("MissingArgFor","Missing argument for %1",e.currentCS);var f=!0;"{"===i&&(e.i++,f=!1),e.Push(e.itemFactory.create("braket").setProperties({barmax:o,barcount:0,open:c,close:l,stretchy:d,single:f}))},t.Bar=function(e,n){var c="|"===n?"|":"\u2225",l=e.stack.Top();if("braket"!==l.kind||l.getProperty("barcount")>=l.getProperty("barmax")){var d=e.create("token","mo",{texClass:M.TEXCLASS.ORD,stretchy:!1},c);e.Push(d)}else{if("|"===c&&"|"===e.GetNext()&&(e.i++,c="\u2225"),l.getProperty("stretchy")){var f=e.create("node","TeXAtom",[],{texClass:M.TEXCLASS.CLOSE});e.Push(f),l.setProperty("barcount",l.getProperty("barcount")+1),f=e.create("token","mo",{stretchy:!0,braketbar:!0},c),e.Push(f),f=e.create("node","TeXAtom",[],{texClass:M.TEXCLASS.OPEN}),e.Push(f)}else{var i=e.create("token","mo",{stretchy:!1,braketbar:!0},c);e.Push(i)}}},m.default=t},58218:(B,m,v)=>{var w;Object.defineProperty(m,"__esModule",{value:!0}),m.BussproofsConfiguration=void 0;var y=v(8890),M=v(46608),x=v(71182);v(40541),m.BussproofsConfiguration=y.Configuration.create("bussproofs",{handler:{macro:["Bussproofs-macros"],environment:["Bussproofs-environments"]},items:(w={},w[M.ProofTreeItem.prototype.kind]=M.ProofTreeItem,w),preprocessors:[[x.saveDocument,1]],postprocessors:[[x.clearDocument,3],[x.makeBsprAttributes,2],[x.balanceRules,1]]})},46608:function(B,m,v){var o,w=this&&this.__extends||(o=function(i,f){return(o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(h,a){h.__proto__=a}||function(h,a){for(var g in a)Object.prototype.hasOwnProperty.call(a,g)&&(h[g]=a[g])})(i,f)},function(i,f){if("function"!=typeof f&&null!==f)throw new TypeError("Class extends value "+String(f)+" is not a constructor or null");function h(){this.constructor=i}o(i,f),i.prototype=null===f?Object.create(f):(h.prototype=f.prototype,new h)}),y=this&&this.__createBinding||(Object.create?function(o,i,f,h){void 0===h&&(h=f);var a=Object.getOwnPropertyDescriptor(i,f);(!a||("get"in a?!i.__esModule:a.writable||a.configurable))&&(a={enumerable:!0,get:function(){return i[f]}}),Object.defineProperty(o,h,a)}:function(o,i,f,h){void 0===h&&(h=f),o[h]=i[f]}),M=this&&this.__setModuleDefault||(Object.create?function(o,i){Object.defineProperty(o,"default",{enumerable:!0,value:i})}:function(o,i){o.default=i}),x=this&&this.__importStar||function(o){if(o&&o.__esModule)return o;var i={};if(null!=o)for(var f in o)"default"!==f&&Object.prototype.hasOwnProperty.call(o,f)&&y(i,o,f);return M(i,o),i},t=this&&this.__importDefault||function(o){return o&&o.__esModule?o:{default:o}};Object.defineProperty(m,"__esModule",{value:!0}),m.ProofTreeItem=void 0;var e=t(v(6257)),n=v(90117),c=t(v(72944)),l=x(v(71182)),d=function(o){function i(){var f=null!==o&&o.apply(this,arguments)||this;return f.leftLabel=null,f.rigthLabel=null,f.innerStack=new c.default(f.factory,{},!0),f}return w(i,o),Object.defineProperty(i.prototype,"kind",{get:function(){return"proofTree"},enumerable:!1,configurable:!0}),i.prototype.checkItem=function(f){if(f.isKind("end")&&"prooftree"===f.getName()){var h=this.toMml();return l.setProperty(h,"proof",!0),[[this.factory.create("mml",h),f],!0]}if(f.isKind("stop"))throw new e.default("EnvMissingEnd","Missing \\end{%1}",this.getName());return this.innerStack.Push(f),n.BaseItem.fail},i.prototype.toMml=function(){var f=o.prototype.toMml.call(this),h=this.innerStack.Top();if(h.isKind("start")&&!h.Size())return f;this.innerStack.Push(this.factory.create("stop"));var a=this.innerStack.Top().toMml();return this.create("node","mrow",[a,f],{})},i}(n.BaseItem);m.ProofTreeItem=d},40541:function(B,m,v){var w=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(m,"__esModule",{value:!0});var y=w(v(10757)),M=w(v(70004)),x=v(82652);new x.CommandMap("Bussproofs-macros",{AxiomC:"Axiom",UnaryInfC:["Inference",1],BinaryInfC:["Inference",2],TrinaryInfC:["Inference",3],QuaternaryInfC:["Inference",4],QuinaryInfC:["Inference",5],RightLabel:["Label","right"],LeftLabel:["Label","left"],AXC:"Axiom",UIC:["Inference",1],BIC:["Inference",2],TIC:["Inference",3],RL:["Label","right"],LL:["Label","left"],noLine:["SetLine","none",!1],singleLine:["SetLine","solid",!1],solidLine:["SetLine","solid",!1],dashedLine:["SetLine","dashed",!1],alwaysNoLine:["SetLine","none",!0],alwaysSingleLine:["SetLine","solid",!0],alwaysSolidLine:["SetLine","solid",!0],alwaysDashedLine:["SetLine","dashed",!0],rootAtTop:["RootAtTop",!0],alwaysRootAtTop:["RootAtTop",!0],rootAtBottom:["RootAtTop",!1],alwaysRootAtBottom:["RootAtTop",!1],fCenter:"FCenter",Axiom:"AxiomF",UnaryInf:["InferenceF",1],BinaryInf:["InferenceF",2],TrinaryInf:["InferenceF",3],QuaternaryInf:["InferenceF",4],QuinaryInf:["InferenceF",5]},y.default),new x.EnvironmentMap("Bussproofs-environments",M.default.environment,{prooftree:["Prooftree",null,!1]},y.default)},10757:function(B,m,v){var w=this&&this.__createBinding||(Object.create?function(a,g,r,u){void 0===u&&(u=r);var p=Object.getOwnPropertyDescriptor(g,r);(!p||("get"in p?!g.__esModule:p.writable||p.configurable))&&(p={enumerable:!0,get:function(){return g[r]}}),Object.defineProperty(a,u,p)}:function(a,g,r,u){void 0===u&&(u=r),a[u]=g[r]}),y=this&&this.__setModuleDefault||(Object.create?function(a,g){Object.defineProperty(a,"default",{enumerable:!0,value:g})}:function(a,g){a.default=g}),M=this&&this.__importStar||function(a){if(a&&a.__esModule)return a;var g={};if(null!=a)for(var r in a)"default"!==r&&Object.prototype.hasOwnProperty.call(a,r)&&w(g,a,r);return y(g,a),g},x=this&&this.__read||function(a,g){var r="function"==typeof Symbol&&a[Symbol.iterator];if(!r)return a;var p,C,u=r.call(a),A=[];try{for(;(void 0===g||g-- >0)&&!(p=u.next()).done;)A.push(p.value)}catch(P){C={error:P}}finally{try{p&&!p.done&&(r=u.return)&&r.call(u)}finally{if(C)throw C.error}}return A},t=this&&this.__spreadArray||function(a,g,r){if(r||2===arguments.length)for(var A,u=0,p=g.length;u<p;u++)(A||!(u in g))&&(A||(A=Array.prototype.slice.call(g,0,u)),A[u]=g[u]);return a.concat(A||Array.prototype.slice.call(g))},e=this&&this.__importDefault||function(a){return a&&a.__esModule?a:{default:a}};Object.defineProperty(m,"__esModule",{value:!0});var n=e(v(6257)),c=e(v(55571)),l=e(v(39194)),d=M(v(71182)),o={Prooftree:function(a,g){return a.Push(g),a.itemFactory.create("proofTree").setProperties({name:g.getName(),line:"solid",currentLine:"solid",rootAtTop:!1})},Axiom:function(a,g){var r=a.stack.Top();if("proofTree"!==r.kind)throw new n.default("IllegalProofCommand","Proof commands only allowed in prooftree environment.");var u=i(a,a.GetArgument(g));d.setProperty(u,"axiom",!0),r.Push(u)}},i=function(a,g){var r=l.default.internalMath(a,l.default.trimSpaces(g),0);if(!r[0].childNodes[0].childNodes.length)return a.create("node","mrow",[]);var u=a.create("node","mspace",[],{width:".5ex"}),p=a.create("node","mspace",[],{width:".5ex"});return a.create("node","mrow",t(t([u],x(r),!1),[p],!1))};function f(a,g,r,u,p,A,C){var S,b,O,T,P=a.create("node","mtr",[a.create("node","mtd",[g],{})],{}),s=a.create("node","mtr",[a.create("node","mtd",r,{})],{}),_=a.create("node","mtable",C?[s,P]:[P,s],{align:"top 2",rowlines:A,framespacing:"0 0"});if(d.setProperty(_,"inferenceRule",C?"up":"down"),u&&(S=a.create("node","mpadded",[u],{height:"+.5em",width:"+.5em",voffset:"-.15em"}),d.setProperty(S,"prooflabel","left")),p&&(b=a.create("node","mpadded",[p],{height:"+.5em",width:"+.5em",voffset:"-.15em"}),d.setProperty(b,"prooflabel","right")),u&&p)O=[S,_,b],T="both";else if(u)O=[S,_],T="left";else{if(!p)return _;O=[_,b],T="right"}return _=a.create("node","mrow",O),d.setProperty(_,"labelledRule",T),_}function h(a,g){if("$"!==a.GetNext())throw new n.default("IllegalUseOfCommand","Use of %1 does not match it's definition.",g);a.i++;var u=a.GetUpTo(g,"$");if(-1===u.indexOf("\\fCenter"))throw new n.default("IllegalUseOfCommand","Missing \\fCenter in %1.",g);var p=x(u.split("\\fCenter"),2),A=p[0],C=p[1],P=new c.default(A,a.stack.env,a.configuration).mml(),s=new c.default(C,a.stack.env,a.configuration).mml(),_=new c.default("\\fCenter",a.stack.env,a.configuration).mml(),S=a.create("node","mtd",[P],{}),b=a.create("node","mtd",[_],{}),O=a.create("node","mtd",[s],{}),T=a.create("node","mtr",[S,b,O],{}),D=a.create("node","mtable",[T],{columnspacing:".5ex",columnalign:"center 2"});return d.setProperty(D,"sequent",!0),a.configuration.addNode("sequent",T),D}o.Inference=function(a,g,r){var u=a.stack.Top();if("proofTree"!==u.kind)throw new n.default("IllegalProofCommand","Proof commands only allowed in prooftree environment.");if(u.Size()<r)throw new n.default("BadProofTree","Proof tree badly specified.");var p=u.getProperty("rootAtTop"),A=1!==r||u.Peek()[0].childNodes.length?r:0,C=[];do{C.length&&C.unshift(a.create("node","mtd",[],{})),C.unshift(a.create("node","mtd",[u.Pop()],{rowalign:p?"top":"bottom"})),r--}while(r>0);var P=a.create("node","mtr",C,{}),s=a.create("node","mtable",[P],{framespacing:"0 0"}),_=i(a,a.GetArgument(g)),S=u.getProperty("currentLine");S!==u.getProperty("line")&&u.setProperty("currentLine",u.getProperty("line"));var b=f(a,s,[_],u.getProperty("left"),u.getProperty("right"),S,p);u.setProperty("left",null),u.setProperty("right",null),d.setProperty(b,"inference",A),a.configuration.addNode("inference",b),u.Push(b)},o.Label=function(a,g,r){var u=a.stack.Top();if("proofTree"!==u.kind)throw new n.default("IllegalProofCommand","Proof commands only allowed in prooftree environment.");var p=l.default.internalMath(a,a.GetArgument(g),0),A=p.length>1?a.create("node","mrow",p,{}):p[0];u.setProperty(r,A)},o.SetLine=function(a,g,r,u){var p=a.stack.Top();if("proofTree"!==p.kind)throw new n.default("IllegalProofCommand","Proof commands only allowed in prooftree environment.");p.setProperty("currentLine",r),u&&p.setProperty("line",r)},o.RootAtTop=function(a,g,r){var u=a.stack.Top();if("proofTree"!==u.kind)throw new n.default("IllegalProofCommand","Proof commands only allowed in prooftree environment.");u.setProperty("rootAtTop",r)},o.AxiomF=function(a,g){var r=a.stack.Top();if("proofTree"!==r.kind)throw new n.default("IllegalProofCommand","Proof commands only allowed in prooftree environment.");var u=h(a,g);d.setProperty(u,"axiom",!0),r.Push(u)},o.FCenter=function(a,g){},o.InferenceF=function(a,g,r){var u=a.stack.Top();if("proofTree"!==u.kind)throw new n.default("IllegalProofCommand","Proof commands only allowed in prooftree environment.");if(u.Size()<r)throw new n.default("BadProofTree","Proof tree badly specified.");var p=u.getProperty("rootAtTop"),A=1!==r||u.Peek()[0].childNodes.length?r:0,C=[];do{C.length&&C.unshift(a.create("node","mtd",[],{})),C.unshift(a.create("node","mtd",[u.Pop()],{rowalign:p?"top":"bottom"})),r--}while(r>0);var P=a.create("node","mtr",C,{}),s=a.create("node","mtable",[P],{framespacing:"0 0"}),_=h(a,g),S=u.getProperty("currentLine");S!==u.getProperty("line")&&u.setProperty("currentLine",u.getProperty("line"));var b=f(a,s,[_],u.getProperty("left"),u.getProperty("right"),S,p);u.setProperty("left",null),u.setProperty("right",null),d.setProperty(b,"inference",A),a.configuration.addNode("inference",b),u.Push(b)},m.default=o},71182:function(B,m,v){var x,w=this&&this.__read||function(E,j){var G="function"==typeof Symbol&&E[Symbol.iterator];if(!G)return E;var F,q,N=G.call(E),R=[];try{for(;(void 0===j||j-- >0)&&!(F=N.next()).done;)R.push(F.value)}catch(V){q={error:V}}finally{try{F&&!F.done&&(G=N.return)&&G.call(N)}finally{if(q)throw q.error}}return R},y=this&&this.__values||function(E){var j="function"==typeof Symbol&&Symbol.iterator,G=j&&E[j],N=0;if(G)return G.call(E);if(E&&"number"==typeof E.length)return{next:function(){return E&&N>=E.length&&(E=void 0),{value:E&&E[N++],done:!E}}};throw new TypeError(j?"Object is not iterable.":"Symbol.iterator is not defined.")},M=this&&this.__importDefault||function(E){return E&&E.__esModule?E:{default:E}};Object.defineProperty(m,"__esModule",{value:!0}),m.clearDocument=m.saveDocument=m.makeBsprAttributes=m.removeProperty=m.getProperty=m.setProperty=m.balanceRules=void 0;var t=M(v(60909)),e=M(v(39194)),n=null,c=null,l=function(E){return c.root=E,n.outputJax.getBBox(c,n).w},d=function(E){for(var j=0;E&&!t.default.isType(E,"mtable");){if(t.default.isType(E,"text"))return null;t.default.isType(E,"mrow")?(E=E.childNodes[0],j=0):(E=E.parent.childNodes[j],j++)}return E},o=function(E,j){return E.childNodes["up"===j?1:0].childNodes[0].childNodes[0].childNodes[0].childNodes[0]},i=function(E,j){return E.childNodes[j].childNodes[0].childNodes[0]},f=function(E){return i(E,0)},h=function(E){return i(E,E.childNodes.length-1)},a=function(E,j){return E.childNodes["up"===j?0:1].childNodes[0].childNodes[0].childNodes[0]},g=function(E){for(;E&&!t.default.isType(E,"mtd");)E=E.parent;return E},r=function(E){return E.parent.childNodes[E.parent.childNodes.indexOf(E)+1]},p=function(E){for(;E&&null==(0,m.getProperty)(E,"inference");)E=E.parent;return E},A=function(E,j,G){void 0===G&&(G=!1);var N=0;if(E===j)return N;if(E!==j.parent){var F=E.childNodes,R=G?F.length-1:0;t.default.isType(F[R],"mspace")&&(N+=l(F[R])),E=j.parent}if(E===j)return N;var q=E.childNodes,V=G?q.length-1:0;return q[V]!==j&&(N+=l(q[V])),N},C=function(E,j){void 0===j&&(j=!1);var G=d(E),N=a(G,(0,m.getProperty)(G,"inferenceRule"));return A(E,G,j)+(l(G)-l(N))/2},P=function(E,j,G,N){if(void 0===N&&(N=!1),(0,m.getProperty)(j,"inferenceRule")||(0,m.getProperty)(j,"labelledRule")){var F=E.nodeFactory.create("node","mrow");j.parent.replaceChild(F,j),F.setChildren([j]),s(j,F),j=F}var R=N?j.childNodes.length-1:0,q=j.childNodes[R];t.default.isType(q,"mspace")?t.default.setAttribute(q,"width",e.default.Em(e.default.dimen2em(t.default.getAttribute(q,"width"))+G)):(q=E.nodeFactory.create("node","mspace",[],{width:e.default.Em(G)}),N?j.appendChild(q):(q.parent=j,j.childNodes.unshift(q)))},s=function(E,j){["inference","proof","maxAdjust","labelledRule"].forEach(function(N){var F=(0,m.getProperty)(E,N);null!=F&&((0,m.setProperty)(j,N,F),(0,m.removeProperty)(E,N))})},S=function(E,j,G,N,F){var R=E.nodeFactory.create("node","mspace",[],{width:e.default.Em(F)});if("left"===N){var q=j.childNodes[G].childNodes[0];R.parent=q,q.childNodes.unshift(R)}else j.childNodes[G].appendChild(R);(0,m.setProperty)(j.parent,"sequentAdjust_"+N,F)},b=function(E,j){for(var G=j.pop();j.length;){var N=j.pop(),F=w(O(G,N),2),R=F[0],q=F[1];(0,m.getProperty)(G.parent,"axiom")&&(S(E,R<0?G:N,0,"left",Math.abs(R)),S(E,q<0?G:N,2,"right",Math.abs(q))),G=N}},O=function(E,j){var G=l(E.childNodes[2]),N=l(j.childNodes[2]);return[l(E.childNodes[0])-l(j.childNodes[0]),G-N]};m.balanceRules=function(E){var j,G;c=new E.document.options.MathItem("",null,E.math.display);var N=E.data;!function(E){var j=E.nodeLists.sequent;if(j)for(var G=j.length-1,N=void 0;N=j[G];G--)if((0,m.getProperty)(N,"sequentProcessed"))(0,m.removeProperty)(N,"sequentProcessed");else{var F=[],R=p(N);if(1===(0,m.getProperty)(R,"inference")){for(F.push(N);1===(0,m.getProperty)(R,"inference");){R=d(R);var q=f(o(R,(0,m.getProperty)(R,"inferenceRule"))),V=(0,m.getProperty)(q,"inferenceRule")?a(q,(0,m.getProperty)(q,"inferenceRule")):q;(0,m.getProperty)(V,"sequent")&&(N=V.childNodes[0],F.push(N),(0,m.setProperty)(N,"sequentProcessed",!0)),R=q}b(E,F)}}}(N);var F=N.nodeLists.inference||[];try{for(var R=y(F),q=R.next();!q.done;q=R.next()){var V=q.value,rt=(0,m.getProperty)(V,"proof"),Q=d(V),tt=o(Q,(0,m.getProperty)(Q,"inferenceRule")),W=f(tt);if((0,m.getProperty)(W,"inference")){var Z=C(W);if(Z){P(N,W,-Z);var ot=A(V,Q,!1);P(N,V,Z-ot)}}var Y=h(tt);if(null!=(0,m.getProperty)(Y,"inference")){var X=C(Y,!0);P(N,Y,-X,!0);var et=A(V,Q,!0),nt=(0,m.getProperty)(V,"maxAdjust");null!=nt&&(X=Math.max(X,nt));var J=void 0;if(rt||!(J=g(V))){P(N,(0,m.getProperty)(V,"proof")?V:V.parent,X-et,!0);continue}var at=r(J);if(at){var it=N.nodeFactory.create("node","mspace",[],{width:X-et+"em"});at.appendChild(it),V.removeProperty("maxAdjust");continue}var K=p(J);!K||(X=(0,m.getProperty)(K,"maxAdjust")?Math.max((0,m.getProperty)(K,"maxAdjust"),X):X,(0,m.setProperty)(K,"maxAdjust",X))}}}catch(lt){j={error:lt}}finally{try{q&&!q.done&&(G=R.return)&&G.call(R)}finally{if(j)throw j.error}}};var D="bspr_",I=((x={}).bspr_maxAdjust=!0,x);m.setProperty=function(E,j,G){t.default.setProperty(E,D+j,G)};m.getProperty=function(E,j){return t.default.getProperty(E,D+j)};m.removeProperty=function(E,j){E.removeProperty(D+j)};m.makeBsprAttributes=function(E){E.data.root.walkTree(function(j,G){var N=[];j.getPropertyNames().forEach(function(F){!I[F]&&F.match(RegExp("^"+D))&&N.push(F+":"+j.getProperty(F))}),N.length&&t.default.setAttribute(j,"semantics",N.join(";"))})};m.saveDocument=function(E){if(!("getBBox"in(n=E.document).outputJax))throw Error("The bussproofs extension requires an output jax with a getBBox() method")};m.clearDocument=function(E){n=null}},43313:function(B,m,v){var w=this&&this.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(m,"__esModule",{value:!0}),m.CancelConfiguration=m.CancelMethods=void 0;var y=v(8890),M=v(60742),x=v(82652),t=w(v(39194)),e=v(88687);m.CancelMethods={},m.CancelMethods.Cancel=function(n,c,l){var d=n.GetBrackets(c,""),o=n.ParseArg(c),i=t.default.keyvalOptions(d,e.ENCLOSE_OPTIONS);i.notation=l,n.Push(n.create("node","menclose",[o],i))},m.CancelMethods.CancelTo=function(n,c){var l=n.GetBrackets(c,""),d=n.ParseArg(c),o=n.ParseArg(c),i=t.default.keyvalOptions(l,e.ENCLOSE_OPTIONS);i.notation=[M.TexConstant.Notation.UPDIAGONALSTRIKE,M.TexConstant.Notation.UPDIAGONALARROW,M.TexConstant.Notation.NORTHEASTARROW].join(" "),d=n.create("node","mpadded",[d],{depth:"-.1em",height:"+.1em",voffset:".1em"}),n.Push(n.create("node","msup",[n.create("node","menclose",[o],i),d]))},new x.CommandMap("cancel",{cancel:["Cancel",M.TexConstant.Notation.UPDIAGONALSTRIKE],bcancel:["Cancel",M.TexConstant.Notation.DOWNDIAGONALSTRIKE],xcancel:["Cancel",M.TexConstant.Notation.UPDIAGONALSTRIKE+" "+M.TexConstant.Notation.DOWNDIAGONALSTRIKE],cancelto:"CancelTo"},m.CancelMethods),m.CancelConfiguration=y.Configuration.create("cancel",{handler:{macro:["cancel"]}})},31541:function(B,m,v){var M,h,w=this&&this.__extends||(h=function(a,g){return(h=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,u){r.__proto__=u}||function(r,u){for(var p in u)Object.prototype.hasOwnProperty.call(u,p)&&(r[p]=u[p])})(a,g)},function(a,g){if("function"!=typeof g&&null!==g)throw new TypeError("Class extends value "+String(g)+" is not a constructor or null");function r(){this.constructor=a}h(a,g),a.prototype=null===g?Object.create(g):(r.prototype=g.prototype,new r)}),y=this&&this.__importDefault||function(h){return h&&h.__esModule?h:{default:h}};Object.defineProperty(m,"__esModule",{value:!0}),m.CasesConfiguration=m.CasesMethods=m.CasesTags=m.CasesBeginItem=void 0;var x=v(8890),t=v(82652),e=y(v(39194)),n=y(v(2158)),c=y(v(6257)),l=v(33012),d=v(22124),o=v(70770),i=function(h){function a(){return null!==h&&h.apply(this,arguments)||this}return w(a,h),Object.defineProperty(a.prototype,"kind",{get:function(){return"cases-begin"},enumerable:!1,configurable:!0}),a.prototype.checkItem=function(g){return g.isKind("end")&&g.getName()===this.getName()&&this.getProperty("end")?(this.setProperty("end",!1),[[],!0]):h.prototype.checkItem.call(this,g)},a}(l.BeginItem);m.CasesBeginItem=i;var f=function(h){function a(){var g=null!==h&&h.apply(this,arguments)||this;return g.subcounter=0,g}return w(a,h),a.prototype.start=function(g,r,u){this.subcounter=0,h.prototype.start.call(this,g,r,u)},a.prototype.autoTag=function(){null==this.currentTag.tag&&("subnumcases"===this.currentTag.env?(0===this.subcounter&&this.counter++,this.subcounter++,this.tag(this.formatNumber(this.counter,this.subcounter),!1)):((0===this.subcounter||"numcases-left"!==this.currentTag.env)&&this.counter++,this.tag(this.formatNumber(this.counter),!1)))},a.prototype.formatNumber=function(g,r){return void 0===r&&(r=null),g.toString()+(null===r?"":String.fromCharCode(96+r))},a}(d.AmsTags);m.CasesTags=f,m.CasesMethods={NumCases:function(h,a){if(h.stack.env.closing===a.getName()){delete h.stack.env.closing,h.Push(h.itemFactory.create("end").setProperty("name",a.getName()));var g=h.stack.Top(),r=g.Last,u=e.default.copyNode(r,h),p=g.getProperty("left");return o.EmpheqUtil.left(r,u,p+"\\empheqlbrace\\,",h,"numcases-left"),h.Push(h.itemFactory.create("end").setProperty("name",a.getName())),null}p=h.GetArgument("\\begin{"+a.getName()+"}");a.setProperty("left",p);var A=n.default.EqnArray(h,a,!0,!0,"ll");return A.arraydef.displaystyle=!1,A.arraydef.rowspacing=".2em",A.setProperty("numCases",!0),h.Push(a),A},Entry:function(h,a){if(!h.stack.Top().getProperty("numCases"))return n.default.Entry(h,a);h.Push(h.itemFactory.create("cell").setProperties({isEntry:!0,name:a}));for(var g=h.string,r=0,u=h.i,p=g.length;u<p;){var A=g.charAt(u);if("{"===A)r++,u++;else if("}"===A){if(0===r)break;r--,u++}else{if("&"===A&&0===r)throw new c.default("ExtraCasesAlignTab","Extra alignment tab in text for numcase environment");if("\\"===A&&0===r){var C=(g.slice(u+1).match(/^[a-z]+|./i)||[])[0];if("\\"===C||"cr"===C||"end"===C||"label"===C)break;u+=C.length}else u++}}var P=g.substr(h.i,u-h.i).replace(/^\s*/,"");h.PushAll(e.default.internalMath(h,P,0)),h.i=u}},new t.EnvironmentMap("cases-env",o.EmpheqUtil.environment,{numcases:["NumCases","cases"],subnumcases:["NumCases","cases"]},m.CasesMethods),new t.MacroMap("cases-macros",{"&":"Entry"},m.CasesMethods),m.CasesConfiguration=x.Configuration.create("cases",{handler:{environment:["cases-env"],character:["cases-macros"]},items:(M={},M[i.prototype.kind]=i,M),tags:{cases:f}})},88254:function(B,m,v){var w=this&&this.__values||function(l){var d="function"==typeof Symbol&&Symbol.iterator,o=d&&l[d],i=0;if(o)return o.call(l);if(l&&"number"==typeof l.length)return{next:function(){return l&&i>=l.length&&(l=void 0),{value:l&&l[i++],done:!l}}};throw new TypeError(d?"Object is not iterable.":"Symbol.iterator is not defined.")},y=this&&this.__importDefault||function(l){return l&&l.__esModule?l:{default:l}};Object.defineProperty(m,"__esModule",{value:!0}),m.CenternotConfiguration=m.filterCenterOver=void 0;var M=v(8890),x=y(v(55571)),t=y(v(60909)),e=v(82652),n=y(v(2158));function c(l){var d,o,i=l.data;try{for(var f=w(i.getList("centerOver")),h=f.next();!h.done;h=f.next()){var a=h.value,g=t.default.getTexClass(a.childNodes[0].childNodes[0]);null!==g&&t.default.setProperties(a.parent.parent.parent.parent.parent.parent,{texClass:g})}}catch(r){d={error:r}}finally{try{h&&!h.done&&(o=f.return)&&o.call(f)}finally{if(d)throw d.error}}}new e.CommandMap("centernot",{centerOver:"CenterOver",centernot:["Macro","\\centerOver{#1}{{\u29f8}}",1]},{CenterOver:function(l,d){var o="{"+l.GetArgument(d)+"}",i=l.ParseArg(d),f=new x.default(o,l.stack.env,l.configuration).mml(),h=l.create("node","TeXAtom",[new x.default(o,l.stack.env,l.configuration).mml(),l.create("node","mpadded",[l.create("node","mpadded",[i],{width:0,lspace:"-.5width"}),l.create("node","mphantom",[f])],{width:0,lspace:"-.5width"})]);l.configuration.addNode("centerOver",f),l.Push(h)},Macro:n.default.Macro}),m.filterCenterOver=c,m.CenternotConfiguration=M.Configuration.create("centernot",{handler:{macro:["centernot"]},postprocessors:[c]})},89112:(B,m,v)=>{Object.defineProperty(m,"__esModule",{value:!0}),m.ColorConfiguration=void 0;var w=v(82652),y=v(8890),M=v(18997),x=v(67281);new w.CommandMap("color",{color:"Color",textcolor:"TextColor",definecolor:"DefineColor",colorbox:"ColorBox",fcolorbox:"FColorBox"},M.ColorMethods);m.ColorConfiguration=y.Configuration.create("color",{handler:{macro:["color"]},options:{color:{padding:"5px",borderWidth:"2px"}},config:function(e,n){n.parseOptions.packageData.set("color",{model:new x.ColorModel})}})},52631:(B,m)=>{Object.defineProperty(m,"__esModule",{value:!0}),m.COLORS=void 0,m.COLORS=new Map([["Apricot","#FBB982"],["Aquamarine","#00B5BE"],["Bittersweet","#C04F17"],["Black","#221E1F"],["Blue","#2D2F92"],["BlueGreen","#00B3B8"],["BlueViolet","#473992"],["BrickRed","#B6321C"],["Brown","#792500"],["BurntOrange","#F7921D"],["CadetBlue","#74729A"],["CarnationPink","#F282B4"],["Cerulean","#00A2E3"],["CornflowerBlue","#41B0E4"],["Cyan","#00AEEF"],["Dandelion","#FDBC42"],["DarkOrchid","#A4538A"],["Emerald","#00A99D"],["ForestGreen","#009B55"],["Fuchsia","#8C368C"],["Goldenrod","#FFDF42"],["Gray","#949698"],["Green","#00A64F"],["GreenYellow","#DFE674"],["JungleGreen","#00A99A"],["Lavender","#F49EC4"],["LimeGreen","#8DC73E"],["Magenta","#EC008C"],["Mahogany","#A9341F"],["Maroon","#AF3235"],["Melon","#F89E7B"],["MidnightBlue","#006795"],["Mulberry","#A93C93"],["NavyBlue","#006EB8"],["OliveGreen","#3C8031"],["Orange","#F58137"],["OrangeRed","#ED135A"],["Orchid","#AF72B0"],["Peach","#F7965A"],["Periwinkle","#7977B8"],["PineGreen","#008B72"],["Plum","#92268F"],["ProcessBlue","#00B0F0"],["Purple","#99479B"],["RawSienna","#974006"],["Red","#ED1B23"],["RedOrange","#F26035"],["RedViolet","#A1246B"],["Rhodamine","#EF559F"],["RoyalBlue","#0071BC"],["RoyalPurple","#613F99"],["RubineRed","#ED017D"],["Salmon","#F69289"],["SeaGreen","#3FBC9D"],["Sepia","#671800"],["SkyBlue","#46C5DD"],["SpringGreen","#C6DC67"],["Tan","#DA9D76"],["TealBlue","#00AEB3"],["Thistle","#D883B7"],["Turquoise","#00B4CE"],["Violet","#58429B"],["VioletRed","#EF58A0"],["White","#FFFFFF"],["WildStrawberry","#EE2967"],["Yellow","#FFF200"],["YellowGreen","#98CC70"],["YellowOrange","#FAA21A"]])},18997:function(B,m,v){var w=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(m,"__esModule",{value:!0}),m.ColorMethods=void 0;var y=w(v(60909)),M=w(v(39194));function x(t){var e="+".concat(t),n=t.replace(/^.*?([a-z]*)$/,"$1"),c=2*parseFloat(e);return{width:"+".concat(c).concat(n),height:e,depth:e,lspace:t}}m.ColorMethods={},m.ColorMethods.Color=function(t,e){var n=t.GetBrackets(e,""),c=t.GetArgument(e),d=t.configuration.packageData.get("color").model.getColor(n,c),o=t.itemFactory.create("style").setProperties({styles:{mathcolor:d}});t.stack.env.color=d,t.Push(o)},m.ColorMethods.TextColor=function(t,e){var n=t.GetBrackets(e,""),c=t.GetArgument(e),d=t.configuration.packageData.get("color").model.getColor(n,c),o=t.stack.env.color;t.stack.env.color=d;var i=t.ParseArg(e);o?t.stack.env.color=o:delete t.stack.env.color;var f=t.create("node","mstyle",[i],{mathcolor:d});t.Push(f)},m.ColorMethods.DefineColor=function(t,e){var n=t.GetArgument(e),c=t.GetArgument(e),l=t.GetArgument(e);t.configuration.packageData.get("color").model.defineColor(c,n,l)},m.ColorMethods.ColorBox=function(t,e){var n=t.GetArgument(e),c=M.default.internalMath(t,t.GetArgument(e)),l=t.configuration.packageData.get("color").model,d=t.create("node","mpadded",c,{mathbackground:l.getColor("named",n)});y.default.setProperties(d,x(t.options.color.padding)),t.Push(d)},m.ColorMethods.FColorBox=function(t,e){var n=t.GetArgument(e),c=t.GetArgument(e),l=M.default.internalMath(t,t.GetArgument(e)),d=t.options.color,o=t.configuration.packageData.get("color").model,i=t.create("node","mpadded",l,{mathbackground:o.getColor("named",c),style:"border: ".concat(d.borderWidth," solid ").concat(o.getColor("named",n))});y.default.setProperties(i,x(d.padding)),t.Push(i)}},67281:function(B,m,v){var w=this&&this.__values||function(n){var c="function"==typeof Symbol&&Symbol.iterator,l=c&&n[c],d=0;if(l)return l.call(n);if(n&&"number"==typeof n.length)return{next:function(){return n&&d>=n.length&&(n=void 0),{value:n&&n[d++],done:!n}}};throw new TypeError(c?"Object is not iterable.":"Symbol.iterator is not defined.")},y=this&&this.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(m,"__esModule",{value:!0}),m.ColorModel=void 0;var M=y(v(6257)),x=v(52631),t=new Map,e=function(){function n(){this.userColors=new Map}return n.prototype.normalizeColor=function(c,l){if(!c||"named"===c)return l;if(t.has(c))return t.get(c)(l);throw new M.default("UndefinedColorModel","Color model '%1' not defined",c)},n.prototype.getColor=function(c,l){return c&&"named"!==c?this.normalizeColor(c,l):this.getColorByName(l)},n.prototype.getColorByName=function(c){return this.userColors.has(c)?this.userColors.get(c):x.COLORS.has(c)?x.COLORS.get(c):c},n.prototype.defineColor=function(c,l,d){var o=this.normalizeColor(c,d);this.userColors.set(l,o)},n}();m.ColorModel=e,t.set("rgb",function(n){var c,l,d=n.trim().split(/\s*,\s*/),o="#";if(3!==d.length)throw new M.default("ModelArg1","Color values for the %1 model require 3 numbers","rgb");try{for(var i=w(d),f=i.next();!f.done;f=i.next()){var h=f.value;if(!h.match(/^(\d+(\.\d*)?|\.\d+)$/))throw new M.default("InvalidDecimalNumber","Invalid decimal number");var a=parseFloat(h);if(a<0||a>1)throw new M.default("ModelArg2","Color values for the %1 model must be between %2 and %3","rgb","0","1");var g=Math.floor(255*a).toString(16);g.length<2&&(g="0"+g),o+=g}}catch(r){c={error:r}}finally{try{f&&!f.done&&(l=i.return)&&l.call(i)}finally{if(c)throw c.error}}return o}),t.set("RGB",function(n){var c,l,d=n.trim().split(/\s*,\s*/),o="#";if(3!==d.length)throw new M.default("ModelArg1","Color values for the %1 model require 3 numbers","RGB");try{for(var i=w(d),f=i.next();!f.done;f=i.next()){var h=f.value;if(!h.match(/^\d+$/))throw new M.default("InvalidNumber","Invalid number");var a=parseInt(h);if(a>255)throw new M.default("ModelArg2","Color values for the %1 model must be between %2 and %3","RGB","0","255");var g=a.toString(16);g.length<2&&(g="0"+g),o+=g}}catch(r){c={error:r}}finally{try{f&&!f.done&&(l=i.return)&&l.call(i)}finally{if(c)throw c.error}}return o}),t.set("gray",function(n){if(!n.match(/^\s*(\d+(\.\d*)?|\.\d+)\s*$/))throw new M.default("InvalidDecimalNumber","Invalid decimal number");var c=parseFloat(n);if(c<0||c>1)throw new M.default("ModelArg2","Color values for the %1 model must be between %2 and %3","gray","0","1");var l=Math.floor(255*c).toString(16);return l.length<2&&(l="0"+l),"#".concat(l).concat(l).concat(l)})},52830:function(B,m,v){var l,w=this&&this.__extends||(l=function(d,o){return(l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,f){i.__proto__=f}||function(i,f){for(var h in f)Object.prototype.hasOwnProperty.call(f,h)&&(i[h]=f[h])})(d,o)},function(d,o){if("function"!=typeof o&&null!==o)throw new TypeError("Class extends value "+String(o)+" is not a constructor or null");function i(){this.constructor=d}l(d,o),d.prototype=null===o?Object.create(o):(i.prototype=o.prototype,new i)}),y=this&&this.__importDefault||function(l){return l&&l.__esModule?l:{default:l}};Object.defineProperty(m,"__esModule",{value:!0}),m.ColortblConfiguration=m.ColorArrayItem=void 0;var M=v(33012),x=v(8890),t=v(82652),e=y(v(6257)),n=function(l){function d(){var o=null!==l&&l.apply(this,arguments)||this;return o.color={cell:"",row:"",col:[]},o.hasColor=!1,o}return w(d,l),d.prototype.EndEntry=function(){l.prototype.EndEntry.call(this);var o=this.row[this.row.length-1],i=this.color.cell||this.color.row||this.color.col[this.row.length-1];i&&(o.attributes.set("mathbackground",i),this.color.cell="",this.hasColor=!0)},d.prototype.EndRow=function(){l.prototype.EndRow.call(this),this.color.row=""},d.prototype.createMml=function(){var o=l.prototype.createMml.call(this),i=o.isKind("mrow")?o.childNodes[1]:o;return i.isKind("menclose")&&(i=i.childNodes[0].childNodes[0]),this.hasColor&&"none"===i.attributes.get("frame")&&i.attributes.set("frame",""),o},d}(M.ArrayItem);m.ColorArrayItem=n,new t.CommandMap("colortbl",{cellcolor:["TableColor","cell"],rowcolor:["TableColor","row"],columncolor:["TableColor","col"]},{TableColor:function(l,d,o){var i=l.configuration.packageData.get("color").model,f=l.GetBrackets(d,""),h=i.getColor(f,l.GetArgument(d)),a=l.stack.Top();if(!(a instanceof n))throw new e.default("UnsupportedTableColor","Unsupported use of %1",l.currentCS);if("col"===o){if(a.table.length)throw new e.default("ColumnColorNotTop","%1 must be in the top row",d);a.color.col[a.row.length]=h,l.GetBrackets(d,"")&&l.GetBrackets(d,"")}else if(a.color[o]=h,"row"===o&&(a.Size()||a.row.length))throw new e.default("RowColorNotFirst","%1 must be at the beginning of a row",d)}});m.ColortblConfiguration=x.Configuration.create("colortbl",{handler:{macro:["colortbl"]},items:{array:n},priority:10,config:[function(l,d){d.parseOptions.packageData.has("color")||x.ConfigurationHandler.get("color").config(l,d)},10]})},25951:(B,m,v)=>{Object.defineProperty(m,"__esModule",{value:!0}),m.ColorConfiguration=m.ColorV2Methods=void 0;var w=v(82652),y=v(8890);m.ColorV2Methods={Color:function(M,x){var t=M.GetArgument(x),e=M.stack.env.color;M.stack.env.color=t;var n=M.ParseArg(x);e?M.stack.env.color=e:delete M.stack.env.color;var c=M.create("node","mstyle",[n],{mathcolor:t});M.Push(c)}},new w.CommandMap("colorv2",{color:"Color"},m.ColorV2Methods),m.ColorConfiguration=y.Configuration.create("colorv2",{handler:{macro:["colorv2"]}})},25886:function(B,m,v){var M,w=this&&this.__values||function(r){var u="function"==typeof Symbol&&Symbol.iterator,p=u&&r[u],A=0;if(p)return p.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&A>=r.length&&(r=void 0),{value:r&&r[A++],done:!r}}};throw new TypeError(u?"Object is not iterable.":"Symbol.iterator is not defined.")},y=this&&this.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(m,"__esModule",{value:!0}),m.ConfigMacrosConfiguration=void 0;var x=v(8890),t=v(80084),e=v(82652),n=y(v(70004)),c=v(95889),l=y(v(8362)),d=v(68838),o="configmacros-map",i="configmacros-env-map";m.ConfigMacrosConfiguration=x.Configuration.create("configmacros",{init:function f(r){new e.CommandMap(o,{},{}),new e.EnvironmentMap(i,n.default.environment,{},{}),r.append(x.Configuration.local({handler:{macro:[o],environment:[i]},priority:3}))},config:function h(r,u){(function a(r){var u,p,A=r.parseOptions.handlers.retrieve(o),C=r.parseOptions.options.macros;try{for(var P=w(Object.keys(C)),s=P.next();!s.done;s=P.next()){var _=s.value,S="string"==typeof C[_]?[C[_]]:C[_],b=Array.isArray(S[2])?new c.Macro(_,l.default.MacroWithTemplate,S.slice(0,2).concat(S[2])):new c.Macro(_,l.default.Macro,S);A.add(_,b)}}catch(O){u={error:O}}finally{try{s&&!s.done&&(p=P.return)&&p.call(P)}finally{if(u)throw u.error}}})(u),function g(r){var u,p,A=r.parseOptions.handlers.retrieve(i),C=r.parseOptions.options.environments;try{for(var P=w(Object.keys(C)),s=P.next();!s.done;s=P.next()){var _=s.value;A.add(_,new c.Macro(_,l.default.BeginEnv,[!0].concat(C[_])))}}catch(S){u={error:S}}finally{try{s&&!s.done&&(p=P.return)&&p.call(P)}finally{if(u)throw u.error}}}(u)},items:(M={},M[d.BeginEnvItem.prototype.kind]=d.BeginEnvItem,M),options:{macros:(0,t.expandable)({}),environments:(0,t.expandable)({})}})},94822:function(B,m,v){var x,i,w=this&&this.__extends||(i=function(f,h){return(i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(a,g){a.__proto__=g}||function(a,g){for(var r in g)Object.prototype.hasOwnProperty.call(g,r)&&(a[r]=g[r])})(f,h)},function(f,h){if("function"!=typeof h&&null!==h)throw new TypeError("Class extends value "+String(h)+" is not a constructor or null");function a(){this.constructor=f}i(f,h),f.prototype=null===h?Object.create(h):(a.prototype=h.prototype,new a)}),y=this&&this.__read||function(i,f){var h="function"==typeof Symbol&&i[Symbol.iterator];if(!h)return i;var g,u,a=h.call(i),r=[];try{for(;(void 0===f||f-- >0)&&!(g=a.next()).done;)r.push(g.value)}catch(p){u={error:p}}finally{try{g&&!g.done&&(h=a.return)&&h.call(a)}finally{if(u)throw u.error}}return r},M=this&&this.__importDefault||function(i){return i&&i.__esModule?i:{default:i}};Object.defineProperty(m,"__esModule",{value:!0}),m.EmpheqConfiguration=m.EmpheqMethods=m.EmpheqBeginItem=void 0;var t=v(8890),e=v(82652),n=M(v(39194)),c=M(v(6257)),l=v(33012),d=v(70770),o=function(i){function f(){return null!==i&&i.apply(this,arguments)||this}return w(f,i),Object.defineProperty(f.prototype,"kind",{get:function(){return"empheq-begin"},enumerable:!1,configurable:!0}),f.prototype.checkItem=function(h){return h.isKind("end")&&h.getName()===this.getName()&&this.setProperty("end",!1),i.prototype.checkItem.call(this,h)},f}(l.BeginItem);m.EmpheqBeginItem=o,m.EmpheqMethods={Empheq:function(i,f){if(i.stack.env.closing===f.getName()){delete i.stack.env.closing,i.Push(i.itemFactory.create("end").setProperty("name",i.stack.global.empheq)),i.stack.global.empheq="";var h=i.stack.Top();d.EmpheqUtil.adjustTable(h,i),i.Push(i.itemFactory.create("end").setProperty("name","empheq"))}else{n.default.checkEqnEnv(i),delete i.stack.global.eqnenv;var a=i.GetBrackets("\\begin{"+f.getName()+"}")||"",g=y((i.GetArgument("\\begin{"+f.getName()+"}")||"").split(/=/),2),r=g[0],u=g[1];if(!d.EmpheqUtil.checkEnv(r))throw new c.default("UnknownEnv",'Unknown environment "%1"',r);a&&f.setProperties(d.EmpheqUtil.splitOptions(a,{left:1,right:1})),i.stack.global.empheq=r,i.string="\\begin{"+r+"}"+(u?"{"+u+"}":"")+i.string.slice(i.i),i.i=0,i.Push(f)}},EmpheqMO:function(i,f,h){i.Push(i.create("token","mo",{},h))},EmpheqDelim:function(i,f){var h=i.GetDelimiter(f);i.Push(i.create("token","mo",{stretchy:!0,symmetric:!0},h))}},new e.EnvironmentMap("empheq-env",d.EmpheqUtil.environment,{empheq:["Empheq","empheq"]},m.EmpheqMethods),new e.CommandMap("empheq-macros",{empheqlbrace:["EmpheqMO","{"],empheqrbrace:["EmpheqMO","}"],empheqlbrack:["EmpheqMO","["],empheqrbrack:["EmpheqMO","]"],empheqlangle:["EmpheqMO","\u27e8"],empheqrangle:["EmpheqMO","\u27e9"],empheqlparen:["EmpheqMO","("],empheqrparen:["EmpheqMO",")"],empheqlvert:["EmpheqMO","|"],empheqrvert:["EmpheqMO","|"],empheqlVert:["EmpheqMO","\u2016"],empheqrVert:["EmpheqMO","\u2016"],empheqlfloor:["EmpheqMO","\u230a"],empheqrfloor:["EmpheqMO","\u230b"],empheqlceil:["EmpheqMO","\u2308"],empheqrceil:["EmpheqMO","\u2309"],empheqbiglbrace:["EmpheqMO","{"],empheqbigrbrace:["EmpheqMO","}"],empheqbiglbrack:["EmpheqMO","["],empheqbigrbrack:["EmpheqMO","]"],empheqbiglangle:["EmpheqMO","\u27e8"],empheqbigrangle:["EmpheqMO","\u27e9"],empheqbiglparen:["EmpheqMO","("],empheqbigrparen:["EmpheqMO",")"],empheqbiglvert:["EmpheqMO","|"],empheqbigrvert:["EmpheqMO","|"],empheqbiglVert:["EmpheqMO","\u2016"],empheqbigrVert:["EmpheqMO","\u2016"],empheqbiglfloor:["EmpheqMO","\u230a"],empheqbigrfloor:["EmpheqMO","\u230b"],empheqbiglceil:["EmpheqMO","\u2308"],empheqbigrceil:["EmpheqMO","\u2309"],empheql:"EmpheqDelim",empheqr:"EmpheqDelim",empheqbigl:"EmpheqDelim",empheqbigr:"EmpheqDelim"},m.EmpheqMethods),m.EmpheqConfiguration=t.Configuration.create("empheq",{handler:{macro:["empheq-macros"],environment:["empheq-env"]},items:(x={},x[o.prototype.kind]=o,x)})},70770:function(B,m,v){var w=this&&this.__read||function(n,c){var l="function"==typeof Symbol&&n[Symbol.iterator];if(!l)return n;var o,f,d=l.call(n),i=[];try{for(;(void 0===c||c-- >0)&&!(o=d.next()).done;)i.push(o.value)}catch(h){f={error:h}}finally{try{o&&!o.done&&(l=d.return)&&l.call(d)}finally{if(f)throw f.error}}return i},y=this&&this.__spreadArray||function(n,c,l){if(l||2===arguments.length)for(var i,d=0,o=c.length;d<o;d++)(i||!(d in c))&&(i||(i=Array.prototype.slice.call(c,0,d)),i[d]=c[d]);return n.concat(i||Array.prototype.slice.call(c))},M=this&&this.__values||function(n){var c="function"==typeof Symbol&&Symbol.iterator,l=c&&n[c],d=0;if(l)return l.call(n);if(n&&"number"==typeof n.length)return{next:function(){return n&&d>=n.length&&(n=void 0),{value:n&&n[d++],done:!n}}};throw new TypeError(c?"Object is not iterable.":"Symbol.iterator is not defined.")},x=this&&this.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(m,"__esModule",{value:!0}),m.EmpheqUtil=void 0;var t=x(v(39194)),e=x(v(55571));m.EmpheqUtil={environment:function(n,c,l,d){var o=d[0],i=n.itemFactory.create(o+"-begin").setProperties({name:c,end:o});n.Push(l.apply(void 0,y([n,i],w(d.slice(1)),!1)))},splitOptions:function(n,c){return void 0===c&&(c=null),t.default.keyvalOptions(n,c,!0)},columnCount:function(n){var c,l,d=0;try{for(var o=M(n.childNodes),i=o.next();!i.done;i=o.next()){var f=i.value,h=f.childNodes.length-(f.isKind("mlabeledtr")?1:0);h>d&&(d=h)}}catch(a){c={error:a}}finally{try{i&&!i.done&&(l=o.return)&&l.call(o)}finally{if(c)throw c.error}}return d},cellBlock:function(n,c,l,d){var o,i,f=l.create("node","mpadded",[],{height:0,depth:0,voffset:"-1height"}),h=new e.default(n,l.stack.env,l.configuration),a=h.mml();d&&h.configuration.tags.label&&(h.configuration.tags.currentTag.env=d,h.configuration.tags.getTag(!0));try{for(var g=M(a.isInferred?a.childNodes:[a]),r=g.next();!r.done;r=g.next()){var u=r.value;f.appendChild(u)}}catch(p){o={error:p}}finally{try{r&&!r.done&&(i=g.return)&&i.call(g)}finally{if(o)throw o.error}}return f.appendChild(l.create("node","mphantom",[l.create("node","mpadded",[c],{width:0})])),f},topRowTable:function(n,c){var l=t.default.copyNode(n,c);return l.setChildren(l.childNodes.slice(0,1)),l.attributes.set("align","baseline 1"),n.factory.create("mphantom",{},[c.create("node","mpadded",[l],{width:0})])},rowspanCell:function(n,c,l,d,o){n.appendChild(d.create("node","mpadded",[this.cellBlock(c,t.default.copyNode(l,d),d,o),this.topRowTable(l,d)],{height:0,depth:0,voffset:"height"}))},left:function(n,c,l,d,o){var i,f,h;void 0===o&&(o=""),n.attributes.set("columnalign","right "+(n.attributes.get("columnalign")||"")),n.attributes.set("columnspacing","0em "+(n.attributes.get("columnspacing")||""));try{for(var a=M(n.childNodes.slice(0).reverse()),g=a.next();!g.done;g=a.next()){var r=g.value;h=d.create("node","mtd"),r.childNodes.unshift(h),h.parent=r,r.isKind("mlabeledtr")&&(r.childNodes[0]=r.childNodes[1],r.childNodes[1]=h)}}catch(u){i={error:u}}finally{try{g&&!g.done&&(f=a.return)&&f.call(a)}finally{if(i)throw i.error}}this.rowspanCell(h,l,c,d,o)},right:function(n,c,l,d,o){void 0===o&&(o=""),0===n.childNodes.length&&n.appendChild(d.create("node","mtr"));for(var i=m.EmpheqUtil.columnCount(n),f=n.childNodes[0];f.childNodes.length<i;)f.appendChild(d.create("node","mtd"));var h=f.appendChild(d.create("node","mtd"));m.EmpheqUtil.rowspanCell(h,l,c,d,o),n.attributes.set("columnalign",(n.attributes.get("columnalign")||"").split(/ /).slice(0,i).join(" ")+" left"),n.attributes.set("columnspacing",(n.attributes.get("columnspacing")||"").split(/ /).slice(0,i-1).join(" ")+" 0em")},adjustTable:function(n,c){var l=n.getProperty("left"),d=n.getProperty("right");if(l||d){var o=n.Last,i=t.default.copyNode(o,c);l&&this.left(o,i,l,c),d&&this.right(o,i,d,c)}},allowEnv:{equation:!0,align:!0,gather:!0,flalign:!0,alignat:!0,multline:!0},checkEnv:function(n){return this.allowEnv.hasOwnProperty(n.replace(/\*$/,""))||!1}}},88687:function(B,m,v){var w=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(m,"__esModule",{value:!0}),m.EncloseConfiguration=m.EncloseMethods=m.ENCLOSE_OPTIONS=void 0;var y=v(8890),M=v(82652),x=w(v(39194));m.ENCLOSE_OPTIONS={"data-arrowhead":1,color:1,mathcolor:1,background:1,mathbackground:1,"data-padding":1,"data-thickness":1},m.EncloseMethods={},m.EncloseMethods.Enclose=function(t,e){var n=t.GetArgument(e).replace(/,/g," "),c=t.GetBrackets(e,""),l=t.ParseArg(e),d=x.default.keyvalOptions(c,m.ENCLOSE_OPTIONS);d.notation=n,t.Push(t.create("node","menclose",[l],d))},new M.CommandMap("enclose",{enclose:"Enclose"},m.EncloseMethods),m.EncloseConfiguration=y.Configuration.create("enclose",{handler:{macro:["enclose"]}})},93846:function(B,m,v){var w=this&&this.__importDefault||function(l){return l&&l.__esModule?l:{default:l}};Object.defineProperty(m,"__esModule",{value:!0}),m.ExtpfeilConfiguration=m.ExtpfeilMethods=void 0;var y=v(8890),M=v(82652),x=v(86800),t=w(v(53245)),e=v(20196),n=w(v(6257));m.ExtpfeilMethods={},m.ExtpfeilMethods.xArrow=x.AmsMethods.xArrow,m.ExtpfeilMethods.NewExtArrow=function(l,d){var o=l.GetArgument(d),i=l.GetArgument(d),f=l.GetArgument(d);if(!o.match(/^\\([a-z]+|.)$/i))throw new n.default("NewextarrowArg1","First argument to %1 must be a control sequence name",d);if(!i.match(/^(\d+),(\d+)$/))throw new n.default("NewextarrowArg2","Second argument to %1 must be two integers separated by a comma",d);if(!f.match(/^(\d+|0x[0-9A-F]+)$/i))throw new n.default("NewextarrowArg3","Third argument to %1 must be a unicode character number",d);o=o.substr(1);var h=i.split(",");t.default.addMacro(l,o,m.ExtpfeilMethods.xArrow,[parseInt(f),parseInt(h[0]),parseInt(h[1])])},new M.CommandMap("extpfeil",{xtwoheadrightarrow:["xArrow",8608,12,16],xtwoheadleftarrow:["xArrow",8606,17,13],xmapsto:["xArrow",8614,6,7],xlongequal:["xArrow",61,7,7],xtofrom:["xArrow",8644,12,12],Newextarrow:"NewExtArrow"},m.ExtpfeilMethods);m.ExtpfeilConfiguration=y.Configuration.create("extpfeil",{handler:{macro:["extpfeil"]},init:function(l){e.NewcommandConfiguration.init(l)}})},83190:(B,m,v)=>{Object.defineProperty(m,"__esModule",{value:!0}),m.GensymbConfiguration=void 0;var w=v(8890),y=v(60742);new(v(82652).CharacterMap)("gensymb-symbols",function x(t,e){var n=e.attributes||{};n.mathvariant=y.TexConstant.Variant.NORMAL,n.class="MathML-Unit";var c=t.create("token","mi",n,e.char);t.Push(c)},{ohm:"\u2126",degree:"\xb0",celsius:"\u2103",perthousand:"\u2030",micro:"\xb5"}),m.GensymbConfiguration=w.Configuration.create("gensymb",{handler:{macro:["gensymb-symbols"]}})},59548:function(B,m,v){var w=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(m,"__esModule",{value:!0}),m.HtmlConfiguration=void 0;var y=v(8890),M=v(82652),x=w(v(35404));new M.CommandMap("html_macros",{href:"Href",class:"Class",style:"Style",cssId:"Id"},x.default),m.HtmlConfiguration=y.Configuration.create("html",{handler:{macro:["html_macros"]}})},35404:function(B,m,v){var w=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(m,"__esModule",{value:!0});var y=w(v(60909)),M={Href:function(t,e){var n=t.GetArgument(e),c=x(t,e);y.default.setAttribute(c,"href",n),t.Push(c)},Class:function(t,e){var n=t.GetArgument(e),c=x(t,e),l=y.default.getAttribute(c,"class");l&&(n=l+" "+n),y.default.setAttribute(c,"class",n),t.Push(c)},Style:function(t,e){var n=t.GetArgument(e),c=x(t,e),l=y.default.getAttribute(c,"style");l&&(";"!==n.charAt(n.length-1)&&(n+=";"),n=l+" "+n),y.default.setAttribute(c,"style",n),t.Push(c)},Id:function(t,e){var n=t.GetArgument(e),c=x(t,e);y.default.setAttribute(c,"id",n),t.Push(c)}},x=function(t,e){var n=t.ParseArg(e);if(!y.default.isInferred(n))return n;var c=y.default.getChildren(n);if(1===c.length)return c[0];var l=t.create("node","mrow");return y.default.copyChildren(n,l),y.default.copyAttributes(n,l),l};m.default=M},41461:function(B,m,v){var M,w=this&&this.__values||function(h){var a="function"==typeof Symbol&&Symbol.iterator,g=a&&h[a],r=0;if(g)return g.call(h);if(h&&"number"==typeof h.length)return{next:function(){return h&&r>=h.length&&(h=void 0),{value:h&&h[r++],done:!h}}};throw new TypeError(a?"Object is not iterable.":"Symbol.iterator is not defined.")},y=this&&this.__importDefault||function(h){return h&&h.__esModule?h:{default:h}};Object.defineProperty(m,"__esModule",{value:!0}),m.MathtoolsConfiguration=m.fixPrescripts=m.PAIREDDELIMS=void 0;var x=v(8890),t=v(82652),e=y(v(60909)),n=v(80084);v(93177);var c=v(84470),l=v(61400),d=v(77401);function f(h){var a,g,r,u,p,A,C=h.data;try{for(var P=w(C.getList("mmultiscripts")),s=P.next();!s.done;s=P.next()){var _=s.value;if(_.getProperty("fixPrescript")){var S=e.default.getChildren(_),b=0;try{for(var O=(r=void 0,w([1,2])),T=O.next();!T.done;T=O.next()){S[D=T.value]||(e.default.setChild(_,D,C.nodeFactory.create("node","none")),b++)}}catch(L){r={error:L}}finally{try{T&&!T.done&&(u=O.return)&&u.call(O)}finally{if(r)throw r.error}}try{for(var I=(p=void 0,w([4,5])),k=I.next();!k.done;k=I.next()){var D=k.value;e.default.isType(S[D],"mrow")&&0===e.default.getChildren(S[D]).length&&e.default.setChild(_,D,C.nodeFactory.create("node","none"))}}catch(L){p={error:L}}finally{try{k&&!k.done&&(A=I.return)&&A.call(I)}finally{if(p)throw p.error}}2===b&&S.splice(1,2)}}}catch(L){a={error:L}}finally{try{s&&!s.done&&(g=P.return)&&g.call(P)}finally{if(a)throw a.error}}}m.PAIREDDELIMS="mathtools-paired-delims",m.fixPrescripts=f,m.MathtoolsConfiguration=x.Configuration.create("mathtools",{handler:{macro:["mathtools-macros","mathtools-delimiters"],environment:["mathtools-environments"],delimiter:["mathtools-delimiters"],character:["mathtools-characters"]},items:(M={},M[d.MultlinedItem.prototype.kind]=d.MultlinedItem,M),init:function o(h){new t.CommandMap(m.PAIREDDELIMS,{},{}),h.append(x.Configuration.local({handler:{macro:[m.PAIREDDELIMS]},priority:-5}))},config:function i(h,a){var g,r,u=a.parseOptions,p=u.options.mathtools.pairedDelimiters;try{for(var A=w(Object.keys(p)),C=A.next();!C.done;C=A.next()){var P=C.value;c.MathtoolsUtil.addPairedDelims(u,P,p[P])}}catch(s){g={error:s}}finally{try{C&&!C.done&&(r=A.return)&&r.call(A)}finally{if(g)throw g.error}}(0,l.MathtoolsTagFormat)(h,a)},postprocessors:[[f,-6]],options:{mathtools:{multlinegap:"1em","multlined-pos":"c","firstline-afterskip":"","lastline-preskip":"","smallmatrix-align":"c",shortvdotsadjustabove:".2em",shortvdotsadjustbelow:".2em",centercolon:!1,"centercolon-offset":".04em","thincolon-dx":"-.04em","thincolon-dw":"-.08em","use-unicode":!1,"prescript-sub-format":"","prescript-sup-format":"","prescript-arg-format":"","allow-mathtoolsset":!0,pairedDelimiters:(0,n.expandable)({}),tagforms:(0,n.expandable)({})}}})},77401:function(B,m,v){var n,w=this&&this.__extends||(n=function(c,l){return(n=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,o){d.__proto__=o}||function(d,o){for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(d[i]=o[i])})(c,l)},function(c,l){if("function"!=typeof l&&null!==l)throw new TypeError("Class extends value "+String(l)+" is not a constructor or null");function d(){this.constructor=c}n(c,l),c.prototype=null===l?Object.create(l):(d.prototype=l.prototype,new d)}),y=this&&this.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(m,"__esModule",{value:!0}),m.MultlinedItem=void 0;var M=v(66866),x=y(v(60909)),t=v(60742),e=function(n){function c(){return null!==n&&n.apply(this,arguments)||this}return w(c,n),Object.defineProperty(c.prototype,"kind",{get:function(){return"multlined"},enumerable:!1,configurable:!0}),c.prototype.EndTable=function(){if((this.Size()||this.row.length)&&(this.EndEntry(),this.EndRow()),this.table.length>1){var l=this.factory.configuration.options.mathtools,d=l.multlinegap,o=l["firstline-afterskip"]||d,i=l["lastline-preskip"]||d,f=x.default.getChildren(this.table[0])[0];x.default.getAttribute(f,"columnalign")!==t.TexConstant.Align.RIGHT&&f.appendChild(this.create("node","mspace",[],{width:o}));var h=x.default.getChildren(this.table[this.table.length-1])[0];if(x.default.getAttribute(h,"columnalign")!==t.TexConstant.Align.LEFT){var a=x.default.getChildren(h)[0];a.childNodes.unshift(null);var g=this.create("node","mspace",[],{width:i});x.default.setChild(a,0,g)}}n.prototype.EndTable.call(this)},c}(M.MultlineItem);m.MultlinedItem=e},93177:function(B,m,v){var w=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(m,"__esModule",{value:!0});var y=w(v(70004)),M=v(82652),x=v(60742),t=v(87491);new M.CommandMap("mathtools-macros",{shoveleft:["HandleShove",x.TexConstant.Align.LEFT],shoveright:["HandleShove",x.TexConstant.Align.RIGHT],xleftrightarrow:["xArrow",8596,10,10],xLeftarrow:["xArrow",8656,12,7],xRightarrow:["xArrow",8658,7,12],xLeftrightarrow:["xArrow",8660,12,12],xhookleftarrow:["xArrow",8617,10,5],xhookrightarrow:["xArrow",8618,5,10],xmapsto:["xArrow",8614,10,10],xrightharpoondown:["xArrow",8641,5,10],xleftharpoondown:["xArrow",8637,10,5],xrightleftharpoons:["xArrow",8652,10,10],xrightharpoonup:["xArrow",8640,5,10],xleftharpoonup:["xArrow",8636,10,5],xleftrightharpoons:["xArrow",8651,10,10],mathllap:["MathLap","l",!1],mathrlap:["MathLap","r",!1],mathclap:["MathLap","c",!1],clap:["MtLap","c"],textllap:["MtLap","l"],textrlap:["MtLap","r"],textclap:["MtLap","c"],cramped:"Cramped",crampedllap:["MathLap","l",!0],crampedrlap:["MathLap","r",!0],crampedclap:["MathLap","c",!0],crampedsubstack:["Macro","\\begin{crampedsubarray}{c}#1\\end{crampedsubarray}",1],mathmbox:"MathMBox",mathmakebox:"MathMakeBox",overbracket:"UnderOverBracket",underbracket:"UnderOverBracket",refeq:"HandleRef",MoveEqLeft:["Macro","\\hspace{#1em}&\\hspace{-#1em}",1,"2"],Aboxed:"Aboxed",ArrowBetweenLines:"ArrowBetweenLines",vdotswithin:"VDotsWithin",shortvdotswithin:"ShortVDotsWithin",MTFlushSpaceAbove:"FlushSpaceAbove",MTFlushSpaceBelow:"FlushSpaceBelow",DeclarePairedDelimiter:"DeclarePairedDelimiter",DeclarePairedDelimiterX:"DeclarePairedDelimiterX",DeclarePairedDelimiterXPP:"DeclarePairedDelimiterXPP",DeclarePairedDelimiters:"DeclarePairedDelimiter",DeclarePairedDelimitersX:"DeclarePairedDelimiterX",DeclarePairedDelimitersXPP:"DeclarePairedDelimiterXPP",centercolon:["CenterColon",!0,!0],ordinarycolon:["CenterColon",!1],MTThinColon:["CenterColon",!0,!0,!0],coloneqq:["Relation",":=","\u2254"],Coloneqq:["Relation","::=","\u2a74"],coloneq:["Relation",":-"],Coloneq:["Relation","::-"],eqqcolon:["Relation","=:","\u2255"],Eqqcolon:["Relation","=::"],eqcolon:["Relation","-:","\u2239"],Eqcolon:["Relation","-::"],colonapprox:["Relation",":\\approx"],Colonapprox:["Relation","::\\approx"],colonsim:["Relation",":\\sim"],Colonsim:["Relation","::\\sim"],dblcolon:["Relation","::","\u2237"],nuparrow:["NArrow","\u2191",".06em"],ndownarrow:["NArrow","\u2193",".25em"],bigtimes:["Macro","\\mathop{\\Large\\kern-.1em\\boldsymbol{\\times}\\kern-.1em}"],splitfrac:["SplitFrac",!1],splitdfrac:["SplitFrac",!0],xmathstrut:"XMathStrut",prescript:"Prescript",newtagform:["NewTagForm",!1],renewtagform:["NewTagForm",!0],usetagform:"UseTagForm",adjustlimits:["MacroWithTemplate","\\mathop{{#1}\\vphantom{{#3}}}_{{#2}\\vphantom{{#4}}}\\mathop{{#3}\\vphantom{{#1}}}_{{#4}\\vphantom{{#2}}}",4,,"_",,"_"],mathtoolsset:"SetOptions"},t.MathtoolsMethods),new M.EnvironmentMap("mathtools-environments",y.default.environment,{dcases:["Array",null,"\\{","","ll",null,".2em","D"],rcases:["Array",null,"","\\}","ll",null,".2em"],drcases:["Array",null,"","\\}","ll",null,".2em","D"],"dcases*":["Cases",null,"{","","D"],"rcases*":["Cases",null,"","}"],"drcases*":["Cases",null,"","}","D"],"cases*":["Cases",null,"{",""],"matrix*":["MtMatrix",null,null,null],"pmatrix*":["MtMatrix",null,"(",")"],"bmatrix*":["MtMatrix",null,"[","]"],"Bmatrix*":["MtMatrix",null,"\\{","\\}"],"vmatrix*":["MtMatrix",null,"\\vert","\\vert"],"Vmatrix*":["MtMatrix",null,"\\Vert","\\Vert"],"smallmatrix*":["MtSmallMatrix",null,null,null],psmallmatrix:["MtSmallMatrix",null,"(",")","c"],"psmallmatrix*":["MtSmallMatrix",null,"(",")"],bsmallmatrix:["MtSmallMatrix",null,"[","]","c"],"bsmallmatrix*":["MtSmallMatrix",null,"[","]"],Bsmallmatrix:["MtSmallMatrix",null,"\\{","\\}","c"],"Bsmallmatrix*":["MtSmallMatrix",null,"\\{","\\}"],vsmallmatrix:["MtSmallMatrix",null,"\\vert","\\vert","c"],"vsmallmatrix*":["MtSmallMatrix",null,"\\vert","\\vert"],Vsmallmatrix:["MtSmallMatrix",null,"\\Vert","\\Vert","c"],"Vsmallmatrix*":["MtSmallMatrix",null,"\\Vert","\\Vert"],crampedsubarray:["Array",null,null,null,null,"0em","0.1em","S'",1],multlined:"MtMultlined",spreadlines:["SpreadLines",!0],lgathered:["AmsEqnArray",null,null,null,"l",null,".5em","D"],rgathered:["AmsEqnArray",null,null,null,"r",null,".5em","D"]},t.MathtoolsMethods),new M.DelimiterMap("mathtools-delimiters",y.default.delimiter,{"\\lparen":"(","\\rparen":")"}),new M.CommandMap("mathtools-characters",{":":["CenterColon",!0]},t.MathtoolsMethods)},87491:function(B,m,v){var w=this&&this.__assign||function(){return w=Object.assign||function(r){for(var u,p=1,A=arguments.length;p<A;p++)for(var C in u=arguments[p])Object.prototype.hasOwnProperty.call(u,C)&&(r[C]=u[C]);return r},w.apply(this,arguments)},y=this&&this.__read||function(r,u){var p="function"==typeof Symbol&&r[Symbol.iterator];if(!p)return r;var C,s,A=p.call(r),P=[];try{for(;(void 0===u||u-- >0)&&!(C=A.next()).done;)P.push(C.value)}catch(_){s={error:_}}finally{try{C&&!C.done&&(p=A.return)&&p.call(A)}finally{if(s)throw s.error}}return P},M=this&&this.__values||function(r){var u="function"==typeof Symbol&&Symbol.iterator,p=u&&r[u],A=0;if(p)return p.call(r);if(r&&"number"==typeof r.length)return{next:function(){return r&&A>=r.length&&(r=void 0),{value:r&&r[A++],done:!r}}};throw new TypeError(u?"Object is not iterable.":"Symbol.iterator is not defined.")},x=this&&this.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(m,"__esModule",{value:!0}),m.MathtoolsMethods=void 0;var t=x(v(39194)),e=v(86800),n=x(v(2158)),c=x(v(55571)),l=x(v(6257)),d=x(v(60909)),o=v(91585),i=v(21108),f=v(80084),h=x(v(53245)),a=x(v(8362)),g=v(84470);m.MathtoolsMethods={MtMatrix:function(r,u,p,A){var C=r.GetBrackets("\\begin{".concat(u.getName(),"}"),"c");return m.MathtoolsMethods.Array(r,u,p,A,C)},MtSmallMatrix:function(r,u,p,A,C){return C||(C=r.GetBrackets("\\begin{".concat(u.getName(),"}"),r.options.mathtools["smallmatrix-align"])),m.MathtoolsMethods.Array(r,u,p,A,C,t.default.Em(1/3),".2em","S",1)},MtMultlined:function(r,u){var p,A="\\begin{".concat(u.getName(),"}"),C=r.GetBrackets(A,r.options.mathtools["multlined-pos"]||"c"),P=C?r.GetBrackets(A,""):"";C&&!C.match(/^[cbt]$/)&&(P=(p=y([C,P],2))[0],C=p[1]),r.Push(u);var s=r.itemFactory.create("multlined",r,u);return s.arraydef={displaystyle:!0,rowspacing:".5em",width:P||"auto",columnwidth:"100%"},t.default.setArrayAlign(s,C||"c")},HandleShove:function(r,u,p){var A=r.stack.Top();if("multline"!==A.kind&&"multlined"!==A.kind)throw new l.default("CommandInMultlined","%1 can only appear within the multline or multlined environments",u);if(A.Size())throw new l.default("CommandAtTheBeginingOfLine","%1 must come at the beginning of the line",u);A.setProperty("shove",p);var C=r.GetBrackets(u),P=r.ParseArg(u);if(C){var s=r.create("node","mrow",[]),_=r.create("node","mspace",[],{width:C});"left"===p?(s.appendChild(_),s.appendChild(P)):(s.appendChild(P),s.appendChild(_)),P=s}r.Push(P)},SpreadLines:function(r,u){var p,A;if(r.stack.env.closing===u.getName()){delete r.stack.env.closing;var C=r.stack.Pop(),P=C.toMml(),s=C.getProperty("spread");if(P.isInferred)try{for(var _=M(d.default.getChildren(P)),S=_.next();!S.done;S=_.next()){var b=S.value;g.MathtoolsUtil.spreadLines(b,s)}}catch(O){p={error:O}}finally{try{S&&!S.done&&(A=_.return)&&A.call(_)}finally{if(p)throw p.error}}else g.MathtoolsUtil.spreadLines(P,s);r.Push(P)}else{s=r.GetDimen("\\begin{".concat(u.getName(),"}"));u.setProperty("spread",s),r.Push(u)}},Cases:function(r,u,p,A,C){var P=r.itemFactory.create("array").setProperty("casesEnv",u.getName());return P.arraydef={rowspacing:".2em",columnspacing:"1em",columnalign:"left"},"D"===C&&(P.arraydef.displaystyle=!0),P.setProperties({open:p,close:A}),r.Push(u),P},MathLap:function(r,u,p,A){var C=r.GetBrackets(u,"").trim(),P=r.create("node","mstyle",[r.create("node","mpadded",[r.ParseArg(u)],w({width:0},"r"===p?{}:{lspace:"l"===p?"-1width":"-.5width"}))],{"data-cramped":A});g.MathtoolsUtil.setDisplayLevel(P,C),r.Push(r.create("node","TeXAtom",[P]))},Cramped:function(r,u){var p=r.GetBrackets(u,"").trim(),A=r.ParseArg(u),C=r.create("node","mstyle",[A],{"data-cramped":!0});g.MathtoolsUtil.setDisplayLevel(C,p),r.Push(C)},MtLap:function(r,u,p){var A=t.default.internalMath(r,r.GetArgument(u),0),C=r.create("node","mpadded",A,{width:0});"r"!==p&&d.default.setAttribute(C,"lspace","l"===p?"-1width":"-.5width"),r.Push(C)},MathMakeBox:function(r,u){var p=r.GetBrackets(u),A=r.GetBrackets(u,"c"),C=r.create("node","mpadded",[r.ParseArg(u)]);p&&d.default.setAttribute(C,"width",p);var P=(0,f.lookup)(A,{c:"center",r:"right"},"");P&&d.default.setAttribute(C,"data-align",P),r.Push(C)},MathMBox:function(r,u){r.Push(r.create("node","mrow",[r.ParseArg(u)]))},UnderOverBracket:function(r,u){var p=(0,i.length2em)(r.GetBrackets(u,".1em"),.1),A=r.GetBrackets(u,".2em"),C=r.GetArgument(u),P=y("o"===u.charAt(1)?["over","accent","bottom"]:["under","accentunder","top"],3),s=P[0],_=P[1],S=P[2],b=(0,i.em)(p),O=new c.default(C,r.stack.env,r.configuration).mml(),T=new c.default(C,r.stack.env,r.configuration).mml(),D=r.create("node","mpadded",[r.create("node","mphantom",[T])],{style:"border: ".concat(b," solid; border-").concat(S,": none"),height:A,depth:0}),I=t.default.underOver(r,O,D,s,!0),k=d.default.getChildAt(d.default.getChildAt(I,0),0);d.default.setAttribute(k,_,!0),r.Push(I)},Aboxed:function(r,u){var p=g.MathtoolsUtil.checkAlignment(r,u);p.row.length%2==1&&p.row.push(r.create("node","mtd",[]));var A=r.GetArgument(u),C=r.string.substr(r.i);r.string=A+"&&\\endAboxed",r.i=0;var P=r.GetUpTo(u,"&"),s=r.GetUpTo(u,"&");r.GetUpTo(u,"\\endAboxed");var _=t.default.substituteArgs(r,[P,s],"\\rlap{\\boxed{#1{}#2}}\\kern.267em\\phantom{#1}&\\phantom{{}#2}\\kern.267em");r.string=_+C,r.i=0},ArrowBetweenLines:function(r,u){var p=g.MathtoolsUtil.checkAlignment(r,u);if(p.Size()||p.row.length)throw new l.default("BetweenLines","%1 must be on a row by itself",u);var A=r.GetStar(),C=r.GetBrackets(u,"\\Updownarrow");A&&(p.EndEntry(),p.EndEntry());var P=A?"\\quad"+C:C+"\\quad",s=new c.default(P,r.stack.env,r.configuration).mml();r.Push(s),p.EndEntry(),p.EndRow()},VDotsWithin:function(r,u){var p=r.stack.Top(),A=p.getProperty("flushspaceabove")===p.table.length,C="\\mmlToken{mi}{}"+r.GetArgument(u)+"\\mmlToken{mi}{}",P=new c.default(C,r.stack.env,r.configuration).mml(),s=r.create("node","mpadded",[r.create("node","mpadded",[r.create("node","mo",[r.create("text","\u22ee")])],w({width:0,lspace:"-.5width"},A?{height:"-.6em",voffset:"-.18em"}:{})),r.create("node","mphantom",[P])],{lspace:".5width"});r.Push(s)},ShortVDotsWithin:function(r,u){var p=r.stack.Top(),A=r.GetStar();m.MathtoolsMethods.FlushSpaceAbove(r,"\\MTFlushSpaceAbove"),!A&&p.EndEntry(),m.MathtoolsMethods.VDotsWithin(r,"\\vdotswithin"),A&&p.EndEntry(),m.MathtoolsMethods.FlushSpaceBelow(r,"\\MTFlushSpaceBelow")},FlushSpaceAbove:function(r,u){var p=g.MathtoolsUtil.checkAlignment(r,u);p.setProperty("flushspaceabove",p.table.length),p.addRowSpacing("-"+r.options.mathtools.shortvdotsadjustabove)},FlushSpaceBelow:function(r,u){var p=g.MathtoolsUtil.checkAlignment(r,u);p.Size()&&p.EndEntry(),p.EndRow(),p.addRowSpacing("-"+r.options.mathtools.shortvdotsadjustbelow)},PairedDelimiters:function(r,u,p,A,C,P,s,_){void 0===C&&(C="#1"),void 0===P&&(P=1),void 0===s&&(s=""),void 0===_&&(_="");var S=r.GetStar(),b=S?"":r.GetBrackets(u),O=y(S?["\\left","\\right"]:b?[b+"l",b+"r"]:["",""],2),T=O[0],D=O[1],I=S?"\\middle":b||"";if(P){for(var k=[],L=k.length;L<P;L++)k.push(r.GetArgument(u));s=t.default.substituteArgs(r,k,s),C=t.default.substituteArgs(r,k,C),_=t.default.substituteArgs(r,k,_)}C=C.replace(/\\delimsize/g,I),r.string=[s,T,p,C,D,A,_,r.string.substr(r.i)].reduce(function($,U){return t.default.addArgs(r,$,U)},""),r.i=0,t.default.checkMaxMacros(r)},DeclarePairedDelimiter:function(r,u){var p=h.default.GetCsNameArgument(r,u),A=r.GetArgument(u),C=r.GetArgument(u);g.MathtoolsUtil.addPairedDelims(r.configuration,p,[A,C])},DeclarePairedDelimiterX:function(r,u){var p=h.default.GetCsNameArgument(r,u),A=h.default.GetArgCount(r,u),C=r.GetArgument(u),P=r.GetArgument(u),s=r.GetArgument(u);g.MathtoolsUtil.addPairedDelims(r.configuration,p,[C,P,s,A])},DeclarePairedDelimiterXPP:function(r,u){var p=h.default.GetCsNameArgument(r,u),A=h.default.GetArgCount(r,u),C=r.GetArgument(u),P=r.GetArgument(u),s=r.GetArgument(u),_=r.GetArgument(u),S=r.GetArgument(u);g.MathtoolsUtil.addPairedDelims(r.configuration,p,[P,s,S,A,C,_])},CenterColon:function(r,u,p,A,C){void 0===A&&(A=!1),void 0===C&&(C=!1);var P=r.options.mathtools,s=r.create("token","mo",{},":");if(p&&(P.centercolon||A)){var _=P["centercolon-offset"];s=r.create("node","mpadded",[s],w({voffset:_,height:"+".concat(_),depth:"-".concat(_)},C?{width:P["thincolon-dw"],lspace:P["thincolon-dx"]}:{}))}r.Push(s)},Relation:function(r,u,p,A){r.options.mathtools["use-unicode"]&&A?r.Push(r.create("token","mo",{texClass:o.TEXCLASS.REL},A)):(p="\\mathrel{"+p.replace(/:/g,"\\MTThinColon").replace(/-/g,"\\mathrel{-}")+"}",r.string=t.default.addArgs(r,p,r.string.substr(r.i)),r.i=0)},NArrow:function(r,u,p,A){r.Push(r.create("node","TeXAtom",[r.create("token","mtext",{},p),r.create("node","mpadded",[r.create("node","mpadded",[r.create("node","menclose",[r.create("node","mspace",[],{height:".2em",depth:0,width:".4em"})],{notation:"updiagonalstrike","data-thickness":".05em","data-padding":0})],{width:0,lspace:"-.5width",voffset:A}),r.create("node","mphantom",[r.create("token","mtext",{},p)])],{width:0,lspace:"-.5width"})],{texClass:o.TEXCLASS.REL}))},SplitFrac:function(r,u,p){var A=r.ParseArg(u),C=r.ParseArg(u);r.Push(r.create("node","mstyle",[r.create("node","mfrac",[r.create("node","mstyle",[A,r.create("token","mi"),r.create("token","mspace",{width:"1em"})],{scriptlevel:0}),r.create("node","mstyle",[r.create("token","mspace",{width:"1em"}),r.create("token","mi"),C],{scriptlevel:0})],{linethickness:0,numalign:"left",denomalign:"right"})],{displaystyle:p,scriptlevel:0}))},XMathStrut:function(r,u){var p=r.GetBrackets(u),A=r.GetArgument(u);A=g.MathtoolsUtil.plusOrMinus(u,A),p=g.MathtoolsUtil.plusOrMinus(u,p||A),r.Push(r.create("node","TeXAtom",[r.create("node","mpadded",[r.create("node","mphantom",[r.create("token","mo",{stretchy:!1},"(")])],{width:0,height:A+"height",depth:p+"depth"})],{texClass:o.TEXCLASS.ORD}))},Prescript:function(r,u){var p=g.MathtoolsUtil.getScript(r,u,"sup"),A=g.MathtoolsUtil.getScript(r,u,"sub"),C=g.MathtoolsUtil.getScript(r,u,"arg");if(d.default.isType(p,"none")&&d.default.isType(A,"none"))r.Push(C);else{var P=r.create("node","mmultiscripts",[C]);d.default.getChildren(P).push(null,null),d.default.appendChildren(P,[r.create("node","mprescripts"),A,p]),P.setProperty("fixPrescript",!0),r.Push(P)}},NewTagForm:function(r,u,p){void 0===p&&(p=!1);var A=r.tags;if(!("mtFormats"in A))throw new l.default("TagsNotMT","%1 can only be used with ams or mathtools tags",u);var C=r.GetArgument(u).trim();if(!C)throw new l.default("InvalidTagFormID","Tag form name can't be empty");var P=r.GetBrackets(u,""),s=r.GetArgument(u),_=r.GetArgument(u);if(!p&&A.mtFormats.has(C))throw new l.default("DuplicateTagForm","Duplicate tag form: %1",C);A.mtFormats.set(C,[s,_,P])},UseTagForm:function(r,u){var p=r.tags;if(!("mtFormats"in p))throw new l.default("TagsNotMT","%1 can only be used with ams or mathtools tags",u);var A=r.GetArgument(u).trim();if(A){if(!p.mtFormats.has(A))throw new l.default("UndefinedTagForm","Undefined tag form: %1",A);p.mtCurrent=p.mtFormats.get(A)}else p.mtCurrent=null},SetOptions:function(r,u){var p,A,C=r.options.mathtools;if(!C["allow-mathtoolsset"])throw new l.default("ForbiddenMathtoolsSet","%1 is disabled",u);var P={};Object.keys(C).forEach(function(T){"pariedDelimiters"!==T&&"tagforms"!==T&&"allow-mathtoolsset"!==T&&(P[T]=1)});var s=r.GetArgument(u),_=t.default.keyvalOptions(s,P,!0);try{for(var S=M(Object.keys(_)),b=S.next();!b.done;b=S.next()){var O=b.value;C[O]=_[O]}}catch(T){p={error:T}}finally{try{b&&!b.done&&(A=S.return)&&A.call(S)}finally{if(p)throw p.error}}},Array:n.default.Array,Macro:n.default.Macro,xArrow:e.AmsMethods.xArrow,HandleRef:e.AmsMethods.HandleRef,AmsEqnArray:e.AmsMethods.AmsEqnArray,MacroWithTemplate:a.default.MacroWithTemplate}},61400:function(B,m,v){var l,w=this&&this.__extends||(l=function(d,o){return(l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(i,f){i.__proto__=f}||function(i,f){for(var h in f)Object.prototype.hasOwnProperty.call(f,h)&&(i[h]=f[h])})(d,o)},function(d,o){if("function"!=typeof o&&null!==o)throw new TypeError("Class extends value "+String(o)+" is not a constructor or null");function i(){this.constructor=d}l(d,o),d.prototype=null===o?Object.create(o):(i.prototype=o.prototype,new i)}),y=this&&this.__values||function(l){var d="function"==typeof Symbol&&Symbol.iterator,o=d&&l[d],i=0;if(o)return o.call(l);if(l&&"number"==typeof l.length)return{next:function(){return l&&i>=l.length&&(l=void 0),{value:l&&l[i++],done:!l}}};throw new TypeError(d?"Object is not iterable.":"Symbol.iterator is not defined.")},M=this&&this.__read||function(l,d){var o="function"==typeof Symbol&&l[Symbol.iterator];if(!o)return l;var f,a,i=o.call(l),h=[];try{for(;(void 0===d||d-- >0)&&!(f=i.next()).done;)h.push(f.value)}catch(g){a={error:g}}finally{try{f&&!f.done&&(o=i.return)&&o.call(i)}finally{if(a)throw a.error}}return h},x=this&&this.__importDefault||function(l){return l&&l.__esModule?l:{default:l}};Object.defineProperty(m,"__esModule",{value:!0}),m.MathtoolsTagFormat=void 0;var t=x(v(6257)),e=v(22450),n=0;m.MathtoolsTagFormat=function c(l,d){var o=d.parseOptions.options.tags;"base"!==o&&l.tags.hasOwnProperty(o)&&e.TagsFactory.add(o,l.tags[o]);var f=function(a){function g(){var r,u,p=a.call(this)||this;p.mtFormats=new Map,p.mtCurrent=null;var A=d.parseOptions.options.mathtools.tagforms;try{for(var C=y(Object.keys(A)),P=C.next();!P.done;P=C.next()){var s=P.value;if(!Array.isArray(A[s])||3!==A[s].length)throw new t.default("InvalidTagFormDef",'The tag form definition for "%1" should be an array fo three strings',s);p.mtFormats.set(s,A[s])}}catch(_){r={error:_}}finally{try{P&&!P.done&&(u=C.return)&&u.call(C)}finally{if(r)throw r.error}}return p}return w(g,a),g.prototype.formatTag=function(r){if(this.mtCurrent){var u=M(this.mtCurrent,3),p=u[0],A=u[1],C=u[2];return C?"".concat(p).concat(C,"{").concat(r,"}").concat(A):"".concat(p).concat(r).concat(A)}return a.prototype.formatTag.call(this,r)},g}(e.TagsFactory.create(d.parseOptions.options.tags).constructor),h="MathtoolsTags-"+ ++n;e.TagsFactory.add(h,f),d.parseOptions.options.tags=h}},84470:function(B,m,v){var w=this&&this.__read||function(o,i){var f="function"==typeof Symbol&&o[Symbol.iterator];if(!f)return o;var a,r,h=f.call(o),g=[];try{for(;(void 0===i||i-- >0)&&!(a=h.next()).done;)g.push(a.value)}catch(u){r={error:u}}finally{try{a&&!a.done&&(f=h.return)&&f.call(h)}finally{if(r)throw r.error}}return g},y=this&&this.__importDefault||function(o){return o&&o.__esModule?o:{default:o}};Object.defineProperty(m,"__esModule",{value:!0}),m.MathtoolsUtil=void 0;var M=v(33012),x=y(v(39194)),t=y(v(55571)),e=y(v(6257)),n=v(95889),c=v(80084),l=v(87491),d=v(41461);m.MathtoolsUtil={setDisplayLevel:function(o,i){if(i){var f=w((0,c.lookup)(i,{"\\displaystyle":[!0,0],"\\textstyle":[!1,0],"\\scriptstyle":[!1,1],"\\scriptscriptstyle":[!1,2]},[null,null]),2),h=f[0],a=f[1];null!==h&&(o.attributes.set("displaystyle",h),o.attributes.set("scriptlevel",a))}},checkAlignment:function(o,i){var f=o.stack.Top();if(f.kind!==M.EqnArrayItem.prototype.kind)throw new e.default("NotInAlignment","%1 can only be used in aligment environments",i);return f},addPairedDelims:function(o,i,f){o.handlers.retrieve(d.PAIREDDELIMS).add(i,new n.Macro(i,l.MathtoolsMethods.PairedDelimiters,f))},spreadLines:function(o,i){if(o.isKind("mtable")){var f=o.attributes.get("rowspacing");if(f){var h=x.default.dimen2em(i);f=f.split(/ /).map(function(a){return x.default.Em(Math.max(0,x.default.dimen2em(a)+h))}).join(" ")}else f=i;o.attributes.set("rowspacing",f)}},plusOrMinus:function(o,i){if(!(i=i.trim()).match(/^[-+]?(?:\d+(?:\.\d*)?|\.\d+)$/))throw new e.default("NotANumber","Argument to %1 is not a number",o);return i.match(/^[-+]/)?i:"+"+i},getScript:function(o,i,f){var h=x.default.trimSpaces(o.GetArgument(i));if(""===h)return o.create("node","none");var a=o.options.mathtools["prescript-".concat(f,"-format")];return a&&(h="".concat(a,"{").concat(h,"}")),new t.default(h,o.stack.env,o.configuration).mml()}}},30203:function(B,m,v){var w=this&&this.__importDefault||function(l){return l&&l.__esModule?l:{default:l}};Object.defineProperty(m,"__esModule",{value:!0}),m.MhchemConfiguration=void 0;var y=v(8890),M=v(82652),x=w(v(6257)),t=w(v(2158)),e=v(86800),n=v(53320),c={};c.Macro=t.default.Macro,c.xArrow=e.AmsMethods.xArrow,c.Machine=function(l,d,o){var f,i=l.GetArgument(d);try{f=n.mhchemParser.toTex(i,o)}catch(h){throw new x.default(h[0],h[1])}l.string=f+l.string.substr(l.i),l.i=0},new M.CommandMap("mhchem",{ce:["Machine","ce"],pu:["Machine","pu"],longrightleftharpoons:["Macro","\\stackrel{\\textstyle{-}\\!\\!{\\rightharpoonup}}{\\smash{{\\leftharpoondown}\\!\\!{-}}}"],longRightleftharpoons:["Macro","\\stackrel{\\textstyle{-}\\!\\!{\\rightharpoonup}}{\\smash{\\leftharpoondown}}"],longLeftrightharpoons:["Macro","\\stackrel{\\textstyle\\vphantom{{-}}{\\rightharpoonup}}{\\smash{{\\leftharpoondown}\\!\\!{-}}}"],longleftrightarrows:["Macro","\\stackrel{\\longrightarrow}{\\smash{\\longleftarrow}\\Rule{0px}{.25em}{0px}}"],tripledash:["Macro","\\vphantom{-}\\raise2mu{\\kern2mu\\tiny\\text{-}\\kern1mu\\text{-}\\kern1mu\\text{-}\\kern2mu}"],xleftrightarrow:["xArrow",8596,6,6],xrightleftharpoons:["xArrow",8652,5,7],xRightleftharpoons:["xArrow",8652,5,7],xLeftrightharpoons:["xArrow",8652,5,7]},c),m.MhchemConfiguration=y.Configuration.create("mhchem",{handler:{macro:["mhchem"]}})},20196:function(B,m,v){var t,w=this&&this.__createBinding||(Object.create?function(i,f,h,a){void 0===a&&(a=h);var g=Object.getOwnPropertyDescriptor(f,h);(!g||("get"in g?!f.__esModule:g.writable||g.configurable))&&(g={enumerable:!0,get:function(){return f[h]}}),Object.defineProperty(i,a,g)}:function(i,f,h,a){void 0===a&&(a=h),i[a]=f[h]}),y=this&&this.__setModuleDefault||(Object.create?function(i,f){Object.defineProperty(i,"default",{enumerable:!0,value:f})}:function(i,f){i.default=f}),M=this&&this.__importStar||function(i){if(i&&i.__esModule)return i;var f={};if(null!=i)for(var h in i)"default"!==h&&Object.prototype.hasOwnProperty.call(i,h)&&w(f,i,h);return y(f,i),f},x=this&&this.__importDefault||function(i){return i&&i.__esModule?i:{default:i}};Object.defineProperty(m,"__esModule",{value:!0}),m.NewcommandConfiguration=void 0;var e=v(8890),n=v(68838),c=x(v(53245));v(73978);var l=x(v(70004)),d=M(v(82652));m.NewcommandConfiguration=e.Configuration.create("newcommand",{handler:{macro:["Newcommand-macros"]},items:(t={},t[n.BeginEnvItem.prototype.kind]=n.BeginEnvItem,t),options:{maxMacros:1e3},init:function(i){new d.DelimiterMap(c.default.NEW_DELIMITER,l.default.delimiter,{}),new d.CommandMap(c.default.NEW_COMMAND,{},{}),new d.EnvironmentMap(c.default.NEW_ENVIRONMENT,l.default.environment,{},{}),i.append(e.Configuration.local({handler:{character:[],delimiter:[c.default.NEW_DELIMITER],macro:[c.default.NEW_DELIMITER,c.default.NEW_COMMAND],environment:[c.default.NEW_ENVIRONMENT]},priority:-1}))}})},68838:function(B,m,v){var e,w=this&&this.__extends||(e=function(n,c){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(l,d){l.__proto__=d}||function(l,d){for(var o in d)Object.prototype.hasOwnProperty.call(d,o)&&(l[o]=d[o])})(n,c)},function(n,c){if("function"!=typeof c&&null!==c)throw new TypeError("Class extends value "+String(c)+" is not a constructor or null");function l(){this.constructor=n}e(n,c),n.prototype=null===c?Object.create(c):(l.prototype=c.prototype,new l)}),y=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(m,"__esModule",{value:!0}),m.BeginEnvItem=void 0;var M=y(v(6257)),t=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return w(n,e),Object.defineProperty(n.prototype,"kind",{get:function(){return"beginEnv"},enumerable:!1,configurable:!0}),Object.defineProperty(n.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),n.prototype.checkItem=function(c){if(c.isKind("end")){if(c.getName()!==this.getName())throw new M.default("EnvBadEnd","\\begin{%1} ended with \\end{%2}",this.getName(),c.getName());return[[this.factory.create("mml",this.toMml())],!0]}if(c.isKind("stop"))throw new M.default("EnvMissingEnd","Missing \\end{%1}",this.getName());return e.prototype.checkItem.call(this,c)},n}(v(90117).BaseItem);m.BeginEnvItem=t},73978:function(B,m,v){var w=this&&this.__importDefault||function(x){return x&&x.__esModule?x:{default:x}};Object.defineProperty(m,"__esModule",{value:!0});var y=w(v(8362));new(v(82652).CommandMap)("Newcommand-macros",{newcommand:"NewCommand",renewcommand:"NewCommand",newenvironment:"NewEnvironment",renewenvironment:"NewEnvironment",def:"MacroDef",let:"Let"},y.default)},8362:function(B,m,v){var w=this&&this.__createBinding||(Object.create?function(o,i,f,h){void 0===h&&(h=f);var a=Object.getOwnPropertyDescriptor(i,f);(!a||("get"in a?!i.__esModule:a.writable||a.configurable))&&(a={enumerable:!0,get:function(){return i[f]}}),Object.defineProperty(o,h,a)}:function(o,i,f,h){void 0===h&&(h=f),o[h]=i[f]}),y=this&&this.__setModuleDefault||(Object.create?function(o,i){Object.defineProperty(o,"default",{enumerable:!0,value:i})}:function(o,i){o.default=i}),M=this&&this.__importStar||function(o){if(o&&o.__esModule)return o;var i={};if(null!=o)for(var f in o)"default"!==f&&Object.prototype.hasOwnProperty.call(o,f)&&w(i,o,f);return y(i,o),i},x=this&&this.__importDefault||function(o){return o&&o.__esModule?o:{default:o}};Object.defineProperty(m,"__esModule",{value:!0});var t=x(v(6257)),e=M(v(82652)),n=x(v(2158)),c=x(v(39194)),l=x(v(53245)),d={NewCommand:function(o,i){var f=l.default.GetCsNameArgument(o,i),h=l.default.GetArgCount(o,i),a=o.GetBrackets(i),g=o.GetArgument(i);l.default.addMacro(o,f,d.Macro,[g,h,a])},NewEnvironment:function(o,i){var f=c.default.trimSpaces(o.GetArgument(i)),h=l.default.GetArgCount(o,i),a=o.GetBrackets(i),g=o.GetArgument(i),r=o.GetArgument(i);l.default.addEnvironment(o,f,d.BeginEnv,[!0,g,r,h,a])},MacroDef:function(o,i){var f=l.default.GetCSname(o,i),h=l.default.GetTemplate(o,i,"\\"+f),a=o.GetArgument(i);h instanceof Array?l.default.addMacro(o,f,d.MacroWithTemplate,[a].concat(h)):l.default.addMacro(o,f,d.Macro,[a,h])},Let:function(o,i){var f=l.default.GetCSname(o,i),h=o.GetNext();"="===h&&(o.i++,h=o.GetNext());var a=o.configuration.handlers;if("\\"!==h){o.i++;var C=a.get("delimiter").lookup(h);C?l.default.addDelimiter(o,"\\"+f,C.char,C.attributes):l.default.addMacro(o,f,d.Macro,[h])}else{i=l.default.GetCSname(o,i);var g=a.get("delimiter").lookup("\\"+i);if(g)return void l.default.addDelimiter(o,"\\"+f,g.char,g.attributes);var r=a.get("macro").applicable(i);if(!r)return;if(r instanceof e.MacroMap){var u=r.lookup(i);return void l.default.addMacro(o,f,u.func,u.args,u.symbol)}g=r.lookup(i);var p=l.default.disassembleSymbol(f,g);l.default.addMacro(o,f,function(P,s){for(var _=[],S=2;S<arguments.length;S++)_[S-2]=arguments[S];var b=l.default.assembleSymbol(_);return r.parser(P,b)},p)}},MacroWithTemplate:function(o,i,f,h){for(var a=[],g=4;g<arguments.length;g++)a[g-4]=arguments[g];var r=parseInt(h,10);if(r){var u=[];if(o.GetNext(),a[0]&&!l.default.MatchParam(o,a[0]))throw new t.default("MismatchUseDef","Use of %1 doesn't match its definition",i);for(var p=0;p<r;p++)u.push(l.default.GetParameter(o,i,a[p+1]));f=c.default.substituteArgs(o,u,f)}o.string=c.default.addArgs(o,f,o.string.slice(o.i)),o.i=0,c.default.checkMaxMacros(o)},BeginEnv:function(o,i,f,h,a,g){if(i.getProperty("end")&&o.stack.env.closing===i.getName()){delete o.stack.env.closing;var r=o.string.slice(o.i);return o.string=h,o.i=0,o.Parse(),o.string=r,o.i=0,o.itemFactory.create("end").setProperty("name",i.getName())}if(a){var u=[];if(null!=g){var p=o.GetBrackets("\\begin{"+i.getName()+"}");u.push(p??g)}for(var A=u.length;A<a;A++)u.push(o.GetArgument("\\begin{"+i.getName()+"}"));f=c.default.substituteArgs(o,u,f),h=c.default.substituteArgs(o,[],h)}return o.string=c.default.addArgs(o,f,o.string.slice(o.i)),o.i=0,o.itemFactory.create("beginEnv").setProperty("name",i.getName())}};d.Macro=n.default.Macro,m.default=d},53245:function(B,m,v){var w=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(m,"__esModule",{value:!0});var t,y=w(v(39194)),M=w(v(6257)),x=v(95889);(function(e){function h(u,p){return u.string.substr(u.i,p.length)!==p||p.match(/\\[a-z]+$/i)&&u.string.charAt(u.i+p.length).match(/[a-z]/i)?0:(u.i+=p.length,1)}e.disassembleSymbol=function n(u,p){var A=[u,p.char];if(p.attributes)for(var C in p.attributes)A.push(C),A.push(p.attributes[C]);return A},e.assembleSymbol=function c(u){for(var p=u[0],A=u[1],C={},P=2;P<u.length;P+=2)C[u[P]]=u[P+1];return new x.Symbol(p,A,C)},e.GetCSname=function l(u,p){if("\\"!==u.GetNext())throw new M.default("MissingCS","%1 must be followed by a control sequence",p);return y.default.trimSpaces(u.GetArgument(p)).substr(1)},e.GetCsNameArgument=function d(u,p){var A=y.default.trimSpaces(u.GetArgument(p));if("\\"===A.charAt(0)&&(A=A.substr(1)),!A.match(/^(.|[a-z]+)$/i))throw new M.default("IllegalControlSequenceName","Illegal control sequence name for %1",p);return A},e.GetArgCount=function o(u,p){var A=u.GetBrackets(p);if(A&&!(A=y.default.trimSpaces(A)).match(/^[0-9]+$/))throw new M.default("IllegalParamNumber","Illegal number of parameters specified in %1",p);return A},e.GetTemplate=function i(u,p,A){for(var C=u.GetNext(),P=[],s=0,_=u.i;u.i<u.string.length;){if("#"===(C=u.GetNext())){if(_!==u.i&&(P[s]=u.string.substr(_,u.i-_)),!(C=u.string.charAt(++u.i)).match(/^[1-9]$/))throw new M.default("CantUseHash2","Illegal use of # in template for %1",A);if(parseInt(C)!==++s)throw new M.default("SequentialParam","Parameters for %1 must be numbered sequentially",A);_=u.i+1}else if("{"===C)return _!==u.i&&(P[s]=u.string.substr(_,u.i-_)),P.length>0?[s.toString()].concat(P):s;u.i++}throw new M.default("MissingReplacementString","Missing replacement string for definition of %1",p)},e.GetParameter=function f(u,p,A){if(null==A)return u.GetArgument(p);for(var C=u.i,P=0,s=0;u.i<u.string.length;){var _=u.string.charAt(u.i);if("{"===_)u.i===C&&(s=1),u.GetArgument(p),P=u.i-C;else{if(h(u,A))return s&&(C++,P-=2),u.string.substr(C,P);if("\\"===_){u.i++,P++,s=0;var S=u.string.substr(u.i).match(/[a-z]+|./i);S&&(u.i+=S[0].length,P=u.i-C)}else u.i++,P++,s=0}}throw new M.default("RunawayArgument","Runaway argument for %1?",p)},e.MatchParam=h,e.addDelimiter=function a(u,p,A,C){u.configuration.handlers.retrieve(e.NEW_DELIMITER).add(p,new x.Symbol(p,A,C))},e.addMacro=function g(u,p,A,C,P){void 0===P&&(P=""),u.configuration.handlers.retrieve(e.NEW_COMMAND).add(p,new x.Macro(P||p,A,C))},e.addEnvironment=function r(u,p,A,C){u.configuration.handlers.retrieve(e.NEW_ENVIRONMENT).add(p,new x.Macro(p,A,C))},e.NEW_DELIMITER="new-Delimiter",e.NEW_COMMAND="new-Command",e.NEW_ENVIRONMENT="new-Environment"})(t||(t={})),m.default=t},3861:(B,m,v)=>{Object.defineProperty(m,"__esModule",{value:!0}),m.NoErrorsConfiguration=void 0;var w=v(8890);m.NoErrorsConfiguration=w.Configuration.create("noerrors",{nodes:{error:function y(M,x,t,e){var n=M.create("token","mtext",{},e.replace(/\n/g," "));return M.create("node","merror",[n],{"data-mjx-error":x,title:x})}}})},97370:function(B,m,v){var w=this&&this.__values||function(x){var t="function"==typeof Symbol&&Symbol.iterator,e=t&&x[t],n=0;if(e)return e.call(x);if(x&&"number"==typeof x.length)return{next:function(){return x&&n>=x.length&&(x=void 0),{value:x&&x[n++],done:!x}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")};Object.defineProperty(m,"__esModule",{value:!0}),m.NoUndefinedConfiguration=void 0;var y=v(8890);m.NoUndefinedConfiguration=y.Configuration.create("noundefined",{fallback:{macro:function M(x,t){var e,n,c=x.create("text","\\"+t),l=x.options.noundefined||{},d={};try{for(var o=w(["color","background","size"]),i=o.next();!i.done;i=o.next()){var f=i.value;l[f]&&(d["math"+f]=l[f])}}catch(h){e={error:h}}finally{try{i&&!i.done&&(n=o.return)&&n.call(o)}finally{if(e)throw e.error}}x.Push(x.create("node","mtext",[],d,c))}},options:{noundefined:{color:"red",background:"",size:""}},priority:3})},31097:(B,m,v)=>{var w;Object.defineProperty(m,"__esModule",{value:!0}),m.PhysicsConfiguration=void 0;var y=v(8890),M=v(12745);v(87725),m.PhysicsConfiguration=y.Configuration.create("physics",{handler:{macro:["Physics-automatic-bracing-macros","Physics-vector-macros","Physics-vector-mo","Physics-vector-mi","Physics-derivative-macros","Physics-expressions-macros","Physics-quick-quad-macros","Physics-bra-ket-macros","Physics-matrix-macros"],character:["Physics-characters"],environment:["Physics-aux-envs"]},items:(w={},w[M.AutoOpen.prototype.kind]=M.AutoOpen,w),options:{physics:{italicdiff:!1,arrowdel:!1}}})},12745:function(B,m,v){var c,w=this&&this.__extends||(c=function(l,d){return(c=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(o,i){o.__proto__=i}||function(o,i){for(var f in i)Object.prototype.hasOwnProperty.call(i,f)&&(o[f]=i[f])})(l,d)},function(l,d){if("function"!=typeof d&&null!==d)throw new TypeError("Class extends value "+String(d)+" is not a constructor or null");function o(){this.constructor=l}c(l,d),l.prototype=null===d?Object.create(d):(o.prototype=d.prototype,new o)}),y=this&&this.__importDefault||function(c){return c&&c.__esModule?c:{default:c}};Object.defineProperty(m,"__esModule",{value:!0}),m.AutoOpen=void 0;var M=v(90117),x=y(v(39194)),t=y(v(60909)),e=y(v(55571)),n=function(c){function l(){var d=null!==c&&c.apply(this,arguments)||this;return d.openCount=0,d}return w(l,c),Object.defineProperty(l.prototype,"kind",{get:function(){return"auto open"},enumerable:!1,configurable:!0}),Object.defineProperty(l.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),l.prototype.toMml=function(){var d=this.factory.configuration.parser,o=this.getProperty("right");if(this.getProperty("smash")){var i=c.prototype.toMml.call(this),f=d.create("node","mpadded",[i],{height:0,depth:0});this.Clear(),this.Push(d.create("node","TeXAtom",[f]))}o&&this.Push(new e.default(o,d.stack.env,d.configuration).mml());var h=x.default.fenced(this.factory.configuration,this.getProperty("open"),c.prototype.toMml.call(this),this.getProperty("close"),this.getProperty("big"));return t.default.removeProperties(h,"open","close","texClass"),h},l.prototype.checkItem=function(d){if(d.isKind("mml")&&1===d.Size()){var o=d.toMml();o.isKind("mo")&&o.getText()===this.getProperty("open")&&this.openCount++}var i=d.getProperty("autoclose");return i&&i===this.getProperty("close")&&!this.openCount--?this.getProperty("ignore")?(this.Clear(),[[],!0]):[[this.toMml()],!0]:c.prototype.checkItem.call(this,d)},l.errors=Object.assign(Object.create(M.BaseItem.errors),{stop:["ExtraOrMissingDelims","Extra open or missing close delimiter"]}),l}(M.BaseItem);m.AutoOpen=n},87725:function(B,m,v){var w=this&&this.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(m,"__esModule",{value:!0});var y=v(82652),M=w(v(98972)),x=v(60742),t=w(v(70004)),e=v(91585);new y.CommandMap("Physics-automatic-bracing-macros",{quantity:"Quantity",qty:"Quantity",pqty:["Quantity","(",")",!0],bqty:["Quantity","[","]",!0],vqty:["Quantity","|","|",!0],Bqty:["Quantity","\\{","\\}",!0],absolutevalue:["Quantity","|","|",!0],abs:["Quantity","|","|",!0],norm:["Quantity","\\|","\\|",!0],evaluated:"Eval",eval:"Eval",order:["Quantity","(",")",!0,"O",x.TexConstant.Variant.CALLIGRAPHIC],commutator:"Commutator",comm:"Commutator",anticommutator:["Commutator","\\{","\\}"],acomm:["Commutator","\\{","\\}"],poissonbracket:["Commutator","\\{","\\}"],pb:["Commutator","\\{","\\}"]},M.default),new y.CharacterMap("Physics-vector-mo",t.default.mathchar0mo,{dotproduct:["\u22c5",{mathvariant:x.TexConstant.Variant.BOLD}],vdot:["\u22c5",{mathvariant:x.TexConstant.Variant.BOLD}],crossproduct:"\xd7",cross:"\xd7",cp:"\xd7",gradientnabla:["\u2207",{mathvariant:x.TexConstant.Variant.BOLD}]}),new y.CharacterMap("Physics-vector-mi",t.default.mathchar0mi,{real:["\u211c",{mathvariant:x.TexConstant.Variant.NORMAL}],imaginary:["\u2111",{mathvariant:x.TexConstant.Variant.NORMAL}]}),new y.CommandMap("Physics-vector-macros",{vnabla:"Vnabla",vectorbold:"VectorBold",vb:"VectorBold",vectorarrow:["StarMacro",1,"\\vec{\\vb","{#1}}"],va:["StarMacro",1,"\\vec{\\vb","{#1}}"],vectorunit:["StarMacro",1,"\\hat{\\vb","{#1}}"],vu:["StarMacro",1,"\\hat{\\vb","{#1}}"],gradient:["OperatorApplication","\\vnabla","(","["],grad:["OperatorApplication","\\vnabla","(","["],divergence:["VectorOperator","\\vnabla\\vdot","(","["],div:["VectorOperator","\\vnabla\\vdot","(","["],curl:["VectorOperator","\\vnabla\\crossproduct","(","["],laplacian:["OperatorApplication","\\nabla^2","(","["]},M.default),new y.CommandMap("Physics-expressions-macros",{sin:"Expression",sinh:"Expression",arcsin:"Expression",asin:"Expression",cos:"Expression",cosh:"Expression",arccos:"Expression",acos:"Expression",tan:"Expression",tanh:"Expression",arctan:"Expression",atan:"Expression",csc:"Expression",csch:"Expression",arccsc:"Expression",acsc:"Expression",sec:"Expression",sech:"Expression",arcsec:"Expression",asec:"Expression",cot:"Expression",coth:"Expression",arccot:"Expression",acot:"Expression",exp:["Expression",!1],log:"Expression",ln:"Expression",det:["Expression",!1],Pr:["Expression",!1],tr:["Expression",!1],trace:["Expression",!1,"tr"],Tr:["Expression",!1],Trace:["Expression",!1,"Tr"],rank:"NamedFn",erf:["Expression",!1],Residue:["Macro","\\mathrm{Res}"],Res:["OperatorApplication","\\Residue","(","[","{"],principalvalue:["OperatorApplication","{\\cal P}"],pv:["OperatorApplication","{\\cal P}"],PV:["OperatorApplication","{\\rm P.V.}"],Re:["OperatorApplication","\\mathrm{Re}","{"],Im:["OperatorApplication","\\mathrm{Im}","{"],sine:["NamedFn","sin"],hypsine:["NamedFn","sinh"],arcsine:["NamedFn","arcsin"],asine:["NamedFn","asin"],cosine:["NamedFn","cos"],hypcosine:["NamedFn","cosh"],arccosine:["NamedFn","arccos"],acosine:["NamedFn","acos"],tangent:["NamedFn","tan"],hyptangent:["NamedFn","tanh"],arctangent:["NamedFn","arctan"],atangent:["NamedFn","atan"],cosecant:["NamedFn","csc"],hypcosecant:["NamedFn","csch"],arccosecant:["NamedFn","arccsc"],acosecant:["NamedFn","acsc"],secant:["NamedFn","sec"],hypsecant:["NamedFn","sech"],arcsecant:["NamedFn","arcsec"],asecant:["NamedFn","asec"],cotangent:["NamedFn","cot"],hypcotangent:["NamedFn","coth"],arccotangent:["NamedFn","arccot"],acotangent:["NamedFn","acot"],exponential:["NamedFn","exp"],logarithm:["NamedFn","log"],naturallogarithm:["NamedFn","ln"],determinant:["NamedFn","det"],Probability:["NamedFn","Pr"]},M.default),new y.CommandMap("Physics-quick-quad-macros",{qqtext:"Qqtext",qq:"Qqtext",qcomma:["Macro","\\qqtext*{,}"],qc:["Macro","\\qqtext*{,}"],qcc:["Qqtext","c.c."],qif:["Qqtext","if"],qthen:["Qqtext","then"],qelse:["Qqtext","else"],qotherwise:["Qqtext","otherwise"],qunless:["Qqtext","unless"],qgiven:["Qqtext","given"],qusing:["Qqtext","using"],qassume:["Qqtext","assume"],qsince:["Qqtext","since"],qlet:["Qqtext","let"],qfor:["Qqtext","for"],qall:["Qqtext","all"],qeven:["Qqtext","even"],qodd:["Qqtext","odd"],qinteger:["Qqtext","integer"],qand:["Qqtext","and"],qor:["Qqtext","or"],qas:["Qqtext","as"],qin:["Qqtext","in"]},M.default),new y.CommandMap("Physics-derivative-macros",{diffd:"DiffD",flatfrac:["Macro","\\left.#1\\middle/#2\\right.",2],differential:["Differential","\\diffd"],dd:["Differential","\\diffd"],variation:["Differential","\\delta"],var:["Differential","\\delta"],derivative:["Derivative",2,"\\diffd"],dv:["Derivative",2,"\\diffd"],partialderivative:["Derivative",3,"\\partial"],pderivative:["Derivative",3,"\\partial"],pdv:["Derivative",3,"\\partial"],functionalderivative:["Derivative",2,"\\delta"],fderivative:["Derivative",2,"\\delta"],fdv:["Derivative",2,"\\delta"]},M.default),new y.CommandMap("Physics-bra-ket-macros",{bra:"Bra",ket:"Ket",innerproduct:"BraKet",ip:"BraKet",braket:"BraKet",outerproduct:"KetBra",dyad:"KetBra",ketbra:"KetBra",op:"KetBra",expectationvalue:"Expectation",expval:"Expectation",ev:"Expectation",matrixelement:"MatrixElement",matrixel:"MatrixElement",mel:"MatrixElement"},M.default),new y.CommandMap("Physics-matrix-macros",{matrixquantity:"MatrixQuantity",mqty:"MatrixQuantity",pmqty:["Macro","\\mqty(#1)",1],Pmqty:["Macro","\\mqty*(#1)",1],bmqty:["Macro","\\mqty[#1]",1],vmqty:["Macro","\\mqty|#1|",1],smallmatrixquantity:["MatrixQuantity",!0],smqty:["MatrixQuantity",!0],spmqty:["Macro","\\smqty(#1)",1],sPmqty:["Macro","\\smqty*(#1)",1],sbmqty:["Macro","\\smqty[#1]",1],svmqty:["Macro","\\smqty|#1|",1],matrixdeterminant:["Macro","\\vmqty{#1}",1],mdet:["Macro","\\vmqty{#1}",1],smdet:["Macro","\\svmqty{#1}",1],identitymatrix:"IdentityMatrix",imat:"IdentityMatrix",xmatrix:"XMatrix",xmat:"XMatrix",zeromatrix:["Macro","\\xmat{0}{#1}{#2}",2],zmat:["Macro","\\xmat{0}{#1}{#2}",2],paulimatrix:"PauliMatrix",pmat:"PauliMatrix",diagonalmatrix:"DiagonalMatrix",dmat:"DiagonalMatrix",antidiagonalmatrix:["DiagonalMatrix",!0],admat:["DiagonalMatrix",!0]},M.default),new y.EnvironmentMap("Physics-aux-envs",t.default.environment,{smallmatrix:["Array",null,null,null,"c","0.333em",".2em","S",1]},M.default),new y.MacroMap("Physics-characters",{"|":["AutoClose",e.TEXCLASS.ORD],")":"AutoClose","]":"AutoClose"},M.default)},98972:function(B,m,v){var w=this&&this.__read||function(s,_){var S="function"==typeof Symbol&&s[Symbol.iterator];if(!S)return s;var O,D,b=S.call(s),T=[];try{for(;(void 0===_||_-- >0)&&!(O=b.next()).done;)T.push(O.value)}catch(I){D={error:I}}finally{try{O&&!O.done&&(S=b.return)&&S.call(b)}finally{if(D)throw D.error}}return T},y=this&&this.__importDefault||function(s){return s&&s.__esModule?s:{default:s}};Object.defineProperty(m,"__esModule",{value:!0});var M=y(v(2158)),x=y(v(55571)),t=y(v(6257)),e=v(91585),n=y(v(39194)),c=y(v(60909)),l=v(42023),d={},o={"(":")","[":"]","{":"}","|":"|"},i=/^(b|B)i(g{1,2})$/;d.Quantity=function(s,_,S,b,O,T,D){void 0===S&&(S="("),void 0===b&&(b=")"),void 0===O&&(O=!1),void 0===T&&(T=""),void 0===D&&(D="");var I=!!O&&s.GetStar(),k=s.GetNext(),L=s.i,$=null;if("\\"===k){if(s.i++,!($=s.GetCS()).match(i)){var U=s.create("node","mrow");return s.Push(n.default.fenced(s.configuration,S,U,b)),void(s.i=L)}k=s.GetNext()}var z=o[k];if(O&&"{"!==k)throw new t.default("MissingArgFor","Missing argument for %1",s.currentCS);if(!z){U=s.create("node","mrow");return s.Push(n.default.fenced(s.configuration,S,U,b)),void(s.i=L)}if(T){var H=s.create("token","mi",{texClass:e.TEXCLASS.OP},T);D&&c.default.setAttribute(H,"mathvariant",D),s.Push(s.itemFactory.create("fn",H))}if("{"===k){var E=s.GetArgument(_);return k=O?S:"\\{",z=O?b:"\\}",E=I?k+" "+E+" "+z:$?"\\"+$+"l"+k+" "+E+" \\"+$+"r"+z:"\\left"+k+" "+E+" \\right"+z,void s.Push(new x.default(E,s.stack.env,s.configuration).mml())}O&&(k=S,z=b),s.i++,s.Push(s.itemFactory.create("auto open").setProperties({open:k,close:z,big:$}))},d.Eval=function(s,_){var S=s.GetStar(),b=s.GetNext();if("{"!==b){if("("===b||"["===b)return s.i++,void s.Push(s.itemFactory.create("auto open").setProperties({open:b,close:"|",smash:S,right:"\\vphantom{\\int}"}));throw new t.default("MissingArgFor","Missing argument for %1",s.currentCS)}var O=s.GetArgument(_),T="\\left. "+(S?"\\smash{"+O+"}":O)+" \\vphantom{\\int}\\right|";s.string=s.string.slice(0,s.i)+T+s.string.slice(s.i)},d.Commutator=function(s,_,S,b){void 0===S&&(S="["),void 0===b&&(b="]");var O=s.GetStar(),T=s.GetNext(),D=null;if("\\"===T){if(s.i++,!(D=s.GetCS()).match(i))throw new t.default("MissingArgFor","Missing argument for %1",s.currentCS);T=s.GetNext()}if("{"!==T)throw new t.default("MissingArgFor","Missing argument for %1",s.currentCS);var L=s.GetArgument(_)+","+s.GetArgument(_);L=O?S+" "+L+" "+b:D?"\\"+D+"l"+S+" "+L+" \\"+D+"r"+b:"\\left"+S+" "+L+" \\right"+b,s.Push(new x.default(L,s.stack.env,s.configuration).mml())};var f=[65,90],h=[97,122],a=[913,937],g=[945,969],r=[48,57];function u(s,_){return s>=_[0]&&s<=_[1]}function p(s,_,S,b){var O=s.configuration.parser,T=l.NodeFactory.createToken(s,_,S,b),D=b.codePointAt(0);return 1===b.length&&!O.stack.env.font&&O.stack.env.vectorFont&&(u(D,f)||u(D,h)||u(D,a)||u(D,r)||u(D,g)&&O.stack.env.vectorStar||c.default.getAttribute(T,"accent"))&&c.default.setAttribute(T,"mathvariant",O.stack.env.vectorFont),T}d.VectorBold=function(s,_){var S=s.GetStar(),b=s.GetArgument(_),O=s.configuration.nodeFactory.get("token"),T=s.stack.env.font;delete s.stack.env.font,s.configuration.nodeFactory.set("token",p),s.stack.env.vectorFont=S?"bold-italic":"bold",s.stack.env.vectorStar=S;var D=new x.default(b,s.stack.env,s.configuration).mml();T&&(s.stack.env.font=T),delete s.stack.env.vectorFont,delete s.stack.env.vectorStar,s.configuration.nodeFactory.set("token",O),s.Push(D)},d.StarMacro=function(s,_,S){for(var b=[],O=3;O<arguments.length;O++)b[O-3]=arguments[O];var T=s.GetStar(),D=[];if(S)for(var I=D.length;I<S;I++)D.push(s.GetArgument(_));var k=b.join(T?"*":"");k=n.default.substituteArgs(s,D,k),s.string=n.default.addArgs(s,k,s.string.slice(s.i)),s.i=0,n.default.checkMaxMacros(s)};var A=function(s,_,S,b,O){var T=new x.default(b,s.stack.env,s.configuration).mml();s.Push(s.itemFactory.create(_,T));var D=s.GetNext(),I=o[D];if(I){var U=-1!==O.indexOf(D);if("{"===D){var z=(U?"\\left\\{":"")+" "+s.GetArgument(S)+" "+(U?"\\right\\}":"");return s.string=z+s.string.slice(s.i),void(s.i=0)}!U||(s.i++,s.Push(s.itemFactory.create("auto open").setProperties({open:D,close:I})))}};function C(s,_,S){var b=w(s,3),O=b[0],T=b[1],D=b[2];return _&&S?"\\left\\langle{".concat(O,"}\\middle\\vert{").concat(T,"}\\middle\\vert{").concat(D,"}\\right\\rangle"):_?"\\langle{".concat(O,"}\\vert{").concat(T,"}\\vert{").concat(D,"}\\rangle"):"\\left\\langle{".concat(O,"}\\right\\vert{").concat(T,"}\\left\\vert{").concat(D,"}\\right\\rangle")}d.OperatorApplication=function(s,_,S){for(var b=[],O=3;O<arguments.length;O++)b[O-3]=arguments[O];A(s,"fn",_,S,b)},d.VectorOperator=function(s,_,S){for(var b=[],O=3;O<arguments.length;O++)b[O-3]=arguments[O];A(s,"mml",_,S,b)},d.Expression=function(s,_,S,b){void 0===S&&(S=!0),void 0===b&&(b=""),b=b||_.slice(1);var O=S?s.GetBrackets(_):null,T=s.create("token","mi",{texClass:e.TEXCLASS.OP},b);if(O){var D=new x.default(O,s.stack.env,s.configuration).mml();T=s.create("node","msup",[T,D])}s.Push(s.itemFactory.create("fn",T)),"("===s.GetNext()&&(s.i++,s.Push(s.itemFactory.create("auto open").setProperties({open:"(",close:")"})))},d.Qqtext=function(s,_,S){var T=(s.GetStar()?"":"\\quad")+"\\text{"+(S||s.GetArgument(_))+"}\\quad ";s.string=s.string.slice(0,s.i)+T+s.string.slice(s.i)},d.Differential=function(s,_,S){var b=s.GetBrackets(_),O=null!=b?"^{"+b+"}":" ",T="("===s.GetNext(),D="{"===s.GetNext(),I=S+O;if(T||D)if(D){I+=s.GetArgument(_);k=new x.default(I,s.stack.env,s.configuration).mml();s.Push(s.create("node","TeXAtom",[k],{texClass:e.TEXCLASS.OP}))}else s.Push(new x.default(I,s.stack.env,s.configuration).mml()),s.i++,s.Push(s.itemFactory.create("auto open").setProperties({open:"(",close:")"}));else{I+=s.GetArgument(_,!0)||"";var k=new x.default(I,s.stack.env,s.configuration).mml();s.Push(k)}},d.Derivative=function(s,_,S,b){var O=s.GetStar(),T=s.GetBrackets(_),D=1,I=[];for(I.push(s.GetArgument(_));"{"===s.GetNext()&&D<S;)I.push(s.GetArgument(_)),D++;var k=!1,L=" ",$=" ";S>2&&I.length>2?(L="^{"+(I.length-1)+"}",k=!0):null!=T&&(S>2&&I.length>1&&(k=!0),$=L="^{"+T+"}");for(var U=O?"\\flatfrac":"\\frac",z=I.length>1?I[0]:"",H=I.length>1?I[1]:I[0],E="",j=2,G=void 0;G=I[j];j++)E+=b+" "+G;var N=U+"{"+b+L+z+"}{"+b+" "+H+$+" "+E+"}";s.Push(new x.default(N,s.stack.env,s.configuration).mml()),"("===s.GetNext()&&(s.i++,s.Push(s.itemFactory.create("auto open").setProperties({open:"(",close:")",ignore:k})))},d.Bra=function(s,_){var S=s.GetStar(),b=s.GetArgument(_),O="",T=!1,D=!1;if("\\"===s.GetNext()){var I=s.i;s.i++;var k=s.GetCS(),L=s.lookup("macro",k);L&&"ket"===L.symbol?(T=!0,I=s.i,D=s.GetStar(),"{"===s.GetNext()?O=s.GetArgument(k,!0):(s.i=I,D=!1)):s.i=I}var $="";$=T?S||D?"\\langle{".concat(b,"}\\vert{").concat(O,"}\\rangle"):"\\left\\langle{".concat(b,"}\\middle\\vert{").concat(O,"}\\right\\rangle"):S||D?"\\langle{".concat(b,"}\\vert"):"\\left\\langle{".concat(b,"}\\right\\vert{").concat(O,"}"),s.Push(new x.default($,s.stack.env,s.configuration).mml())},d.Ket=function(s,_){var S=s.GetStar(),b=s.GetArgument(_),O=S?"\\vert{".concat(b,"}\\rangle"):"\\left\\vert{".concat(b,"}\\right\\rangle");s.Push(new x.default(O,s.stack.env,s.configuration).mml())},d.BraKet=function(s,_){var S=s.GetStar(),b=s.GetArgument(_),O=null;"{"===s.GetNext()&&(O=s.GetArgument(_,!0));var T="";T=null==O?S?"\\langle{".concat(b,"}\\vert{").concat(b,"}\\rangle"):"\\left\\langle{".concat(b,"}\\middle\\vert{").concat(b,"}\\right\\rangle"):S?"\\langle{".concat(b,"}\\vert{").concat(O,"}\\rangle"):"\\left\\langle{".concat(b,"}\\middle\\vert{").concat(O,"}\\right\\rangle"),s.Push(new x.default(T,s.stack.env,s.configuration).mml())},d.KetBra=function(s,_){var S=s.GetStar(),b=s.GetArgument(_),O=null;"{"===s.GetNext()&&(O=s.GetArgument(_,!0));var T="";T=null==O?S?"\\vert{".concat(b,"}\\rangle\\!\\langle{").concat(b,"}\\vert"):"\\left\\vert{".concat(b,"}\\middle\\rangle\\!\\middle\\langle{").concat(b,"}\\right\\vert"):S?"\\vert{".concat(b,"}\\rangle\\!\\langle{").concat(O,"}\\vert"):"\\left\\vert{".concat(b,"}\\middle\\rangle\\!\\middle\\langle{").concat(O,"}\\right\\vert"),s.Push(new x.default(T,s.stack.env,s.configuration).mml())},d.Expectation=function(s,_){var S=s.GetStar(),b=S&&s.GetStar(),O=s.GetArgument(_),T=null;"{"===s.GetNext()&&(T=s.GetArgument(_,!0));var D=O&&T?C([T,O,T],S,b):S?"\\langle {".concat(O,"} \\rangle"):"\\left\\langle {".concat(O,"} \\right\\rangle");s.Push(new x.default(D,s.stack.env,s.configuration).mml())},d.MatrixElement=function(s,_){var S=s.GetStar(),b=S&&s.GetStar(),I=C([s.GetArgument(_),s.GetArgument(_),s.GetArgument(_)],S,b);s.Push(new x.default(I,s.stack.env,s.configuration).mml())},d.MatrixQuantity=function(s,_,S){var b=s.GetStar(),T=S?"smallmatrix":"array",D="",I="",k="";switch(s.GetNext()){case"{":D=s.GetArgument(_);break;case"(":s.i++,I=b?"\\lgroup":"(",k=b?"\\rgroup":")",D=s.GetUpTo(_,")");break;case"[":s.i++,I="[",k="]",D=s.GetUpTo(_,"]");break;case"|":s.i++,I="|",k="|",D=s.GetUpTo(_,"|");break;default:I="(",k=")"}var L=(I?"\\left":"")+I+"\\begin{"+T+"}{} "+D+"\\end{"+T+"}"+(I?"\\right":"")+k;s.Push(new x.default(L,s.stack.env,s.configuration).mml())},d.IdentityMatrix=function(s,_){var S=s.GetArgument(_),b=parseInt(S,10);if(isNaN(b))throw new t.default("InvalidNumber","Invalid number");if(b<=1)return s.string="1"+s.string.slice(s.i),void(s.i=0);for(var O=Array(b).fill("0"),T=[],D=0;D<b;D++){var I=O.slice();I[D]="1",T.push(I.join(" & "))}s.string=T.join("\\\\ ")+s.string.slice(s.i),s.i=0},d.XMatrix=function(s,_){var S=s.GetStar(),b=s.GetArgument(_),O=s.GetArgument(_),T=s.GetArgument(_),D=parseInt(O,10),I=parseInt(T,10);if(isNaN(D)||isNaN(I)||I.toString()!==T||D.toString()!==O)throw new t.default("InvalidNumber","Invalid number");if(D=D<1?1:D,I=I<1?1:I,!S){var k=Array(I).fill(b).join(" & "),L=Array(D).fill(k).join("\\\\ ");return s.string=L+s.string.slice(s.i),void(s.i=0)}var $="";if(1===D&&1===I)$=b;else if(1===D){k=[];for(var U=1;U<=I;U++)k.push("".concat(b,"_{").concat(U,"}"));$=k.join(" & ")}else if(1===I){for(k=[],U=1;U<=D;U++)k.push("".concat(b,"_{").concat(U,"}"));$=k.join("\\\\ ")}else{var z=[];for(U=1;U<=D;U++){k=[];for(var H=1;H<=I;H++)k.push("".concat(b,"_{{").concat(U,"}{").concat(H,"}}"));z.push(k.join(" & "))}$=z.join("\\\\ ")}s.string=$+s.string.slice(s.i),s.i=0},d.PauliMatrix=function(s,_){var S=s.GetArgument(_),b=S.slice(1);switch(S[0]){case"0":b+=" 1 & 0\\\\ 0 & 1";break;case"1":case"x":b+=" 0 & 1\\\\ 1 & 0";break;case"2":case"y":b+=" 0 & -i\\\\ i & 0";break;case"3":case"z":b+=" 1 & 0\\\\ 0 & -1"}s.string=b+s.string.slice(s.i),s.i=0},d.DiagonalMatrix=function(s,_,S){if("{"===s.GetNext()){var b=s.i;s.GetArgument(_);var O=s.i;s.i=b+1;for(var T=[],D="",I=s.i;I<O;){try{D=s.GetUpTo(_,",")}catch{s.i=O,T.push(s.string.slice(I,O-1));break}if(s.i>=O){T.push(s.string.slice(I,O));break}I=s.i,T.push(D)}s.string=function P(s,_){for(var S=s.length,b=[],O=0;O<S;O++)b.push(Array(_?S-O:O+1).join("&")+"\\mqty{"+s[O]+"}");return b.join("\\\\ ")}(T,S)+s.string.slice(O),s.i=0}},d.AutoClose=function(s,_,S){var b=s.create("token","mo",{stretchy:!1},_),O=s.itemFactory.create("mml",b).setProperties({autoclose:_});s.Push(O)},d.Vnabla=function(s,_){var S=s.options.physics.arrowdel?"\\vec{\\gradientnabla}":"{\\gradientnabla}";return s.Push(new x.default(S,s.stack.env,s.configuration).mml())},d.DiffD=function(s,_){var S=s.options.physics.italicdiff?"d":"{\\rm d}";return s.Push(new x.default(S,s.stack.env,s.configuration).mml())},d.Macro=M.default.Macro,d.NamedFn=M.default.NamedFn,d.Array=M.default.Array,m.default=d},23792:function(B,m,v){var w=this&&this.__values||function(i){var f="function"==typeof Symbol&&Symbol.iterator,h=f&&i[f],a=0;if(h)return h.call(i);if(i&&"number"==typeof i.length)return{next:function(){return i&&a>=i.length&&(i=void 0),{value:i&&i[a++],done:!i}}};throw new TypeError(f?"Object is not iterable.":"Symbol.iterator is not defined.")},y=this&&this.__importDefault||function(i){return i&&i.__esModule?i:{default:i}};Object.defineProperty(m,"__esModule",{value:!0}),m.SetOptionsConfiguration=m.SetOptionsUtil=void 0;var M=v(8890),x=v(82652),t=y(v(6257)),e=y(v(39194)),n=v(95889),c=y(v(2158)),l=v(80084);m.SetOptionsUtil={filterPackage:function(i,f){if("tex"!==f&&!M.ConfigurationHandler.get(f))throw new t.default("NotAPackage","Not a defined package: %1",f);var h=i.options.setoptions,a=h.allowOptions[f];if(void 0===a&&!h.allowPackageDefault||!1===a)throw new t.default("PackageNotSettable",'Options can\'t be set for package "%1"',f);return!0},filterOption:function(i,f,h){var a,g=i.options.setoptions,r=g.allowOptions[f]||{},u=r.hasOwnProperty(h)&&!(0,l.isObject)(r[h])?r[h]:null;if(!1===u||null===u&&!g.allowOptionsDefault)throw new t.default("OptionNotSettable",'Option "%1" is not allowed to be set',h);if(!(null===(a="tex"===f?i.options:i.options[f])||void 0===a?void 0:a.hasOwnProperty(h)))throw"tex"===f?new t.default("InvalidTexOption",'Invalid TeX option "%1"',h):new t.default("InvalidOptionKey",'Invalid option "%1" for package "%2"',h,f);return!0},filterValue:function(i,f,h,a){return a}};var d=new x.CommandMap("setoptions",{setOptions:"SetOptions"},{SetOptions:function(i,f){var h,a,g=i.GetBrackets(f)||"tex",r=e.default.keyvalOptions(i.GetArgument(f)),u=i.options.setoptions;if(u.filterPackage(i,g))try{for(var p=w(Object.keys(r)),A=p.next();!A.done;A=p.next()){var C=A.value;u.filterOption(i,g,C)&&(("tex"===g?i.options:i.options[g])[C]=u.filterValue(i,g,C,r[C]))}}catch(P){h={error:P}}finally{try{A&&!A.done&&(a=p.return)&&a.call(p)}finally{if(h)throw h.error}}}});m.SetOptionsConfiguration=M.Configuration.create("setoptions",{handler:{macro:["setoptions"]},config:function o(i,f){var h=f.parseOptions.handlers.get("macro").lookup("require");h&&(d.add("Require",new n.Macro("Require",h._func)),d.add("require",new n.Macro("require",c.default.Macro,["\\Require{#2}\\setOptions[#2]{#1}",2,""])))},priority:3,options:{setoptions:{filterPackage:m.SetOptionsUtil.filterPackage,filterOption:m.SetOptionsUtil.filterOption,filterValue:m.SetOptionsUtil.filterValue,allowPackageDefault:!0,allowOptionsDefault:!0,allowOptions:(0,l.expandable)({tex:{FindTeX:!1,formatError:!1,package:!1,baseURL:!1,tags:!1,maxBuffer:!1,maxMaxros:!1,macros:!1,environments:!1},setoptions:!1,autoload:!1,require:!1,configmacros:!1,tagformat:!1})}}})},90108:function(B,m,v){var e,w=this&&this.__extends||(e=function(n,c){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(l,d){l.__proto__=d}||function(l,d){for(var o in d)Object.prototype.hasOwnProperty.call(d,o)&&(l[o]=d[o])})(n,c)},function(n,c){if("function"!=typeof c&&null!==c)throw new TypeError("Class extends value "+String(c)+" is not a constructor or null");function l(){this.constructor=n}e(n,c),n.prototype=null===c?Object.create(c):(l.prototype=c.prototype,new l)});Object.defineProperty(m,"__esModule",{value:!0}),m.TagFormatConfiguration=m.tagformatConfig=void 0;var y=v(8890),M=v(22450),x=0;function t(e,n){var c=n.parseOptions.options.tags;"base"!==c&&e.tags.hasOwnProperty(c)&&M.TagsFactory.add(c,e.tags[c]);var d=function(i){function f(){return null!==i&&i.apply(this,arguments)||this}return w(f,i),f.prototype.formatNumber=function(h){return n.parseOptions.options.tagformat.number(h)},f.prototype.formatTag=function(h){return n.parseOptions.options.tagformat.tag(h)},f.prototype.formatId=function(h){return n.parseOptions.options.tagformat.id(h)},f.prototype.formatUrl=function(h,a){return n.parseOptions.options.tagformat.url(h,a)},f}(M.TagsFactory.create(n.parseOptions.options.tags).constructor),o="configTags-"+ ++x;M.TagsFactory.add(o,d),n.parseOptions.options.tags=o}m.tagformatConfig=t,m.TagFormatConfiguration=y.Configuration.create("tagformat",{config:[t,10],options:{tagformat:{number:function(e){return e.toString()},tag:function(e){return"("+e+")"},id:function(e){return"mjx-eqn:"+e.replace(/\s/g,"_")},url:function(e,n){return n+"#"+encodeURIComponent(e)}}}})},85966:(B,m,v)=>{Object.defineProperty(m,"__esModule",{value:!0}),m.TextcompConfiguration=void 0;var w=v(8890);v(35588),m.TextcompConfiguration=w.Configuration.create("textcomp",{handler:{macro:["textcomp-macros"]}})},35588:function(B,m,v){var w=this&&this.__importDefault||function(n){return n&&n.__esModule?n:{default:n}};Object.defineProperty(m,"__esModule",{value:!0});var y=v(82652),M=v(60742),x=v(68290),t=w(v(39194)),e=v(49102);new y.CommandMap("textcomp-macros",{textasciicircum:["Insert","^"],textasciitilde:["Insert","~"],textasteriskcentered:["Insert","*"],textbackslash:["Insert","\\"],textbar:["Insert","|"],textbraceleft:["Insert","{"],textbraceright:["Insert","}"],textbullet:["Insert","\u2022"],textdagger:["Insert","\u2020"],textdaggerdbl:["Insert","\u2021"],textellipsis:["Insert","\u2026"],textemdash:["Insert","\u2014"],textendash:["Insert","\u2013"],textexclamdown:["Insert","\xa1"],textgreater:["Insert",">"],textless:["Insert","<"],textordfeminine:["Insert","\xaa"],textordmasculine:["Insert","\xba"],textparagraph:["Insert","\xb6"],textperiodcentered:["Insert","\xb7"],textquestiondown:["Insert","\xbf"],textquotedblleft:["Insert","\u201c"],textquotedblright:["Insert","\u201d"],textquoteleft:["Insert","\u2018"],textquoteright:["Insert","\u2019"],textsection:["Insert","\xa7"],textunderscore:["Insert","_"],textvisiblespace:["Insert","\u2423"],textacutedbl:["Insert","\u02dd"],textasciiacute:["Insert","\xb4"],textasciibreve:["Insert","\u02d8"],textasciicaron:["Insert","\u02c7"],textasciidieresis:["Insert","\xa8"],textasciimacron:["Insert","\xaf"],textgravedbl:["Insert","\u02f5"],texttildelow:["Insert","\u02f7"],textbaht:["Insert","\u0e3f"],textcent:["Insert","\xa2"],textcolonmonetary:["Insert","\u20a1"],textcurrency:["Insert","\xa4"],textdollar:["Insert","$"],textdong:["Insert","\u20ab"],texteuro:["Insert","\u20ac"],textflorin:["Insert","\u0192"],textguarani:["Insert","\u20b2"],textlira:["Insert","\u20a4"],textnaira:["Insert","\u20a6"],textpeso:["Insert","\u20b1"],textsterling:["Insert","\xa3"],textwon:["Insert","\u20a9"],textyen:["Insert","\xa5"],textcircledP:["Insert","\u2117"],textcompwordmark:["Insert","\u200c"],textcopyleft:["Insert","\u{1f12f}"],textcopyright:["Insert","\xa9"],textregistered:["Insert","\xae"],textservicemark:["Insert","\u2120"],texttrademark:["Insert","\u2122"],textbardbl:["Insert","\u2016"],textbigcircle:["Insert","\u25ef"],textblank:["Insert","\u2422"],textbrokenbar:["Insert","\xa6"],textdiscount:["Insert","\u2052"],textestimated:["Insert","\u212e"],textinterrobang:["Insert","\u203d"],textinterrobangdown:["Insert","\u2e18"],textmusicalnote:["Insert","\u266a"],textnumero:["Insert","\u2116"],textopenbullet:["Insert","\u25e6"],textpertenthousand:["Insert","\u2031"],textperthousand:["Insert","\u2030"],textrecipe:["Insert","\u211e"],textreferencemark:["Insert","\u203b"],textlangle:["Insert","\u2329"],textrangle:["Insert","\u232a"],textlbrackdbl:["Insert","\u27e6"],textrbrackdbl:["Insert","\u27e7"],textlquill:["Insert","\u2045"],textrquill:["Insert","\u2046"],textcelsius:["Insert","\u2103"],textdegree:["Insert","\xb0"],textdiv:["Insert","\xf7"],textdownarrow:["Insert","\u2193"],textfractionsolidus:["Insert","\u2044"],textleftarrow:["Insert","\u2190"],textlnot:["Insert","\xac"],textmho:["Insert","\u2127"],textminus:["Insert","\u2212"],textmu:["Insert","\xb5"],textohm:["Insert","\u2126"],textonehalf:["Insert","\xbd"],textonequarter:["Insert","\xbc"],textonesuperior:["Insert","\xb9"],textpm:["Insert","\xb1"],textrightarrow:["Insert","\u2192"],textsurd:["Insert","\u221a"],textthreequarters:["Insert","\xbe"],textthreesuperior:["Insert","\xb3"],texttimes:["Insert","\xd7"],texttwosuperior:["Insert","\xb2"],textuparrow:["Insert","\u2191"],textborn:["Insert","*"],textdied:["Insert","\u2020"],textdivorced:["Insert","\u26ae"],textmarried:["Insert","\u26ad"],textcentoldstyle:["Insert","\xa2",M.TexConstant.Variant.OLDSTYLE],textdollaroldstyle:["Insert","$",M.TexConstant.Variant.OLDSTYLE],textzerooldstyle:["Insert","0",M.TexConstant.Variant.OLDSTYLE],textoneoldstyle:["Insert","1",M.TexConstant.Variant.OLDSTYLE],texttwooldstyle:["Insert","2",M.TexConstant.Variant.OLDSTYLE],textthreeoldstyle:["Insert","3",M.TexConstant.Variant.OLDSTYLE],textfouroldstyle:["Insert","4",M.TexConstant.Variant.OLDSTYLE],textfiveoldstyle:["Insert","5",M.TexConstant.Variant.OLDSTYLE],textsixoldstyle:["Insert","6",M.TexConstant.Variant.OLDSTYLE],textsevenoldstyle:["Insert","7",M.TexConstant.Variant.OLDSTYLE],texteightoldstyle:["Insert","8",M.TexConstant.Variant.OLDSTYLE],textnineoldstyle:["Insert","9",M.TexConstant.Variant.OLDSTYLE]},{Insert:function(n,c,l,d){if(n instanceof e.TextParser){if(!d)return void x.TextMacrosMethods.Insert(n,c,l);n.saveText()}n.Push(t.default.internalText(n,l,d?{mathvariant:d}:{}))}})},78740:function(B,m,v){var y,w=this&&this.__importDefault||function(d){return d&&d.__esModule?d:{default:d}};Object.defineProperty(m,"__esModule",{value:!0}),m.TextMacrosConfiguration=m.TextBaseConfiguration=void 0;var M=v(8890),x=w(v(46708)),t=v(22450),e=v(33012),n=v(49102),c=v(68290);function l(d,o,i,f){var h=d.configuration.packageData.get("textmacros");return d instanceof n.TextParser||(h.texParser=d),[new n.TextParser(o,f?{mathvariant:f}:{},h.parseOptions,i).mml()]}v(38069),m.TextBaseConfiguration=M.Configuration.create("text-base",{parser:"text",handler:{character:["command","text-special"],macro:["text-macros"]},fallback:{character:function(d,o){d.text+=o},macro:function(d,o){var i=d.texParser,f=i.lookup("macro",o);f&&f._func!==c.TextMacrosMethods.Macro&&d.Error("MathMacro","%1 is only supported in math mode","\\"+o),i.parse("macro",[d,o])}},items:(y={},y[e.StartItem.prototype.kind]=e.StartItem,y[e.StopItem.prototype.kind]=e.StopItem,y[e.MmlItem.prototype.kind]=e.MmlItem,y[e.StyleItem.prototype.kind]=e.StyleItem,y)}),m.TextMacrosConfiguration=M.Configuration.create("textmacros",{config:function(d,o){var i=new M.ParserConfiguration(o.parseOptions.options.textmacros.packages,["tex","text"]);i.init();var f=new x.default(i,[]);f.options=o.parseOptions.options,i.config(o),t.TagsFactory.addTags(i.tags),f.tags=t.TagsFactory.getDefault(),f.tags.configuration=f,f.packageData=o.parseOptions.packageData,f.packageData.set("textmacros",{parseOptions:f,jax:o,texParser:null}),f.options.internalMath=l},preprocessors:[function(d){var o=d.data.packageData.get("textmacros");o.parseOptions.nodeFactory.setMmlFactory(o.jax.mmlFactory)}],options:{textmacros:{packages:["text-base"]}}})},38069:(B,m,v)=>{Object.defineProperty(m,"__esModule",{value:!0});var w=v(82652),y=v(60742),M=v(68290),x=v(21108);new w.MacroMap("text-special",{$:"Math","%":"Comment","^":"MathModeOnly",_:"MathModeOnly","&":"Misplaced","#":"Misplaced","~":"Tilde"," ":"Space","\t":"Space","\r":"Space","\n":"Space","\xa0":"Tilde","{":"OpenBrace","}":"CloseBrace","`":"OpenQuote","'":"CloseQuote"},M.TextMacrosMethods),new w.CommandMap("text-macros",{"(":"Math",$:"SelfQuote",_:"SelfQuote","%":"SelfQuote","{":"SelfQuote","}":"SelfQuote"," ":"SelfQuote","&":"SelfQuote","#":"SelfQuote","\\":"SelfQuote","'":["Accent","\xb4"],"\u2019":["Accent","\xb4"],"`":["Accent","`"],"\u2018":["Accent","`"],"^":["Accent","^"],'"':["Accent","\xa8"],"~":["Accent","~"],"=":["Accent","\xaf"],".":["Accent","\u02d9"],u:["Accent","\u02d8"],v:["Accent","\u02c7"],emph:"Emph",rm:["SetFont",y.TexConstant.Variant.NORMAL],mit:["SetFont",y.TexConstant.Variant.ITALIC],oldstyle:["SetFont",y.TexConstant.Variant.OLDSTYLE],cal:["SetFont",y.TexConstant.Variant.CALLIGRAPHIC],it:["SetFont","-tex-mathit"],bf:["SetFont",y.TexConstant.Variant.BOLD],bbFont:["SetFont",y.TexConstant.Variant.DOUBLESTRUCK],scr:["SetFont",y.TexConstant.Variant.SCRIPT],frak:["SetFont",y.TexConstant.Variant.FRAKTUR],sf:["SetFont",y.TexConstant.Variant.SANSSERIF],tt:["SetFont",y.TexConstant.Variant.MONOSPACE],tiny:["SetSize",.5],Tiny:["SetSize",.6],scriptsize:["SetSize",.7],small:["SetSize",.85],normalsize:["SetSize",1],large:["SetSize",1.2],Large:["SetSize",1.44],LARGE:["SetSize",1.73],huge:["SetSize",2.07],Huge:["SetSize",2.49],Bbb:["Macro","{\\bbFont #1}",1],textnormal:["Macro","{\\rm #1}",1],textup:["Macro","{\\rm #1}",1],textrm:["Macro","{\\rm #1}",1],textit:["Macro","{\\it #1}",1],textbf:["Macro","{\\bf #1}",1],textsf:["Macro","{\\sf #1}",1],texttt:["Macro","{\\tt #1}",1],dagger:["Insert","\u2020"],ddagger:["Insert","\u2021"],S:["Insert","\xa7"],",":["Spacer",x.MATHSPACE.thinmathspace],":":["Spacer",x.MATHSPACE.mediummathspace],">":["Spacer",x.MATHSPACE.mediummathspace],";":["Spacer",x.MATHSPACE.thickmathspace],"!":["Spacer",x.MATHSPACE.negativethinmathspace],enspace:["Spacer",.5],quad:["Spacer",1],qquad:["Spacer",2],thinspace:["Spacer",x.MATHSPACE.thinmathspace],negthinspace:["Spacer",x.MATHSPACE.negativethinmathspace],hskip:"Hskip",hspace:"Hskip",kern:"Hskip",mskip:"Hskip",mspace:"Hskip",mkern:"Hskip",rule:"rule",Rule:["Rule"],Space:["Rule","blank"],color:"CheckAutoload",textcolor:"CheckAutoload",colorbox:"CheckAutoload",fcolorbox:"CheckAutoload",href:"CheckAutoload",style:"CheckAutoload",class:"CheckAutoload",cssId:"CheckAutoload",unicode:"CheckAutoload",ref:["HandleRef",!1],eqref:["HandleRef",!0]},M.TextMacrosMethods)},68290:function(B,m,v){var w=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(m,"__esModule",{value:!0}),m.TextMacrosMethods=void 0;var y=w(v(55571)),M=v(185),x=w(v(2158));m.TextMacrosMethods={Comment:function(t,e){for(;t.i<t.string.length&&"\n"!==t.string.charAt(t.i);)t.i++;t.i++},Math:function(t,e){t.saveText();for(var c,l,n=t.i,d=0;l=t.GetNext();)switch(c=t.i++,l){case"\\":")"===t.GetCS()&&(l="\\(");case"$":if(0===d&&e===l){var i=t.texParser.configuration,f=new y.default(t.string.substr(n,c-n),t.stack.env,i).mml();return void t.PushMath(f)}break;case"{":d++;break;case"}":0===d&&t.Error("ExtraCloseMissingOpen","Extra close brace or missing open brace"),d--}t.Error("MathNotTerminated","Math-mode is not properly terminated")},MathModeOnly:function(t,e){t.Error("MathModeOnly","'%1' allowed only in math mode",e)},Misplaced:function(t,e){t.Error("Misplaced","'%1' can not be used here",e)},OpenBrace:function(t,e){var n=t.stack.env;t.envStack.push(n),t.stack.env=Object.assign({},n)},CloseBrace:function(t,e){t.envStack.length?(t.saveText(),t.stack.env=t.envStack.pop()):t.Error("ExtraCloseMissingOpen","Extra close brace or missing open brace")},OpenQuote:function(t,e){t.string.charAt(t.i)===e?(t.text+="\u201c",t.i++):t.text+="\u2018"},CloseQuote:function(t,e){t.string.charAt(t.i)===e?(t.text+="\u201d",t.i++):t.text+="\u2019"},Tilde:function(t,e){t.text+="\xa0"},Space:function(t,e){for(t.text+=" ";t.GetNext().match(/\s/);)t.i++},SelfQuote:function(t,e){t.text+=e.substr(1)},Insert:function(t,e,n){t.text+=n},Accent:function(t,e,n){var c=t.ParseArg(e),l=t.create("token","mo",{},n);t.addAttributes(l),t.Push(t.create("node","mover",[c,l]))},Emph:function(t,e){var n="-tex-mathit"===t.stack.env.mathvariant?"normal":"-tex-mathit";t.Push(t.ParseTextArg(e,{mathvariant:n}))},SetFont:function(t,e,n){t.saveText(),t.stack.env.mathvariant=n},SetSize:function(t,e,n){t.saveText(),t.stack.env.mathsize=n},CheckAutoload:function(t,e){var n=t.configuration.packageData.get("autoload"),c=t.texParser;e=e.slice(1);var l=c.lookup("macro",e);if(!l||n&&l._func===n.Autoload){if(c.parse("macro",[c,e]),!l)return;(0,M.retryAfter)(Promise.resolve())}c.parse("macro",[t,e])},Macro:x.default.Macro,Spacer:x.default.Spacer,Hskip:x.default.Hskip,rule:x.default.rule,Rule:x.default.Rule,HandleRef:x.default.HandleRef}},49102:function(B,m,v){var f,w=this&&this.__extends||(f=function(h,a){return(f=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(g,r){g.__proto__=r}||function(g,r){for(var u in r)Object.prototype.hasOwnProperty.call(r,u)&&(g[u]=r[u])})(h,a)},function(h,a){if("function"!=typeof a&&null!==a)throw new TypeError("Class extends value "+String(a)+" is not a constructor or null");function g(){this.constructor=h}f(h,a),h.prototype=null===a?Object.create(a):(g.prototype=a.prototype,new g)}),y=this&&this.__values||function(f){var h="function"==typeof Symbol&&Symbol.iterator,a=h&&f[h],g=0;if(a)return a.call(f);if(f&&"number"==typeof f.length)return{next:function(){return f&&g>=f.length&&(f=void 0),{value:f&&f[g++],done:!f}}};throw new TypeError(h?"Object is not iterable.":"Symbol.iterator is not defined.")},M=this&&this.__read||function(f,h){var a="function"==typeof Symbol&&f[Symbol.iterator];if(!a)return f;var r,p,g=a.call(f),u=[];try{for(;(void 0===h||h-- >0)&&!(r=g.next()).done;)u.push(r.value)}catch(A){p={error:A}}finally{try{r&&!r.done&&(a=g.return)&&a.call(g)}finally{if(p)throw p.error}}return u},x=this&&this.__spreadArray||function(f,h,a){if(a||2===arguments.length)for(var u,g=0,r=h.length;g<r;g++)(u||!(g in h))&&(u||(u=Array.prototype.slice.call(h,0,g)),u[g]=h[g]);return f.concat(u||Array.prototype.slice.call(h))},t=this&&this.__importDefault||function(f){return f&&f.__esModule?f:{default:f}};Object.defineProperty(m,"__esModule",{value:!0}),m.TextParser=void 0;var e=t(v(55571)),n=t(v(6257)),c=t(v(39194)),l=v(91585),d=t(v(60909)),o=v(33012),i=function(f){function h(a,g,r,u){var p=f.call(this,a,g,r)||this;return p.level=u,p}return w(h,f),Object.defineProperty(h.prototype,"texParser",{get:function(){return this.configuration.packageData.get("textmacros").texParser},enumerable:!1,configurable:!0}),Object.defineProperty(h.prototype,"tags",{get:function(){return this.texParser.tags},enumerable:!1,configurable:!0}),h.prototype.mml=function(){return null!=this.level?this.create("node","mstyle",this.nodes,{displaystyle:!1,scriptlevel:this.level}):1===this.nodes.length?this.nodes[0]:this.create("node","mrow",this.nodes)},h.prototype.Parse=function(){this.text="",this.nodes=[],this.envStack=[],f.prototype.Parse.call(this)},h.prototype.saveText=function(){if(this.text){var a=this.stack.env.mathvariant,g=c.default.internalText(this,this.text,a?{mathvariant:a}:{});this.text="",this.Push(g)}},h.prototype.Push=function(a){if(this.text&&this.saveText(),a instanceof o.StopItem)return f.prototype.Push.call(this,a);a instanceof o.StyleItem?this.stack.env.mathcolor=this.stack.env.color:a instanceof l.AbstractMmlNode&&(this.addAttributes(a),this.nodes.push(a))},h.prototype.PushMath=function(a){var g,r,u=this.stack.env;a.isKind("TeXAtom")||(a=this.create("node","TeXAtom",[a]));try{for(var p=y(["mathsize","mathcolor"]),A=p.next();!A.done;A=p.next()){var C=A.value;u[C]&&!a.attributes.getExplicit(C)&&(!a.isToken&&!a.isKind("mstyle")&&(a=this.create("node","mstyle",[a])),d.default.setAttribute(a,C,u[C]))}}catch(P){g={error:P}}finally{try{A&&!A.done&&(r=p.return)&&r.call(p)}finally{if(g)throw g.error}}a.isInferred&&(a=this.create("node","mrow",a.childNodes)),this.nodes.push(a)},h.prototype.addAttributes=function(a){var g,r,u=this.stack.env;if(a.isToken)try{for(var p=y(["mathsize","mathcolor","mathvariant"]),A=p.next();!A.done;A=p.next()){var C=A.value;u[C]&&!a.attributes.getExplicit(C)&&d.default.setAttribute(a,C,u[C])}}catch(P){g={error:P}}finally{try{A&&!A.done&&(r=p.return)&&r.call(p)}finally{if(g)throw g.error}}},h.prototype.ParseTextArg=function(a,g){return new h(this.GetArgument(a),g=Object.assign(Object.assign({},this.stack.env),g),this.configuration).mml()},h.prototype.ParseArg=function(a){return new h(this.GetArgument(a),this.stack.env,this.configuration).mml()},h.prototype.Error=function(a,g){for(var r=[],u=2;u<arguments.length;u++)r[u-2]=arguments[u];throw new(n.default.bind.apply(n.default,x([void 0,a,g],M(r),!1)))},h}(e.default);m.TextParser=i},34531:function(B,m,v){var w=this&&this.__importDefault||function(l){return l&&l.__esModule?l:{default:l}};Object.defineProperty(m,"__esModule",{value:!0}),m.UnicodeConfiguration=m.UnicodeMethods=void 0;var y=v(8890),M=w(v(6257)),x=v(82652),t=w(v(39194)),e=w(v(60909)),n=v(56692);m.UnicodeMethods={};var c={};m.UnicodeMethods.Unicode=function(l,d){var o=l.GetBrackets(d),i=null,f=null;o&&(o.replace(/ /g,"").match(/^(\d+(\.\d*)?|\.\d+),(\d+(\.\d*)?|\.\d+)$/)?(i=o.replace(/ /g,"").split(/,/),f=l.GetBrackets(d)):f=o);var h=t.default.trimSpaces(l.GetArgument(d)).replace(/^0x/,"x");if(!h.match(/^(x[0-9A-Fa-f]+|[0-9]+)$/))throw new M.default("BadUnicode","Argument to \\unicode must be a number");var a=parseInt(h.match(/^x/)?"0"+h:h);c[a]?f||(f=c[a][2]):c[a]=[800,200,f,a],i&&(c[a][0]=Math.floor(1e3*parseFloat(i[0])),c[a][1]=Math.floor(1e3*parseFloat(i[1])));var g=l.stack.env.font,r={};f?(c[a][2]=r.fontfamily=f.replace(/'/g,"'"),g&&(g.match(/bold/)&&(r.fontweight="bold"),g.match(/italic|-mathit/)&&(r.fontstyle="italic"))):g&&(r.mathvariant=g);var u=l.create("token","mtext",r,(0,n.numeric)(h));e.default.setProperty(u,"unicode",!0),l.Push(u)},new x.CommandMap("unicode",{unicode:"Unicode"},m.UnicodeMethods),m.UnicodeConfiguration=y.Configuration.create("unicode",{handler:{macro:["unicode"]}})},13601:(B,m,v)=>{Object.defineProperty(m,"__esModule",{value:!0}),m.UpgreekConfiguration=void 0;var w=v(8890),y=v(82652),M=v(60742);new y.CharacterMap("upgreek",function x(t,e){var n=e.attributes||{};n.mathvariant=M.TexConstant.Variant.NORMAL;var c=t.create("token","mi",n,e.char);t.Push(c)},{upalpha:"\u03b1",upbeta:"\u03b2",upgamma:"\u03b3",updelta:"\u03b4",upepsilon:"\u03f5",upzeta:"\u03b6",upeta:"\u03b7",uptheta:"\u03b8",upiota:"\u03b9",upkappa:"\u03ba",uplambda:"\u03bb",upmu:"\u03bc",upnu:"\u03bd",upxi:"\u03be",upomicron:"\u03bf",uppi:"\u03c0",uprho:"\u03c1",upsigma:"\u03c3",uptau:"\u03c4",upupsilon:"\u03c5",upphi:"\u03d5",upchi:"\u03c7",uppsi:"\u03c8",upomega:"\u03c9",upvarepsilon:"\u03b5",upvartheta:"\u03d1",upvarpi:"\u03d6",upvarrho:"\u03f1",upvarsigma:"\u03c2",upvarphi:"\u03c6",Upgamma:"\u0393",Updelta:"\u0394",Uptheta:"\u0398",Uplambda:"\u039b",Upxi:"\u039e",Uppi:"\u03a0",Upsigma:"\u03a3",Upupsilon:"\u03a5",Upphi:"\u03a6",Uppsi:"\u03a8",Upomega:"\u03a9"}),m.UpgreekConfiguration=w.Configuration.create("upgreek",{handler:{macro:["upgreek"]}})},14961:function(B,m,v){var w=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(m,"__esModule",{value:!0}),m.VerbConfiguration=m.VerbMethods=void 0;var y=v(8890),M=v(60742),x=v(82652),t=w(v(6257));m.VerbMethods={},m.VerbMethods.Verb=function(e,n){var c=e.GetNext(),l=++e.i;if(""===c)throw new t.default("MissingArgFor","Missing argument for %1",n);for(;e.i<e.string.length&&e.string.charAt(e.i)!==c;)e.i++;if(e.i===e.string.length)throw new t.default("NoClosingDelim","Can't find closing delimiter for %1",e.currentCS);var d=e.string.slice(l,e.i).replace(/ /g,"\xa0");e.i++,e.Push(e.create("token","mtext",{mathvariant:M.TexConstant.Variant.MONOSPACE},d))},new x.CommandMap("verb",{verb:"Verb"},m.VerbMethods),m.VerbConfiguration=y.Configuration.create("verb",{handler:{macro:["verb"]}})},53320:(B,m)=>{Object.defineProperty(m,"__esModule",{value:!0}),m.mhchemParser=void 0;var v=function(){function t(){}return t.toTex=function(e,n){return M.go(y.go(e,n),"tex"!==n)},t}();function w(t){var e,n,c={};for(e in t)for(n in t[e]){var l=n.split("|");t[e][n].stateArray=l;for(var d=0;d<l.length;d++)c[l[d]]=[]}for(e in t)for(n in t[e])for(l=t[e][n].stateArray||[],d=0;d<l.length;d++){var o=t[e][n];o.action_=[].concat(o.action_);for(var i=0;i<o.action_.length;i++)"string"==typeof o.action_[i]&&(o.action_[i]={type_:o.action_[i]});for(var f=e.split("|"),h=0;h<f.length;h++)if("*"===l[d]){var a=void 0;for(a in c)c[a].push({pattern:f[h],task:o})}else c[l[d]].push({pattern:f[h],task:o})}return c}m.mhchemParser=v;var y={go:function(t,e){if(!t)return[];void 0===e&&(e="ce");var n="0",c={parenthesisLevel:0};t=(t=(t=t.replace(/\n/g," ")).replace(/[\u2212\u2013\u2014\u2010]/g,"-")).replace(/[\u2026]/g,"...");for(var l,d=10,o=[];;){l!==t?(d=10,l=t):d--;var i=y.stateMachines[e],f=i.transitions[n]||i.transitions["*"];t:for(var h=0;h<f.length;h++){var a=y.patterns.match_(f[h].pattern,t);if(a){for(var g=f[h].task,r=0;r<g.action_.length;r++){var u=void 0;if(i.actions[g.action_[r].type_])u=i.actions[g.action_[r].type_](c,a.match_,g.action_[r].option);else{if(!y.actions[g.action_[r].type_])throw["MhchemBugA","mhchem bug A. Please report. ("+g.action_[r].type_+")"];u=y.actions[g.action_[r].type_](c,a.match_,g.action_[r].option)}y.concatArray(o,u)}if(n=g.nextState||n,!(t.length>0))return o;if(g.revisit||(t=a.remainder),!g.toContinue)break t}}if(d<=0)throw["MhchemBugU","mhchem bug U. Please report."]}},concatArray:function(t,e){if(e)if(Array.isArray(e))for(var n=0;n<e.length;n++)t.push(e[n]);else t.push(e)},patterns:{patterns:{empty:/^$/,else:/^./,else2:/^./,space:/^\s/,"space A":/^\s(?=[A-Z\\$])/,space$:/^\s$/,"a-z":/^[a-z]/,x:/^x/,x$:/^x$/,i$:/^i$/,letters:/^(?:[a-zA-Z\u03B1-\u03C9\u0391-\u03A9?@]|(?:\\(?:alpha|beta|gamma|delta|epsilon|zeta|eta|theta|iota|kappa|lambda|mu|nu|xi|omicron|pi|rho|sigma|tau|upsilon|phi|chi|psi|omega|Gamma|Delta|Theta|Lambda|Xi|Pi|Sigma|Upsilon|Phi|Psi|Omega)(?:\s+|\{\}|(?![a-zA-Z]))))+/,"\\greek":/^\\(?:alpha|beta|gamma|delta|epsilon|zeta|eta|theta|iota|kappa|lambda|mu|nu|xi|omicron|pi|rho|sigma|tau|upsilon|phi|chi|psi|omega|Gamma|Delta|Theta|Lambda|Xi|Pi|Sigma|Upsilon|Phi|Psi|Omega)(?:\s+|\{\}|(?![a-zA-Z]))/,"one lowercase latin letter $":/^(?:([a-z])(?:$|[^a-zA-Z]))$/,"$one lowercase latin letter$ $":/^\$(?:([a-z])(?:$|[^a-zA-Z]))\$$/,"one lowercase greek letter $":/^(?:\$?[\u03B1-\u03C9]\$?|\$?\\(?:alpha|beta|gamma|delta|epsilon|zeta|eta|theta|iota|kappa|lambda|mu|nu|xi|omicron|pi|rho|sigma|tau|upsilon|phi|chi|psi|omega)\s*\$?)(?:\s+|\{\}|(?![a-zA-Z]))$/,digits:/^[0-9]+/,"-9.,9":/^[+\-]?(?:[0-9]+(?:[,.][0-9]+)?|[0-9]*(?:\.[0-9]+))/,"-9.,9 no missing 0":/^[+\-]?[0-9]+(?:[.,][0-9]+)?/,"(-)(9.,9)(e)(99)":function(t){var e=t.match(/^(\+\-|\+\/\-|\+|\-|\\pm\s?)?([0-9]+(?:[,.][0-9]+)?|[0-9]*(?:\.[0-9]+))?(\((?:[0-9]+(?:[,.][0-9]+)?|[0-9]*(?:\.[0-9]+))\))?(?:(?:([eE])|\s*(\*|x|\\times|\u00D7)\s*10\^)([+\-]?[0-9]+|\{[+\-]?[0-9]+\}))?/);return e&&e[0]?{match_:e.slice(1),remainder:t.substr(e[0].length)}:null},"(-)(9)^(-9)":/^(\+\-|\+\/\-|\+|\-|\\pm\s?)?([0-9]+(?:[,.][0-9]+)?|[0-9]*(?:\.[0-9]+)?)\^([+\-]?[0-9]+|\{[+\-]?[0-9]+\})/,"state of aggregation $":function(t){var e=y.patterns.findObserveGroups(t,"",/^\([a-z]{1,3}(?=[\),])/,")","");if(e&&e.remainder.match(/^($|[\s,;\)\]\}])/))return e;var n=t.match(/^(?:\((?:\\ca\s?)?\$[amothc]\$\))/);return n?{match_:n[0],remainder:t.substr(n[0].length)}:null},"_{(state of aggregation)}$":/^_\{(\([a-z]{1,3}\))\}/,"{[(":/^(?:\\\{|\[|\()/,")]}":/^(?:\)|\]|\\\})/,", ":/^[,;]\s*/,",":/^[,;]/,".":/^[.]/,". __* ":/^([.\u22C5\u00B7\u2022]|[*])\s*/,"...":/^\.\.\.(?=$|[^.])/,"^{(...)}":function(t){return y.patterns.findObserveGroups(t,"^{","","","}")},"^($...$)":function(t){return y.patterns.findObserveGroups(t,"^","$","$","")},"^a":/^\^([0-9]+|[^\\_])/,"^\\x{}{}":function(t){return y.patterns.findObserveGroups(t,"^",/^\\[a-zA-Z]+\{/,"}","","","{","}","",!0)},"^\\x{}":function(t){return y.patterns.findObserveGroups(t,"^",/^\\[a-zA-Z]+\{/,"}","")},"^\\x":/^\^(\\[a-zA-Z]+)\s*/,"^(-1)":/^\^(-?\d+)/,"'":/^'/,"_{(...)}":function(t){return y.patterns.findObserveGroups(t,"_{","","","}")},"_($...$)":function(t){return y.patterns.findObserveGroups(t,"_","$","$","")},_9:/^_([+\-]?[0-9]+|[^\\])/,"_\\x{}{}":function(t){return y.patterns.findObserveGroups(t,"_",/^\\[a-zA-Z]+\{/,"}","","","{","}","",!0)},"_\\x{}":function(t){return y.patterns.findObserveGroups(t,"_",/^\\[a-zA-Z]+\{/,"}","")},"_\\x":/^_(\\[a-zA-Z]+)\s*/,"^_":/^(?:\^(?=_)|\_(?=\^)|[\^_]$)/,"{}^":/^\{\}(?=\^)/,"{}":/^\{\}/,"{...}":function(t){return y.patterns.findObserveGroups(t,"","{","}","")},"{(...)}":function(t){return y.patterns.findObserveGroups(t,"{","","","}")},"$...$":function(t){return y.patterns.findObserveGroups(t,"","$","$","")},"${(...)}$__$(...)$":function(t){return y.patterns.findObserveGroups(t,"${","","","}$")||y.patterns.findObserveGroups(t,"$","","","$")},"=<>":/^[=<>]/,"#":/^[#\u2261]/,"+":/^\+/,"-$":/^-(?=[\s_},;\]/]|$|\([a-z]+\))/,"-9":/^-(?=[0-9])/,"- orbital overlap":/^-(?=(?:[spd]|sp)(?:$|[\s,;\)\]\}]))/,"-":/^-/,"pm-operator":/^(?:\\pm|\$\\pm\$|\+-|\+\/-)/,operator:/^(?:\+|(?:[\-=<>]|<<|>>|\\approx|\$\\approx\$)(?=\s|$|-?[0-9]))/,arrowUpDown:/^(?:v|\(v\)|\^|\(\^\))(?=$|[\s,;\)\]\}])/,"\\bond{(...)}":function(t){return y.patterns.findObserveGroups(t,"\\bond{","","","}")},"->":/^(?:<->|<-->|->|<-|<=>>|<<=>|<=>|[\u2192\u27F6\u21CC])/,CMT:/^[CMT](?=\[)/,"[(...)]":function(t){return y.patterns.findObserveGroups(t,"[","","","]")},"1st-level escape":/^(&|\\\\|\\hline)\s*/,"\\,":/^(?:\\[,\ ;:])/,"\\x{}{}":function(t){return y.patterns.findObserveGroups(t,"",/^\\[a-zA-Z]+\{/,"}","","","{","}","",!0)},"\\x{}":function(t){return y.patterns.findObserveGroups(t,"",/^\\[a-zA-Z]+\{/,"}","")},"\\ca":/^\\ca(?:\s+|(?![a-zA-Z]))/,"\\x":/^(?:\\[a-zA-Z]+\s*|\\[_&{}%])/,orbital:/^(?:[0-9]{1,2}[spdfgh]|[0-9]{0,2}sp)(?=$|[^a-zA-Z])/,others:/^[\/~|]/,"\\frac{(...)}":function(t){return y.patterns.findObserveGroups(t,"\\frac{","","","}","{","","","}")},"\\overset{(...)}":function(t){return y.patterns.findObserveGroups(t,"\\overset{","","","}","{","","","}")},"\\underset{(...)}":function(t){return y.patterns.findObserveGroups(t,"\\underset{","","","}","{","","","}")},"\\underbrace{(...)}":function(t){return y.patterns.findObserveGroups(t,"\\underbrace{","","","}_","{","","","}")},"\\color{(...)}":function(t){return y.patterns.findObserveGroups(t,"\\color{","","","}")},"\\color{(...)}{(...)}":function(t){return y.patterns.findObserveGroups(t,"\\color{","","","}","{","","","}")||y.patterns.findObserveGroups(t,"\\color","\\","",/^(?=\{)/,"{","","","}")},"\\ce{(...)}":function(t){return y.patterns.findObserveGroups(t,"\\ce{","","","}")},"\\pu{(...)}":function(t){return y.patterns.findObserveGroups(t,"\\pu{","","","}")},oxidation$:/^(?:[+-][IVX]+|(?:\\pm|\$\\pm\$|\+-|\+\/-)\s*0)$/,"d-oxidation$":/^(?:[+-]?[IVX]+|(?:\\pm|\$\\pm\$|\+-|\+\/-)\s*0)$/,"1/2$":/^[+\-]?(?:[0-9]+|\$[a-z]\$|[a-z])\/[0-9]+(?:\$[a-z]\$|[a-z])?$/,amount:function(t){var e;if(e=t.match(/^(?:(?:(?:\([+\-]?[0-9]+\/[0-9]+\)|[+\-]?(?:[0-9]+|\$[a-z]\$|[a-z])\/[0-9]+|[+\-]?[0-9]+[.,][0-9]+|[+\-]?\.[0-9]+|[+\-]?[0-9]+)(?:[a-z](?=\s*[A-Z]))?)|[+\-]?[a-z](?=\s*[A-Z])|\+(?!\s))/))return{match_:e[0],remainder:t.substr(e[0].length)};var n=y.patterns.findObserveGroups(t,"","$","$","");return n&&(e=n.match_.match(/^\$(?:\(?[+\-]?(?:[0-9]*[a-z]?[+\-])?[0-9]*[a-z](?:[+\-][0-9]*[a-z]?)?\)?|\+|-)\$$/))?{match_:e[0],remainder:t.substr(e[0].length)}:null},amount2:function(t){return this.amount(t)},"(KV letters),":/^(?:[A-Z][a-z]{0,2}|i)(?=,)/,formula$:function(t){if(t.match(/^\([a-z]+\)$/))return null;var e=t.match(/^(?:[a-z]|(?:[0-9\ \+\-\,\.\(\)]+[a-z])+[0-9\ \+\-\,\.\(\)]*|(?:[a-z][0-9\ \+\-\,\.\(\)]+)+[a-z]?)$/);return e?{match_:e[0],remainder:t.substr(e[0].length)}:null},uprightEntities:/^(?:pH|pOH|pC|pK|iPr|iBu)(?=$|[^a-zA-Z])/,"/":/^\s*(\/)\s*/,"//":/^\s*(\/\/)\s*/,"*":/^\s*[*.]\s*/},findObserveGroups:function(t,e,n,c,l,d,o,i,f,h){var a=function(P,s){if("string"==typeof s)return 0!==P.indexOf(s)?null:s;var _=P.match(s);return _?_[0]:null},r=a(t,e);if(null===r||(t=t.substr(r.length),null===(r=a(t,n))))return null;var u=function(P,s,_){for(var S=0;s<P.length;){var b=P.charAt(s),O=a(P.substr(s),_);if(null!==O&&0===S)return{endMatchBegin:s,endMatchEnd:s+O.length};if("{"===b)S++;else if("}"===b){if(0===S)throw["ExtraCloseMissingOpen","Extra close brace or missing open brace"];S--}s++}return null}(t,r.length,c||l);if(null===u)return null;var p=t.substring(0,c?u.endMatchEnd:u.endMatchBegin);if(d||o){var A=this.findObserveGroups(t.substr(u.endMatchEnd),d,o,i,f);if(null===A)return null;var C=[p,A.match_];return{match_:h?C.join(""):C,remainder:A.remainder}}return{match_:p,remainder:t.substr(u.endMatchEnd)}},match_:function(t,e){var n=y.patterns.patterns[t];if(void 0===n)throw["MhchemBugP","mhchem bug P. Please report. ("+t+")"];if("function"==typeof n)return y.patterns.patterns[t](e);var c=e.match(n);return c?c.length>2?{match_:c.slice(1),remainder:e.substr(c[0].length)}:{match_:c[1]||c[0],remainder:e.substr(c[0].length)}:null}},actions:{"a=":function(t,e){t.a=(t.a||"")+e},"b=":function(t,e){t.b=(t.b||"")+e},"p=":function(t,e){t.p=(t.p||"")+e},"o=":function(t,e){t.o=(t.o||"")+e},"o=+p1":function(t,e,n){t.o=(t.o||"")+n},"q=":function(t,e){t.q=(t.q||"")+e},"d=":function(t,e){t.d=(t.d||"")+e},"rm=":function(t,e){t.rm=(t.rm||"")+e},"text=":function(t,e){t.text_=(t.text_||"")+e},insert:function(t,e,n){return{type_:n}},"insert+p1":function(t,e,n){return{type_:n,p1:e}},"insert+p1+p2":function(t,e,n){return{type_:n,p1:e[0],p2:e[1]}},copy:function(t,e){return e},write:function(t,e,n){return n},rm:function(t,e){return{type_:"rm",p1:e}},text:function(t,e){return y.go(e,"text")},"tex-math":function(t,e){return y.go(e,"tex-math")},"tex-math tight":function(t,e){return y.go(e,"tex-math tight")},bond:function(t,e,n){return{type_:"bond",kind_:n||e}},"color0-output":function(t,e){return{type_:"color0",color:e}},ce:function(t,e){return y.go(e,"ce")},pu:function(t,e){return y.go(e,"pu")},"1/2":function(t,e){var n=[];e.match(/^[+\-]/)&&(n.push(e.substr(0,1)),e=e.substr(1));var c=e.match(/^([0-9]+|\$[a-z]\$|[a-z])\/([0-9]+)(\$[a-z]\$|[a-z])?$/);return c[1]=c[1].replace(/\$/g,""),n.push({type_:"frac",p1:c[1],p2:c[2]}),c[3]&&(c[3]=c[3].replace(/\$/g,""),n.push({type_:"tex-math",p1:c[3]})),n},"9,9":function(t,e){return y.go(e,"9,9")}},stateMachines:{tex:{transitions:w({empty:{0:{action_:"copy"}},"\\ce{(...)}":{0:{action_:[{type_:"write",option:"{"},"ce",{type_:"write",option:"}"}]}},"\\pu{(...)}":{0:{action_:[{type_:"write",option:"{"},"pu",{type_:"write",option:"}"}]}},else:{0:{action_:"copy"}}}),actions:{}},ce:{transitions:w({empty:{"*":{action_:"output"}},else:{"0|1|2":{action_:"beginsWithBond=false",revisit:!0,toContinue:!0}},oxidation$:{0:{action_:"oxidation-output"}},CMT:{r:{action_:"rdt=",nextState:"rt"},rd:{action_:"rqt=",nextState:"rdt"}},arrowUpDown:{"0|1|2|as":{action_:["sb=false","output","operator"],nextState:"1"}},uprightEntities:{"0|1|2":{action_:["o=","output"],nextState:"1"}},orbital:{"0|1|2|3":{action_:"o=",nextState:"o"}},"->":{"0|1|2|3":{action_:"r=",nextState:"r"},"a|as":{action_:["output","r="],nextState:"r"},"*":{action_:["output","r="],nextState:"r"}},"+":{o:{action_:"d= kv",nextState:"d"},"d|D":{action_:"d=",nextState:"d"},q:{action_:"d=",nextState:"qd"},"qd|qD":{action_:"d=",nextState:"qd"},dq:{action_:["output","d="],nextState:"d"},3:{action_:["sb=false","output","operator"],nextState:"0"}},amount:{"0|2":{action_:"a=",nextState:"a"}},"pm-operator":{"0|1|2|a|as":{action_:["sb=false","output",{type_:"operator",option:"\\pm"}],nextState:"0"}},operator:{"0|1|2|a|as":{action_:["sb=false","output","operator"],nextState:"0"}},"-$":{"o|q":{action_:["charge or bond","output"],nextState:"qd"},d:{action_:"d=",nextState:"d"},D:{action_:["output",{type_:"bond",option:"-"}],nextState:"3"},q:{action_:"d=",nextState:"qd"},qd:{action_:"d=",nextState:"qd"},"qD|dq":{action_:["output",{type_:"bond",option:"-"}],nextState:"3"}},"-9":{"3|o":{action_:["output",{type_:"insert",option:"hyphen"}],nextState:"3"}},"- orbital overlap":{o:{action_:["output",{type_:"insert",option:"hyphen"}],nextState:"2"},d:{action_:["output",{type_:"insert",option:"hyphen"}],nextState:"2"}},"-":{"0|1|2":{action_:[{type_:"output",option:1},"beginsWithBond=true",{type_:"bond",option:"-"}],nextState:"3"},3:{action_:{type_:"bond",option:"-"}},a:{action_:["output",{type_:"insert",option:"hyphen"}],nextState:"2"},as:{action_:[{type_:"output",option:2},{type_:"bond",option:"-"}],nextState:"3"},b:{action_:"b="},o:{action_:{type_:"- after o/d",option:!1},nextState:"2"},q:{action_:{type_:"- after o/d",option:!1},nextState:"2"},"d|qd|dq":{action_:{type_:"- after o/d",option:!0},nextState:"2"},"D|qD|p":{action_:["output",{type_:"bond",option:"-"}],nextState:"3"}},amount2:{"1|3":{action_:"a=",nextState:"a"}},letters:{"0|1|2|3|a|as|b|p|bp|o":{action_:"o=",nextState:"o"},"q|dq":{action_:["output","o="],nextState:"o"},"d|D|qd|qD":{action_:"o after d",nextState:"o"}},digits:{o:{action_:"q=",nextState:"q"},"d|D":{action_:"q=",nextState:"dq"},q:{action_:["output","o="],nextState:"o"},a:{action_:"o=",nextState:"o"}},"space A":{"b|p|bp":{action_:[]}},space:{a:{action_:[],nextState:"as"},0:{action_:"sb=false"},"1|2":{action_:"sb=true"},"r|rt|rd|rdt|rdq":{action_:"output",nextState:"0"},"*":{action_:["output","sb=true"],nextState:"1"}},"1st-level escape":{"1|2":{action_:["output",{type_:"insert+p1",option:"1st-level escape"}]},"*":{action_:["output",{type_:"insert+p1",option:"1st-level escape"}],nextState:"0"}},"[(...)]":{"r|rt":{action_:"rd=",nextState:"rd"},"rd|rdt":{action_:"rq=",nextState:"rdq"}},"...":{"o|d|D|dq|qd|qD":{action_:["output",{type_:"bond",option:"..."}],nextState:"3"},"*":{action_:[{type_:"output",option:1},{type_:"insert",option:"ellipsis"}],nextState:"1"}},". __* ":{"*":{action_:["output",{type_:"insert",option:"addition compound"}],nextState:"1"}},"state of aggregation $":{"*":{action_:["output","state of aggregation"],nextState:"1"}},"{[(":{"a|as|o":{action_:["o=","output","parenthesisLevel++"],nextState:"2"},"0|1|2|3":{action_:["o=","output","parenthesisLevel++"],nextState:"2"},"*":{action_:["output","o=","output","parenthesisLevel++"],nextState:"2"}},")]}":{"0|1|2|3|b|p|bp|o":{action_:["o=","parenthesisLevel--"],nextState:"o"},"a|as|d|D|q|qd|qD|dq":{action_:["output","o=","parenthesisLevel--"],nextState:"o"}},", ":{"*":{action_:["output","comma"],nextState:"0"}},"^_":{"*":{action_:[]}},"^{(...)}|^($...$)":{"0|1|2|as":{action_:"b=",nextState:"b"},p:{action_:"b=",nextState:"bp"},"3|o":{action_:"d= kv",nextState:"D"},q:{action_:"d=",nextState:"qD"},"d|D|qd|qD|dq":{action_:["output","d="],nextState:"D"}},"^a|^\\x{}{}|^\\x{}|^\\x|'":{"0|1|2|as":{action_:"b=",nextState:"b"},p:{action_:"b=",nextState:"bp"},"3|o":{action_:"d= kv",nextState:"d"},q:{action_:"d=",nextState:"qd"},"d|qd|D|qD":{action_:"d="},dq:{action_:["output","d="],nextState:"d"}},"_{(state of aggregation)}$":{"d|D|q|qd|qD|dq":{action_:["output","q="],nextState:"q"}},"_{(...)}|_($...$)|_9|_\\x{}{}|_\\x{}|_\\x":{"0|1|2|as":{action_:"p=",nextState:"p"},b:{action_:"p=",nextState:"bp"},"3|o":{action_:"q=",nextState:"q"},"d|D":{action_:"q=",nextState:"dq"},"q|qd|qD|dq":{action_:["output","q="],nextState:"q"}},"=<>":{"0|1|2|3|a|as|o|q|d|D|qd|qD|dq":{action_:[{type_:"output",option:2},"bond"],nextState:"3"}},"#":{"0|1|2|3|a|as|o":{action_:[{type_:"output",option:2},{type_:"bond",option:"#"}],nextState:"3"}},"{}^":{"*":{action_:[{type_:"output",option:1},{type_:"insert",option:"tinySkip"}],nextState:"1"}},"{}":{"*":{action_:{type_:"output",option:1},nextState:"1"}},"{...}":{"0|1|2|3|a|as|b|p|bp":{action_:"o=",nextState:"o"},"o|d|D|q|qd|qD|dq":{action_:["output","o="],nextState:"o"}},"$...$":{a:{action_:"a="},"0|1|2|3|as|b|p|bp|o":{action_:"o=",nextState:"o"},"as|o":{action_:"o="},"q|d|D|qd|qD|dq":{action_:["output","o="],nextState:"o"}},"\\bond{(...)}":{"*":{action_:[{type_:"output",option:2},"bond"],nextState:"3"}},"\\frac{(...)}":{"*":{action_:[{type_:"output",option:1},"frac-output"],nextState:"3"}},"\\overset{(...)}":{"*":{action_:[{type_:"output",option:2},"overset-output"],nextState:"3"}},"\\underset{(...)}":{"*":{action_:[{type_:"output",option:2},"underset-output"],nextState:"3"}},"\\underbrace{(...)}":{"*":{action_:[{type_:"output",option:2},"underbrace-output"],nextState:"3"}},"\\color{(...)}{(...)}":{"*":{action_:[{type_:"output",option:2},"color-output"],nextState:"3"}},"\\color{(...)}":{"*":{action_:[{type_:"output",option:2},"color0-output"]}},"\\ce{(...)}":{"*":{action_:[{type_:"output",option:2},"ce"],nextState:"3"}},"\\,":{"*":{action_:[{type_:"output",option:1},"copy"],nextState:"1"}},"\\pu{(...)}":{"*":{action_:["output",{type_:"write",option:"{"},"pu",{type_:"write",option:"}"}],nextState:"3"}},"\\x{}{}|\\x{}|\\x":{"0|1|2|3|a|as|b|p|bp|o|c0":{action_:["o=","output"],nextState:"3"},"*":{action_:["output","o=","output"],nextState:"3"}},others:{"*":{action_:[{type_:"output",option:1},"copy"],nextState:"3"}},else2:{a:{action_:"a to o",nextState:"o",revisit:!0},as:{action_:["output","sb=true"],nextState:"1",revisit:!0},"r|rt|rd|rdt|rdq":{action_:["output"],nextState:"0",revisit:!0},"*":{action_:["output","copy"],nextState:"3"}}}),actions:{"o after d":function(t,e){var n;if((t.d||"").match(/^[1-9][0-9]*$/)){var c=t.d;t.d=void 0,(n=this.output(t)).push({type_:"tinySkip"}),t.b=c}else n=this.output(t);return y.actions["o="](t,e),n},"d= kv":function(t,e){t.d=e,t.dType="kv"},"charge or bond":function(t,e){if(t.beginsWithBond){var n=[];return y.concatArray(n,this.output(t)),y.concatArray(n,y.actions.bond(t,e,"-")),n}t.d=e},"- after o/d":function(t,e,n){var c=y.patterns.match_("orbital",t.o||""),l=y.patterns.match_("one lowercase greek letter $",t.o||""),d=y.patterns.match_("one lowercase latin letter $",t.o||""),o=y.patterns.match_("$one lowercase latin letter$ $",t.o||""),i="-"===e&&(c&&""===c.remainder||l||d||o);i&&!t.a&&!t.b&&!t.p&&!t.d&&!t.q&&!c&&d&&(t.o="$"+t.o+"$");var f=[];return i?(y.concatArray(f,this.output(t)),f.push({type_:"hyphen"})):(c=y.patterns.match_("digits",t.d||""),n&&c&&""===c.remainder?(y.concatArray(f,y.actions["d="](t,e)),y.concatArray(f,this.output(t))):(y.concatArray(f,this.output(t)),y.concatArray(f,y.actions.bond(t,e,"-")))),f},"a to o":function(t){t.o=t.a,t.a=void 0},"sb=true":function(t){t.sb=!0},"sb=false":function(t){t.sb=!1},"beginsWithBond=true":function(t){t.beginsWithBond=!0},"beginsWithBond=false":function(t){t.beginsWithBond=!1},"parenthesisLevel++":function(t){t.parenthesisLevel++},"parenthesisLevel--":function(t){t.parenthesisLevel--},"state of aggregation":function(t,e){return{type_:"state of aggregation",p1:y.go(e,"o")}},comma:function(t,e){var n=e.replace(/\s*$/,"");return n!==e&&0===t.parenthesisLevel?{type_:"comma enumeration L",p1:n}:{type_:"comma enumeration M",p1:n}},output:function(t,e,n){var c;if(t.r){var l=void 0;l="M"===t.rdt?y.go(t.rd,"tex-math"):"T"===t.rdt?[{type_:"text",p1:t.rd||""}]:y.go(t.rd,"ce");var d=void 0;d="M"===t.rqt?y.go(t.rq,"tex-math"):"T"===t.rqt?[{type_:"text",p1:t.rq||""}]:y.go(t.rq,"ce"),c={type_:"arrow",r:t.r,rd:l,rq:d}}else c=[],!t.a&&!t.b&&!t.p&&!t.o&&!t.q&&!t.d&&!n||(t.sb&&c.push({type_:"entitySkip"}),t.o||t.q||t.d||t.b||t.p||2===n?t.o||t.q||t.d||!t.b&&!t.p?t.o&&"kv"===t.dType&&y.patterns.match_("d-oxidation$",t.d||"")?t.dType="oxidation":t.o&&"kv"===t.dType&&!t.q&&(t.dType=void 0):(t.o=t.a,t.d=t.b,t.q=t.p,t.a=t.b=t.p=void 0):(t.o=t.a,t.a=void 0),c.push({type_:"chemfive",a:y.go(t.a,"a"),b:y.go(t.b,"bd"),p:y.go(t.p,"pq"),o:y.go(t.o,"o"),q:y.go(t.q,"pq"),d:y.go(t.d,"oxidation"===t.dType?"oxidation":"bd"),dType:t.dType}));for(var o in t)"parenthesisLevel"!==o&&"beginsWithBond"!==o&&delete t[o];return c},"oxidation-output":function(t,e){var n=["{"];return y.concatArray(n,y.go(e,"oxidation")),n.push("}"),n},"frac-output":function(t,e){return{type_:"frac-ce",p1:y.go(e[0],"ce"),p2:y.go(e[1],"ce")}},"overset-output":function(t,e){return{type_:"overset",p1:y.go(e[0],"ce"),p2:y.go(e[1],"ce")}},"underset-output":function(t,e){return{type_:"underset",p1:y.go(e[0],"ce"),p2:y.go(e[1],"ce")}},"underbrace-output":function(t,e){return{type_:"underbrace",p1:y.go(e[0],"ce"),p2:y.go(e[1],"ce")}},"color-output":function(t,e){return{type_:"color",color1:e[0],color2:y.go(e[1],"ce")}},"r=":function(t,e){t.r=e},"rdt=":function(t,e){t.rdt=e},"rd=":function(t,e){t.rd=e},"rqt=":function(t,e){t.rqt=e},"rq=":function(t,e){t.rq=e},operator:function(t,e,n){return{type_:"operator",kind_:n||e}}}},a:{transitions:w({empty:{"*":{action_:[]}},"1/2$":{0:{action_:"1/2"}},else:{0:{action_:[],nextState:"1",revisit:!0}},"${(...)}$__$(...)$":{"*":{action_:"tex-math tight",nextState:"1"}},",":{"*":{action_:{type_:"insert",option:"commaDecimal"}}},else2:{"*":{action_:"copy"}}}),actions:{}},o:{transitions:w({empty:{"*":{action_:[]}},"1/2$":{0:{action_:"1/2"}},else:{0:{action_:[],nextState:"1",revisit:!0}},letters:{"*":{action_:"rm"}},"\\ca":{"*":{action_:{type_:"insert",option:"circa"}}},"\\pu{(...)}":{"*":{action_:[{type_:"write",option:"{"},"pu",{type_:"write",option:"}"}]}},"\\x{}{}|\\x{}|\\x":{"*":{action_:"copy"}},"${(...)}$__$(...)$":{"*":{action_:"tex-math"}},"{(...)}":{"*":{action_:[{type_:"write",option:"{"},"text",{type_:"write",option:"}"}]}},else2:{"*":{action_:"copy"}}}),actions:{}},text:{transitions:w({empty:{"*":{action_:"output"}},"{...}":{"*":{action_:"text="}},"${(...)}$__$(...)$":{"*":{action_:"tex-math"}},"\\greek":{"*":{action_:["output","rm"]}},"\\pu{(...)}":{"*":{action_:["output",{type_:"write",option:"{"},"pu",{type_:"write",option:"}"}]}},"\\,|\\x{}{}|\\x{}|\\x":{"*":{action_:["output","copy"]}},else:{"*":{action_:"text="}}}),actions:{output:function(t){if(t.text_){var e={type_:"text",p1:t.text_};for(var n in t)delete t[n];return e}}}},pq:{transitions:w({empty:{"*":{action_:[]}},"state of aggregation $":{"*":{action_:"state of aggregation"}},i$:{0:{action_:[],nextState:"!f",revisit:!0}},"(KV letters),":{0:{action_:"rm",nextState:"0"}},formula$:{0:{action_:[],nextState:"f",revisit:!0}},"1/2$":{0:{action_:"1/2"}},else:{0:{action_:[],nextState:"!f",revisit:!0}},"${(...)}$__$(...)$":{"*":{action_:"tex-math"}},"{(...)}":{"*":{action_:"text"}},"a-z":{f:{action_:"tex-math"}},letters:{"*":{action_:"rm"}},"-9.,9":{"*":{action_:"9,9"}},",":{"*":{action_:{type_:"insert+p1",option:"comma enumeration S"}}},"\\color{(...)}{(...)}":{"*":{action_:"color-output"}},"\\color{(...)}":{"*":{action_:"color0-output"}},"\\ce{(...)}":{"*":{action_:"ce"}},"\\pu{(...)}":{"*":{action_:[{type_:"write",option:"{"},"pu",{type_:"write",option:"}"}]}},"\\,|\\x{}{}|\\x{}|\\x":{"*":{action_:"copy"}},else2:{"*":{action_:"copy"}}}),actions:{"state of aggregation":function(t,e){return{type_:"state of aggregation subscript",p1:y.go(e,"o")}},"color-output":function(t,e){return{type_:"color",color1:e[0],color2:y.go(e[1],"pq")}}}},bd:{transitions:w({empty:{"*":{action_:[]}},x$:{0:{action_:[],nextState:"!f",revisit:!0}},formula$:{0:{action_:[],nextState:"f",revisit:!0}},else:{0:{action_:[],nextState:"!f",revisit:!0}},"-9.,9 no missing 0":{"*":{action_:"9,9"}},".":{"*":{action_:{type_:"insert",option:"electron dot"}}},"a-z":{f:{action_:"tex-math"}},x:{"*":{action_:{type_:"insert",option:"KV x"}}},letters:{"*":{action_:"rm"}},"'":{"*":{action_:{type_:"insert",option:"prime"}}},"${(...)}$__$(...)$":{"*":{action_:"tex-math"}},"{(...)}":{"*":{action_:"text"}},"\\color{(...)}{(...)}":{"*":{action_:"color-output"}},"\\color{(...)}":{"*":{action_:"color0-output"}},"\\ce{(...)}":{"*":{action_:"ce"}},"\\pu{(...)}":{"*":{action_:[{type_:"write",option:"{"},"pu",{type_:"write",option:"}"}]}},"\\,|\\x{}{}|\\x{}|\\x":{"*":{action_:"copy"}},else2:{"*":{action_:"copy"}}}),actions:{"color-output":function(t,e){return{type_:"color",color1:e[0],color2:y.go(e[1],"bd")}}}},oxidation:{transitions:w({empty:{"*":{action_:"roman-numeral"}},"pm-operator":{"*":{action_:{type_:"o=+p1",option:"\\pm"}}},else:{"*":{action_:"o="}}}),actions:{"roman-numeral":function(t){return{type_:"roman numeral",p1:t.o||""}}}},"tex-math":{transitions:w({empty:{"*":{action_:"output"}},"\\ce{(...)}":{"*":{action_:["output","ce"]}},"\\pu{(...)}":{"*":{action_:["output",{type_:"write",option:"{"},"pu",{type_:"write",option:"}"}]}},"{...}|\\,|\\x{}{}|\\x{}|\\x":{"*":{action_:"o="}},else:{"*":{action_:"o="}}}),actions:{output:function(t){if(t.o){var e={type_:"tex-math",p1:t.o};for(var n in t)delete t[n];return e}}}},"tex-math tight":{transitions:w({empty:{"*":{action_:"output"}},"\\ce{(...)}":{"*":{action_:["output","ce"]}},"\\pu{(...)}":{"*":{action_:["output",{type_:"write",option:"{"},"pu",{type_:"write",option:"}"}]}},"{...}|\\,|\\x{}{}|\\x{}|\\x":{"*":{action_:"o="}},"-|+":{"*":{action_:"tight operator"}},else:{"*":{action_:"o="}}}),actions:{"tight operator":function(t,e){t.o=(t.o||"")+"{"+e+"}"},output:function(t){if(t.o){var e={type_:"tex-math",p1:t.o};for(var n in t)delete t[n];return e}}}},"9,9":{transitions:w({empty:{"*":{action_:[]}},",":{"*":{action_:"comma"}},else:{"*":{action_:"copy"}}}),actions:{comma:function(){return{type_:"commaDecimal"}}}},pu:{transitions:w({empty:{"*":{action_:"output"}},space$:{"*":{action_:["output","space"]}},"{[(|)]}":{"0|a":{action_:"copy"}},"(-)(9)^(-9)":{0:{action_:"number^",nextState:"a"}},"(-)(9.,9)(e)(99)":{0:{action_:"enumber",nextState:"a"}},space:{"0|a":{action_:[]}},"pm-operator":{"0|a":{action_:{type_:"operator",option:"\\pm"},nextState:"0"}},operator:{"0|a":{action_:"copy",nextState:"0"}},"//":{d:{action_:"o=",nextState:"/"}},"/":{d:{action_:"o=",nextState:"/"}},"{...}|else":{"0|d":{action_:"d=",nextState:"d"},a:{action_:["space","d="],nextState:"d"},"/|q":{action_:"q=",nextState:"q"}}}),actions:{enumber:function(t,e){var n=[];return"+-"===e[0]||"+/-"===e[0]?n.push("\\pm "):e[0]&&n.push(e[0]),e[1]&&(y.concatArray(n,y.go(e[1],"pu-9,9")),e[2]&&(e[2].match(/[,.]/)?y.concatArray(n,y.go(e[2],"pu-9,9")):n.push(e[2])),(e[3]||e[4])&&("e"===e[3]||"*"===e[4]?n.push({type_:"cdot"}):n.push({type_:"times"}))),e[5]&&n.push("10^{"+e[5]+"}"),n},"number^":function(t,e){var n=[];return"+-"===e[0]||"+/-"===e[0]?n.push("\\pm "):e[0]&&n.push(e[0]),y.concatArray(n,y.go(e[1],"pu-9,9")),n.push("^{"+e[2]+"}"),n},operator:function(t,e,n){return{type_:"operator",kind_:n||e}},space:function(){return{type_:"pu-space-1"}},output:function(t){var e,n=y.patterns.match_("{(...)}",t.d||"");n&&""===n.remainder&&(t.d=n.match_);var c=y.patterns.match_("{(...)}",t.q||"");if(c&&""===c.remainder&&(t.q=c.match_),t.d&&(t.d=t.d.replace(/\u00B0C|\^oC|\^{o}C/g,"{}^{\\circ}C"),t.d=t.d.replace(/\u00B0F|\^oF|\^{o}F/g,"{}^{\\circ}F")),t.q){t.q=t.q.replace(/\u00B0C|\^oC|\^{o}C/g,"{}^{\\circ}C"),t.q=t.q.replace(/\u00B0F|\^oF|\^{o}F/g,"{}^{\\circ}F");var l={d:y.go(t.d,"pu"),q:y.go(t.q,"pu")};"//"===t.o?e={type_:"pu-frac",p1:l.d,p2:l.q}:(e=l.d,l.d.length>1||l.q.length>1?e.push({type_:" / "}):e.push({type_:"/"}),y.concatArray(e,l.q))}else e=y.go(t.d,"pu-2");for(var d in t)delete t[d];return e}}},"pu-2":{transitions:w({empty:{"*":{action_:"output"}},"*":{"*":{action_:["output","cdot"],nextState:"0"}},"\\x":{"*":{action_:"rm="}},space:{"*":{action_:["output","space"],nextState:"0"}},"^{(...)}|^(-1)":{1:{action_:"^(-1)"}},"-9.,9":{0:{action_:"rm=",nextState:"0"},1:{action_:"^(-1)",nextState:"0"}},"{...}|else":{"*":{action_:"rm=",nextState:"1"}}}),actions:{cdot:function(){return{type_:"tight cdot"}},"^(-1)":function(t,e){t.rm+="^{"+e+"}"},space:function(){return{type_:"pu-space-2"}},output:function(t){var e=[];if(t.rm){var n=y.patterns.match_("{(...)}",t.rm||"");e=n&&""===n.remainder?y.go(n.match_,"pu"):{type_:"rm",p1:t.rm}}for(var c in t)delete t[c];return e}}},"pu-9,9":{transitions:w({empty:{0:{action_:"output-0"},o:{action_:"output-o"}},",":{0:{action_:["output-0","comma"],nextState:"o"}},".":{0:{action_:["output-0","copy"],nextState:"o"}},else:{"*":{action_:"text="}}}),actions:{comma:function(){return{type_:"commaDecimal"}},"output-0":function(t){var e=[];if(t.text_=t.text_||"",t.text_.length>4){var n=t.text_.length%3;0===n&&(n=3);for(var c=t.text_.length-3;c>0;c-=3)e.push(t.text_.substr(c,3)),e.push({type_:"1000 separator"});e.push(t.text_.substr(0,n)),e.reverse()}else e.push(t.text_);for(var l in t)delete t[l];return e},"output-o":function(t){var e=[];if(t.text_=t.text_||"",t.text_.length>4){var n=t.text_.length-3,c=void 0;for(c=0;c<n;c+=3)e.push(t.text_.substr(c,3)),e.push({type_:"1000 separator"});e.push(t.text_.substr(c))}else e.push(t.text_);for(var l in t)delete t[l];return e}}}}},M={go:function(t,e){if(!t)return"";for(var n="",c=!1,l=0;l<t.length;l++){var d=t[l];"string"==typeof d?n+=d:(n+=M._go2(d),"1st-level escape"===d.type_&&(c=!0))}return e&&!c&&n&&(n="{"+n+"}"),n},_goInner:function(t){return M.go(t,!1)},_go2:function(t){var e;switch(t.type_){case"chemfive":e="";var n={a:M._goInner(t.a),b:M._goInner(t.b),p:M._goInner(t.p),o:M._goInner(t.o),q:M._goInner(t.q),d:M._goInner(t.d)};n.a&&(n.a.match(/^[+\-]/)&&(n.a="{"+n.a+"}"),e+=n.a+"\\,"),(n.b||n.p)&&(e+="{\\vphantom{A}}",e+="^{\\hphantom{"+(n.b||"")+"}}_{\\hphantom{"+(n.p||"")+"}}",e+="\\mkern-1.5mu",e+="{\\vphantom{A}}",e+="^{\\smash[t]{\\vphantom{2}}\\llap{"+(n.b||"")+"}}",e+="_{\\vphantom{2}\\llap{\\smash[t]{"+(n.p||"")+"}}}"),n.o&&(n.o.match(/^[+\-]/)&&(n.o="{"+n.o+"}"),e+=n.o),"kv"===t.dType?((n.d||n.q)&&(e+="{\\vphantom{A}}"),n.d&&(e+="^{"+n.d+"}"),n.q&&(e+="_{\\smash[t]{"+n.q+"}}")):"oxidation"===t.dType?(n.d&&(e+="{\\vphantom{A}}",e+="^{"+n.d+"}"),n.q&&(e+="{\\vphantom{A}}",e+="_{\\smash[t]{"+n.q+"}}")):(n.q&&(e+="{\\vphantom{A}}",e+="_{\\smash[t]{"+n.q+"}}"),n.d&&(e+="{\\vphantom{A}}",e+="^{"+n.d+"}"));break;case"rm":case"roman numeral":e="\\mathrm{"+t.p1+"}";break;case"text":t.p1.match(/[\^_]/)?(t.p1=t.p1.replace(" ","~").replace("-","\\text{-}"),e="\\mathrm{"+t.p1+"}"):e="\\text{"+t.p1+"}";break;case"state of aggregation":e="\\mskip2mu "+M._goInner(t.p1);break;case"state of aggregation subscript":e="\\mskip1mu "+M._goInner(t.p1);break;case"bond":if(!(e=M._getBond(t.kind_)))throw["MhchemErrorBond","mhchem Error. Unknown bond type ("+t.kind_+")"];break;case"frac":var c="\\frac{"+t.p1+"}{"+t.p2+"}";e="\\mathchoice{\\textstyle"+c+"}{"+c+"}{"+c+"}{"+c+"}";break;case"pu-frac":var l="\\frac{"+M._goInner(t.p1)+"}{"+M._goInner(t.p2)+"}";e="\\mathchoice{\\textstyle"+l+"}{"+l+"}{"+l+"}{"+l+"}";break;case"tex-math":case"1st-level escape":e=t.p1+" ";break;case"frac-ce":e="\\frac{"+M._goInner(t.p1)+"}{"+M._goInner(t.p2)+"}";break;case"overset":e="\\overset{"+M._goInner(t.p1)+"}{"+M._goInner(t.p2)+"}";break;case"underset":e="\\underset{"+M._goInner(t.p1)+"}{"+M._goInner(t.p2)+"}";break;case"underbrace":e="\\underbrace{"+M._goInner(t.p1)+"}_{"+M._goInner(t.p2)+"}";break;case"color":e="{\\color{"+t.color1+"}{"+M._goInner(t.color2)+"}}";break;case"color0":e="\\color{"+t.color+"}";break;case"arrow":var d={rd:M._goInner(t.rd),rq:M._goInner(t.rq)},o=M._getArrow(t.r);d.rd||d.rq?"<=>"===t.r||"<=>>"===t.r||"<<=>"===t.r||"<--\x3e"===t.r?(o="\\long"+o,d.rd&&(o="\\overset{"+d.rd+"}{"+o+"}"),d.rq&&(o="<--\x3e"===t.r?"\\underset{\\lower2mu{"+d.rq+"}}{"+o+"}":"\\underset{\\lower6mu{"+d.rq+"}}{"+o+"}"),o=" {}\\mathrel{"+o+"}{} "):(d.rq&&(o+="[{"+d.rq+"}]"),o=" {}\\mathrel{\\x"+(o+="{"+d.rd+"}")+"}{} "):o=" {}\\mathrel{\\long"+o+"}{} ",e=o;break;case"operator":e=M._getOperator(t.kind_);break;case"space":e=" ";break;case"tinySkip":e="\\mkern2mu";break;case"entitySkip":case"pu-space-1":e="~";break;case"pu-space-2":e="\\mkern3mu ";break;case"1000 separator":e="\\mkern2mu ";break;case"commaDecimal":e="{,}";break;case"comma enumeration L":e="{"+t.p1+"}\\mkern6mu ";break;case"comma enumeration M":e="{"+t.p1+"}\\mkern3mu ";break;case"comma enumeration S":e="{"+t.p1+"}\\mkern1mu ";break;case"hyphen":e="\\text{-}";break;case"addition compound":e="\\,{\\cdot}\\,";break;case"electron dot":e="\\mkern1mu \\bullet\\mkern1mu ";break;case"KV x":e="{\\times}";break;case"prime":e="\\prime ";break;case"cdot":e="\\cdot ";break;case"tight cdot":e="\\mkern1mu{\\cdot}\\mkern1mu ";break;case"times":e="\\times ";break;case"circa":e="{\\sim}";break;case"^":e="uparrow";break;case"v":e="downarrow";break;case"ellipsis":e="\\ldots ";break;case"/":e="/";break;case" / ":e="\\,/\\,";break;default:throw["MhchemBugT","mhchem bug T. Please report."]}return e},_getArrow:function(t){switch(t){case"->":case"\u2192":case"\u27f6":return"rightarrow";case"<-":return"leftarrow";case"<->":return"leftrightarrow";case"<--\x3e":return"leftrightarrows";case"<=>":case"\u21cc":return"rightleftharpoons";case"<=>>":return"Rightleftharpoons";case"<<=>":return"Leftrightharpoons";default:throw["MhchemBugT","mhchem bug T. Please report."]}},_getBond:function(t){switch(t){case"-":case"1":return"{-}";case"=":case"2":return"{=}";case"#":case"3":return"{\\equiv}";case"~":return"{\\tripledash}";case"~-":return"{\\rlap{\\lower.1em{-}}\\raise.1em{\\tripledash}}";case"~=":case"~--":return"{\\rlap{\\lower.2em{-}}\\rlap{\\raise.2em{\\tripledash}}-}";case"-~-":return"{\\rlap{\\lower.2em{-}}\\rlap{\\raise.2em{-}}\\tripledash}";case"...":return"{{\\cdot}{\\cdot}{\\cdot}}";case"....":return"{{\\cdot}{\\cdot}{\\cdot}{\\cdot}}";case"->":return"{\\rightarrow}";case"<-":return"{\\leftarrow}";case"<":return"{<}";case">":return"{>}";default:throw["MhchemBugT","mhchem bug T. Please report."]}},_getOperator:function(t){switch(t){case"+":return" {}+{} ";case"-":return" {}-{} ";case"=":return" {}={} ";case"<":return" {}<{} ";case">":return" {}>{} ";case"<<":return" {}\\ll{} ";case">>":return" {}\\gg{} ";case"\\pm":return" {}\\pm{} ";case"\\approx":case"$\\approx$":return" {}\\approx{} ";case"v":case"(v)":return" \\downarrow{} ";case"^":case"(^)":return" \\uparrow{} ";default:throw["MhchemBugT","mhchem bug T. Please report."]}}}}}]);