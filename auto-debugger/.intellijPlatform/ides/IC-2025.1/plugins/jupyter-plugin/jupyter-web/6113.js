"use strict";(self.webpackChunkjupyter_web=self.webpackChunkjupyter_web||[]).push([[6113],{95272:function(V,v){var I=this&&this.__values||function(w){var A="function"==typeof Symbol&&Symbol.iterator,s=A&&w[A],m=0;if(s)return s.call(w);if(w&&"number"==typeof w.length)return{next:function(){return w&&m>=w.length&&(w=void 0),{value:w&&w[m++],done:!w}}};throw new TypeError(A?"Object is not iterable.":"Symbol.iterator is not defined.")},C=this&&this.__read||function(w,A){var s="function"==typeof Symbol&&w[Symbol.iterator];if(!s)return w;var u,e,m=s.call(w),f=[];try{for(;(void 0===A||A-- >0)&&!(u=m.next()).done;)f.push(u.value)}catch(o){e={error:o}}finally{try{u&&!u.done&&(s=m.return)&&s.call(m)}finally{if(e)throw e.error}}return f},D=this&&this.__spreadArray||function(w,A,s){if(s||2===arguments.length)for(var f,m=0,u=A.length;m<u;m++)(f||!(m in A))&&(f||(f=Array.prototype.slice.call(A,0,m)),f[m]=A[m]);return w.concat(f||Array.prototype.slice.call(A))};Object.defineProperty(v,"__esModule",{value:!0}),v.AbstractFactory=void 0;var _=function(){function w(A){var s,m;void 0===A&&(A=null),this.defaultKind="unknown",this.nodeMap=new Map,this.node={},null===A&&(A=this.constructor.defaultNodes);try{for(var u=I(Object.keys(A)),f=u.next();!f.done;f=u.next()){var e=f.value;this.setNodeClass(e,A[e])}}catch(o){s={error:o}}finally{try{f&&!f.done&&(m=u.return)&&m.call(u)}finally{if(s)throw s.error}}}return w.prototype.create=function(A){for(var s=[],m=1;m<arguments.length;m++)s[m-1]=arguments[m];return(this.node[A]||this.node[this.defaultKind]).apply(void 0,D([],C(s),!1))},w.prototype.setNodeClass=function(A,s){this.nodeMap.set(A,s);var m=this,u=this.nodeMap.get(A);this.node[A]=function(){for(var f=[],e=0;e<arguments.length;e++)f[e]=arguments[e];return new(u.bind.apply(u,D([void 0,m],C(f),!1)))}},w.prototype.getNodeClass=function(A){return this.nodeMap.get(A)},w.prototype.deleteNodeClass=function(A){this.nodeMap.delete(A),delete this.node[A]},w.prototype.nodeIsKind=function(A,s){return A instanceof this.getNodeClass(s)},w.prototype.getKinds=function(){return Array.from(this.nodeMap.keys())},w.defaultNodes={},w}();v.AbstractFactory=_},42023:function(V,v,I){var C=this&&this.__read||function(s,m){var u="function"==typeof Symbol&&s[Symbol.iterator];if(!u)return s;var e,h,f=u.call(s),o=[];try{for(;(void 0===m||m-- >0)&&!(e=f.next()).done;)o.push(e.value)}catch(d){h={error:d}}finally{try{e&&!e.done&&(u=f.return)&&u.call(f)}finally{if(h)throw h.error}}return o},D=this&&this.__spreadArray||function(s,m,u){if(u||2===arguments.length)for(var o,f=0,e=m.length;f<e;f++)(o||!(f in m))&&(o||(o=Array.prototype.slice.call(m,0,f)),o[f]=m[f]);return s.concat(o||Array.prototype.slice.call(m))},_=this&&this.__importDefault||function(s){return s&&s.__esModule?s:{default:s}};Object.defineProperty(v,"__esModule",{value:!0}),v.NodeFactory=void 0;var w=_(I(60909)),A=function(){function s(){this.mmlFactory=null,this.factory={node:s.createNode,token:s.createToken,text:s.createText,error:s.createError}}return s.createNode=function(m,u,f,e,o){void 0===f&&(f=[]),void 0===e&&(e={});var h=m.mmlFactory.create(u);return h.setChildren(f),o&&h.appendChild(o),w.default.setProperties(h,e),h},s.createToken=function(m,u,f,e){void 0===f&&(f={}),void 0===e&&(e="");var o=m.create("text",e);return m.create("node",u,[],f,o)},s.createText=function(m,u){return null==u?null:m.mmlFactory.create("text").setText(u)},s.createError=function(m,u){var f=m.create("text",u),e=m.create("node","mtext",[],{},f);return m.create("node","merror",[e],{"data-mjx-error":u})},s.prototype.setMmlFactory=function(m){this.mmlFactory=m},s.prototype.set=function(m,u){this.factory[m]=u},s.prototype.setCreators=function(m){for(var u in m)this.set(u,m[u])},s.prototype.create=function(m){for(var u=[],f=1;f<arguments.length;f++)u[f-1]=arguments[f];var e=this.factory[m]||this.factory.node,o=e.apply(void 0,D([this,u[0]],C(u.slice(1)),!1));return"node"===m&&this.configuration.addNode(u[0],o),o},s.prototype.get=function(m){return this.factory[m]},s}();v.NodeFactory=A},70004:function(V,v,I){var C=this&&this.__read||function(u,f){var e="function"==typeof Symbol&&u[Symbol.iterator];if(!e)return u;var h,P,o=e.call(u),d=[];try{for(;(void 0===f||f-- >0)&&!(h=o.next()).done;)d.push(h.value)}catch(F){P={error:F}}finally{try{h&&!h.done&&(e=o.return)&&e.call(o)}finally{if(P)throw P.error}}return d},D=this&&this.__spreadArray||function(u,f,e){if(e||2===arguments.length)for(var d,o=0,h=f.length;o<h;o++)(d||!(o in f))&&(d||(d=Array.prototype.slice.call(f,0,o)),d[o]=f[o]);return u.concat(d||Array.prototype.slice.call(f))},_=this&&this.__importDefault||function(u){return u&&u.__esModule?u:{default:u}};Object.defineProperty(v,"__esModule",{value:!0});var m,u,w=_(I(60909)),A=I(60742),s=_(I(39194));(u=m||(m={})).variable=function f(E,g){var x=s.default.getFontDef(E),j=E.stack.env;j.multiLetterIdentifiers&&""!==j.font&&(g=E.string.substr(E.i-1).match(j.multiLetterIdentifiers)[0],E.i+=g.length-1,x.mathvariant===A.TexConstant.Variant.NORMAL&&j.noAutoOP&&g.length>1&&(x.autoOP=!1));var O=E.create("token","mi",x,g);E.Push(O)},u.digit=function e(E,g){var x,j=E.configuration.options.digits,O=E.string.slice(E.i-1).match(j),t=s.default.getFontDef(E);O?(x=E.create("token","mn",t,O[0].replace(/[{}]/g,"")),E.i+=O[0].length-1):x=E.create("token","mo",t,g),E.Push(x)},u.controlSequence=function o(E,g){var x=E.GetCS();E.parse("macro",[E,x])},u.mathchar0mi=function h(E,g){var x=g.attributes||{mathvariant:A.TexConstant.Variant.ITALIC},j=E.create("token","mi",x,g.char);E.Push(j)},u.mathchar0mo=function d(E,g){var x=g.attributes||{};x.stretchy=!1;var j=E.create("token","mo",x,g.char);w.default.setProperty(j,"fixStretchy",!0),E.configuration.addNode("fixStretchy",j),E.Push(j)},u.mathchar7=function P(E,g){var x=g.attributes||{mathvariant:A.TexConstant.Variant.NORMAL};E.stack.env.font&&(x.mathvariant=E.stack.env.font);var j=E.create("token","mi",x,g.char);E.Push(j)},u.delimiter=function F(E,g){var x=g.attributes||{};x=Object.assign({fence:!1,stretchy:!1},x);var j=E.create("token","mo",x,g.char);E.Push(j)},u.environment=function N(E,g,x,j){var O=j[0],t=E.itemFactory.create("begin").setProperties({name:g,end:O});t=x.apply(void 0,D([E,t],C(j.slice(1)),!1)),E.Push(t)},v.default=m},46708:function(V,v,I){var C=this&&this.__read||function(e,o){var h="function"==typeof Symbol&&e[Symbol.iterator];if(!h)return e;var P,N,d=h.call(e),F=[];try{for(;(void 0===o||o-- >0)&&!(P=d.next()).done;)F.push(P.value)}catch(E){N={error:E}}finally{try{P&&!P.done&&(h=d.return)&&h.call(d)}finally{if(N)throw N.error}}return F},D=this&&this.__spreadArray||function(e,o,h){if(h||2===arguments.length)for(var F,d=0,P=o.length;d<P;d++)(F||!(d in o))&&(F||(F=Array.prototype.slice.call(o,0,d)),F[d]=o[d]);return e.concat(F||Array.prototype.slice.call(o))},_=this&&this.__values||function(e){var o="function"==typeof Symbol&&Symbol.iterator,h=o&&e[o],d=0;if(h)return h.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&d>=e.length&&(e=void 0),{value:e&&e[d++],done:!e}}};throw new TypeError(o?"Object is not iterable.":"Symbol.iterator is not defined.")},w=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(v,"__esModule",{value:!0});var A=w(I(20164)),s=I(42023),m=w(I(60909)),u=I(80084),f=function(){function e(o,h){void 0===h&&(h=[]),this.options={},this.packageData=new Map,this.parsers=[],this.root=null,this.nodeLists={},this.error=!1,this.handlers=o.handlers,this.nodeFactory=new s.NodeFactory,this.nodeFactory.configuration=this,this.nodeFactory.setCreators(o.nodes),this.itemFactory=new A.default(o.items),this.itemFactory.configuration=this,u.defaultOptions.apply(void 0,D([this.options],C(h),!1)),(0,u.defaultOptions)(this.options,o.options)}return e.prototype.pushParser=function(o){this.parsers.unshift(o)},e.prototype.popParser=function(){this.parsers.shift()},Object.defineProperty(e.prototype,"parser",{get:function(){return this.parsers[0]},enumerable:!1,configurable:!0}),e.prototype.clear=function(){this.parsers=[],this.root=null,this.nodeLists={},this.error=!1,this.tags.resetTag()},e.prototype.addNode=function(o,h){var d=this.nodeLists[o];if(d||(d=this.nodeLists[o]=[]),d.push(h),h.kind!==o){var P=m.default.getProperty(h,"in-lists")||"",F=(P?P.split(/,/):[]).concat(o).join(",");m.default.setProperty(h,"in-lists",F)}},e.prototype.getList=function(o){var h,d,P=this.nodeLists[o]||[],F=[];try{for(var N=_(P),E=N.next();!E.done;E=N.next()){var g=E.value;this.inTree(g)&&F.push(g)}}catch(x){h={error:x}}finally{try{E&&!E.done&&(d=N.return)&&d.call(N)}finally{if(h)throw h.error}}return this.nodeLists[o]=F,F},e.prototype.removeFromList=function(o,h){var d,P,F=this.nodeLists[o]||[];try{for(var N=_(h),E=N.next();!E.done;E=N.next()){var g=E.value,x=F.indexOf(g);x>=0&&F.splice(x,1)}}catch(j){d={error:j}}finally{try{E&&!E.done&&(P=N.return)&&P.call(N)}finally{if(d)throw d.error}}},e.prototype.inTree=function(o){for(;o&&o!==this.root;)o=o.parent;return!!o},e}();v.default=f},90117:function(V,v,I){var f,C=this&&this.__extends||(f=function(e,o){return(f=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(h,d){h.__proto__=d}||function(h,d){for(var P in d)Object.prototype.hasOwnProperty.call(d,P)&&(h[P]=d[P])})(e,o)},function(e,o){if("function"!=typeof o&&null!==o)throw new TypeError("Class extends value "+String(o)+" is not a constructor or null");function h(){this.constructor=e}f(e,o),e.prototype=null===o?Object.create(o):(h.prototype=o.prototype,new h)}),D=this&&this.__read||function(f,e){var o="function"==typeof Symbol&&f[Symbol.iterator];if(!o)return f;var d,F,h=o.call(f),P=[];try{for(;(void 0===e||e-- >0)&&!(d=h.next()).done;)P.push(d.value)}catch(N){F={error:N}}finally{try{d&&!d.done&&(o=h.return)&&o.call(h)}finally{if(F)throw F.error}}return P},_=this&&this.__spreadArray||function(f,e,o){if(o||2===arguments.length)for(var P,h=0,d=e.length;h<d;h++)(P||!(h in e))&&(P||(P=Array.prototype.slice.call(e,0,h)),P[h]=e[h]);return f.concat(P||Array.prototype.slice.call(e))},w=this&&this.__values||function(f){var e="function"==typeof Symbol&&Symbol.iterator,o=e&&f[e],h=0;if(o)return o.call(f);if(f&&"number"==typeof f.length)return{next:function(){return f&&h>=f.length&&(f=void 0),{value:f&&f[h++],done:!f}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},A=this&&this.__importDefault||function(f){return f&&f.__esModule?f:{default:f}};Object.defineProperty(v,"__esModule",{value:!0}),v.BaseItem=v.MmlStack=void 0;var s=A(I(6257)),m=function(){function f(e){this._nodes=e}return Object.defineProperty(f.prototype,"nodes",{get:function(){return this._nodes},enumerable:!1,configurable:!0}),f.prototype.Push=function(){for(var e,o=[],h=0;h<arguments.length;h++)o[h]=arguments[h];(e=this._nodes).push.apply(e,_([],D(o),!1))},f.prototype.Pop=function(){return this._nodes.pop()},Object.defineProperty(f.prototype,"First",{get:function(){return this._nodes[this.Size()-1]},set:function(e){this._nodes[this.Size()-1]=e},enumerable:!1,configurable:!0}),Object.defineProperty(f.prototype,"Last",{get:function(){return this._nodes[0]},set:function(e){this._nodes[0]=e},enumerable:!1,configurable:!0}),f.prototype.Peek=function(e){return null==e&&(e=1),this._nodes.slice(this.Size()-e)},f.prototype.Size=function(){return this._nodes.length},f.prototype.Clear=function(){this._nodes=[]},f.prototype.toMml=function(e,o){return void 0===e&&(e=!0),1!==this._nodes.length||o?this.create("node",e?"inferredMrow":"mrow",this._nodes,{}):this.First},f.prototype.create=function(e){for(var o,h=[],d=1;d<arguments.length;d++)h[d-1]=arguments[d];return(o=this.factory.configuration.nodeFactory).create.apply(o,_([e],D(h),!1))},f}();v.MmlStack=m;var u=function(f){function e(o){for(var h=[],d=1;d<arguments.length;d++)h[d-1]=arguments[d];var P=f.call(this,h)||this;return P.factory=o,P.global={},P._properties={},P.isOpen&&(P._env={}),P}return C(e,f),Object.defineProperty(e.prototype,"kind",{get:function(){return"base"},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"env",{get:function(){return this._env},set:function(o){this._env=o},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"copyEnv",{get:function(){return!0},enumerable:!1,configurable:!0}),e.prototype.getProperty=function(o){return this._properties[o]},e.prototype.setProperty=function(o,h){return this._properties[o]=h,this},Object.defineProperty(e.prototype,"isOpen",{get:function(){return!1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isClose",{get:function(){return!1},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"isFinal",{get:function(){return!1},enumerable:!1,configurable:!0}),e.prototype.isKind=function(o){return o===this.kind},e.prototype.checkItem=function(o){if(o.isKind("over")&&this.isOpen&&(o.setProperty("num",this.toMml(!1)),this.Clear()),o.isKind("cell")&&this.isOpen){if(o.getProperty("linebreak"))return e.fail;throw new s.default("Misplaced","Misplaced %1",o.getName())}if(o.isClose&&this.getErrors(o.kind)){var h=D(this.getErrors(o.kind),2),d=h[0],P=h[1];throw new s.default(d,P,o.getName())}return o.isFinal?(this.Push(o.First),e.fail):e.success},e.prototype.clearEnv=function(){var o,h;try{for(var d=w(Object.keys(this.env)),P=d.next();!P.done;P=d.next()){var F=P.value;delete this.env[F]}}catch(N){o={error:N}}finally{try{P&&!P.done&&(h=d.return)&&h.call(d)}finally{if(o)throw o.error}}},e.prototype.setProperties=function(o){return Object.assign(this._properties,o),this},e.prototype.getName=function(){return this.getProperty("name")},e.prototype.toString=function(){return this.kind+"["+this.nodes.join("; ")+"]"},e.prototype.getErrors=function(o){return(this.constructor.errors||{})[o]||e.errors[o]},e.fail=[null,!1],e.success=[null,!0],e.errors={end:["MissingBeginExtraEnd","Missing \\begin{%1} or extra \\end{%1}"],close:["ExtraCloseMissingOpen","Extra close brace or missing open brace"],right:["MissingLeftExtraRight","Missing \\left or extra \\right"],middle:["ExtraMiddle","Extra \\middle"]},e}(m);v.BaseItem=u},20164:function(V,v,I){var D,m,C=this&&this.__extends||(m=function(u,f){return(m=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,o){e.__proto__=o}||function(e,o){for(var h in o)Object.prototype.hasOwnProperty.call(o,h)&&(e[h]=o[h])})(u,f)},function(u,f){if("function"!=typeof f&&null!==f)throw new TypeError("Class extends value "+String(f)+" is not a constructor or null");function e(){this.constructor=u}m(u,f),u.prototype=null===f?Object.create(f):(e.prototype=f.prototype,new e)});Object.defineProperty(v,"__esModule",{value:!0});var _=I(90117),w=I(95272),A=function(m){function u(){return null!==m&&m.apply(this,arguments)||this}return C(u,m),u}(_.BaseItem),s=function(m){function u(){var f=null!==m&&m.apply(this,arguments)||this;return f.defaultKind="dummy",f.configuration=null,f}return C(u,m),u.DefaultStackItems=((D={})[A.prototype.kind]=A,D),u}(w.AbstractFactory);v.default=s},60742:(V,v)=>{var C;Object.defineProperty(v,"__esModule",{value:!0}),v.TexConstant=void 0,(C=v.TexConstant||(v.TexConstant={})).Variant={NORMAL:"normal",BOLD:"bold",ITALIC:"italic",BOLDITALIC:"bold-italic",DOUBLESTRUCK:"double-struck",FRAKTUR:"fraktur",BOLDFRAKTUR:"bold-fraktur",SCRIPT:"script",BOLDSCRIPT:"bold-script",SANSSERIF:"sans-serif",BOLDSANSSERIF:"bold-sans-serif",SANSSERIFITALIC:"sans-serif-italic",SANSSERIFBOLDITALIC:"sans-serif-bold-italic",MONOSPACE:"monospace",INITIAL:"inital",TAILED:"tailed",LOOPED:"looped",STRETCHED:"stretched",CALLIGRAPHIC:"-tex-calligraphic",BOLDCALLIGRAPHIC:"-tex-bold-calligraphic",OLDSTYLE:"-tex-oldstyle",BOLDOLDSTYLE:"-tex-bold-oldstyle",MATHITALIC:"-tex-mathit"},C.Form={PREFIX:"prefix",INFIX:"infix",POSTFIX:"postfix"},C.LineBreak={AUTO:"auto",NEWLINE:"newline",NOBREAK:"nobreak",GOODBREAK:"goodbreak",BADBREAK:"badbreak"},C.LineBreakStyle={BEFORE:"before",AFTER:"after",DUPLICATE:"duplicate",INFIXLINBREAKSTYLE:"infixlinebreakstyle"},C.IndentAlign={LEFT:"left",CENTER:"center",RIGHT:"right",AUTO:"auto",ID:"id",INDENTALIGN:"indentalign"},C.IndentShift={INDENTSHIFT:"indentshift"},C.LineThickness={THIN:"thin",MEDIUM:"medium",THICK:"thick"},C.Notation={LONGDIV:"longdiv",ACTUARIAL:"actuarial",PHASORANGLE:"phasorangle",RADICAL:"radical",BOX:"box",ROUNDEDBOX:"roundedbox",CIRCLE:"circle",LEFT:"left",RIGHT:"right",TOP:"top",BOTTOM:"bottom",UPDIAGONALSTRIKE:"updiagonalstrike",DOWNDIAGONALSTRIKE:"downdiagonalstrike",VERTICALSTRIKE:"verticalstrike",HORIZONTALSTRIKE:"horizontalstrike",NORTHEASTARROW:"northeastarrow",MADRUWB:"madruwb",UPDIAGONALARROW:"updiagonalarrow"},C.Align={TOP:"top",BOTTOM:"bottom",CENTER:"center",BASELINE:"baseline",AXIS:"axis",LEFT:"left",RIGHT:"right"},C.Lines={NONE:"none",SOLID:"solid",DASHED:"dashed"},C.Side={LEFT:"left",RIGHT:"right",LEFTOVERLAP:"leftoverlap",RIGHTOVERLAP:"rightoverlap"},C.Width={AUTO:"auto",FIT:"fit"},C.Actiontype={TOGGLE:"toggle",STATUSLINE:"statusline",TOOLTIP:"tooltip",INPUT:"input"},C.Overflow={LINBREAK:"linebreak",SCROLL:"scroll",ELIDE:"elide",TRUNCATE:"truncate",SCALE:"scale"},C.Unit={EM:"em",EX:"ex",PX:"px",IN:"in",CM:"cm",MM:"mm",PT:"pt",PC:"pc"}},80677:function(V,v,I){var m,O,C=this&&this.__extends||(O=function(t,i){return(O=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,a){r.__proto__=a}||function(r,a){for(var n in a)Object.prototype.hasOwnProperty.call(a,n)&&(r[n]=a[n])})(t,i)},function(t,i){if("function"!=typeof i&&null!==i)throw new TypeError("Class extends value "+String(i)+" is not a constructor or null");function r(){this.constructor=t}O(t,i),t.prototype=null===i?Object.create(i):(r.prototype=i.prototype,new r)}),D=this&&this.__createBinding||(Object.create?function(O,t,i,r){void 0===r&&(r=i);var a=Object.getOwnPropertyDescriptor(t,i);(!a||("get"in a?!t.__esModule:a.writable||a.configurable))&&(a={enumerable:!0,get:function(){return t[i]}}),Object.defineProperty(O,r,a)}:function(O,t,i,r){void 0===r&&(r=i),O[r]=t[i]}),_=this&&this.__setModuleDefault||(Object.create?function(O,t){Object.defineProperty(O,"default",{enumerable:!0,value:t})}:function(O,t){O.default=t}),w=this&&this.__importStar||function(O){if(O&&O.__esModule)return O;var t={};if(null!=O)for(var i in O)"default"!==i&&Object.prototype.hasOwnProperty.call(O,i)&&D(t,O,i);return _(t,O),t},A=this&&this.__values||function(O){var t="function"==typeof Symbol&&Symbol.iterator,i=t&&O[t],r=0;if(i)return i.call(O);if(O&&"number"==typeof O.length)return{next:function(){return O&&r>=O.length&&(O=void 0),{value:O&&O[r++],done:!O}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")},s=this&&this.__importDefault||function(O){return O&&O.__esModule?O:{default:O}};Object.defineProperty(v,"__esModule",{value:!0}),v.BaseConfiguration=v.BaseTags=v.Other=void 0;var u=I(8890),f=I(96393),e=s(I(6257)),o=s(I(60909)),h=I(82652),d=w(I(33012)),P=I(22450);I(73338);var F=I(75738);function N(O,t){var r=O.stack.env.font?{mathvariant:O.stack.env.font}:{},a=f.MapHandler.getMap("remap").lookup(t),n=(0,F.getRange)(t),b=n?n[3]:"mo",T=O.create("token",b,r,a?a.char:t);n[4]&&T.attributes.set("mathvariant",n[4]),"mo"===b&&(o.default.setProperty(T,"fixStretchy",!0),O.configuration.addNode("fixStretchy",T)),O.Push(T)}new h.CharacterMap("remap",null,{"-":"\u2212","*":"\u2217","`":"\u2018"}),v.Other=N;var j=function(O){function t(){return null!==O&&O.apply(this,arguments)||this}return C(t,O),t}(P.AbstractTags);v.BaseTags=j,v.BaseConfiguration=u.Configuration.create("base",{handler:{character:["command","special","letter","digit"],delimiter:["delimiter"],macro:["delimiter","macros","mathchar0mi","mathchar0mo","mathchar7"],environment:["environment"]},fallback:{character:N,macro:function E(O,t){throw new e.default("UndefinedControlSequence","Undefined control sequence %1","\\"+t)},environment:function g(O,t){throw new e.default("UnknownEnv","Unknown environment '%1'",t)}},items:(m={},m[d.StartItem.prototype.kind]=d.StartItem,m[d.StopItem.prototype.kind]=d.StopItem,m[d.OpenItem.prototype.kind]=d.OpenItem,m[d.CloseItem.prototype.kind]=d.CloseItem,m[d.PrimeItem.prototype.kind]=d.PrimeItem,m[d.SubsupItem.prototype.kind]=d.SubsupItem,m[d.OverItem.prototype.kind]=d.OverItem,m[d.LeftItem.prototype.kind]=d.LeftItem,m[d.Middle.prototype.kind]=d.Middle,m[d.RightItem.prototype.kind]=d.RightItem,m[d.BeginItem.prototype.kind]=d.BeginItem,m[d.EndItem.prototype.kind]=d.EndItem,m[d.StyleItem.prototype.kind]=d.StyleItem,m[d.PositionItem.prototype.kind]=d.PositionItem,m[d.CellItem.prototype.kind]=d.CellItem,m[d.MmlItem.prototype.kind]=d.MmlItem,m[d.FnItem.prototype.kind]=d.FnItem,m[d.NotItem.prototype.kind]=d.NotItem,m[d.NonscriptItem.prototype.kind]=d.NonscriptItem,m[d.DotsItem.prototype.kind]=d.DotsItem,m[d.ArrayItem.prototype.kind]=d.ArrayItem,m[d.EqnArrayItem.prototype.kind]=d.EqnArrayItem,m[d.EquationItem.prototype.kind]=d.EquationItem,m),options:{maxMacros:1e3,baseURL:typeof document>"u"||0===document.getElementsByTagName("base").length?"":String(document.location).replace(/#.*$/,"")},tags:{base:j},postprocessors:[[function x(O){var t,i,r=O.data;try{for(var a=A(r.getList("nonscript")),n=a.next();!n.done;n=a.next()){var b=n.value;if(b.attributes.get("scriptlevel")>0){var T=b.parent;if(T.childNodes.splice(T.childIndex(b),1),r.removeFromList(b.kind,[b]),b.isKind("mrow")){var M=b.childNodes[0];r.removeFromList("mstyle",[M]),r.removeFromList("mspace",M.childNodes[0].childNodes)}}else b.isKind("mrow")&&(b.parent.replaceChild(b.childNodes[0],b),r.removeFromList("mrow",[b]))}}catch(k){t={error:k}}finally{try{n&&!n.done&&(i=a.return)&&i.call(a)}finally{if(t)throw t.error}}},-4]]})},33012:function(V,v,I){var y,C=this&&this.__extends||(y=function(c,l){return(y=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(p,S){p.__proto__=S}||function(p,S){for(var L in S)Object.prototype.hasOwnProperty.call(S,L)&&(p[L]=S[L])})(c,l)},function(c,l){if("function"!=typeof l&&null!==l)throw new TypeError("Class extends value "+String(l)+" is not a constructor or null");function p(){this.constructor=c}y(c,l),c.prototype=null===l?Object.create(l):(p.prototype=l.prototype,new p)}),D=this&&this.__read||function(y,c){var l="function"==typeof Symbol&&y[Symbol.iterator];if(!l)return y;var S,U,p=l.call(y),L=[];try{for(;(void 0===c||c-- >0)&&!(S=p.next()).done;)L.push(S.value)}catch(H){U={error:H}}finally{try{S&&!S.done&&(l=p.return)&&l.call(p)}finally{if(U)throw U.error}}return L},_=this&&this.__spreadArray||function(y,c,l){if(l||2===arguments.length)for(var L,p=0,S=c.length;p<S;p++)(L||!(p in c))&&(L||(L=Array.prototype.slice.call(c,0,p)),L[p]=c[p]);return y.concat(L||Array.prototype.slice.call(c))},w=this&&this.__importDefault||function(y){return y&&y.__esModule?y:{default:y}};Object.defineProperty(v,"__esModule",{value:!0}),v.EquationItem=v.EqnArrayItem=v.ArrayItem=v.DotsItem=v.NonscriptItem=v.NotItem=v.FnItem=v.MmlItem=v.CellItem=v.PositionItem=v.StyleItem=v.EndItem=v.BeginItem=v.RightItem=v.Middle=v.LeftItem=v.OverItem=v.SubsupItem=v.PrimeItem=v.CloseItem=v.OpenItem=v.StopItem=v.StartItem=void 0;var A=I(96393),s=I(56692),m=I(91585),u=w(I(6257)),f=w(I(39194)),e=w(I(60909)),o=I(90117),h=function(y){function c(l,p){var S=y.call(this,l)||this;return S.global=p,S}return C(c,y),Object.defineProperty(c.prototype,"kind",{get:function(){return"start"},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),c.prototype.checkItem=function(l){if(l.isKind("stop")){var p=this.toMml();return this.global.isInner||(p=this.factory.configuration.tags.finalize(p,this.env)),[[this.factory.create("mml",p)],!0]}return y.prototype.checkItem.call(this,l)},c}(o.BaseItem);v.StartItem=h;var d=function(y){function c(){return null!==y&&y.apply(this,arguments)||this}return C(c,y),Object.defineProperty(c.prototype,"kind",{get:function(){return"stop"},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),c}(o.BaseItem);v.StopItem=d;var P=function(y){function c(){return null!==y&&y.apply(this,arguments)||this}return C(c,y),Object.defineProperty(c.prototype,"kind",{get:function(){return"open"},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),c.prototype.checkItem=function(l){if(l.isKind("close")){var p=this.toMml(),S=this.create("node","TeXAtom",[p]);return[[this.factory.create("mml",S)],!0]}return y.prototype.checkItem.call(this,l)},c.errors=Object.assign(Object.create(o.BaseItem.errors),{stop:["ExtraOpenMissingClose","Extra open brace or missing close brace"]}),c}(o.BaseItem);v.OpenItem=P;var F=function(y){function c(){return null!==y&&y.apply(this,arguments)||this}return C(c,y),Object.defineProperty(c.prototype,"kind",{get:function(){return"close"},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),c}(o.BaseItem);v.CloseItem=F;var N=function(y){function c(){return null!==y&&y.apply(this,arguments)||this}return C(c,y),Object.defineProperty(c.prototype,"kind",{get:function(){return"prime"},enumerable:!1,configurable:!0}),c.prototype.checkItem=function(l){var p=D(this.Peek(2),2),S=p[0],L=p[1];return!e.default.isType(S,"msubsup")||e.default.isType(S,"msup")?[[this.create("node","msup",[S,L]),l],!0]:(e.default.setChild(S,S.sup,L),[[S,l],!0])},c}(o.BaseItem);v.PrimeItem=N;var E=function(y){function c(){return null!==y&&y.apply(this,arguments)||this}return C(c,y),Object.defineProperty(c.prototype,"kind",{get:function(){return"subsup"},enumerable:!1,configurable:!0}),c.prototype.checkItem=function(l){if(l.isKind("open")||l.isKind("left"))return o.BaseItem.success;var p=this.First,S=this.getProperty("position");if(l.isKind("mml")){if(this.getProperty("primes"))if(2!==S)e.default.setChild(p,2,this.getProperty("primes"));else{e.default.setProperty(this.getProperty("primes"),"variantForm",!0);var L=this.create("node","mrow",[this.getProperty("primes"),l.First]);l.First=L}return e.default.setChild(p,S,l.First),null!=this.getProperty("movesupsub")&&e.default.setProperty(p,"movesupsub",this.getProperty("movesupsub")),[[this.factory.create("mml",p)],!0]}if(y.prototype.checkItem.call(this,l)[1]){var H=this.getErrors(["","sub","sup"][S]);throw new(u.default.bind.apply(u.default,_([void 0,H[0],H[1]],D(H.splice(2)),!1)))}return null},c.errors=Object.assign(Object.create(o.BaseItem.errors),{stop:["MissingScript","Missing superscript or subscript argument"],sup:["MissingOpenForSup","Missing open brace for superscript"],sub:["MissingOpenForSub","Missing open brace for subscript"]}),c}(o.BaseItem);v.SubsupItem=E;var g=function(y){function c(l){var p=y.call(this,l)||this;return p.setProperty("name","\\over"),p}return C(c,y),Object.defineProperty(c.prototype,"kind",{get:function(){return"over"},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),c.prototype.checkItem=function(l){if(l.isKind("over"))throw new u.default("AmbiguousUseOf","Ambiguous use of %1",l.getName());if(l.isClose){var p=this.create("node","mfrac",[this.getProperty("num"),this.toMml(!1)]);return null!=this.getProperty("thickness")&&e.default.setAttribute(p,"linethickness",this.getProperty("thickness")),(this.getProperty("open")||this.getProperty("close"))&&(e.default.setProperty(p,"withDelims",!0),p=f.default.fixedFence(this.factory.configuration,this.getProperty("open"),p,this.getProperty("close"))),[[this.factory.create("mml",p),l],!0]}return y.prototype.checkItem.call(this,l)},c.prototype.toString=function(){return"over["+this.getProperty("num")+" / "+this.nodes.join("; ")+"]"},c}(o.BaseItem);v.OverItem=g;var x=function(y){function c(l,p){var S=y.call(this,l)||this;return S.setProperty("delim",p),S}return C(c,y),Object.defineProperty(c.prototype,"kind",{get:function(){return"left"},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),c.prototype.checkItem=function(l){if(l.isKind("right"))return[[this.factory.create("mml",f.default.fenced(this.factory.configuration,this.getProperty("delim"),this.toMml(),l.getProperty("delim"),"",l.getProperty("color")))],!0];if(l.isKind("middle")){var p={stretchy:!0};return l.getProperty("color")&&(p.mathcolor=l.getProperty("color")),this.Push(this.create("node","TeXAtom",[],{texClass:m.TEXCLASS.CLOSE}),this.create("token","mo",p,l.getProperty("delim")),this.create("node","TeXAtom",[],{texClass:m.TEXCLASS.OPEN})),this.env={},[[this],!0]}return y.prototype.checkItem.call(this,l)},c.errors=Object.assign(Object.create(o.BaseItem.errors),{stop:["ExtraLeftMissingRight","Extra \\left or missing \\right"]}),c}(o.BaseItem);v.LeftItem=x;var j=function(y){function c(l,p,S){var L=y.call(this,l)||this;return L.setProperty("delim",p),S&&L.setProperty("color",S),L}return C(c,y),Object.defineProperty(c.prototype,"kind",{get:function(){return"middle"},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),c}(o.BaseItem);v.Middle=j;var O=function(y){function c(l,p,S){var L=y.call(this,l)||this;return L.setProperty("delim",p),S&&L.setProperty("color",S),L}return C(c,y),Object.defineProperty(c.prototype,"kind",{get:function(){return"right"},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),c}(o.BaseItem);v.RightItem=O;var t=function(y){function c(){return null!==y&&y.apply(this,arguments)||this}return C(c,y),Object.defineProperty(c.prototype,"kind",{get:function(){return"begin"},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),c.prototype.checkItem=function(l){if(l.isKind("end")){if(l.getName()!==this.getName())throw new u.default("EnvBadEnd","\\begin{%1} ended with \\end{%2}",this.getName(),l.getName());return this.getProperty("end")?o.BaseItem.fail:[[this.factory.create("mml",this.toMml())],!0]}if(l.isKind("stop"))throw new u.default("EnvMissingEnd","Missing \\end{%1}",this.getName());return y.prototype.checkItem.call(this,l)},c}(o.BaseItem);v.BeginItem=t;var i=function(y){function c(){return null!==y&&y.apply(this,arguments)||this}return C(c,y),Object.defineProperty(c.prototype,"kind",{get:function(){return"end"},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),c}(o.BaseItem);v.EndItem=i;var r=function(y){function c(){return null!==y&&y.apply(this,arguments)||this}return C(c,y),Object.defineProperty(c.prototype,"kind",{get:function(){return"style"},enumerable:!1,configurable:!0}),c.prototype.checkItem=function(l){if(!l.isClose)return y.prototype.checkItem.call(this,l);var p=this.create("node","mstyle",this.nodes,this.getProperty("styles"));return[[this.factory.create("mml",p),l],!0]},c}(o.BaseItem);v.StyleItem=r;var a=function(y){function c(){return null!==y&&y.apply(this,arguments)||this}return C(c,y),Object.defineProperty(c.prototype,"kind",{get:function(){return"position"},enumerable:!1,configurable:!0}),c.prototype.checkItem=function(l){if(l.isClose)throw new u.default("MissingBoxFor","Missing box for %1",this.getName());if(l.isFinal){var p=l.toMml();switch(this.getProperty("move")){case"vertical":return p=this.create("node","mpadded",[p],{height:this.getProperty("dh"),depth:this.getProperty("dd"),voffset:this.getProperty("dh")}),[[this.factory.create("mml",p)],!0];case"horizontal":return[[this.factory.create("mml",this.getProperty("left")),l,this.factory.create("mml",this.getProperty("right"))],!0]}}return y.prototype.checkItem.call(this,l)},c}(o.BaseItem);v.PositionItem=a;var n=function(y){function c(){return null!==y&&y.apply(this,arguments)||this}return C(c,y),Object.defineProperty(c.prototype,"kind",{get:function(){return"cell"},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"isClose",{get:function(){return!0},enumerable:!1,configurable:!0}),c}(o.BaseItem);v.CellItem=n;var b=function(y){function c(){return null!==y&&y.apply(this,arguments)||this}return C(c,y),Object.defineProperty(c.prototype,"isFinal",{get:function(){return!0},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"kind",{get:function(){return"mml"},enumerable:!1,configurable:!0}),c}(o.BaseItem);v.MmlItem=b;var T=function(y){function c(){return null!==y&&y.apply(this,arguments)||this}return C(c,y),Object.defineProperty(c.prototype,"kind",{get:function(){return"fn"},enumerable:!1,configurable:!0}),c.prototype.checkItem=function(l){var p=this.First;if(p){if(l.isOpen)return o.BaseItem.success;if(!l.isKind("fn")){var S=l.First;if(!l.isKind("mml")||!S)return[[p,l],!0];if(e.default.isType(S,"mstyle")&&S.childNodes.length&&e.default.isType(S.childNodes[0].childNodes[0],"mspace")||e.default.isType(S,"mspace"))return[[p,l],!0];e.default.isEmbellished(S)&&(S=e.default.getCoreMO(S));var L=e.default.getForm(S);if(null!=L&&[0,0,1,1,0,1,1,0,0,0][L[2]])return[[p,l],!0]}var U=this.create("token","mo",{texClass:m.TEXCLASS.NONE},s.entities.ApplyFunction);return[[p,U,l],!0]}return y.prototype.checkItem.apply(this,arguments)},c}(o.BaseItem);v.FnItem=T;var M=function(y){function c(){var l=null!==y&&y.apply(this,arguments)||this;return l.remap=A.MapHandler.getMap("not_remap"),l}return C(c,y),Object.defineProperty(c.prototype,"kind",{get:function(){return"not"},enumerable:!1,configurable:!0}),c.prototype.checkItem=function(l){var p,S,L;if(l.isKind("open")||l.isKind("left"))return o.BaseItem.success;if(l.isKind("mml")&&(e.default.isType(l.First,"mo")||e.default.isType(l.First,"mi")||e.default.isType(l.First,"mtext"))&&(p=l.First,1===(S=e.default.getText(p)).length&&!e.default.getProperty(p,"movesupsub")&&1===e.default.getChildren(p).length))return this.remap.contains(S)?(L=this.create("text",this.remap.lookup(S).char),e.default.setChild(p,0,L)):(L=this.create("text","\u0338"),e.default.appendChildren(p,[L])),[[l],!0];L=this.create("text","\u29f8");var U=this.create("node","mtext",[],{},L),H=this.create("node","mpadded",[U],{width:0});return[[p=this.create("node","TeXAtom",[H],{texClass:m.TEXCLASS.REL}),l],!0]},c}(o.BaseItem);v.NotItem=M;var k=function(y){function c(){return null!==y&&y.apply(this,arguments)||this}return C(c,y),Object.defineProperty(c.prototype,"kind",{get:function(){return"nonscript"},enumerable:!1,configurable:!0}),c.prototype.checkItem=function(l){if(l.isKind("mml")&&1===l.Size()){var p=l.First;if(p.isKind("mstyle")&&p.notParent&&(p=e.default.getChildren(e.default.getChildren(p)[0])[0]),p.isKind("mspace")){if(p!==l.First){var S=this.create("node","mrow",[l.Pop()]);l.Push(S)}this.factory.configuration.addNode("nonscript",l.First)}}return[[l],!0]},c}(o.BaseItem);v.NonscriptItem=k;var B=function(y){function c(){return null!==y&&y.apply(this,arguments)||this}return C(c,y),Object.defineProperty(c.prototype,"kind",{get:function(){return"dots"},enumerable:!1,configurable:!0}),c.prototype.checkItem=function(l){if(l.isKind("open")||l.isKind("left"))return o.BaseItem.success;var p=this.getProperty("ldots"),S=l.First;if(l.isKind("mml")&&e.default.isEmbellished(S)){var L=e.default.getTexClass(e.default.getCoreMO(S));(L===m.TEXCLASS.BIN||L===m.TEXCLASS.REL)&&(p=this.getProperty("cdots"))}return[[p,l],!0]},c}(o.BaseItem);v.DotsItem=B;var R=function(y){function c(){var l=null!==y&&y.apply(this,arguments)||this;return l.table=[],l.row=[],l.frame=[],l.hfill=[],l.arraydef={},l.dashed=!1,l}return C(c,y),Object.defineProperty(c.prototype,"kind",{get:function(){return"array"},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"copyEnv",{get:function(){return!1},enumerable:!1,configurable:!0}),c.prototype.checkItem=function(l){if(l.isClose&&!l.isKind("over")){if(l.getProperty("isEntry"))return this.EndEntry(),this.clearEnv(),o.BaseItem.fail;if(l.getProperty("isCR"))return this.EndEntry(),this.EndRow(),this.clearEnv(),o.BaseItem.fail;this.EndTable(),this.clearEnv();var p=this.factory.create("mml",this.createMml());if(this.getProperty("requireClose")){if(l.isKind("close"))return[[p],!0];throw new u.default("MissingCloseBrace","Missing close brace")}return[[p,l],!0]}return y.prototype.checkItem.call(this,l)},c.prototype.createMml=function(){var l=this.arraydef.scriptlevel;delete this.arraydef.scriptlevel;var p=this.create("node","mtable",this.table,this.arraydef);return l&&p.setProperty("scriptlevel",l),4===this.frame.length?e.default.setAttribute(p,"frame",this.dashed?"dashed":"solid"):this.frame.length&&(this.arraydef.rowlines&&(this.arraydef.rowlines=this.arraydef.rowlines.replace(/none( none)+$/,"none")),e.default.setAttribute(p,"frame",""),p=this.create("node","menclose",[p],{notation:this.frame.join(" ")}),("none"!==(this.arraydef.columnlines||"none")||"none"!==(this.arraydef.rowlines||"none"))&&e.default.setAttribute(p,"data-padding",0)),(this.getProperty("open")||this.getProperty("close"))&&(p=f.default.fenced(this.factory.configuration,this.getProperty("open"),p,this.getProperty("close"))),p},c.prototype.EndEntry=function(){var l=this.create("node","mtd",this.nodes);this.hfill.length&&(0===this.hfill[0]&&e.default.setAttribute(l,"columnalign","right"),this.hfill[this.hfill.length-1]===this.Size()&&e.default.setAttribute(l,"columnalign",e.default.getAttribute(l,"columnalign")?"center":"left")),this.row.push(l),this.Clear(),this.hfill=[]},c.prototype.EndRow=function(){var l;this.getProperty("isNumbered")&&3===this.row.length?(this.row.unshift(this.row.pop()),l=this.create("node","mlabeledtr",this.row)):l=this.create("node","mtr",this.row),this.table.push(l),this.row=[]},c.prototype.EndTable=function(){(this.Size()||this.row.length)&&(this.EndEntry(),this.EndRow()),this.checkLines()},c.prototype.checkLines=function(){if(this.arraydef.rowlines){var l=this.arraydef.rowlines.split(/ /);l.length===this.table.length?(this.frame.push("bottom"),l.pop(),this.arraydef.rowlines=l.join(" ")):l.length<this.table.length-1&&(this.arraydef.rowlines+=" none")}if(this.getProperty("rowspacing")){for(var p=this.arraydef.rowspacing.split(/ /);p.length<this.table.length;)p.push(this.getProperty("rowspacing")+"em");this.arraydef.rowspacing=p.join(" ")}},c.prototype.addRowSpacing=function(l){if(this.arraydef.rowspacing){var p=this.arraydef.rowspacing.split(/ /);if(!this.getProperty("rowspacing")){var S=f.default.dimen2em(p[0]);this.setProperty("rowspacing",S)}for(var L=this.getProperty("rowspacing");p.length<this.table.length;)p.push(f.default.Em(L));p[this.table.length-1]=f.default.Em(Math.max(0,L+f.default.dimen2em(l))),this.arraydef.rowspacing=p.join(" ")}},c}(o.BaseItem);v.ArrayItem=R;var X=function(y){function c(l){for(var p=[],S=1;S<arguments.length;S++)p[S-1]=arguments[S];var L=y.call(this,l)||this;return L.maxrow=0,L.factory.configuration.tags.start(p[0],p[2],p[1]),L}return C(c,y),Object.defineProperty(c.prototype,"kind",{get:function(){return"eqnarray"},enumerable:!1,configurable:!0}),c.prototype.EndEntry=function(){this.row.length&&f.default.fixInitialMO(this.factory.configuration,this.nodes);var l=this.create("node","mtd",this.nodes);this.row.push(l),this.Clear()},c.prototype.EndRow=function(){this.row.length>this.maxrow&&(this.maxrow=this.row.length);var l="mtr",p=this.factory.configuration.tags.getTag();p&&(this.row=[p].concat(this.row),l="mlabeledtr"),this.factory.configuration.tags.clearTag();var S=this.create("node",l,this.row);this.table.push(S),this.row=[]},c.prototype.EndTable=function(){y.prototype.EndTable.call(this),this.factory.configuration.tags.end(),this.extendArray("columnalign",this.maxrow),this.extendArray("columnwidth",this.maxrow),this.extendArray("columnspacing",this.maxrow-1)},c.prototype.extendArray=function(l,p){if(this.arraydef[l]){var S=this.arraydef[l].split(/ /),L=_([],D(S),!1);if(L.length>1){for(;L.length<p;)L.push.apply(L,_([],D(S),!1));this.arraydef[l]=L.slice(0,p).join(" ")}}},c}(R);v.EqnArrayItem=X;var G=function(y){function c(l){for(var p=[],S=1;S<arguments.length;S++)p[S-1]=arguments[S];var L=y.call(this,l)||this;return L.factory.configuration.tags.start("equation",!0,p[0]),L}return C(c,y),Object.defineProperty(c.prototype,"kind",{get:function(){return"equation"},enumerable:!1,configurable:!0}),Object.defineProperty(c.prototype,"isOpen",{get:function(){return!0},enumerable:!1,configurable:!0}),c.prototype.checkItem=function(l){if(l.isKind("end")){var p=this.toMml(),S=this.factory.configuration.tags.getTag();return this.factory.configuration.tags.end(),[[S?this.factory.configuration.tags.enTag(p,S):p,l],!0]}if(l.isKind("stop"))throw new u.default("EnvMissingEnd","Missing \\end{%1}",this.getName());return y.prototype.checkItem.call(this,l)},c}(o.BaseItem);v.EquationItem=G},73338:function(V,v,I){var C=this&&this.__createBinding||(Object.create?function(h,d,P,F){void 0===F&&(F=P);var N=Object.getOwnPropertyDescriptor(d,P);(!N||("get"in N?!d.__esModule:N.writable||N.configurable))&&(N={enumerable:!0,get:function(){return d[P]}}),Object.defineProperty(h,F,N)}:function(h,d,P,F){void 0===F&&(F=P),h[F]=d[P]}),D=this&&this.__setModuleDefault||(Object.create?function(h,d){Object.defineProperty(h,"default",{enumerable:!0,value:d})}:function(h,d){h.default=d}),_=this&&this.__importStar||function(h){if(h&&h.__esModule)return h;var d={};if(null!=h)for(var P in h)"default"!==P&&Object.prototype.hasOwnProperty.call(h,P)&&C(d,h,P);return D(d,h),d},w=this&&this.__importDefault||function(h){return h&&h.__esModule?h:{default:h}};Object.defineProperty(v,"__esModule",{value:!0});var A=_(I(82652)),s=I(60742),m=w(I(2158)),u=w(I(70004)),f=w(I(39194)),e=I(91585),o=I(21108);new A.RegExpMap("letter",u.default.variable,/[a-z]/i),new A.RegExpMap("digit",u.default.digit,/[0-9.,]/),new A.RegExpMap("command",u.default.controlSequence,/^\\/),new A.MacroMap("special",{"{":"Open","}":"Close","~":"Tilde","^":"Superscript",_:"Subscript"," ":"Space","\t":"Space","\r":"Space","\n":"Space","'":"Prime","%":"Comment","&":"Entry","#":"Hash","\xa0":"Space","\u2019":"Prime"},m.default),new A.CharacterMap("mathchar0mi",u.default.mathchar0mi,{alpha:"\u03b1",beta:"\u03b2",gamma:"\u03b3",delta:"\u03b4",epsilon:"\u03f5",zeta:"\u03b6",eta:"\u03b7",theta:"\u03b8",iota:"\u03b9",kappa:"\u03ba",lambda:"\u03bb",mu:"\u03bc",nu:"\u03bd",xi:"\u03be",omicron:"\u03bf",pi:"\u03c0",rho:"\u03c1",sigma:"\u03c3",tau:"\u03c4",upsilon:"\u03c5",phi:"\u03d5",chi:"\u03c7",psi:"\u03c8",omega:"\u03c9",varepsilon:"\u03b5",vartheta:"\u03d1",varpi:"\u03d6",varrho:"\u03f1",varsigma:"\u03c2",varphi:"\u03c6",S:["\xa7",{mathvariant:s.TexConstant.Variant.NORMAL}],aleph:["\u2135",{mathvariant:s.TexConstant.Variant.NORMAL}],hbar:["\u210f",{variantForm:!0}],imath:"\u0131",jmath:"\u0237",ell:"\u2113",wp:["\u2118",{mathvariant:s.TexConstant.Variant.NORMAL}],Re:["\u211c",{mathvariant:s.TexConstant.Variant.NORMAL}],Im:["\u2111",{mathvariant:s.TexConstant.Variant.NORMAL}],partial:["\u2202",{mathvariant:s.TexConstant.Variant.ITALIC}],infty:["\u221e",{mathvariant:s.TexConstant.Variant.NORMAL}],prime:["\u2032",{variantForm:!0}],emptyset:["\u2205",{mathvariant:s.TexConstant.Variant.NORMAL}],nabla:["\u2207",{mathvariant:s.TexConstant.Variant.NORMAL}],top:["\u22a4",{mathvariant:s.TexConstant.Variant.NORMAL}],bot:["\u22a5",{mathvariant:s.TexConstant.Variant.NORMAL}],angle:["\u2220",{mathvariant:s.TexConstant.Variant.NORMAL}],triangle:["\u25b3",{mathvariant:s.TexConstant.Variant.NORMAL}],backslash:["\u2216",{mathvariant:s.TexConstant.Variant.NORMAL}],forall:["\u2200",{mathvariant:s.TexConstant.Variant.NORMAL}],exists:["\u2203",{mathvariant:s.TexConstant.Variant.NORMAL}],neg:["\xac",{mathvariant:s.TexConstant.Variant.NORMAL}],lnot:["\xac",{mathvariant:s.TexConstant.Variant.NORMAL}],flat:["\u266d",{mathvariant:s.TexConstant.Variant.NORMAL}],natural:["\u266e",{mathvariant:s.TexConstant.Variant.NORMAL}],sharp:["\u266f",{mathvariant:s.TexConstant.Variant.NORMAL}],clubsuit:["\u2663",{mathvariant:s.TexConstant.Variant.NORMAL}],diamondsuit:["\u2662",{mathvariant:s.TexConstant.Variant.NORMAL}],heartsuit:["\u2661",{mathvariant:s.TexConstant.Variant.NORMAL}],spadesuit:["\u2660",{mathvariant:s.TexConstant.Variant.NORMAL}]}),new A.CharacterMap("mathchar0mo",u.default.mathchar0mo,{surd:"\u221a",coprod:["\u2210",{texClass:e.TEXCLASS.OP,movesupsub:!0}],bigvee:["\u22c1",{texClass:e.TEXCLASS.OP,movesupsub:!0}],bigwedge:["\u22c0",{texClass:e.TEXCLASS.OP,movesupsub:!0}],biguplus:["\u2a04",{texClass:e.TEXCLASS.OP,movesupsub:!0}],bigcap:["\u22c2",{texClass:e.TEXCLASS.OP,movesupsub:!0}],bigcup:["\u22c3",{texClass:e.TEXCLASS.OP,movesupsub:!0}],int:["\u222b",{texClass:e.TEXCLASS.OP}],intop:["\u222b",{texClass:e.TEXCLASS.OP,movesupsub:!0,movablelimits:!0}],iint:["\u222c",{texClass:e.TEXCLASS.OP}],iiint:["\u222d",{texClass:e.TEXCLASS.OP}],prod:["\u220f",{texClass:e.TEXCLASS.OP,movesupsub:!0}],sum:["\u2211",{texClass:e.TEXCLASS.OP,movesupsub:!0}],bigotimes:["\u2a02",{texClass:e.TEXCLASS.OP,movesupsub:!0}],bigoplus:["\u2a01",{texClass:e.TEXCLASS.OP,movesupsub:!0}],bigodot:["\u2a00",{texClass:e.TEXCLASS.OP,movesupsub:!0}],oint:["\u222e",{texClass:e.TEXCLASS.OP}],bigsqcup:["\u2a06",{texClass:e.TEXCLASS.OP,movesupsub:!0}],smallint:["\u222b",{largeop:!1}],triangleleft:"\u25c3",triangleright:"\u25b9",bigtriangleup:"\u25b3",bigtriangledown:"\u25bd",wedge:"\u2227",land:"\u2227",vee:"\u2228",lor:"\u2228",cap:"\u2229",cup:"\u222a",ddagger:"\u2021",dagger:"\u2020",sqcap:"\u2293",sqcup:"\u2294",uplus:"\u228e",amalg:"\u2a3f",diamond:"\u22c4",bullet:"\u2219",wr:"\u2240",div:"\xf7",divsymbol:"\xf7",odot:["\u2299",{largeop:!1}],oslash:["\u2298",{largeop:!1}],otimes:["\u2297",{largeop:!1}],ominus:["\u2296",{largeop:!1}],oplus:["\u2295",{largeop:!1}],mp:"\u2213",pm:"\xb1",circ:"\u2218",bigcirc:"\u25ef",setminus:"\u2216",cdot:"\u22c5",ast:"\u2217",times:"\xd7",star:"\u22c6",propto:"\u221d",sqsubseteq:"\u2291",sqsupseteq:"\u2292",parallel:"\u2225",mid:"\u2223",dashv:"\u22a3",vdash:"\u22a2",leq:"\u2264",le:"\u2264",geq:"\u2265",ge:"\u2265",lt:"<",gt:">",succ:"\u227b",prec:"\u227a",approx:"\u2248",succeq:"\u2ab0",preceq:"\u2aaf",supset:"\u2283",subset:"\u2282",supseteq:"\u2287",subseteq:"\u2286",in:"\u2208",ni:"\u220b",notin:"\u2209",owns:"\u220b",gg:"\u226b",ll:"\u226a",sim:"\u223c",simeq:"\u2243",perp:"\u22a5",equiv:"\u2261",asymp:"\u224d",smile:"\u2323",frown:"\u2322",ne:"\u2260",neq:"\u2260",cong:"\u2245",doteq:"\u2250",bowtie:"\u22c8",models:"\u22a8",notChar:"\u29f8",Leftrightarrow:"\u21d4",Leftarrow:"\u21d0",Rightarrow:"\u21d2",leftrightarrow:"\u2194",leftarrow:"\u2190",gets:"\u2190",rightarrow:"\u2192",to:["\u2192",{accent:!1}],mapsto:"\u21a6",leftharpoonup:"\u21bc",leftharpoondown:"\u21bd",rightharpoonup:"\u21c0",rightharpoondown:"\u21c1",nearrow:"\u2197",searrow:"\u2198",nwarrow:"\u2196",swarrow:"\u2199",rightleftharpoons:"\u21cc",hookrightarrow:"\u21aa",hookleftarrow:"\u21a9",longleftarrow:"\u27f5",Longleftarrow:"\u27f8",longrightarrow:"\u27f6",Longrightarrow:"\u27f9",Longleftrightarrow:"\u27fa",longleftrightarrow:"\u27f7",longmapsto:"\u27fc",ldots:"\u2026",cdots:"\u22ef",vdots:"\u22ee",ddots:"\u22f1",dotsc:"\u2026",dotsb:"\u22ef",dotsm:"\u22ef",dotsi:"\u22ef",dotso:"\u2026",ldotp:[".",{texClass:e.TEXCLASS.PUNCT}],cdotp:["\u22c5",{texClass:e.TEXCLASS.PUNCT}],colon:[":",{texClass:e.TEXCLASS.PUNCT}]}),new A.CharacterMap("mathchar7",u.default.mathchar7,{Gamma:"\u0393",Delta:"\u0394",Theta:"\u0398",Lambda:"\u039b",Xi:"\u039e",Pi:"\u03a0",Sigma:"\u03a3",Upsilon:"\u03a5",Phi:"\u03a6",Psi:"\u03a8",Omega:"\u03a9",_:"_","#":"#",$:"$","%":"%","&":"&",And:"&"}),new A.DelimiterMap("delimiter",u.default.delimiter,{"(":"(",")":")","[":"[","]":"]","<":"\u27e8",">":"\u27e9","\\lt":"\u27e8","\\gt":"\u27e9","/":"/","|":["|",{texClass:e.TEXCLASS.ORD}],".":"","\\\\":"\\","\\lmoustache":"\u23b0","\\rmoustache":"\u23b1","\\lgroup":"\u27ee","\\rgroup":"\u27ef","\\arrowvert":"\u23d0","\\Arrowvert":"\u2016","\\bracevert":"\u23aa","\\Vert":["\u2016",{texClass:e.TEXCLASS.ORD}],"\\|":["\u2016",{texClass:e.TEXCLASS.ORD}],"\\vert":["|",{texClass:e.TEXCLASS.ORD}],"\\uparrow":"\u2191","\\downarrow":"\u2193","\\updownarrow":"\u2195","\\Uparrow":"\u21d1","\\Downarrow":"\u21d3","\\Updownarrow":"\u21d5","\\backslash":"\\","\\rangle":"\u27e9","\\langle":"\u27e8","\\rbrace":"}","\\lbrace":"{","\\}":"}","\\{":"{","\\rceil":"\u2309","\\lceil":"\u2308","\\rfloor":"\u230b","\\lfloor":"\u230a","\\lbrack":"[","\\rbrack":"]"}),new A.CommandMap("macros",{displaystyle:["SetStyle","D",!0,0],textstyle:["SetStyle","T",!1,0],scriptstyle:["SetStyle","S",!1,1],scriptscriptstyle:["SetStyle","SS",!1,2],rm:["SetFont",s.TexConstant.Variant.NORMAL],mit:["SetFont",s.TexConstant.Variant.ITALIC],oldstyle:["SetFont",s.TexConstant.Variant.OLDSTYLE],cal:["SetFont",s.TexConstant.Variant.CALLIGRAPHIC],it:["SetFont",s.TexConstant.Variant.MATHITALIC],bf:["SetFont",s.TexConstant.Variant.BOLD],bbFont:["SetFont",s.TexConstant.Variant.DOUBLESTRUCK],scr:["SetFont",s.TexConstant.Variant.SCRIPT],frak:["SetFont",s.TexConstant.Variant.FRAKTUR],sf:["SetFont",s.TexConstant.Variant.SANSSERIF],tt:["SetFont",s.TexConstant.Variant.MONOSPACE],mathrm:["MathFont",s.TexConstant.Variant.NORMAL],mathup:["MathFont",s.TexConstant.Variant.NORMAL],mathnormal:["MathFont",""],mathbf:["MathFont",s.TexConstant.Variant.BOLD],mathbfup:["MathFont",s.TexConstant.Variant.BOLD],mathit:["MathFont",s.TexConstant.Variant.MATHITALIC],mathbfit:["MathFont",s.TexConstant.Variant.BOLDITALIC],mathbb:["MathFont",s.TexConstant.Variant.DOUBLESTRUCK],Bbb:["MathFont",s.TexConstant.Variant.DOUBLESTRUCK],mathfrak:["MathFont",s.TexConstant.Variant.FRAKTUR],mathbffrak:["MathFont",s.TexConstant.Variant.BOLDFRAKTUR],mathscr:["MathFont",s.TexConstant.Variant.SCRIPT],mathbfscr:["MathFont",s.TexConstant.Variant.BOLDSCRIPT],mathsf:["MathFont",s.TexConstant.Variant.SANSSERIF],mathsfup:["MathFont",s.TexConstant.Variant.SANSSERIF],mathbfsf:["MathFont",s.TexConstant.Variant.BOLDSANSSERIF],mathbfsfup:["MathFont",s.TexConstant.Variant.BOLDSANSSERIF],mathsfit:["MathFont",s.TexConstant.Variant.SANSSERIFITALIC],mathbfsfit:["MathFont",s.TexConstant.Variant.SANSSERIFBOLDITALIC],mathtt:["MathFont",s.TexConstant.Variant.MONOSPACE],mathcal:["MathFont",s.TexConstant.Variant.CALLIGRAPHIC],mathbfcal:["MathFont",s.TexConstant.Variant.BOLDCALLIGRAPHIC],symrm:["MathFont",s.TexConstant.Variant.NORMAL],symup:["MathFont",s.TexConstant.Variant.NORMAL],symnormal:["MathFont",""],symbf:["MathFont",s.TexConstant.Variant.BOLD],symbfup:["MathFont",s.TexConstant.Variant.BOLD],symit:["MathFont",s.TexConstant.Variant.ITALIC],symbfit:["MathFont",s.TexConstant.Variant.BOLDITALIC],symbb:["MathFont",s.TexConstant.Variant.DOUBLESTRUCK],symfrak:["MathFont",s.TexConstant.Variant.FRAKTUR],symbffrak:["MathFont",s.TexConstant.Variant.BOLDFRAKTUR],symscr:["MathFont",s.TexConstant.Variant.SCRIPT],symbfscr:["MathFont",s.TexConstant.Variant.BOLDSCRIPT],symsf:["MathFont",s.TexConstant.Variant.SANSSERIF],symsfup:["MathFont",s.TexConstant.Variant.SANSSERIF],symbfsf:["MathFont",s.TexConstant.Variant.BOLDSANSSERIF],symbfsfup:["MathFont",s.TexConstant.Variant.BOLDSANSSERIF],symsfit:["MathFont",s.TexConstant.Variant.SANSSERIFITALIC],symbfsfit:["MathFont",s.TexConstant.Variant.SANSSERIFBOLDITALIC],symtt:["MathFont",s.TexConstant.Variant.MONOSPACE],symcal:["MathFont",s.TexConstant.Variant.CALLIGRAPHIC],symbfcal:["MathFont",s.TexConstant.Variant.BOLDCALLIGRAPHIC],textrm:["HBox",null,s.TexConstant.Variant.NORMAL],textup:["HBox",null,s.TexConstant.Variant.NORMAL],textnormal:["HBox"],textit:["HBox",null,s.TexConstant.Variant.ITALIC],textbf:["HBox",null,s.TexConstant.Variant.BOLD],textsf:["HBox",null,s.TexConstant.Variant.SANSSERIF],texttt:["HBox",null,s.TexConstant.Variant.MONOSPACE],tiny:["SetSize",.5],Tiny:["SetSize",.6],scriptsize:["SetSize",.7],small:["SetSize",.85],normalsize:["SetSize",1],large:["SetSize",1.2],Large:["SetSize",1.44],LARGE:["SetSize",1.73],huge:["SetSize",2.07],Huge:["SetSize",2.49],arcsin:"NamedFn",arccos:"NamedFn",arctan:"NamedFn",arg:"NamedFn",cos:"NamedFn",cosh:"NamedFn",cot:"NamedFn",coth:"NamedFn",csc:"NamedFn",deg:"NamedFn",det:"NamedOp",dim:"NamedFn",exp:"NamedFn",gcd:"NamedOp",hom:"NamedFn",inf:"NamedOp",ker:"NamedFn",lg:"NamedFn",lim:"NamedOp",liminf:["NamedOp","lim&thinsp;inf"],limsup:["NamedOp","lim&thinsp;sup"],ln:"NamedFn",log:"NamedFn",max:"NamedOp",min:"NamedOp",Pr:"NamedOp",sec:"NamedFn",sin:"NamedFn",sinh:"NamedFn",sup:"NamedOp",tan:"NamedFn",tanh:"NamedFn",limits:["Limits",1],nolimits:["Limits",0],overline:["UnderOver","2015"],underline:["UnderOver","2015"],overbrace:["UnderOver","23DE",1],underbrace:["UnderOver","23DF",1],overparen:["UnderOver","23DC"],underparen:["UnderOver","23DD"],overrightarrow:["UnderOver","2192"],underrightarrow:["UnderOver","2192"],overleftarrow:["UnderOver","2190"],underleftarrow:["UnderOver","2190"],overleftrightarrow:["UnderOver","2194"],underleftrightarrow:["UnderOver","2194"],overset:"Overset",underset:"Underset",overunderset:"Overunderset",stackrel:["Macro","\\mathrel{\\mathop{#2}\\limits^{#1}}",2],stackbin:["Macro","\\mathbin{\\mathop{#2}\\limits^{#1}}",2],over:"Over",overwithdelims:"Over",atop:"Over",atopwithdelims:"Over",above:"Over",abovewithdelims:"Over",brace:["Over","{","}"],brack:["Over","[","]"],choose:["Over","(",")"],frac:"Frac",sqrt:"Sqrt",root:"Root",uproot:["MoveRoot","upRoot"],leftroot:["MoveRoot","leftRoot"],left:"LeftRight",right:"LeftRight",middle:"LeftRight",llap:"Lap",rlap:"Lap",raise:"RaiseLower",lower:"RaiseLower",moveleft:"MoveLeftRight",moveright:"MoveLeftRight",",":["Spacer",o.MATHSPACE.thinmathspace],":":["Spacer",o.MATHSPACE.mediummathspace],">":["Spacer",o.MATHSPACE.mediummathspace],";":["Spacer",o.MATHSPACE.thickmathspace],"!":["Spacer",o.MATHSPACE.negativethinmathspace],enspace:["Spacer",.5],quad:["Spacer",1],qquad:["Spacer",2],thinspace:["Spacer",o.MATHSPACE.thinmathspace],negthinspace:["Spacer",o.MATHSPACE.negativethinmathspace],hskip:"Hskip",hspace:"Hskip",kern:"Hskip",mskip:"Hskip",mspace:"Hskip",mkern:"Hskip",rule:"rule",Rule:["Rule"],Space:["Rule","blank"],nonscript:"Nonscript",big:["MakeBig",e.TEXCLASS.ORD,.85],Big:["MakeBig",e.TEXCLASS.ORD,1.15],bigg:["MakeBig",e.TEXCLASS.ORD,1.45],Bigg:["MakeBig",e.TEXCLASS.ORD,1.75],bigl:["MakeBig",e.TEXCLASS.OPEN,.85],Bigl:["MakeBig",e.TEXCLASS.OPEN,1.15],biggl:["MakeBig",e.TEXCLASS.OPEN,1.45],Biggl:["MakeBig",e.TEXCLASS.OPEN,1.75],bigr:["MakeBig",e.TEXCLASS.CLOSE,.85],Bigr:["MakeBig",e.TEXCLASS.CLOSE,1.15],biggr:["MakeBig",e.TEXCLASS.CLOSE,1.45],Biggr:["MakeBig",e.TEXCLASS.CLOSE,1.75],bigm:["MakeBig",e.TEXCLASS.REL,.85],Bigm:["MakeBig",e.TEXCLASS.REL,1.15],biggm:["MakeBig",e.TEXCLASS.REL,1.45],Biggm:["MakeBig",e.TEXCLASS.REL,1.75],mathord:["TeXAtom",e.TEXCLASS.ORD],mathop:["TeXAtom",e.TEXCLASS.OP],mathopen:["TeXAtom",e.TEXCLASS.OPEN],mathclose:["TeXAtom",e.TEXCLASS.CLOSE],mathbin:["TeXAtom",e.TEXCLASS.BIN],mathrel:["TeXAtom",e.TEXCLASS.REL],mathpunct:["TeXAtom",e.TEXCLASS.PUNCT],mathinner:["TeXAtom",e.TEXCLASS.INNER],vcenter:["TeXAtom",e.TEXCLASS.VCENTER],buildrel:"BuildRel",hbox:["HBox",0],text:"HBox",mbox:["HBox",0],fbox:"FBox",boxed:["Macro","\\fbox{$\\displaystyle{#1}$}",1],framebox:"FrameBox",strut:"Strut",mathstrut:["Macro","\\vphantom{(}"],phantom:"Phantom",vphantom:["Phantom",1,0],hphantom:["Phantom",0,1],smash:"Smash",acute:["Accent","00B4"],grave:["Accent","0060"],ddot:["Accent","00A8"],tilde:["Accent","007E"],bar:["Accent","00AF"],breve:["Accent","02D8"],check:["Accent","02C7"],hat:["Accent","005E"],vec:["Accent","2192"],dot:["Accent","02D9"],widetilde:["Accent","007E",1],widehat:["Accent","005E",1],matrix:"Matrix",array:"Matrix",pmatrix:["Matrix","(",")"],cases:["Matrix","{","","left left",null,".1em",null,!0],eqalign:["Matrix",null,null,"right left",(0,o.em)(o.MATHSPACE.thickmathspace),".5em","D"],displaylines:["Matrix",null,null,"center",null,".5em","D"],cr:"Cr","\\":"CrLaTeX",newline:["CrLaTeX",!0],hline:["HLine","solid"],hdashline:["HLine","dashed"],eqalignno:["Matrix",null,null,"right left",(0,o.em)(o.MATHSPACE.thickmathspace),".5em","D",null,"right"],leqalignno:["Matrix",null,null,"right left",(0,o.em)(o.MATHSPACE.thickmathspace),".5em","D",null,"left"],hfill:"HFill",hfil:"HFill",hfilll:"HFill",bmod:["Macro",'\\mmlToken{mo}[lspace="thickmathspace" rspace="thickmathspace"]{mod}'],pmod:["Macro","\\pod{\\mmlToken{mi}{mod}\\kern 6mu #1}",1],mod:["Macro","\\mathchoice{\\kern18mu}{\\kern12mu}{\\kern12mu}{\\kern12mu}\\mmlToken{mi}{mod}\\,\\,#1",1],pod:["Macro","\\mathchoice{\\kern18mu}{\\kern8mu}{\\kern8mu}{\\kern8mu}(#1)",1],iff:["Macro","\\;\\Longleftrightarrow\\;"],skew:["Macro","{{#2{#3\\mkern#1mu}\\mkern-#1mu}{}}",3],pmb:["Macro","\\rlap{#1}\\kern1px{#1}",1],TeX:["Macro","T\\kern-.14em\\lower.5ex{E}\\kern-.115em X"],LaTeX:["Macro","L\\kern-.325em\\raise.21em{\\scriptstyle{A}}\\kern-.17em\\TeX"]," ":["Macro","\\text{ }"],not:"Not",dots:"Dots",space:"Tilde","\xa0":"Tilde",begin:"BeginEnd",end:"BeginEnd",label:"HandleLabel",ref:"HandleRef",nonumber:"HandleNoTag",mathchoice:"MathChoice",mmlToken:"MmlToken"},m.default),new A.EnvironmentMap("environment",u.default.environment,{array:["AlignedArray"],equation:["Equation",null,!0],eqnarray:["EqnArray",null,!0,!0,"rcl",f.default.cols(0,o.MATHSPACE.thickmathspace),".5em"]},m.default),new A.CharacterMap("not_remap",null,{"\u2190":"\u219a","\u2192":"\u219b","\u2194":"\u21ae","\u21d0":"\u21cd","\u21d2":"\u21cf","\u21d4":"\u21ce","\u2208":"\u2209","\u220b":"\u220c","\u2223":"\u2224","\u2225":"\u2226","\u223c":"\u2241","~":"\u2241","\u2243":"\u2244","\u2245":"\u2247","\u2248":"\u2249","\u224d":"\u226d","=":"\u2260","\u2261":"\u2262","<":"\u226e",">":"\u226f","\u2264":"\u2270","\u2265":"\u2271","\u2272":"\u2274","\u2273":"\u2275","\u2276":"\u2278","\u2277":"\u2279","\u227a":"\u2280","\u227b":"\u2281","\u2282":"\u2284","\u2283":"\u2285","\u2286":"\u2288","\u2287":"\u2289","\u22a2":"\u22ac","\u22a8":"\u22ad","\u22a9":"\u22ae","\u22ab":"\u22af","\u227c":"\u22e0","\u227d":"\u22e1","\u2291":"\u22e2","\u2292":"\u22e3","\u22b2":"\u22ea","\u22b3":"\u22eb","\u22b4":"\u22ec","\u22b5":"\u22ed","\u2203":"\u2204"})},2158:function(V,v,I){var C=this&&this.__assign||function(){return C=Object.assign||function(t){for(var i,r=1,a=arguments.length;r<a;r++)for(var n in i=arguments[r])Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n]);return t},C.apply(this,arguments)},D=this&&this.__createBinding||(Object.create?function(t,i,r,a){void 0===a&&(a=r);var n=Object.getOwnPropertyDescriptor(i,r);(!n||("get"in n?!i.__esModule:n.writable||n.configurable))&&(n={enumerable:!0,get:function(){return i[r]}}),Object.defineProperty(t,a,n)}:function(t,i,r,a){void 0===a&&(a=r),t[a]=i[r]}),_=this&&this.__setModuleDefault||(Object.create?function(t,i){Object.defineProperty(t,"default",{enumerable:!0,value:i})}:function(t,i){t.default=i}),w=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var i={};if(null!=t)for(var r in t)"default"!==r&&Object.prototype.hasOwnProperty.call(t,r)&&D(i,t,r);return _(i,t),i},A=this&&this.__read||function(t,i){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,T,a=r.call(t),b=[];try{for(;(void 0===i||i-- >0)&&!(n=a.next()).done;)b.push(n.value)}catch(M){T={error:M}}finally{try{n&&!n.done&&(r=a.return)&&r.call(a)}finally{if(T)throw T.error}}return b},s=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(v,"__esModule",{value:!0});var m=w(I(33012)),u=s(I(60909)),f=s(I(6257)),e=s(I(55571)),o=I(60742),h=s(I(39194)),d=I(91585),P=I(22450),F=I(21108),N=I(56692),E=I(80084),g={},j={fontfamily:1,fontsize:1,fontweight:1,fontstyle:1,color:1,background:1,id:1,class:1,href:1,style:1};function O(t,i){var r=t.stack.env,a=r.inRoot;r.inRoot=!0;var n=new e.default(i,r,t.configuration),b=n.mml(),T=n.stack.global;if(T.leftRoot||T.upRoot){var M={};T.leftRoot&&(M.width=T.leftRoot),T.upRoot&&(M.voffset=T.upRoot,M.height=T.upRoot),b=t.create("node","mpadded",[b],M)}return r.inRoot=a,b}g.Open=function(t,i){t.Push(t.itemFactory.create("open"))},g.Close=function(t,i){t.Push(t.itemFactory.create("close"))},g.Tilde=function(t,i){t.Push(t.create("token","mtext",{},N.entities.nbsp))},g.Space=function(t,i){},g.Superscript=function(t,i){var r;t.GetNext().match(/\d/)&&(t.string=t.string.substr(0,t.i+1)+" "+t.string.substr(t.i+1));var a,n,b=t.stack.Top();b.isKind("prime")?(n=(r=A(b.Peek(2),2))[0],a=r[1],t.stack.Pop()):(n=t.stack.Prev())||(n=t.create("token","mi",{},""));var T=u.default.getProperty(n,"movesupsub"),M=u.default.isType(n,"msubsup")?n.sup:n.over;if(u.default.isType(n,"msubsup")&&!u.default.isType(n,"msup")&&u.default.getChildAt(n,n.sup)||u.default.isType(n,"munderover")&&!u.default.isType(n,"mover")&&u.default.getChildAt(n,n.over)&&!u.default.getProperty(n,"subsupOK"))throw new f.default("DoubleExponent","Double exponent: use braces to clarify");(!u.default.isType(n,"msubsup")||u.default.isType(n,"msup"))&&(T?((!u.default.isType(n,"munderover")||u.default.isType(n,"mover")||u.default.getChildAt(n,n.over))&&(n=t.create("node","munderover",[n],{movesupsub:!0})),M=n.over):M=(n=t.create("node","msubsup",[n])).sup),t.Push(t.itemFactory.create("subsup",n).setProperties({position:M,primes:a,movesupsub:T}))},g.Subscript=function(t,i){var r;t.GetNext().match(/\d/)&&(t.string=t.string.substr(0,t.i+1)+" "+t.string.substr(t.i+1));var a,n,b=t.stack.Top();b.isKind("prime")?(n=(r=A(b.Peek(2),2))[0],a=r[1],t.stack.Pop()):(n=t.stack.Prev())||(n=t.create("token","mi",{},""));var T=u.default.getProperty(n,"movesupsub"),M=u.default.isType(n,"msubsup")?n.sub:n.under;if(u.default.isType(n,"msubsup")&&!u.default.isType(n,"msup")&&u.default.getChildAt(n,n.sub)||u.default.isType(n,"munderover")&&!u.default.isType(n,"mover")&&u.default.getChildAt(n,n.under)&&!u.default.getProperty(n,"subsupOK"))throw new f.default("DoubleSubscripts","Double subscripts: use braces to clarify");(!u.default.isType(n,"msubsup")||u.default.isType(n,"msup"))&&(T?((!u.default.isType(n,"munderover")||u.default.isType(n,"mover")||u.default.getChildAt(n,n.under))&&(n=t.create("node","munderover",[n],{movesupsub:!0})),M=n.under):M=(n=t.create("node","msubsup",[n])).sub),t.Push(t.itemFactory.create("subsup",n).setProperties({position:M,primes:a,movesupsub:T}))},g.Prime=function(t,i){var r=t.stack.Prev();if(r||(r=t.create("node","mi")),u.default.isType(r,"msubsup")&&!u.default.isType(r,"msup")&&u.default.getChildAt(r,r.sup))throw new f.default("DoubleExponentPrime","Prime causes double exponent: use braces to clarify");var a="";t.i--;do{a+=N.entities.prime,t.i++,i=t.GetNext()}while("'"===i||i===N.entities.rsquo);a=["","\u2032","\u2033","\u2034","\u2057"][a.length]||a;var n=t.create("token","mo",{variantForm:!0},a);t.Push(t.itemFactory.create("prime",r,n))},g.Comment=function(t,i){for(;t.i<t.string.length&&"\n"!==t.string.charAt(t.i);)t.i++},g.Hash=function(t,i){throw new f.default("CantUseHash1","You can't use 'macro parameter character #' in math mode")},g.MathFont=function(t,i,r){var a=t.GetArgument(i),n=new e.default(a,C(C({},t.stack.env),{font:r,multiLetterIdentifiers:/^[a-zA-Z]+/,noAutoOP:!0}),t.configuration).mml();t.Push(t.create("node","TeXAtom",[n]))},g.SetFont=function(t,i,r){t.stack.env.font=r},g.SetStyle=function(t,i,r,a,n){t.stack.env.style=r,t.stack.env.level=n,t.Push(t.itemFactory.create("style").setProperty("styles",{displaystyle:a,scriptlevel:n}))},g.SetSize=function(t,i,r){t.stack.env.size=r,t.Push(t.itemFactory.create("style").setProperty("styles",{mathsize:(0,F.em)(r)}))},g.Spacer=function(t,i,r){var a=t.create("node","mspace",[],{width:(0,F.em)(r)}),n=t.create("node","mstyle",[a],{scriptlevel:0});t.Push(n)},g.LeftRight=function(t,i){var r=i.substr(1);t.Push(t.itemFactory.create(r,t.GetDelimiter(i),t.stack.env.color))},g.NamedFn=function(t,i,r){r||(r=i.substr(1));var a=t.create("token","mi",{texClass:d.TEXCLASS.OP},r);t.Push(t.itemFactory.create("fn",a))},g.NamedOp=function(t,i,r){r||(r=i.substr(1)),r=r.replace(/&thinsp;/,"\u2006");var a=t.create("token","mo",{movablelimits:!0,movesupsub:!0,form:o.TexConstant.Form.PREFIX,texClass:d.TEXCLASS.OP},r);t.Push(a)},g.Limits=function(t,i,r){var a=t.stack.Prev(!0);if(!a||u.default.getTexClass(u.default.getCoreMO(a))!==d.TEXCLASS.OP&&null==u.default.getProperty(a,"movesupsub"))throw new f.default("MisplacedLimits","%1 is allowed only on operators",t.currentCS);var b,n=t.stack.Top();u.default.isType(a,"munderover")&&!r?(b=t.create("node","msubsup"),u.default.copyChildren(a,b),a=n.Last=b):u.default.isType(a,"msubsup")&&r&&(b=t.create("node","munderover"),u.default.copyChildren(a,b),a=n.Last=b),u.default.setProperty(a,"movesupsub",!!r),u.default.setProperties(u.default.getCoreMO(a),{movablelimits:!1}),(u.default.getAttribute(a,"movablelimits")||u.default.getProperty(a,"movablelimits"))&&u.default.setProperties(a,{movablelimits:!1})},g.Over=function(t,i,r,a){var n=t.itemFactory.create("over").setProperty("name",t.currentCS);r||a?(n.setProperty("open",r),n.setProperty("close",a)):i.match(/withdelims$/)&&(n.setProperty("open",t.GetDelimiter(i)),n.setProperty("close",t.GetDelimiter(i))),i.match(/^\\above/)?n.setProperty("thickness",t.GetDimen(i)):(i.match(/^\\atop/)||r||a)&&n.setProperty("thickness",0),t.Push(n)},g.Frac=function(t,i){var r=t.ParseArg(i),a=t.ParseArg(i),n=t.create("node","mfrac",[r,a]);t.Push(n)},g.Sqrt=function(t,i){var r=t.GetBrackets(i),a=t.GetArgument(i);"\\frac"===a&&(a+="{"+t.GetArgument(a)+"}{"+t.GetArgument(a)+"}");var n=new e.default(a,t.stack.env,t.configuration).mml();n=r?t.create("node","mroot",[n,O(t,r)]):t.create("node","msqrt",[n]),t.Push(n)},g.Root=function(t,i){var r=t.GetUpTo(i,"\\of"),a=t.ParseArg(i),n=t.create("node","mroot",[a,O(t,r)]);t.Push(n)},g.MoveRoot=function(t,i,r){if(!t.stack.env.inRoot)throw new f.default("MisplacedMoveRoot","%1 can appear only within a root",t.currentCS);if(t.stack.global[r])throw new f.default("MultipleMoveRoot","Multiple use of %1",t.currentCS);var a=t.GetArgument(i);if(!a.match(/-?[0-9]+/))throw new f.default("IntegerArg","The argument to %1 must be an integer",t.currentCS);"-"!==(a=parseInt(a,10)/15+"em").substr(0,1)&&(a="+"+a),t.stack.global[r]=a},g.Accent=function(t,i,r,a){var n=t.ParseArg(i),b=C(C({},h.default.getFontDef(t)),{accent:!0,mathaccent:!0}),T=u.default.createEntity(r),k=t.create("token","mo",b,T);u.default.setAttribute(k,"stretchy",!!a);var B=u.default.isEmbellished(n)?u.default.getCoreMO(n):n;(u.default.isType(B,"mo")||u.default.getProperty(B,"movablelimits"))&&u.default.setProperties(B,{movablelimits:!1});var R=t.create("node","munderover");u.default.setChild(R,0,n),u.default.setChild(R,1,null),u.default.setChild(R,2,k);var X=t.create("node","TeXAtom",[R]);t.Push(X)},g.UnderOver=function(t,i,r,a){var n=u.default.createEntity(r),b=t.create("token","mo",{stretchy:!0,accent:!0},n),T="o"===i.charAt(1)?"over":"under",M=t.ParseArg(i);t.Push(h.default.underOver(t,M,b,T,a))},g.Overset=function(t,i){var r=t.ParseArg(i),a=t.ParseArg(i);h.default.checkMovableLimits(a),r.isKind("mo")&&u.default.setAttribute(r,"accent",!1);var n=t.create("node","mover",[a,r]);t.Push(n)},g.Underset=function(t,i){var r=t.ParseArg(i),a=t.ParseArg(i);h.default.checkMovableLimits(a),r.isKind("mo")&&u.default.setAttribute(r,"accent",!1);var n=t.create("node","munder",[a,r],{accentunder:!1});t.Push(n)},g.Overunderset=function(t,i){var r=t.ParseArg(i),a=t.ParseArg(i),n=t.ParseArg(i);h.default.checkMovableLimits(n),r.isKind("mo")&&u.default.setAttribute(r,"accent",!1),a.isKind("mo")&&u.default.setAttribute(a,"accent",!1);var b=t.create("node","munderover",[n,a,r],{accent:!1,accentunder:!1});t.Push(b)},g.TeXAtom=function(t,i,r){var n,b,T,a={texClass:r};if(r===d.TEXCLASS.OP){a.movesupsub=a.movablelimits=!0;var M=t.GetArgument(i),k=M.match(/^\s*\\rm\s+([a-zA-Z0-9 ]+)$/);k?(a.mathvariant=o.TexConstant.Variant.NORMAL,b=t.create("token","mi",a,k[1])):(T=new e.default(M,t.stack.env,t.configuration).mml(),b=t.create("node","TeXAtom",[T],a)),n=t.itemFactory.create("fn",b)}else T=t.ParseArg(i),n=t.create("node","TeXAtom",[T],a);t.Push(n)},g.MmlToken=function(t,i){var M,r=t.GetArgument(i),a=t.GetBrackets(i,"").replace(/^\s+/,""),n=t.GetArgument(i),b={},T=[];try{M=t.create("node",r)}catch{M=null}if(!M||!M.isToken)throw new f.default("NotMathMLToken","%1 is not a token element",r);for(;""!==a;){var k=a.match(/^([a-z]+)\s*=\s*('[^']*'|"[^"]*"|[^ ,]*)\s*,?\s*/i);if(!k)throw new f.default("InvalidMathMLAttr","Invalid MathML attribute: %1",a);if(!M.attributes.hasDefault(k[1])&&!j[k[1]])throw new f.default("UnknownAttrForElement","%1 is not a recognized attribute for %2",k[1],r);var B=h.default.MmlFilterAttribute(t,k[1],k[2].replace(/^(['"])(.*)\1$/,"$2"));B&&("true"===B.toLowerCase()?B=!0:"false"===B.toLowerCase()&&(B=!1),b[k[1]]=B,T.push(k[1])),a=a.substr(k[0].length)}T.length&&(b["mjx-keep-attrs"]=T.join(" "));var R=t.create("text",n);M.appendChild(R),u.default.setProperties(M,b),t.Push(M)},g.Strut=function(t,i){var r=t.create("node","mrow"),a=t.create("node","mpadded",[r],{height:"8.6pt",depth:"3pt",width:0});t.Push(a)},g.Phantom=function(t,i,r,a){var n=t.create("node","mphantom",[t.ParseArg(i)]);(r||a)&&(n=t.create("node","mpadded",[n]),a&&(u.default.setAttribute(n,"height",0),u.default.setAttribute(n,"depth",0)),r&&u.default.setAttribute(n,"width",0));var b=t.create("node","TeXAtom",[n]);t.Push(b)},g.Smash=function(t,i){var r=h.default.trimSpaces(t.GetBrackets(i,"")),a=t.create("node","mpadded",[t.ParseArg(i)]);switch(r){case"b":u.default.setAttribute(a,"depth",0);break;case"t":u.default.setAttribute(a,"height",0);break;default:u.default.setAttribute(a,"height",0),u.default.setAttribute(a,"depth",0)}var n=t.create("node","TeXAtom",[a]);t.Push(n)},g.Lap=function(t,i){var r=t.create("node","mpadded",[t.ParseArg(i)],{width:0});"\\llap"===i&&u.default.setAttribute(r,"lspace","-1width");var a=t.create("node","TeXAtom",[r]);t.Push(a)},g.RaiseLower=function(t,i){var r=t.GetDimen(i),a=t.itemFactory.create("position").setProperties({name:t.currentCS,move:"vertical"});"-"===r.charAt(0)&&(r=r.slice(1),i="raise"===i.substr(1)?"\\lower":"\\raise"),"\\lower"===i?(a.setProperty("dh","-"+r),a.setProperty("dd","+"+r)):(a.setProperty("dh","+"+r),a.setProperty("dd","-"+r)),t.Push(a)},g.MoveLeftRight=function(t,i){var r=t.GetDimen(i),a="-"===r.charAt(0)?r.slice(1):"-"+r;if("\\moveleft"===i){var n=r;r=a,a=n}t.Push(t.itemFactory.create("position").setProperties({name:t.currentCS,move:"horizontal",left:t.create("node","mspace",[],{width:r}),right:t.create("node","mspace",[],{width:a})}))},g.Hskip=function(t,i){var r=t.create("node","mspace",[],{width:t.GetDimen(i)});t.Push(r)},g.Nonscript=function(t,i){t.Push(t.itemFactory.create("nonscript"))},g.Rule=function(t,i,r){var T={width:t.GetDimen(i),height:t.GetDimen(i),depth:t.GetDimen(i)};"blank"!==r&&(T.mathbackground=t.stack.env.color||"black");var M=t.create("node","mspace",[],T);t.Push(M)},g.rule=function(t,i){var r=t.GetBrackets(i),a=t.GetDimen(i),n=t.GetDimen(i),b=t.create("node","mspace",[],{width:a,height:n,mathbackground:t.stack.env.color||"black"});r&&(b=t.create("node","mpadded",[b],{voffset:r}),r.match(/^\-/)?(u.default.setAttribute(b,"height",r),u.default.setAttribute(b,"depth","+"+r.substr(1))):u.default.setAttribute(b,"height","+"+r)),t.Push(b)},g.MakeBig=function(t,i,r,a){var n=String(a*=1.411764705882353).replace(/(\.\d\d\d).+/,"$1")+"em",b=t.GetDelimiter(i,!0),T=t.create("token","mo",{minsize:n,maxsize:n,fence:!0,stretchy:!0,symmetric:!0},b),M=t.create("node","TeXAtom",[T],{texClass:r});t.Push(M)},g.BuildRel=function(t,i){var r=t.ParseUpTo(i,"\\over"),a=t.ParseArg(i),n=t.create("node","munderover");u.default.setChild(n,0,a),u.default.setChild(n,1,null),u.default.setChild(n,2,r);var b=t.create("node","TeXAtom",[n],{texClass:d.TEXCLASS.REL});t.Push(b)},g.HBox=function(t,i,r,a){t.PushAll(h.default.internalMath(t,t.GetArgument(i),r,a))},g.FBox=function(t,i){var r=h.default.internalMath(t,t.GetArgument(i)),a=t.create("node","menclose",r,{notation:"box"});t.Push(a)},g.FrameBox=function(t,i){var r=t.GetBrackets(i),a=t.GetBrackets(i)||"c",n=h.default.internalMath(t,t.GetArgument(i));r&&(n=[t.create("node","mpadded",n,{width:r,"data-align":(0,E.lookup)(a,{l:"left",r:"right"},"center")})]);var b=t.create("node","TeXAtom",[t.create("node","menclose",n,{notation:"box"})],{texClass:d.TEXCLASS.ORD});t.Push(b)},g.Not=function(t,i){t.Push(t.itemFactory.create("not"))},g.Dots=function(t,i){var r=u.default.createEntity("2026"),a=u.default.createEntity("22EF"),n=t.create("token","mo",{stretchy:!1},r),b=t.create("token","mo",{stretchy:!1},a);t.Push(t.itemFactory.create("dots").setProperties({ldots:n,cdots:b}))},g.Matrix=function(t,i,r,a,n,b,T,M,k,B){var R=t.GetNext();if(""===R)throw new f.default("MissingArgFor","Missing argument for %1",t.currentCS);"{"===R?t.i++:(t.string=R+"}"+t.string.slice(t.i+1),t.i=0);var X=t.itemFactory.create("array").setProperty("requireClose",!0);X.arraydef={rowspacing:T||"4pt",columnspacing:b||"1em"},k&&X.setProperty("isCases",!0),B&&(X.setProperty("isNumbered",!0),X.arraydef.side=B),(r||a)&&(X.setProperty("open",r),X.setProperty("close",a)),"D"===M&&(X.arraydef.displaystyle=!0),null!=n&&(X.arraydef.columnalign=n),t.Push(X)},g.Entry=function(t,i){t.Push(t.itemFactory.create("cell").setProperties({isEntry:!0,name:i}));var r=t.stack.Top(),a=r.getProperty("casesEnv");if(r.getProperty("isCases")||a){for(var b=t.string,T=0,M=-1,k=t.i,B=b.length,R=a?new RegExp("^\\\\end\\s*\\{".concat(a.replace(/\*/,"\\*"),"\\}")):null;k<B;){var X=b.charAt(k);if("{"===X)T++,k++;else if("}"===X)0===T?B=0:(0===--T&&M<0&&(M=k-t.i),k++);else{if("&"===X&&0===T)throw new f.default("ExtraAlignTab","Extra alignment tab in \\cases text");if("\\"===X){var G=b.substr(k);G.match(/^((\\cr)[^a-zA-Z]|\\\\)/)||R&&G.match(R)?B=0:k+=2}else k++}}var y=b.substr(t.i,k-t.i);if(!y.match(/^\s*\\text[^a-zA-Z]/)||M!==y.replace(/\s+$/,"").length-1){var c=h.default.internalMath(t,h.default.trimSpaces(y),0);t.PushAll(c),t.i=k}}},g.Cr=function(t,i){t.Push(t.itemFactory.create("cell").setProperties({isCR:!0,name:i}))},g.CrLaTeX=function(t,i,r){var a;if(void 0===r&&(r=!1),!r&&("*"===t.string.charAt(t.i)&&t.i++,"["===t.string.charAt(t.i))){var n=t.GetBrackets(i,""),b=A(h.default.matchDimen(n),2),T=b[0],M=b[1];if(n&&!T)throw new f.default("BracketMustBeDimension","Bracket argument to %1 must be a dimension",t.currentCS);a=T+M}t.Push(t.itemFactory.create("cell").setProperties({isCR:!0,name:i,linebreak:!0}));var B,k=t.stack.Top();k instanceof m.ArrayItem?a&&k.addRowSpacing(a):(a&&(B=t.create("node","mspace",[],{depth:a}),t.Push(B)),B=t.create("node","mspace",[],{linebreak:o.TexConstant.LineBreak.NEWLINE}),t.Push(B))},g.HLine=function(t,i,r){null==r&&(r="solid");var a=t.stack.Top();if(!(a instanceof m.ArrayItem)||a.Size())throw new f.default("Misplaced","Misplaced %1",t.currentCS);if(a.table.length){for(var n=a.arraydef.rowlines?a.arraydef.rowlines.split(/ /):[];n.length<a.table.length;)n.push("none");n[a.table.length-1]=r,a.arraydef.rowlines=n.join(" ")}else a.frame.push("top")},g.HFill=function(t,i){var r=t.stack.Top();if(!(r instanceof m.ArrayItem))throw new f.default("UnsupportedHFill","Unsupported use of %1",t.currentCS);r.hfill.push(r.Size())},g.BeginEnd=function(t,i){var r=t.GetArgument(i);if(r.match(/\\/i))throw new f.default("InvalidEnv","Invalid environment name '%1'",r);var a=t.configuration.handlers.get("environment").lookup(r);if(a&&"\\end"===i){if(!a.args[0]){var n=t.itemFactory.create("end").setProperty("name",r);return void t.Push(n)}t.stack.env.closing=r}h.default.checkMaxMacros(t,!1),t.parse("environment",[t,r])},g.Array=function(t,i,r,a,n,b,T,M,k){n||(n=t.GetArgument("\\begin{"+i.getName()+"}"));var B=("c"+n).replace(/[^clr|:]/g,"").replace(/[^|:]([|:])+/g,"$1");n=(n=n.replace(/[^clr]/g,"").split("").join(" ")).replace(/l/g,"left").replace(/r/g,"right").replace(/c/g,"center");var R=t.itemFactory.create("array");return R.arraydef={columnalign:n,columnspacing:b||"1em",rowspacing:T||"4pt"},B.match(/[|:]/)&&(B.charAt(0).match(/[|:]/)&&(R.frame.push("left"),R.dashed=":"===B.charAt(0)),B.charAt(B.length-1).match(/[|:]/)&&R.frame.push("right"),B=B.substr(1,B.length-2),R.arraydef.columnlines=B.split("").join(" ").replace(/[^|: ]/g,"none").replace(/\|/g,"solid").replace(/:/g,"dashed")),r&&R.setProperty("open",t.convertDelimiter(r)),a&&R.setProperty("close",t.convertDelimiter(a)),"'"===(M||"").charAt(1)&&(R.arraydef["data-cramped"]=!0,M=M.charAt(0)),"D"===M?R.arraydef.displaystyle=!0:M&&(R.arraydef.displaystyle=!1),"S"===M&&(R.arraydef.scriptlevel=1),k&&(R.arraydef.useHeight=!1),t.Push(i),R},g.AlignedArray=function(t,i){var r=t.GetBrackets("\\begin{"+i.getName()+"}"),a=g.Array(t,i);return h.default.setArrayAlign(a,r)},g.Equation=function(t,i,r){return t.Push(i),h.default.checkEqnEnv(t),t.itemFactory.create("equation",r).setProperty("name",i.getName())},g.EqnArray=function(t,i,r,a,n,b){t.Push(i),a&&h.default.checkEqnEnv(t),n=(n=n.replace(/[^clr]/g,"").split("").join(" ")).replace(/l/g,"left").replace(/r/g,"right").replace(/c/g,"center");var T=t.itemFactory.create("eqnarray",i.getName(),r,a,t.stack.global);return T.arraydef={displaystyle:!0,columnalign:n,columnspacing:b||"1em",rowspacing:"3pt",side:t.options.tagSide,minlabelspacing:t.options.tagIndent},T},g.HandleNoTag=function(t,i){t.tags.notag()},g.HandleLabel=function(t,i){var r=t.GetArgument(i);if(""!==r&&!t.tags.refUpdate){if(t.tags.label)throw new f.default("MultipleCommand","Multiple %1",t.currentCS);if(t.tags.label=r,(t.tags.allLabels[r]||t.tags.labels[r])&&!t.options.ignoreDuplicateLabels)throw new f.default("MultipleLabel","Label '%1' multiply defined",r);t.tags.labels[r]=new P.Label}},g.HandleRef=function(t,i,r){var a=t.GetArgument(i),n=t.tags.allLabels[a]||t.tags.labels[a];n||(t.tags.refUpdate||(t.tags.redo=!0),n=new P.Label);var b=n.tag;r&&(b=t.tags.formatTag(b));var T=t.create("node","mrow",h.default.internalMath(t,b),{href:t.tags.formatUrl(n.id,t.options.baseURL),class:"MathJax_ref"});t.Push(T)},g.Macro=function(t,i,r,a,n){if(a){var b=[];if(null!=n){var T=t.GetBrackets(i);b.push(T??n)}for(var M=b.length;M<a;M++)b.push(t.GetArgument(i));r=h.default.substituteArgs(t,b,r)}t.string=h.default.addArgs(t,r,t.string.slice(t.i)),t.i=0,h.default.checkMaxMacros(t)},g.MathChoice=function(t,i){var r=t.ParseArg(i),a=t.ParseArg(i),n=t.ParseArg(i),b=t.ParseArg(i);t.Push(t.create("node","MathChoice",[r,a,n,b]))},v.default=g},21108:(V,v)=>{Object.defineProperty(v,"__esModule",{value:!0}),v.px=v.emRounded=v.em=v.percent=v.length2em=v.MATHSPACE=v.RELUNITS=v.UNITS=v.BIGDIMEN=void 0,v.BIGDIMEN=1e6,v.UNITS={px:1,in:96,cm:96/2.54,mm:96/25.4},v.RELUNITS={em:1,ex:.431,pt:.1,pc:1.2,mu:1/18},v.MATHSPACE={veryverythinmathspace:1/18,verythinmathspace:2/18,thinmathspace:3/18,mediummathspace:4/18,thickmathspace:5/18,verythickmathspace:6/18,veryverythickmathspace:7/18,negativeveryverythinmathspace:-1/18,negativeverythinmathspace:-2/18,negativethinmathspace:-3/18,negativemediummathspace:-4/18,negativethickmathspace:-5/18,negativeverythickmathspace:-6/18,negativeveryverythickmathspace:-7/18,thin:.04,medium:.06,thick:.1,normal:1,big:2,small:1/Math.sqrt(2),infinity:v.BIGDIMEN},v.length2em=function I(A,s,m,u){if(void 0===s&&(s=0),void 0===m&&(m=1),void 0===u&&(u=16),"string"!=typeof A&&(A=String(A)),""===A||null==A)return s;if(v.MATHSPACE[A])return v.MATHSPACE[A];var f=A.match(/^\s*([-+]?(?:\.\d+|\d+(?:\.\d*)?))?(pt|em|ex|mu|px|pc|in|mm|cm|%)?/);if(!f)return s;var e=parseFloat(f[1]||"1"),o=f[2];return v.UNITS.hasOwnProperty(o)?e*v.UNITS[o]/u/m:v.RELUNITS.hasOwnProperty(o)?e*v.RELUNITS[o]:"%"===o?e/100*s:e*s},v.percent=function C(A){return(100*A).toFixed(1).replace(/\.?0+$/,"")+"%"},v.em=function D(A){return Math.abs(A)<.001?"0":A.toFixed(3).replace(/\.?0+$/,"")+"em"},v.emRounded=function _(A,s){return void 0===s&&(s=16),A=(Math.round(A*s)+.05)/s,Math.abs(A)<.001?"0em":A.toFixed(3).replace(/\.?0+$/,"")+"em"},v.px=function w(A,s,m){return void 0===s&&(s=-v.BIGDIMEN),void 0===m&&(m=16),A*=m,s&&A<s&&(A=s),Math.abs(A)<.1?"0":A.toFixed(1).replace(/\.0$/,"")+"px"}}}]);