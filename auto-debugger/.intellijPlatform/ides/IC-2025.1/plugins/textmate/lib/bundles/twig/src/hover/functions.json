{"attribute": {"prefix": "attribute", "body": "{{ attribute($1) }}$2", "description": "The attribute function can be used to access a \"dynamic\" attribute of a variable", "example": ""}, "block": {"prefix": "block", "body": "{{ block('${block name}') }}$1", "description": "When a template uses inheritance and if you want to print a block multiple times, use the block function", "example": ""}, "constant": {"prefix": "constant", "body": "{{ constant('${const name}') }}$1", "description": "constant returns the constant value for a given string", "example": "{{ some_date | date(constant('DATE_W3C')) }}\n{{ constant('Namespace\\Classname::CONSTANT_NAME') }}"}, "cycle": {"prefix": "cycle", "body": "{{ cycle(${array}, ${position}) }}$1", "description": "The cycle function cycles on an array of values", "example": ""}, "date": {"prefix": "date", "body": "{% set ${currentDate} = date($1) %}$2", "description": "Converts an argument to a date to allow date comparison", "example": "{% date() %}\n{% date('-2days') %}\n{% date('-2days', 'Europe/Paris') %}"}, "dump": {"prefix": "dump", "body": "{{ dump(${array}) }}$1", "description": "(function) dumps information about a template variable. This is mostly useful to debug a template that does not behave as expected by introspecting its variables", "example": ""}, "include": {"prefix": "include function", "body": "{{ include('${filename}.twig') }}$1", "description": "(function) returns the rendered content of a template", "example": ""}, "max": {"prefix": "max", "body": "{% set ${result} = max(${array}) %}$1", "description": "(function) returns the biggest value of a sequence or a set of values", "example": "{{ max(1, 3, 2) }}\n{# returns \"3\" #}\n\n{{ max({2: \"e\", 3: \"a\", 1: \"b\", 5: \"d\", 4: \"c\"}) }}\n{# returns \"e\" #}"}, "min": {"prefix": "min", "body": "{% set ${result} = min(${array}) %}$1", "description": "(function) returns the lowest value of a sequence or a set of values", "example": "{{ min(1, 3, 2) }}\n{# returns \"1\" #}\n\n{{ min({2: \"e\", 3: \"a\", 1: \"b\", 5: \"d\", 4: \"c\"}) }}\n{# returns \"a\" #}"}, "parent": {"prefix": "parent", "body": "{{ parent() }}", "description": "(function) return the content of the block as defined in the base template", "example": "{% extends \"base.html\" %}\n\n{% block sidebar %}\n\t<h3>Table Of Contents</h3>\n\t...\n\t{{ parent() }}\n{% endblock %}"}, "random": {"prefix": "random", "hover": "", "body": "{% set ${result} = random($1) %}$2", "description": "(function) returns a random value depending on the supplied parameter type", "example": "{{ random(['apple', 'orange', 'citrus']) }}\n{# example output: orange #}\n\n{{ random('ABC') }}\n{# example output: C #}\n\n{{ random() }}\n{# example output: 15386094 (works as the native PHP mt_rand function) #}\n\n{{ random(5) }}\n{# example output: 3 #}"}, "range set": {"prefix": "range set", "body": "{% set ${result} = range(${low}, ${high}, ${step}) %}$1", "description": "(function) Returns an array of elements from low to high, inclusive", "example": "{% set result = range(0, 6, 2) %}\n{% dump(result) %}\n{# output: array(0, 2, 4, 6) #}"}, "range": {"prefix": "range", "body": "range(${low}, ${high}, ${step})", "description": "(function) Returns an array of elements from low to high, inclusive", "example": "{% set result = range(0, 6, 2) %}\n{% dump(result) %}\n{# output: array(0, 2, 4, 6) #}"}, "source": {"prefix": "source", "body": "{{ source('${template}.twig') }}$1", "description": "(function) returns the content of a template without rendering it", "example": ""}, "template_from_string": {"prefix": "template_from_string", "body": "{{ include(template_from_string(\"$1\")) }}$2", "description": "(function) loads a template from a string", "example": "{{ include(template_from_string(\"Hello {{ name }}\")) }}"}}