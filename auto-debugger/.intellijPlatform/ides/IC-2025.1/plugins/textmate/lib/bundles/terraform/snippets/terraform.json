{"For Each": {"prefix": "fore", "body": ["for_each = {", "\t${1:key} = \"${2:value}\"", "}"], "description": "The for_each meta-argument accepts a map or a set of strings, and creates an instance for each item in that map or set."}, "Module": {"prefix": "module", "body": ["module \"${1:name}\" {", "\tsource = \"$2\"", "\t$3", "}"]}, "Output": {"prefix": "output", "body": ["output \"${1:name}\" {", "\tvalue = \"$2\"", "}"]}, "Provisioner": {"prefix": "provisioner", "body": ["provisioner \"${1:name}\" {", "${2}", "}"], "description": "Provisioners can be used to model specific actions on the local machine or on a remote machine in order to prepare servers or other infrastructure objects for service."}, "Empty variable": {"prefix": "vare", "body": ["variable \"${1:name}\" {", "\ttype = ${2|string,number,bool|}", "\t${3:description = \"${4:(optional) describe your variable}\"}", "}"], "description": "Variable (empty)"}, "Map variable": {"prefix": "varm", "body": ["variable \"${1:name}\" {", "\ttype = map(${2|string,number,bool|})", "\t${3:description = \"${4:(optional) describe your variable}\"}", "\tdefault = {", "\t\t${5:key1} = \"${6:val1}\"", "\t\t${7:key2} = \"${8:val2}\"", "\t}", "}"], "description": "Variable (map)"}}