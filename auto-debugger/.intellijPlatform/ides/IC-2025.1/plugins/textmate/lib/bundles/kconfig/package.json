{"name": "kconfig-lang", "version": "1.2.0", "description": "Kconfig language support for the Zephyr Project", "license": "MIT", "contributes": {"configuration": [{"title": "Kconfig", "properties": {"kconfig.root": {"type": "string", "description": "Root kconfig file to start indexing."}, "kconfig.env": {"type": "object", "description": "Mapping of environment variables to use in string insertion. Instances of $(VAR) will be replaced by their environment variable VAR's value.", "patternProperties": {".*": {"type": "string"}}}, "kconfig.conf": {"type": "object", "description": "Static configuration items entries as an object.", "patternProperties": {"^CONFIG_\\w[\\w_\\d]*": {"type": ["string", "integer", "boolean"]}}, "additionalProperties": false}, "kconfig.conf_files": {"type": "array", "description": "Array of properties files to always parse before the open properties file.", "items": {"type": "string"}}, "kconfig.cfiles": {"type": "boolean", "description": "Enable Kconfig hover information and go to definition in C files", "default": true}, "kconfig.disable": {"type": "boolean", "description": "Disable Kconfig language features", "default": false}, "kconfig.zephyr.board": {"type": "object", "description": "Zephyr board to compile for", "properties": {"board": {"type": "string"}, "arch": {"type": "string"}, "dir": {"type": "string"}}}, "kconfig.zephyr.west": {"type": "string", "description": "Location of the West tool"}, "kconfig.zephyr.base": {"type": "string", "description": "Override location of Zephyr"}, "kconfig.zephyr.soc_roots": {"type": "array", "description": "Additional out-of-tree SoC root directories, as passed to CMake through -DSOC_ROOT", "items": {"type": "string"}}}}], "commands": [{"command": "kconfig.zephyr.setBoard", "title": "Kconfig: Set board for Zephyr", "enablement": "workspaceFolderCount > 0"}], "languages": [{"id": "kconfig", "aliases": ["Kconfig"], "filenames": ["Kconfig", "Kconfig.zephyr", "Kconfig.defconfig", "Kconfig.soc.defconfig", "Kconfig.soc", "Kconfig.board", "Kconfig.shield", "Kconfig.nrf", "Kconfig.stm32", "Kconfig.modules", "Kconfig.defconfig.xmc4700", "Kconfig.swo", "Kconfig.raytac_mdbt50q_db_33", "Kconfig.numaker_pfm_m467", "Kconfig.mcux_pwt", "Kconfig.mimxrt1050_evk", "Kconfig.nucleo_f207zg", "Kconfig.ltc166x", "Kconfig.vmu_rt1170", "Kconfig.bt510", "Kconfig.defconfig.gd32e507", "Kconfig.x86", "Kconfig.lpcxpresso11u68", "Kconfig.nxp_fs26", "Kconfig.nrf_rram", "Kconfig.iproc", "Kconfig.defconfig.stm32g0c1xx", "Kconfig.nucleo_g031k8", "Kconfig.qemu_xtensa", "Kconfig.features", "Kconfig.raytac_mdbt53_db_40", "Kconfig.template.with_logging", "Kconfig.defconfig.stm32f401xe", "Kconfig.dmic_mcux", "Kconfig.defconfig.viper_bcm58402_a72", "Kconfig.b_l072z_lrwan1", "Kconfig.black_f407zg_pro", "Kconfig.defconfig.stm32wle5xx", "Kconfig.ubx_evkninab3", "Kconfig.sam_v71_xult", "Kconfig.imx8qxp_mek", "Kconfig.msp432p4xx", "Kconfig.nucleo_f031k6", "Kconfig.arduino_portenta_h7", "Kconfig.innblue21", "Kconfig.iso", "Kconfig.nucleo_l552ze_q", "Kconfig.gpio_qdec", "Kconfig.cc1352r1_launchxl", "Kconfig.vfp", "Kconfig.it82xx2_evb", "Kconfig.mb85rcxx", "Kconfig.stm32_rtc", "Kconfig.nrfx", "Kconfig.st25dv_mb1283_disco", "Kconfig.arduino_zero", "Kconfig.sai", "Kconfig.generic_leon3", "Kconfig.loopback", "Kconfig.mimxrt1040_evk", "Kconfig.stm32u5a9j_dk", "Kconfig.pl022", "Kconfig.bt610", "Kconfig.spiram", "Kconfig.sedi", "Kconfig.arduino_opta", "Kconfig.mikroe_clicker_2", "Kconfig.nucleo_g071rb", "Kconfig.hsdk4xd", "Kconfig.stmpe811", "Kconfig.adsp", "Kconfig.stm32f746g_disco", "Kconfig.nxp_edma", "Kconfig.cap", "Kconfig.v2m_musca_s1", "Kconfig.dwc2", "Kconfig.numaker_m2l31ki", "Kconfig.eventfd", "Kconfig.defconfig.stm32g473xx", "Kconfig.gd32e507z_eval", "Kconfig.ubx_bmd330eval", "Kconfig.defconfig.mimx8mm6_m4", "Kconfig.defconfig.gd32e103", "Kconfig.cc13xx_cc26xx_timer", "Kconfig.ti_tps382x", "Kconfig.m5stack_stamps3", "Kconfig.defconfig.stm32f031x6", "Kconfig.aspeed", "Kconfig.gr716a_mini", "Kconfig.defconfig.stm32l010xb", "Kconfig.contextualelectronics_abc", "Kconfig.pbp", "Kconfig.usbc_vbus_adc", "Kconfig.litex_vexriscv", "Kconfig.udoo_neo_full", "Kconfig.saml2x", "Kconfig.nrf_led_matrix", "Kconfig.xtensa_sim", "Kconfig.nucleo_l4a6zg", "Kconfig.defconfig.stm32g0b0xx", "Kconfig.chre", "Kconfig.stm32f412g_disco", "Kconfig.tfm", "Kconfig.defconfig.stm32u5a9xx", "Kconfig.efm32hg_slstk3400a", "Kconfig.ls1046ardb", "Kconfig.kb1200_evb", "Kconfig.defconfig.stm32f411xe", "Kconfig.bbc_microbit", "Kconfig.defconfig.nrf5340_CPUAPP_QKAA", "Kconfig.defconfig.stm32g051xx", "Kconfig.multidomain", "Kconfig.nxp_irqsteer", "Kconfig.olimex_stm32_h407", "Kconfig.ubx_bmd300eval", "Kconfig.defconfig.stm32h7b0xx", "Kconfig.hash_func", "Kconfig.lpcxpresso55s36", "Kconfig.defconfig.mps3_an547", "Kconfig.lpd880x", "Kconfig.ncp5623", "Kconfig.nxp_s32", "Kconfig.defconfig.stm32f746xx", "Kconfig.intel_adl_rvp", "Kconfig.efm32wg_stk3800", "Kconfig.google_kukui", "Kconfig.khadas_edgev", "Kconfig.tmp116", "Kconfig.defconfig.stm32g041xx", "Kconfig.leon_gptimer", "Kconfig.quectel_lcx6g", "Kconfig.mcp4728", "Kconfig.arduino_nano_33_ble", "Kconfig.olimexino_stm32", "Kconfig.mimxrt1060_evkb", "Kconfig.qemu_leon3", "Kconfig.esp_wrover_kit", "Kconfig.esp32s3_devkitm", "Kconfig.mimxrt1010_evk", "Kconfig.riscv_machine", "Kconfig.adi_sdp_k1", "Kconfig.npcx4m8f_evb", "Kconfig.formatting", "Kconfig.smp", "Kconfig.arduino_due", "Kconfig.firmware_loader", "Kconfig.mcux_rgpio", "Kconfig.mcr20a", "Kconfig.cdc", "Kconfig.tcan4x5x", "Kconfig.sja1000", "Kconfig.bl5340_dvk", "Kconfig.icev_wireless", "Kconfig.nucleo_f030r8", "Kconfig.google_twinkie_v2", "Kconfig.hda", "Kconfig.ace", "Kconfig.litex", "Kconfig.defconfig.stm32f098xx", "Kconfig.hifive1", "Kconfig.nucleo_f756zg", "Kconfig.defconfig.stm32l010x4", "Kconfig.sbcon", "Kconfig.nrf9161dk", "Kconfig.adin2111", "Kconfig.defconfig.stm32l552xx", "Kconfig.ht16k33", "Kconfig.sx12xx", "Kconfig.xt-sim", "Kconfig.efm32pg_stk3401a", "Kconfig.defconfig.gd32f350", "Kconfig.leuart_gecko", "Kconfig.nrf21540dk", "Kconfig.cp", "Kconfig.nucleo_g070rb", "Kconfig.obj_core", "Kconfig.andes_atcdmac300", "Kconfig.defconfig.stm32l073xx", "Kconfig.tinycrypt", "Kconfig.defconfig.stm32wle4xx", "Kconfig.nucleo_f103rb", "Kconfig.ascs", "Kconfig.b91", "Kconfig.pandora_stm32l475", "Kconfig.emul_sbs_gauge", "Kconfig.device", "Kconfig.ruuvi_ruuvitag", "Kconfig.defconfig.stm32f415xx", "Kconfig.lpcxpresso55s28", "Kconfig.generic", "Kconfig.wio_terminal", "Kconfig.nios2_msgdma", "Kconfig.defconfig.stm32f302xc", "Kconfig.defconfig.stm32f334x8", "Kconfig.bl654_dvk", "Kconfig.defconfig.stm32g0b1xx", "Kconfig.blackpill_f411ce", "Kconfig.arm_arch", "Kconfig.stm32f7508_dk", "Kconfig.pan1781_evb", "Kconfig.smsc911x", "Kconfig.xiao_ble", "Kconfig.defconfig.nrf5340_CPUNET_QKAA", "Kconfig.mcp251xfd", "Kconfig.pw", "Kconfig.defconfig.stm32wba52xx", "Kconfig.defconfig.stm32u585xx", "Kconfig.lpc", "Kconfig.tmr_cmsdk_apb", "Kconfig.nrf_sw", "Kconfig.spinlock", "Kconfig.defconfig.mimx8mn6_a53", "Kconfig.altera_max10", "Kconfig.mcux_scg", "Kconfig.stm32l1_disco", "Kconfig.stamp_c3", "Kconfig.nucleo_g474re", "Kconfig.intel_adsp", "Kconfig.flash", "Kconfig.xenvm", "Kconfig.cdc_acm", "Kconfig.nxp_pint", "Kconfig.tfm.crypto_modules", "Kconfig.chsc6x", "Kconfig.sparkfun_thing_plus", "Kconfig.actinius_icarus", "Kconfig.intel_ish_5_4_1", "Kconfig.gic", "Kconfig.legend", "Kconfig.cc1200", "Kconfig.defconfig.rk3568", "Kconfig.stm32f072_eval", "Kconfig.bl654_usb", "Kconfig.nrf52840_mdk_usb_dongle", "Kconfig.arch", "Kconfig.xlnx_gem", "Kconfig.nct38xx", "Kconfig.gd32a503v_eval", "Kconfig.defconfig.stm32f412xx", "Kconfig.ds2477_85", "Kconfig.gd32f470i_eval", "Kconfig.altera_jtag", "Kconfig.mcux_edma", "Kconfig.nrf52840dk", "Kconfig.w5500", "Kconfig.pca953x", "Kconfig.nucleo_l053r8", "Kconfig.defconfig.stm32g441xx", "Kconfig.nucleo_l452re", "Kconfig.nxp_pit", "Kconfig.m2gl025_miv", "Kconfig.imx8mn_evk", "Kconfig.pinnacle", "Kconfig.xec", "Kconfig.defconfig.stm32f091xc", "Kconfig.defconfig.stm32h723xx", "Kconfig.twr_ke18f", "Kconfig.dummy", "Kconfig.adv", "Kconfig.fxl6408", "Kconfig.dacx3608", "Kconfig.template.shell_log_queue_size", "Kconfig.dts", "Kconfig.defconfig.stm32h753xx", "Kconfig.mimxrt1062_fmurt6", "Kconfig.stm32_lptim", "Kconfig.rd_rw612_bga", "Kconfig.adafruit_itsybitsy", "Kconfig.nrf_bellboard", "Kconfig.apollo4p_blue_kxr_evb", "Kconfig.cond", "Kconfig.ssp", "Kconfig.cypress", "Kconfig.stm32f072b_disco", "Kconfig.nrf54l15pdk", "Kconfig.ls0xx", "Kconfig.hsdk", "Kconfig.rcar_salvator_xs", "Kconfig.mimxrt1020_evk", "Kconfig.multilevel.aggregator_template", "Kconfig.lp5569", "Kconfig.dmic", "Kconfig.intel_multibootfb", "Kconfig.qemu_x86_tiny", "Kconfig.mcan", "Kconfig.nucleo_l476rg", "Kconfig.imx8mm_evk", "Kconfig.ucans32k1sic", "Kconfig.tfm.partitions", "Kconfig.defconfig.stm32f107xc", "Kconfig.stm32g0316_disco", "Kconfig.ipv6", "Kconfig.innblue22", "Kconfig.stm32h7b3i_dk", "Kconfig.arduino_mkrzero", "Kconfig.96b_neonkey", "Kconfig.nucleo_c031c6", "Kconfig.npcx_itim", "Kconfig.rzt2m_starter_kit", "Kconfig.mimx8mm_phyboard_polis", "Kconfig.stm32373c_eval", "Kconfig.frontends", "Kconfig.phyboard_lyra_am62x", "Kconfig.nrf52_sparkfun", "Kconfig.defconfig.gd32l233", "Kconfig.imx8qm_mek", "Kconfig.rpmsg", "Kconfig.samr34_xpro", "Kconfig.defconfig.stm32f469xx", "Kconfig.defconfig.mec172xnsz", "Kconfig.net", "Kconfig.nrf5340dk", "Kconfig.hw_support", "Kconfig.defconfig.hs5x", "Kconfig.mcux_ecspi", "Kconfig.defconfig.hs_mpuv6", "Kconfig.plic", "Kconfig.bcm958402m2", "Kconfig.pico_pi", "Kconfig.defconfig.hs", "Kconfig.mcux_sim", "Kconfig.andes", "Kconfig.max31790", "Kconfig.sam0_rtc", "Kconfig.defconfig.it82202ax", "Kconfig.qemu_riscv32", "Kconfig.template.log_config.net", "Kconfig.mci_io_mux", "Kconfig.am1805", "Kconfig.intel_vtd", "Kconfig.defconfig.cc3220sf", "Kconfig.pacs", "Kconfig.lpcxpresso55s69", "Kconfig.msp_exp432p401r_launchxl", "Kconfig.niosv_m", "Kconfig.pd", "Kconfig.defconfig.stm32l4p5xx", "Kconfig.iotdk", "Kconfig.processing", "Kconfig.dsa", "Kconfig.defconfig.stm32f030x8", "Kconfig.nucleo_f722ze", "Kconfig.defconfig.stm32l431xx", "Kconfig.at45", "Kconfig.da1469x_dk_pro", "Kconfig.ssd1306", "Kconfig.defconfig.stm32f429xx", "Kconfig.imx", "Kconfig.sparkfun_red_v_things_plus", "Kconfig.defconfig.stm32l486xx", "Kconfig.dragino_nbsn95", "Kconfig.defconfig.stm32f030x4", "Kconfig.opentitan_earlgrey", "Kconfig.defconfig.stm32f303xe", "Kconfig.az3166_iotdevkit", "Kconfig.decawave_dwm1001_dev", "Kconfig.zephyr_gpio", "Kconfig.pca9685", "Kconfig.uname", "Kconfig.andes_atcwdt200", "Kconfig.sensortile_box", "Kconfig.mutex", "Kconfig.lora_e5_mini", "Kconfig.creg_gpio", "Kconfig.uart_pipe", "Kconfig.defconfig.stm32l151xba", "Kconfig.heltec_wifi_lora32_v2", "Kconfig.microbit", "Kconfig.ds2482-800", "Kconfig.mcs", "Kconfig.mercury_xu", "Kconfig.nuvoton", "Kconfig.pcf857x", "Kconfig.stm32h573i_dk", "Kconfig.m5stack_atoms3_lite", "Kconfig.smsc91x", "Kconfig.96b_meerkat96", "Kconfig.esp_at", "Kconfig.ambiq", "Kconfig.defconfig.nrf9151_LACA", "Kconfig.shell", "Kconfig.defconfig.stm32wl55xx", "Kconfig.warp7", "Kconfig.imx_epit", "Kconfig.actinius_icarus_som_dk", "Kconfig.stm32h735g_disco", "Kconfig.defconfig.stm32f303x8", "Kconfig.mcux_flexio", "Kconfig.pi3usb9201", "Kconfig.xtensa", "Kconfig.esp32_tmr", "Kconfig.ssd16xx", "Kconfig.efm32gg_sltb009a", "Kconfig.renesas_ra", "Kconfig.defconfig.rk3399", "Kconfig.intel_socfpga_agilex_socdk", "Kconfig.defconfig.agilex5", "Kconfig.adafruit_qt_py_rp2040", "Kconfig.sqn", "Kconfig.hx8394", "Kconfig.blinfo", "Kconfig.defconfig.stm32l152xc", "Kconfig.otm8009a", "Kconfig.nucleo_h563zi", "Kconfig.ble", "Kconfig.yd_esp32", "Kconfig.enc424j600", "Kconfig.kw41z", "Kconfig.samd20_xpro", "Kconfig.defconfig.stm32l4a6xx", "Kconfig.defconfig.stm32l562xx", "Kconfig.lpcxpresso51u68", "Kconfig.defconfig.stm32f417xx", "Kconfig.imx8mq_evk", "Kconfig.defconfig.stm32l475xx", "Kconfig.ubx_bmd340eval", "Kconfig.nxp_sof_host_dma", "Kconfig.adsp_mtrace", "Kconfig.vf610", "Kconfig.defconfig.stm32f412rx", "Kconfig.mpxxdtyy", "Kconfig.defconfig.stm32l011xx", "Kconfig.gd32f407v_start", "Kconfig.mcp320x", "Kconfig.nucleo_l496zg", "Kconfig.defconfig.stm32l4r5xx", "Kconfig.efm32gg_slwstk6121a", "Kconfig.b_g474e_dpow1", "Kconfig.gd32f350r_eval", "Kconfig.defconfig.em", "Kconfig.stm32f103_mini", "Kconfig.cc32xx", "Kconfig.mcux_lpc_rtc", "Kconfig.key", "Kconfig.defconfig.apollo4p_blue", "Kconfig.mcux_pxp", "Kconfig.defconfig.stm32f756xx", "Kconfig.olimex_lora_stm32wl_devkit", "Kconfig.mcux_os", "Kconfig.defconfig.stm32l4s5xx", "Kconfig.seeeduino_xiao", "Kconfig.defconfig.stm32h725xx", "Kconfig.debug", "Kconfig.nxp_s32_netc", "Kconfig.defconfig.nrf52811_QFAA", "Kconfig.aics", "Kconfig.ifx_xmc4", "Kconfig.rzt2m", "Kconfig.tle9104", "Kconfig.uart_sam", "Kconfig.defconfig.em9d", "Kconfig.mimxrt1015_evk", "Kconfig.efr32xg24_dk2601b", "Kconfig.qemu_x86_64", "Kconfig.bluetooth", "Kconfig.andes_atciic100", "Kconfig.samd5x", "Kconfig.bt_hci", "Kconfig.bq25180", "Kconfig.qemu_cortex_m0", "Kconfig.efr32_radio", "Kconfig.sam0", "Kconfig.mg100", "Kconfig.radio", "Kconfig.it8xxx2_evb", "Kconfig.stm32l562e_dk", "Kconfig.da14695_dk_usb", "Kconfig.imx8mp_evk", "Kconfig.hifive_unleashed", "Kconfig.ds2485", "Kconfig.ene", "Kconfig.defconfig.r8a779f0", "Kconfig.ads1x1x", "Kconfig.minsizerel", "Kconfig.defconfig.gd32f470", "Kconfig.bbram_emul", "Kconfig.defconfig.vpx5", "Kconfig.stm32l4r9i_disco", "Kconfig.defconfig.psoc6_01", "Kconfig.template.shell_log_queue_timeout", "Kconfig.micp", "Kconfig.arduino_nicla_sense_me", "Kconfig.intel_socfpga_agilex5_socdk", "Kconfig.fvp_base_revc_2xaemv8a", "Kconfig.native_linux", "Kconfig.miv", "Kconfig.defconfig.stm32f051x8", "Kconfig.defconfig.stm32u5a5xx", "Kconfig.olimex_esp32_evb", "Kconfig.eeprom", "Kconfig.ads114s0x", "Kconfig.npcx_fiu", "Kconfig.stm32f030_demo", "Kconfig.intel_ish_5_6_0", "Kconfig.platform", "Kconfig.cap1203", "Kconfig.rv32m1_lptmr", "Kconfig.xmc47_relax_kit", "Kconfig.defconfig.stm32g070xx", "Kconfig.lpflexcomm", "Kconfig.lp50xx", "Kconfig.faze", "Kconfig.defconfig.mimx8ml8_m7", "Kconfig.disco_l475_iot1", "Kconfig.scobc_module1", "Kconfig.mc146818", "Kconfig.efi_console", "Kconfig.mcux", "Kconfig.wurthelektronik", "Kconfig.mcux_rtc", "Kconfig.stm32h750b_dk", "Kconfig.m5stack_atoms3", "Kconfig.nxp_s32_gmac", "Kconfig.defconfig.stm32g061xx", "Kconfig.sam4e_xpro", "Kconfig.samd2x", "Kconfig.mmc", "Kconfig.mcux_tpm", "Kconfig.stm32l496g_disco", "Kconfig.uc81xx", "Kconfig.cc1352p1_launchxl", "Kconfig.fnmatch", "Kconfig.mcux_ccm", "Kconfig.defconfig.stm32l452xx", "Kconfig.cc3235sf_launchxl", "Kconfig.qomu", "Kconfig.cadence_nand", "Kconfig.vcp", "Kconfig.stm32wb5mmg", "Kconfig.gt911", "Kconfig.vm", "Kconfig.esp32_rtc", "Kconfig.pan1783a_pa_evb", "Kconfig.npcx9m6f_evb", "Kconfig.is31fl3216a", "Kconfig.fs", "Kconfig.axp192", "Kconfig.cc1352r_sensortag", "Kconfig.agilex5", "Kconfig.lpc_iocon", "Kconfig.system", "Kconfig.adafruit_trinket_m0", "Kconfig.sparkfun_pro_micro_rp2040", "Kconfig.mcp23s17", "Kconfig.defconfig.psoc6_m4", "Kconfig.evdev", "Kconfig.bl654_sensor_board", "Kconfig.native_posix", "Kconfig.bl653_dvk", "Kconfig.cmsdk_apb", "Kconfig.dacx0508", "Kconfig.defconfig.nrf52832_CIAA", "Kconfig.smartbond", "Kconfig.cy8c95xx", "Kconfig.nucleo_f767zi", "Kconfig.longan_nano", "Kconfig.defconfig.gd32f405", "Kconfig.defconfig.rp2040", "Kconfig.ds1307", "Kconfig.projbuild", "Kconfig.ubx_bmd380eval", "Kconfig.icmsg", "Kconfig.nrf9151dk", "Kconfig.xmc45_relax_kit", "Kconfig.dw1000", "Kconfig.defconfig.mimx93.a55", "Kconfig.defconfig.stm32wb55xx", "Kconfig.esp32s2_devkitc", "Kconfig.adafruit_feather_m0_basic_proto", "Kconfig.sysconf", "Kconfig.olimex_stm32_p405", "Kconfig.defconfig.gd32f407", "Kconfig.google_dragonclaw", "Kconfig.reel_board", "Kconfig.gd32f450v_start", "Kconfig.ov2640", "Kconfig.timer", "Kconfig.cdns", "Kconfig.sbs_charger", "Kconfig.defconfig.it81302cx", "Kconfig.mpfs_icicle", "Kconfig.mcux_qtmr", "Kconfig.stm32f4_disco", "Kconfig.nucleo_l432kc", "Kconfig.gd32l233r_eval", "Kconfig.qemu_arc", "Kconfig.bass", "Kconfig.nrf_vevif", "Kconfig.defconfig.stm32g474xx", "Kconfig.defconfig.em11d", "Kconfig.defconfig.stm32l051xx", "Kconfig.defconfig.nrf52820_QDAA", "Kconfig.pcf8563", "Kconfig.adafruit_feather_stm32f405", "Kconfig.bas", "Kconfig.uart", "Kconfig.defconfig.stm32u595xx", "Kconfig.defconfig.stm32f405xx", "Kconfig.defconfig.nrf54h20_cpuapp", "Kconfig.b_l4s5i_iot01a", "Kconfig.efm32pg_stk3402a", "Kconfig.oc_simple", "Kconfig.nucleo_l412rb_p", "Kconfig.defconfig.stm32h735xx", "Kconfig.defconfig.stm32f750xx", "Kconfig.pinetime_devkit0", "Kconfig.nrf52833dk", "Kconfig.rv32m1_vega", "Kconfig.qemu_malta", "Kconfig.nrfx_uart_instance", "Kconfig.andes_atcgpio100", "Kconfig.pan1783_evb", "Kconfig.defconfig.stm32f437xx", "Kconfig.atmel", "Kconfig.sam_e70_xplained", "Kconfig.airoc", "Kconfig.bcm2711", "Kconfig.defconfig.sem", "Kconfig.altera_avalon", "Kconfig.has", "Kconfig.visionfive2", "Kconfig.mcux_smartdma", "Kconfig.arduino_nano_33_iot", "Kconfig.cy8ckit_062s4", "Kconfig.input", "Kconfig.qemu_x86", "Kconfig.defconfig.stm32f407xx", "Kconfig.defconfig.stm32g491xx", "Kconfig.libmetal", "Kconfig.defconfig.stm32f030xc", "Kconfig.links", "Kconfig.nrf52832_mdk", "Kconfig.soc_caps.in", "Kconfig.st7735r", "Kconfig.adi_eval_adin1110ebz", "Kconfig.defconfig.nrf52833_QDAA", "Kconfig.adp_xc7k", "Kconfig.cc26x2r1_launchxl", "Kconfig.defconfig.stm32l072xx", "Kconfig.rwlock", "Kconfig.nrf52840_blip", "Kconfig.nrf5340bsim", "Kconfig.gpio_keys", "Kconfig.bitbang", "Kconfig.eswifi", "Kconfig.pwm", "Kconfig.defconfig.stm32f103xx", "Kconfig.tcp", "Kconfig.nrf51dongle", "Kconfig.defconfig.stm32h503xx", "Kconfig.heltec_wireless_stick_lite_v3", "Kconfig.kv260_r5", "Kconfig.stm32f723e_disco", "Kconfig.up_squared_pro_7000", "Kconfig.thread", "Kconfig.lora_e5_dev_board", "Kconfig.filtering", "Kconfig.nxp_s32_emios", "Kconfig.liteeth", "Kconfig.dtm", "Kconfig.qemu_cortex_m3", "Kconfig.defconfig.agilex", "Kconfig.ipso", "Kconfig.stm32g081b_eval", "Kconfig.nxp_imx", "Kconfig.defconfig.stm32l081xx", "Kconfig.mimxrt1060_evk", "Kconfig.b_u585i_iot02a", "Kconfig.mimxrt685_evk", "Kconfig.qemu_cortex_r5", "Kconfig.pcal64xxa", "Kconfig.defconfig.stm32l496xx", "Kconfig.esp32s3_devkitc", "Kconfig.emsdp", "Kconfig.eos_s3", "Kconfig.getopt", "Kconfig.pt6314", "Kconfig.beaglev_fire", "Kconfig.nrf_grtc", "Kconfig.winc1500", "Kconfig.numaker", "Kconfig.defconfig.em4", "Kconfig.usart_sam", "Kconfig.nucleo_l152re", "Kconfig.da1469x", "Kconfig.intel64", "Kconfig.gpio", "Kconfig.hrs", "Kconfig.virtual", "Kconfig.beetle", "Kconfig.ll_sw_split", "Kconfig.cy8cproto_063_ble", "Kconfig.pm", "Kconfig.enc28j60", "Kconfig.nucleo_f042k6", "Kconfig.mqueue", "Kconfig.gd32e507v_start", "Kconfig.sx1509b", "Kconfig.defconfig.nrf52810_QFAA", "Kconfig.mikroe_mini_m4_for_stm32", "Kconfig.pca9420", "Kconfig.ronoth_lodev", "Kconfig.nucleo_l011k4", "Kconfig.stm32l476g_disco", "Kconfig.syst", "Kconfig.xlnx", "Kconfig.mimxrt1024_evk", "Kconfig.dma_pl330", "Kconfig.defconfig.stm32l451xx", "Kconfig.msc", "Kconfig.release", "Kconfig.barrier", "Kconfig.sync_rtc", "Kconfig.gsm_mux", "Kconfig.ubx_bmd360eval", "Kconfig.defconfig.xmc4500", "Kconfig.blackpill_f401ce", "Kconfig.mcux_sdif", "Kconfig.qemu_kvm_arm64", "Kconfig.esp32c3_luatos_core", "Kconfig.df", "Kconfig.defconfig.mec1501hsz", "Kconfig.qemu_cortex_a9", "Kconfig.nrf51_vbluno51", "Kconfig.gd32vf103v_eval", "Kconfig.nrf52_vbluno52", "Kconfig.intel_adsp_gpdma", "Kconfig.sync_rtc_ipm", "Kconfig.rddrone_fmuk66", "Kconfig.ip_k66f", "Kconfig.nucleo_f413zh", "Kconfig.rt1718s", "Kconfig.zynqmp", "Kconfig.nrf52_blenano2", "Kconfig.nsim", "Kconfig.zybo", "Kconfig.fatfs", "Kconfig.stropts", "Kconfig.sched", "Kconfig.defconfig.mimx8ml8_adsp", "Kconfig.stm32_timer", "Kconfig.tbs", "Kconfig.defconfig.stm32h563xx", "Kconfig.defconfig.cyclonev", "Kconfig.particle_argon", "Kconfig.pthread", "Kconfig.fdtable", "Kconfig.icbmsg", "Kconfig.colibri_imx7d", "Kconfig.rm68200", "Kconfig.lp5562", "Kconfig.nucleo_f429zi", "Kconfig.m5stack_core2", "Kconfig.kvaser", "Kconfig.andes_atcpit100", "Kconfig.template.pooled_type", "Kconfig.stm32h747i_disco", "Kconfig.nrf_xrtc", "Kconfig.defconfig.stm32f769xx", "Kconfig.nrf5", "Kconfig.adafruit_grand_central_m4_express", "Kconfig.npm1100", "Kconfig.telink", "Kconfig.test", "Kconfig.sc18im704", "Kconfig.ubx_evkannab1", "Kconfig.esp32", "Kconfig.gd32", "Kconfig.bl", "Kconfig.defconfig.em5d", "Kconfig.defconfig.mec172xnlj", "Kconfig.cavs", "Kconfig.nrf52840dongle", "Kconfig.mcux_dcp", "Kconfig.defconfig.stm32f745xx", "Kconfig.multilevel", "Kconfig.pinnacle_100_dvk", "Kconfig.nucleo_h7a3zi_q", "Kconfig.nucleo_wl55jc", "Kconfig.lan865x", "Kconfig.stm32_qspi", "Kconfig.test_psa_api", "Kconfig.mr_canhubk3", "Kconfig.defconfig.hs5x_smp", "Kconfig.mcux_ctimer", "Kconfig.gd32f450i_eval", "Kconfig.u_blox_m10", "Kconfig.sifive", "Kconfig.rcar", "Kconfig.tca6424a", "Kconfig.defconfig.stm32h7b3xx", "Kconfig.rak4631", "Kconfig.ia32", "Kconfig.stm32mp157c_dk2", "Kconfig.defconfig.stm32g030xx", "Kconfig.defconfig.gd32vf103", "Kconfig.lp3943", "Kconfig.tcpc_stm32", "Kconfig.acn52832", "Kconfig.peripherals", "Kconfig.e1000", "Kconfig.cdc_ecm", "Kconfig.mimxrt1170_evk", "Kconfig.nordic_qspi_nor", "Kconfig.cc13xx_cc26xx", "Kconfig.beagleconnect_freedom", "Kconfig.nucleo_f334r8", "Kconfig.adp5360", "Kconfig.defconfig.psoc6_02", "Kconfig.defconfig.stm32l152xe", "Kconfig.defconfig.nrf52805_CAAA", "Kconfig.defconfig.gd32f450", "Kconfig.isa", "Kconfig.actinius_icarus_bee", "Kconfig.sensortile_box_pro", "Kconfig.defconfig.em7d_v22", "Kconfig.defconfig.stm32f030x6", "Kconfig.swan_r5", "Kconfig.cadence_qspi_nor", "Kconfig.dtmr_cmsdk_apb", "Kconfig.nxp_mrt", "Kconfig.defconfig.psoc6_04", "Kconfig.nucleo_l073rz", "Kconfig.mchp_xec_rtos", "Kconfig.defconfig.stm32l476xx", "Kconfig.defconfig.stm32g483xx", "Kconfig.nucleo_f446ze", "Kconfig.cortex_m_systick", "Kconfig.npm6001", "Kconfig.mcux_ftm", "Kconfig.ubx_evkninab1", "Kconfig.nrf51dk", "Kconfig.nucleo_u575zi_q", "Kconfig.defconfig.stm32f303x(b-c)", "Kconfig.mec1501modular_assy6885", "Kconfig.sam_gmac", "Kconfig.psa_crypto", "Kconfig.black_f407ve", "Kconfig.npcx7m6fb_evb", "Kconfig.nucleo_f303k8", "Kconfig.st7796s", "Kconfig.hl7800", "Kconfig.littlefs", "Kconfig.is31fl3733", "Kconfig.quectel-bg9x", "Kconfig.native_sim", "Kconfig.st7789v", "Kconfig.bcm958401m2", "Kconfig.mcux_lptmr", "Kconfig.template.log_format_config", "Kconfig.mgmt", "Kconfig.waveshare_open103z", "Kconfig.esp32s2_lolin_mini", "Kconfig.serlcd", "Kconfig.cmsdk_ahb", "Kconfig.sam_hsmci", "Kconfig.qemu_nios2", "Kconfig.lpcxpresso54114", "Kconfig.v2m_beetle", "Kconfig.mcux_gpt", "Kconfig.stats", "Kconfig.nxp_enet_qos", "Kconfig.defconfig.stm32h743xx", "Kconfig.frdm_mcxn947", "Kconfig.defconfig.an521", "Kconfig.arcv2", "Kconfig.cyclonev", "Kconfig.cache", "Kconfig.mcux_flexcomm", "Kconfig.stm32f469i_disco", "Kconfig.adi_eval_adin2111ebz", "Kconfig.defconfig.stm32l433xx", "Kconfig.intel", "Kconfig.mcux_csi", "Kconfig.davinci", "Kconfig.nrf52840_papyr", "Kconfig.defconfig.stm32l4q5xx", "Kconfig.mcp2515", "Kconfig.dw_common", "Kconfig.tps", "Kconfig.ram", "Kconfig.phycore_am62x", "Kconfig.adafruit_kb2040", "Kconfig.pan1780_evb", "Kconfig.signal", "Kconfig.mmio32", "Kconfig.adafruit_feather_m0_lora", "Kconfig.bq24190", "Kconfig.mcux_pcc", "Kconfig.dragino_lsn50", "Kconfig.stm32_ospi", "Kconfig.grlib_spimctrl", "Kconfig.cellular", "Kconfig.mcux_lpsci", "Kconfig.zephyr_serial", "Kconfig.sample_usbd", "Kconfig.stm32_hal", "Kconfig.cmos", "Kconfig.odroid_go", "Kconfig.lmp90xxx", "Kconfig.quick_feather", "Kconfig.syslog", "Kconfig.kincony_kc868_a32", "Kconfig.acrn", "Kconfig.up_squared", "Kconfig.defconfig.stm32g071xx", "Kconfig.ias", "Kconfig.tlc59108", "Kconfig.rv32m1", "Kconfig.max20335", "Kconfig.saml21_xpro", "Kconfig.simulator", "Kconfig.stm32f401_mini", "Kconfig.cp9314", "Kconfig.dwmac", "Kconfig.defconfig.stm32f723xx", "Kconfig.adafruit_feather", "Kconfig.olimex_stm32_h405", "Kconfig.spi", "Kconfig.tla2021", "Kconfig.nios2_qspi", "Kconfig.defconfig.stm32f423xx", "Kconfig.v2", "Kconfig.defconfig.stm32f042x6", "Kconfig.raytac_mdbt53v_db_40", "Kconfig.mps3", "Kconfig.defconfig.stm32l031xx", "Kconfig.gd32e103v_eval", "Kconfig.frdm_kw41z", "Kconfig.stm32f3_disco", "Kconfig.sysbuild", "Kconfig.version", "Kconfig.mt9m114", "Kconfig.samc21n_xpro", "Kconfig.max3421e", "Kconfig.ivshmem", "Kconfig.lpc11u6x", "Kconfig.mimxrt1064_evk", "Kconfig.fixed", "Kconfig.apbuart", "Kconfig.udp", "Kconfig.template.instances_count", "Kconfig.emul", "Kconfig.defconfig.stm32g431xx", "Kconfig.sam_ssc", "Kconfig.bl652_dvk", "Kconfig.intel_lpss", "Kconfig.nucleo_l4r5zi", "Kconfig.pl011", "Kconfig.pcf8523", "Kconfig.eeprom_emu", "Kconfig.thingy53", "Kconfig.dmic_pdm_nrfx", "Kconfig.acrn_ehl_crb", "Kconfig.96b_nitrogen", "Kconfig.shared_irq", "Kconfig.gmap", "Kconfig.rv32m1_tpm", "Kconfig.board.v2", "Kconfig.ov7725", "Kconfig.defconfig.it81302bx", "Kconfig.rtt", "Kconfig.defconfig.stm32f410xx", "Kconfig.defconfig.nrf52840_QIAA", "Kconfig.gsm", "Kconfig.defconfig.em7d", "Kconfig.rm1xx_dvk", "Kconfig.defconfig.cavs_v25", "Kconfig.template.log_config.default.net", "Kconfig.esp32s3_luatos_core", "Kconfig.max11102_17", "Kconfig.mchp_mss_qspi", "Kconfig.adafruit_itsybitsy_m4_express", "Kconfig.defconfig.stm32f722xx", "Kconfig.olimex_stm32_h103", "Kconfig.weact_stm32g431_core", "Kconfig.defconfig.stm32f205xx", "Kconfig.efm32gg_stk3701a", "Kconfig.nrf52_adafruit_feather", "Kconfig.sysbuild.modules", "Kconfig.skeleton", "Kconfig.nrf9131ek", "Kconfig.degu_evk", "Kconfig.defconfig.stm32g484xx", "Kconfig.ice40", "Kconfig.hifive_unmatched", "Kconfig.nrf5340_audio_dk", "Kconfig.defconfig.hs6x_smp", "Kconfig.mcux_imx", "Kconfig.defconfig.stm32h573xx", "Kconfig.host_info", "Kconfig.96b_carbon", "Kconfig.microchip", "Kconfig.circuitdojo_feather", "Kconfig.apollo4p_evb", "Kconfig.stm32_min_dev", "Kconfig.alh", "Kconfig.mcp7940n", "Kconfig.pan1783a_evb", "Kconfig.ad56xx", "Kconfig.ft5336", "Kconfig.defconfig.stm32l010x8", "Kconfig.fvp_baser_aemv8r", "Kconfig.esp32c3_devkitm", "Kconfig.v1", "Kconfig.maxim_ds3231", "Kconfig.intel_socfpga", "Kconfig.tja1103", "Kconfig.gecko", "Kconfig.defconfig.hs6x", "Kconfig.defconfig.hs_smp", "Kconfig.defconfig.stm32l151xb", "Kconfig.semaphore", "Kconfig.96b_argonkey", "Kconfig.defconfig.em6", "Kconfig.xpt2046", "Kconfig.comp", "Kconfig.confstr", "Kconfig.i2c_emul", "Kconfig.cc13xx_cc26xx_rtc", "Kconfig.tracemem", "Kconfig.defconfig.stm32l053xx", "Kconfig.rcar_h3ulcb", "Kconfig.rv32m1_lpuart", "Kconfig.nus", "Kconfig.defconfig.gd32f403", "Kconfig.defconfig.stm32l462xx", "Kconfig.mcuboot", "Kconfig.mctl", "Kconfig.xlnx_ps", "Kconfig.nucleo_f410rb", "Kconfig.teensy40", "Kconfig.nrf51_ble400", "Kconfig.w5500_evb_pico", "Kconfig.spiram.common", "Kconfig.ucifi", "Kconfig.sdhc_cdns", "Kconfig.ltc2451", "Kconfig.psoc6", "Kconfig.stellaris", "Kconfig.rv32m1_lpspi", "Kconfig.stm32_ltdc", "Kconfig.defconfig.stm32u599xx", "Kconfig.esp32_devkitc_wroom", "Kconfig.nucleo_g0b1re", "Kconfig.defconfig.nrf54l15_enga_cpuapp", "Kconfig.nrf_mram", "Kconfig.defconfig.stm32f373xc", "Kconfig.hostlink", "Kconfig.template.log_config", "Kconfig.esp32c3", "Kconfig.nxp", "Kconfig.canopen", "Kconfig.neorv32", "Kconfig.mimxrt595_evk", "Kconfig.ad559x", "Kconfig.mcux_lpc", "Kconfig.mcux_ccm_rev2", "Kconfig.blueclover_plt_demo_v2", "Kconfig.ite_it8xxx2", "Kconfig.s32z2xxdc2", "Kconfig.defconfig.nrf52840_QFAA", "Kconfig.serpente", "Kconfig.sw_generator", "Kconfig.mec15xxevb_assy6853", "Kconfig.open-amp", "Kconfig.analog_axis", "Kconfig.template.log_config_bt", "Kconfig.nxp_enet", "Kconfig.intel_blinky", "Kconfig.mcux_dcnano_lcdif", "Kconfig.stm32vl_disco", "Kconfig.qemu_x86_lakemont", "Kconfig.xmc4xxx_ccu4", "Kconfig.xmc4xxx_ccu8", "Kconfig.nrf52dk", "Kconfig.cc3220sf_launchxl", "Kconfig.sam", "Kconfig.cbprintf", "Kconfig.v2m_musca_b1", "Kconfig.mcux_sctimer", "Kconfig.pca95xx", "Kconfig.gd32vf103c_starter", "Kconfig.fpu", "Kconfig.defconfig.stm32f302x8", "Kconfig.ti_k3", "Kconfig.defconfig.mimx8mm6_a53", "Kconfig.defconfig.stm32u575xx", "Kconfig.96b_aerocore2", "Kconfig.ast1030_evb", "Kconfig.lpcxpresso55s06", "Kconfig.defconfig.stm32f446xx", "Kconfig.mchp_mss", "Kconfig.cyclonev_socdk", "Kconfig.numaker_pfm_m487", "Kconfig.mps2", "Kconfig.spinel", "Kconfig.mimx8mp_phyboard_pollux", "Kconfig.ublox-sara-r4", "Kconfig.led_strip_matrix", "Kconfig.tls-generic", "Kconfig.altera", "Kconfig.nucleo_f070rb", "Kconfig.nucleo_f401re", "Kconfig.defconfig.stm32l4r9xx", "Kconfig.npcx", "Kconfig.efr32bg27_brd2602a", "Kconfig.hash_map", "Kconfig.defconfig.stm32h730xx", "Kconfig.intel_ish_5_8_0", "Kconfig.intel_rpl_s_crb", "Kconfig.nt35510", "Kconfig.spi_emul", "Kconfig.defconfig.nrf54h20_cpurad", "Kconfig.verdin_imx8mp", "Kconfig.nxp_lcdic", "Kconfig.clic", "Kconfig.defconfig.an385", "Kconfig.defconfig.it81202bx", "Kconfig.bt", "Kconfig.mips_cp0", "Kconfig.deprecated", "Kconfig.twr_kv58f220m", "Kconfig.defconfig.stm32f100xx", "Kconfig.simplelink", "Kconfig.defconfig.stm32l422xx", "Kconfig.stm32g071b_disco", "Kconfig.frdm_k64f", "Kconfig.imx8ulp_evk", "Kconfig.steval_fcu001v1", "Kconfig.v1.choice", "Kconfig.stm32f0_disco", "Kconfig.cy8ckit_062_wifi_bt", "Kconfig.mcux_lpspi", "Kconfig.mcux_elcdif", "Kconfig.96b_wistrio", "Kconfig.sam4s_xplained", "Kconfig.backends", "Kconfig.nucleo_l433rc_p", "Kconfig.espi_emul", "Kconfig.esp32_devkitc_wrover", "Kconfig.segger_trb_stm32f407", "Kconfig.ubx_bmd345eval", "Kconfig.xiao_esp32c3", "Kconfig.olimex_stm32_e407", "Kconfig.mcp23xxx", "Kconfig.mcux_dspi", "Kconfig.defconfig.stm32f413xx", "Kconfig.cy8ckit_062_ble", "Kconfig.mcux_mcg", "Kconfig.ql_usbserialport_s3b", "Kconfig.gd32_exti", "Kconfig.esp32s2_saola", "Kconfig.defconfig.ast1030", "Kconfig.efr32bg22_brd4184b", "Kconfig.defconfig.stm32h562xx", "Kconfig.tlv320dac", "Kconfig.fake", "Kconfig.cc2520", "Kconfig.misc", "Kconfig.ataes132a", "Kconfig.nucleo_wba52cg", "Kconfig.vim", "Kconfig.opentitan", "Kconfig.kbd_matrix", "Kconfig.csip", "Kconfig.defconfig.nrf52832_QFAB", "Kconfig.defconfig.mimx8ml8_a53", "Kconfig.gd32f450z_eval", "Kconfig.unit_testing", "Kconfig.sn74hc595", "Kconfig.build_time", "Kconfig.defconfig.it82302ax", "Kconfig.ads1119", "Kconfig.intel_ehl_crb", "Kconfig.esptool", "Kconfig.defconfig.stm32l151xc", "Kconfig.ev11l78a", "Kconfig.smartbond_timer", "Kconfig.template.pooled_ipc_type", "Kconfig.96b_stm32_sensor_mez", "Kconfig.mini_stm32h743", "Kconfig.efr32mg_sltb004a", "Kconfig.defconfig.stm32wl54xx", "Kconfig.nucleo_f446re", "Kconfig.sam4l_ek", "Kconfig.trigger_template", "Kconfig.qemu_riscv64", "Kconfig.defconfig.stm32wba55xx", "Kconfig.nxp_vref", "Kconfig.rpi_4b", "Kconfig.qemu_riscv32e", "Kconfig.nucleo_f303re", "Kconfig.env", "Kconfig.defconfig.series", "Kconfig.simcom-sim7080", "Kconfig.actinius_icarus_som", "Kconfig.sam_twihs", "Kconfig.titanium_ti60_f225", "Kconfig.bd8lb600fs", "Kconfig.mcux_igpio", "Kconfig.we_ophelia1ev", "Kconfig.defconfig.nrf9160_SICA", "Kconfig.xiao_esp32s3", "Kconfig.rtc", "Kconfig.intel_rpl_p_crb", "Kconfig.defconfig.stm32l471xx", "Kconfig.stm32wb5mm_dk", "Kconfig.defconfig.it82002aw", "Kconfig.mcux_snvs", "Kconfig.numaker_rmc", "Kconfig.tco", "Kconfig.samd21_xpro", "Kconfig.defconfig.it81202cx", "Kconfig.stm32f769i_disco", "Kconfig.defconfig.nrf9161_LACA", "Kconfig.nrf52_bsim", "Kconfig.ebyte_e73_tbb", "Kconfig.96b_avenger96", "Kconfig.qemu_cortex_a53", "Kconfig.cpu", "Kconfig.mcux_lpuart", "Kconfig.xec_qmspi", "Kconfig.niosv_g", "Kconfig.esp32s2_fran<PERSON>inho", "Kconfig.intel_adl_crb", "Kconfig.wncm14a2a", "Kconfig.teensy41", "Kconfig.defconfig.stm32h745xx", "Kconfig.defconfig.nrf52833_QIAA", "Kconfig.cf1133", "Kconfig.kinetis", "Kconfig.sip_smc_agilex", "Kconfig.mec172xevb_assy6906", "Kconfig.defconfig.msp432p401r", "Kconfig.hostname", "Kconfig.nucleo_u5a5zj_q", "Kconfig.uac2", "Kconfig.defconfig.stm32h7a3xx", "Kconfig.rm67162", "Kconfig.tlsr9518adk80d", "Kconfig.infineon", "Kconfig.nucleo_f411re", "Kconfig.picolibc", "Kconfig.arty_a7", "Kconfig.imx93_evk", "Kconfig.defconfig.ace20_lnl", "Kconfig.defconfig.stm32mp15_m4", "Kconfig.efr32bg22_brd4184a", "Kconfig.rcar_salvator_x", "Kconfig.nucleo_h743zi", "Kconfig.xen", "Kconfig.ipv4", "Kconfig.tlc5971", "Kconfig.stm32f3_seco_d23", "Kconfig.nucleo_g431rb", "Kconfig.defconfig.stm32f401xc", "Kconfig.defconfig.gd32a503", "Kconfig.defconfig.mcimx7d_m4", "Kconfig.clock", "Kconfig.defconfig.m2l31xxx", "Kconfig.ti_cc32xx", "Kconfig.mcux_syscon", "Kconfig.nucleo_f412zg", "Kconfig.it8xxx2", "Kconfig.fk7b0m1_vbt6", "Kconfig.m5stickc_plus", "Kconfig.nrf54h20dk", "Kconfig.cy8cproto_062_4343w", "Kconfig.adc_keys", "Kconfig.gd32f403z_eval", "Kconfig.defconfig.viper_bcm58402_m7", "Kconfig.rcar_cmt", "Kconfig.ili9xxx", "Kconfig.stm3210c_eval", "Kconfig.frdm_k22f", "Kconfig.nucleo_f091rc", "Kconfig.max1125x", "Kconfig.bbc_microbit_v2", "Kconfig.tmap", "Kconfig.same54_xpro", "Kconfig.nucleo_l031k6", "Kconfig.esp32_ethernet_kit", "Kconfig.nrf_rtc", "Kconfig.frdm_kl25z", "Kconfig.hd44780", "Kconfig.template.with_url", "Kconfig.logging", "Kconfig.defconfig.stm32h747xx", "Kconfig.cst816s", "Kconfig.roc_rk3568_pc", "Kconfig.serial_recovery", "Kconfig.intel_lw", "Kconfig.nrf52840_mdk", "Kconfig.defconfig.m467", "Kconfig.defconfig.stm32f765xx", "Kconfig.hexiwear", "Kconfig.mm_feather", "Kconfig.sdl", "Kconfig.timeaware_gpio_intel", "Kconfig.mode", "Kconfig.esai", "Kconfig.pan1782_evb", "Kconfig.sk_am62", "Kconfig.tca954x", "Kconfig.defconfig.stm32l010x6", "Kconfig.rf2xx", "Kconfig.arduino_uno_r4_minima", "Kconfig.defconfig.stm32l071xx", "Kconfig.nrf9160dk", "Kconfig.lpcxpresso55s16", "Kconfig.dw", "Kconfig.mcp4725", "Kconfig.xilinx_axi", "Kconfig.ds2484", "Kconfig.dis", "Kconfig.template.composite_device_number", "Kconfig.defconfig.ae350", "Kconfig.pca9633", "Kconfig.gc9x01x", "Kconfig.gpio_kbd_matrix", "Kconfig.defconfig.stm32g050xx", "Kconfig.rak5010", "Kconfig.nrf_regtool", "Kconfig.defconfig.em7d_esp", "Kconfig.nucleo_h745zi_q", "Kconfig.ads1112", "Kconfig.mimxrt1160_evk", "Kconfig.defconfig.mimx8mq6_m4", "Kconfig.defconfig.stm32g031xx", "Kconfig.efinix_sapphire", "Kconfig.m5stack_atom_lite", "Kconfig.iproc_pax", "Kconfig.defconfig.stm32l432xx", "Kconfig.in", "Kconfig.stm32f429i_disc1", "Kconfig.silabs", "Kconfig.pat912x", "Kconfig.particle_xenon", "Kconfig.nucleo_f746zg", "Kconfig.defconfig.cc3235sf", "Kconfig.defconfig.stm32f207xx", "Kconfig.holyiot_yj16019", "Kconfig.frdm_k82f", "Kconfig.mm_swiftio", "Kconfig.defconfig.stm32f767xx", "Kconfig.icmsg_me", "Kconfig.particle_boron", "Kconfig.npm1300", "Kconfig.nrfx_ipc_channel", "Kconfig.defconfig.nrf54h20_cpuppr", "Kconfig.rpi_pico", "Kconfig.raytac_mdbt50q_db_40", "Kconfig.defconfig.stm32g081xx", "Kconfig.stack", "Kconfig.pmw3610", "Kconfig.defconfig.stm32f070xb", "Kconfig.defconfig.apollo4p", "Kconfig.sof", "Kconfig.numicro", "Kconfig.nrf_ecb", "Kconfig.defconfig.ace15_mtpm", "Kconfig.defconfig.mcimx6x_m4", "Kconfig.nucleo_wb55rg", "Kconfig.qemu_riscv32_xip", "Kconfig.nor", "Kconfig.defconfig.stm32f072xx", "Kconfig.intel_adsp_hda", "Kconfig.apa102", "Kconfig.xmc4xxx", "Kconfig.nrf51_blenano", "Kconfig.ifx_cat1", "Kconfig.ws2812", "Kconfig.mcux_iuart", "Kconfig.max7219", "Kconfig.mpl", "Kconfig.defconfig.stm32f427xx", "Kconfig.vega", "Kconfig.ubx_evkninab4", "Kconfig.loapic", "Kconfig.adc_emul", "Kconfig.template.log_config_inherit", "Kconfig.itron", "Kconfig.xlnx_psttc", "Kconfig.defconfig.nrf52832_QFAA", "Kconfig.jhd1313", "Kconfig.sam_afec", "Kconfig.defconfig.stm32c031xx", "Kconfig.nucleo_h753zi", "Kconfig.defconfig.stm32l412xx", "Kconfig.renesas_lcdc", "Kconfig.vocs", "Kconfig.stmpe1600", "Kconfig.arduino_giga_r1", "Kconfig.andes_atcspi200", "Kconfig.gatt", "Kconfig.bap", "Kconfig.we_proteus3ev", "Kconfig.rcar_spider_s4", "Kconfig.ads7052", "Kconfig.robokit1", "Kconfig.em_starterkit", "Kconfig.tas6422dac", "Kconfig.memory", "Kconfig.defconfig.stm32h750xx", "Kconfig.defconfig.m487", "Kconfig.defconfig.stm32f105xx", "Kconfig.pan1770_evb", "Kconfig.nucleo_f302r8", "Kconfig.thingy52", "Kconfig.samr21_xpro", "Kconfig.mac", "Kconfig.nucleo_h723zg", "Kconfig.nucleo_wba55cg", "Kconfig.mec172xmodular_assy6930", "Kconfig.we_proteus2ev", "Kconfig.sdmmc", "Kconfig.native_tty", "Kconfig.ns16550", "Kconfig.stm32f411e_disco", "Kconfig.defconfig.nrf9131_LACA", "Kconfig.defconfig.psoc6_m0", "Kconfig.nxp_mailbox", "Kconfig.mpfs", "Kconfig.emul_sdl", "Kconfig.l2cap", "Kconfig.riscv32_virtual", "Kconfig.usb_kw24d512", "Kconfig.defconfig.stm32g4a1xx", "Kconfig.sam_xdmac", "Kconfig.blackpill_f401cc"], "configuration": "./language-configuration.json"}], "grammars": [{"language": "kconfig", "scopeName": "source.kconfig", "path": "./syntaxes/kconfig.tmGrammar.json"}]}}