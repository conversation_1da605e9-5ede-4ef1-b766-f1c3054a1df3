<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>comment</key>
	<string>The recognition of function definitions and compiler directives (such as module, record and macro definitions) requires that each of the aforementioned constructs must be the first string inside a line (except for whitespace).  Also, the function/module/record/macro names must be given unquoted.  -- desp</string>
	<key>fileTypes</key>
	<array>
		<string>erl</string>
		<string>escript</string>
		<string>hrl</string>
		<string>xrl</string>
		<string>yrl</string>
	</array>
	<key>keyEquivalent</key>
	<string>^~E</string>
	<key>name</key>
	<string>Erlang</string>
	<key>patterns</key>
	<array>
		<dict>
			<key>include</key>
			<string>#module-directive</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#import-export-directive</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#behaviour-directive</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#record-directive</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#define-directive</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#macro-directive</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#directive</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#function</string>
		</dict>
		<dict>
			<key>include</key>
			<string>#everything-else</string>
		</dict>
	</array>
	<key>repository</key>
	<dict>
		<key>atom</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(')</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.symbol.begin.erlang</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(')</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.symbol.end.erlang</string>
						</dict>
					</dict>
					<key>name</key>
					<string>constant.other.symbol.quoted.single.erlang</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>captures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>punctuation.definition.escape.erlang</string>
								</dict>
								<key>3</key>
								<dict>
									<key>name</key>
									<string>punctuation.definition.escape.erlang</string>
								</dict>
							</dict>
							<key>match</key>
							<string>(\\)([bdefnrstv\\'"]|(\^)[@-_a-z]|[0-7]{1,3}|x[\da-fA-F]{2})</string>
							<key>name</key>
							<string>constant.other.symbol.escape.erlang</string>
						</dict>
						<dict>
							<key>match</key>
							<string>\\\^?.?</string>
							<key>name</key>
							<string>invalid.illegal.atom.erlang</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>match</key>
					<string>[a-z][a-zA-Z\d@_]*+</string>
					<key>name</key>
					<string>constant.other.symbol.unquoted.erlang</string>
				</dict>
			</array>
		</dict>
		<key>behaviour-directive</key>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.directive.begin.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>keyword.control.directive.behaviour.erlang</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.parameters.begin.erlang</string>
				</dict>
				<key>4</key>
				<dict>
					<key>name</key>
					<string>entity.name.type.class.behaviour.definition.erlang</string>
				</dict>
				<key>5</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.parameters.end.erlang</string>
				</dict>
				<key>6</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.directive.end.erlang</string>
				</dict>
			</dict>
			<key>match</key>
			<string>^\s*+(-)\s*+(behaviour)\s*+(\()\s*+([a-z][a-zA-Z\d@_]*+)\s*+(\))\s*+(\.)</string>
			<key>name</key>
			<string>meta.directive.behaviour.erlang</string>
		</dict>
		<key>binary</key>
		<dict>
			<key>begin</key>
			<string>(&lt;&lt;)</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.binary.begin.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(&gt;&gt;)</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.binary.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>meta.structure.binary.erlang</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.binary.erlang</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.value-size.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(,)|(:)</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#internal-type-specifiers</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#everything-else</string>
				</dict>
			</array>
		</dict>
		<key>character</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.character.erlang</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>constant.character.escape.erlang</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.escape.erlang</string>
						</dict>
						<key>5</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.escape.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(\$)((\\)([bdefnrstv\\'"]|(\^)[@-_a-z]|[0-7]{1,3}|x[\da-fA-F]{2}))</string>
					<key>name</key>
					<string>constant.character.erlang</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\$\\\^?.?</string>
					<key>name</key>
					<string>invalid.illegal.character.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.character.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(\$)[ \S]</string>
					<key>name</key>
					<string>constant.character.erlang</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\$.?</string>
					<key>name</key>
					<string>invalid.illegal.character.erlang</string>
				</dict>
			</array>
		</dict>
		<key>comment</key>
		<dict>
			<key>begin</key>
			<string>(^[ \t]+)?(?=%)</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.whitespace.comment.leading.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(?!\G)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>%</string>
					<key>beginCaptures</key>
					<dict>
						<key>0</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.comment.erlang</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\n</string>
					<key>name</key>
					<string>comment.line.percentage.erlang</string>
				</dict>
			</array>
		</dict>
		<key>define-directive</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>^\s*+(-)\s*+(define)\s*+(\()\s*+([a-zA-Z\d@_]++)\s*+</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.directive.begin.erlang</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>keyword.control.directive.define.erlang</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.parameters.begin.erlang</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.macro.definition.erlang</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(\))\s*+(\.)</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.parameters.end.erlang</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.directive.end.erlang</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.directive.define.erlang</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#everything-else</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>(?=^\s*+-\s*+define\s*+\(\s*+[a-zA-Z\d@_]++\s*+\()</string>
					<key>end</key>
					<string>(\))\s*+(\.)</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.parameters.end.erlang</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.directive.end.erlang</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.directive.define.erlang</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>^\s*+(-)\s*+(define)\s*+(\()\s*+([a-zA-Z\d@_]++)\s*+(\()</string>
							<key>beginCaptures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>punctuation.section.directive.begin.erlang</string>
								</dict>
								<key>2</key>
								<dict>
									<key>name</key>
									<string>keyword.control.directive.define.erlang</string>
								</dict>
								<key>3</key>
								<dict>
									<key>name</key>
									<string>punctuation.definition.parameters.begin.erlang</string>
								</dict>
								<key>4</key>
								<dict>
									<key>name</key>
									<string>entity.name.function.macro.definition.erlang</string>
								</dict>
								<key>5</key>
								<dict>
									<key>name</key>
									<string>punctuation.definition.parameters.begin.erlang</string>
								</dict>
							</dict>
							<key>end</key>
							<string>(\))\s*(,)</string>
							<key>endCaptures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>punctuation.definition.parameters.end.erlang</string>
								</dict>
								<key>2</key>
								<dict>
									<key>name</key>
									<string>punctuation.separator.parameters.erlang</string>
								</dict>
							</dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>match</key>
									<string>,</string>
									<key>name</key>
									<string>punctuation.separator.parameters.erlang</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#everything-else</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>match</key>
							<string>\|\||\||:|;|,|\.|-&gt;</string>
							<key>name</key>
							<string>punctuation.separator.define.erlang</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#everything-else</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>directive</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>^\s*+(-)\s*+([a-z][a-zA-Z\d@_]*+)\s*+(\(?)</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.directive.begin.erlang</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>keyword.control.directive.erlang</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.parameters.begin.erlang</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(\)?)\s*+(\.)</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.parameters.end.erlang</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.directive.end.erlang</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.directive.erlang</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#everything-else</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.directive.begin.erlang</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>keyword.control.directive.erlang</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.directive.end.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>^\s*+(-)\s*+([a-z][a-zA-Z\d@_]*+)\s*+(\.)</string>
					<key>name</key>
					<string>meta.directive.erlang</string>
				</dict>
			</array>
		</dict>
		<key>docstring</key>
		<dict>
			<key>begin</key>
			<string>(?&lt;!")((["]{3,})\s*)(\S.*)?$</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>meta.string.quoted.triple.begin.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.erlang</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>invalid.illegal.string.erlang</string>
				</dict>
			</dict>
			<key>comment</key>
			<string>Only whitespace characters are allowed after the beggining and before the closing sequences and those cannot be in the same line</string>
			<key>end</key>
			<string>^(\s*(\2))(?!")</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>meta.string.quoted.triple.end.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.triple.erlang</string>
		</dict>
		<key>everything-else</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#comment</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#record-usage</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#macro-usage</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#expression</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#keyword</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#textual-operator</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#language-constant</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#function-call</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#tuple</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#list</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#binary</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#parenthesized-expression</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#character</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#number</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#atom</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#sigil-docstring</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#sigil-string</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#docstring</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#string</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#symbolic-operator</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#variable</string>
				</dict>
			</array>
		</dict>
		<key>expression</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>\b(if)\b</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.control.if.erlang</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\b(end)\b</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.control.end.erlang</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.expression.if.erlang</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#internal-expression-punctuation</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#everything-else</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\b(case)\b</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.control.case.erlang</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\b(end)\b</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.control.end.erlang</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.expression.case.erlang</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#internal-expression-punctuation</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#everything-else</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\b(receive)\b</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.control.receive.erlang</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\b(end)\b</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.control.end.erlang</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.expression.receive.erlang</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#internal-expression-punctuation</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#everything-else</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.control.fun.erlang</string>
						</dict>
						<key>10</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.function-arity.erlang</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>entity.name.type.class.module.erlang</string>
						</dict>
						<key>5</key>
						<dict>
							<key>name</key>
							<string>variable.other.erlang</string>
						</dict>
						<key>6</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.module-function.erlang</string>
						</dict>
						<key>8</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.erlang</string>
						</dict>
						<key>9</key>
						<dict>
							<key>name</key>
							<string>variable.other.erlang</string>
						</dict>
					</dict>
					<key>comment</key>
					<string>Implicit function expression with optional module qualifier when both module and function can be atom or variable</string>
					<key>match</key>
					<string>\b(fun)\s+((([a-z][a-zA-Z\d@_]*+)|(_[a-zA-Z\d@_]++|[A-Z][a-zA-Z\d@_]*+))\s*+(:)\s*+)?(([a-z][a-zA-Z\d@_]*+|'[^']*+')|(_[a-zA-Z\d@_]++|[A-Z][a-zA-Z\d@_]*+))\s*(/)</string>
					<key>name</key>
					<string>meta.expression.fun.implicit.erlang</string>
				</dict>
				<dict>
					<key>begin</key>
					<string>\b(fun)\s+(([a-z][a-zA-Z\d@_]*+)|(_[a-zA-Z\d@_]++|[A-Z][a-zA-Z\d@_]*+))\s*+(:)</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.control.fun.erlang</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>entity.name.type.class.module.erlang</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>variable.other.erlang</string>
						</dict>
						<key>5</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.module-function.erlang</string>
						</dict>
					</dict>
					<key>comment</key>
					<string>Implicit function expression with module qualifier when module can be atom or variable and function can by anything</string>
					<key>end</key>
					<string>(/)</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.function-arity.erlang</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.expression.fun.implicit.erlang</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#everything-else</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\b(fun)\s+(?!\()</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.control.fun.erlang</string>
						</dict>
					</dict>
					<key>comment</key>
					<string>Implicit function expression when both module and function can by anything</string>
					<key>end</key>
					<string>(/)</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.function-arity.erlang</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.expression.fun.implicit.erlang</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#everything-else</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\b(fun)\s*+(\()(?=(\s*+\())</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.erlang</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.parameters.begin.erlang</string>
						</dict>
					</dict>
					<key>comment</key>
					<string>Function type in type specification</string>
					<key>end</key>
					<string>(\))</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.parameters.end.erlang</string>
						</dict>
					</dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#everything-else</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\b(fun)\b</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.control.fun.erlang</string>
						</dict>
					</dict>
					<key>comment</key>
					<string>Explicit function expression</string>
					<key>end</key>
					<string>\b(end)\b</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.control.end.erlang</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.expression.fun.erlang</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>(?=\()</string>
							<key>end</key>
							<string>(;)|(?=\bend\b)</string>
							<key>endCaptures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>punctuation.separator.clauses.erlang</string>
								</dict>
							</dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>include</key>
									<string>#internal-function-parts</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>include</key>
							<string>#everything-else</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\b(try)\b</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.control.try.erlang</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\b(end)\b</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.control.end.erlang</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.expression.try.erlang</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#internal-expression-punctuation</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#everything-else</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\b(begin)\b</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.control.begin.erlang</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\b(end)\b</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.control.end.erlang</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.expression.begin.erlang</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#internal-expression-punctuation</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#everything-else</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>\b(maybe)\b</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.control.maybe.erlang</string>
						</dict>
					</dict>
					<key>end</key>
					<string>\b(end)\b</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.control.end.erlang</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.expression.maybe.erlang</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#internal-expression-punctuation</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#everything-else</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>function</key>
		<dict>
			<key>begin</key>
			<string>^\s*+([a-z][a-zA-Z\d@_]*+|'[^']*+')\s*+(?=\()</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>entity.name.function.definition.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(\.)</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.terminator.function.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>meta.function.erlang</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>^\s*+([a-z][a-zA-Z\d@_]*+|'[^']*+')\s*+(?=\()</string>
				</dict>
				<dict>
					<key>begin</key>
					<string>(?=\()</string>
					<key>end</key>
					<string>(;)|(?=\.)</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.clauses.erlang</string>
						</dict>
					</dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#parenthesized-expression</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#internal-function-parts</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>include</key>
					<string>#everything-else</string>
				</dict>
			</array>
		</dict>
		<key>function-call</key>
		<dict>
			<key>begin</key>
			<string>(?=([a-z][a-zA-Z\d@_]*+|'[^']*+'|_[a-zA-Z\d@_]++|[A-Z][a-zA-Z\d@_]*+)\s*+(\(|:\s*+([a-z][a-zA-Z\d@_]*+|'[^']*+'|_[a-zA-Z\d@_]++|[A-Z][a-zA-Z\d@_]*+)\s*+\())</string>
			<key>end</key>
			<string>(\))</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.parameters.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>meta.function-call.erlang</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>((erlang)\s*+(:)\s*+)?(is_atom|is_binary|is_constant|is_float|is_function|is_integer|is_list|is_number|is_pid|is_port|is_reference|is_tuple|is_record|abs|element|hd|length|node|round|self|size|tl|trunc)\s*+(\()</string>
					<key>beginCaptures</key>
					<dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>entity.name.type.class.module.erlang</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.module-function.erlang</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.guard.erlang</string>
						</dict>
						<key>5</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.parameters.begin.erlang</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?=\))</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>match</key>
							<string>,</string>
							<key>name</key>
							<string>punctuation.separator.parameters.erlang</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#everything-else</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>((([a-z][a-zA-Z\d@_]*+|'[^']*+')|(_[a-zA-Z\d@_]++|[A-Z][a-zA-Z\d@_]*+))\s*+(:)\s*+)?(([a-z][a-zA-Z\d@_]*+|'[^']*+')|(_[a-zA-Z\d@_]++|[A-Z][a-zA-Z\d@_]*+))\s*+(\()</string>
					<key>beginCaptures</key>
					<dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>entity.name.type.class.module.erlang</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>variable.other.erlang</string>
						</dict>
						<key>5</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.module-function.erlang</string>
						</dict>
						<key>7</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.erlang</string>
						</dict>
						<key>8</key>
						<dict>
							<key>name</key>
							<string>variable.other.erlang</string>
						</dict>
						<key>9</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.parameters.begin.erlang</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?=\))</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>match</key>
							<string>,</string>
							<key>name</key>
							<string>punctuation.separator.parameters.erlang</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#everything-else</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>import-export-directive</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>^\s*+(-)\s*+(import)\s*+(\()\s*+([a-z][a-zA-Z\d@_]*+|'[^']*+')\s*+(,)</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.directive.begin.erlang</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>keyword.control.directive.import.erlang</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.parameters.begin.erlang</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>entity.name.type.class.module.erlang</string>
						</dict>
						<key>5</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.parameters.erlang</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(\))\s*+(\.)</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.parameters.end.erlang</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.directive.end.erlang</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.directive.import.erlang</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#internal-function-list</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>begin</key>
					<string>^\s*+(-)\s*+(export)\s*+(\()</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.directive.begin.erlang</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>keyword.control.directive.export.erlang</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.parameters.begin.erlang</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(\))\s*+(\.)</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.parameters.end.erlang</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.directive.end.erlang</string>
						</dict>
					</dict>
					<key>name</key>
					<string>meta.directive.export.erlang</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#internal-function-list</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>internal-expression-punctuation</key>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.separator.clause-head-body.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.separator.clauses.erlang</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.separator.expressions.erlang</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(-&gt;)|(;)|(,)</string>
		</dict>
		<key>internal-function-list</key>
		<dict>
			<key>begin</key>
			<string>(\[)</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.list.begin.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(\])</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.list.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>meta.structure.list.function.erlang</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>([a-z][a-zA-Z\d@_]*+|'[^']*+')\s*+(/)</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.erlang</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.function-arity.erlang</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(,)|(?=\])</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.list.erlang</string>
						</dict>
					</dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#everything-else</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>include</key>
					<string>#everything-else</string>
				</dict>
			</array>
		</dict>
		<key>internal-function-parts</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(?=\()</string>
					<key>end</key>
					<string>(-&gt;)</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.clause-head-body.erlang</string>
						</dict>
					</dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>begin</key>
							<string>(\()</string>
							<key>beginCaptures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>punctuation.definition.parameters.begin.erlang</string>
								</dict>
							</dict>
							<key>end</key>
							<string>(\))</string>
							<key>endCaptures</key>
							<dict>
								<key>1</key>
								<dict>
									<key>name</key>
									<string>punctuation.definition.parameters.end.erlang</string>
								</dict>
							</dict>
							<key>patterns</key>
							<array>
								<dict>
									<key>match</key>
									<string>,</string>
									<key>name</key>
									<string>punctuation.separator.parameters.erlang</string>
								</dict>
								<dict>
									<key>include</key>
									<string>#everything-else</string>
								</dict>
							</array>
						</dict>
						<dict>
							<key>match</key>
							<string>,|;</string>
							<key>name</key>
							<string>punctuation.separator.guards.erlang</string>
						</dict>
						<dict>
							<key>include</key>
							<string>#everything-else</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>match</key>
					<string>,</string>
					<key>name</key>
					<string>punctuation.separator.expressions.erlang</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#everything-else</string>
				</dict>
			</array>
		</dict>
		<key>internal-record-body</key>
		<dict>
			<key>begin</key>
			<string>(\{)</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.class.record.begin.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(\})</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.class.record.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>meta.structure.record.erlang</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>begin</key>
					<string>(([a-z][a-zA-Z\d@_]*+|'[^']*+')|(_))</string>
					<key>beginCaptures</key>
					<dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>variable.other.field.erlang</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>variable.language.omitted.field.erlang</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(,)|(?=\})</string>
					<key>endCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.class.record.erlang</string>
						</dict>
					</dict>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#everything-else</string>
						</dict>
					</array>
				</dict>
				<dict>
					<key>include</key>
					<string>#everything-else</string>
				</dict>
			</array>
		</dict>
		<key>internal-string-body</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.escape.erlang</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.escape.erlang</string>
						</dict>
					</dict>
					<key>comment</key>
					<string>escape sequence</string>
					<key>match</key>
					<string>(\\)([bdefnrstv\\'"]|(\^)[@-_a-z]|[0-7]{1,3}|x[\da-fA-F]{2})</string>
					<key>name</key>
					<string>constant.character.escape.erlang</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\\\^?.?</string>
					<key>name</key>
					<string>invalid.illegal.string.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.placeholder.erlang</string>
						</dict>
						<key>10</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.placeholder-parts.erlang</string>
						</dict>
						<key>6</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.placeholder-parts.erlang</string>
						</dict>
					</dict>
					<key>comment</key>
					<string>io:fwrite format control sequence</string>
					<key>match</key>
					<string>(~)((\-)?\d++|(\*))?((\.)(\d++|(\*))?((\.)((\*)|.))?)?[tlkK]*[~cfegswpWPBX#bx\+ni]</string>
					<key>name</key>
					<string>constant.character.format.placeholder.other.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.placeholder.erlang</string>
						</dict>
					</dict>
					<key>comment</key>
					<string>io:fread format control sequence</string>
					<key>match</key>
					<string>(~)(\*)?(\d++)?(t)?[~du\-#fsacl]</string>
					<key>name</key>
					<string>constant.character.format.placeholder.other.erlang</string>
				</dict>
				<dict>
					<key>match</key>
					<string>~[^"]?</string>
					<key>name</key>
					<string>invalid.illegal.string.erlang</string>
				</dict>
			</array>
		</dict>
		<key>internal-type-specifiers</key>
		<dict>
			<key>begin</key>
			<string>(/)</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.separator.value-type.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(?=,|:|&gt;&gt;)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>storage.type.erlang</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>storage.modifier.signedness.erlang</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>storage.modifier.endianness.erlang</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>storage.modifier.unit.erlang</string>
						</dict>
						<key>5</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.unit-specifiers.erlang</string>
						</dict>
						<key>6</key>
						<dict>
							<key>name</key>
							<string>constant.numeric.integer.decimal.erlang</string>
						</dict>
						<key>7</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.type-specifiers.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(integer|float|binary|bytes|bitstring|bits|utf8|utf16|utf32)|(signed|unsigned)|(big|little|native)|(unit)(:)(\d++)|(-)</string>
				</dict>
			</array>
		</dict>
		<key>keyword</key>
		<dict>
			<key>match</key>
			<string>\b(after|begin|case|catch|cond|end|fun|if|let|of|try|receive|when|maybe|else)\b</string>
			<key>name</key>
			<string>keyword.control.erlang</string>
		</dict>
		<key>language-constant</key>
		<dict>
			<key>match</key>
			<string>\b(false|true|undefined)\b</string>
			<key>name</key>
			<string>constant.language</string>
		</dict>
		<key>list</key>
		<dict>
			<key>begin</key>
			<string>(\[)</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.list.begin.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(\])</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.list.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>meta.structure.list.erlang</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\||\|\||,</string>
					<key>name</key>
					<string>punctuation.separator.list.erlang</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#everything-else</string>
				</dict>
			</array>
		</dict>
		<key>macro-directive</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.directive.begin.erlang</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>keyword.control.directive.ifdef.erlang</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.parameters.begin.erlang</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.macro.erlang</string>
						</dict>
						<key>5</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.parameters.end.erlang</string>
						</dict>
						<key>6</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.directive.end.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>^\s*+(-)\s*+(ifdef)\s*+(\()\s*+([a-zA-z\d@_]++)\s*+(\))\s*+(\.)</string>
					<key>name</key>
					<string>meta.directive.ifdef.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.directive.begin.erlang</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>keyword.control.directive.ifndef.erlang</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.parameters.begin.erlang</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.macro.erlang</string>
						</dict>
						<key>5</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.parameters.end.erlang</string>
						</dict>
						<key>6</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.directive.end.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>^\s*+(-)\s*+(ifndef)\s*+(\()\s*+([a-zA-z\d@_]++)\s*+(\))\s*+(\.)</string>
					<key>name</key>
					<string>meta.directive.ifndef.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.directive.begin.erlang</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>keyword.control.directive.undef.erlang</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.parameters.begin.erlang</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>entity.name.function.macro.erlang</string>
						</dict>
						<key>5</key>
						<dict>
							<key>name</key>
							<string>punctuation.definition.parameters.end.erlang</string>
						</dict>
						<key>6</key>
						<dict>
							<key>name</key>
							<string>punctuation.section.directive.end.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>^\s*+(-)\s*+(undef)\s*+(\()\s*+([a-zA-z\d@_]++)\s*+(\))\s*+(\.)</string>
					<key>name</key>
					<string>meta.directive.undef.erlang</string>
				</dict>
			</array>
		</dict>
		<key>macro-usage</key>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>keyword.operator.macro.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>entity.name.function.macro.erlang</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(\?\??)\s*+([a-zA-Z\d@_]++)</string>
			<key>name</key>
			<string>meta.macro-usage.erlang</string>
		</dict>
		<key>module-directive</key>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.directive.begin.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>keyword.control.directive.module.erlang</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.parameters.begin.erlang</string>
				</dict>
				<key>4</key>
				<dict>
					<key>name</key>
					<string>entity.name.type.class.module.definition.erlang</string>
				</dict>
				<key>5</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.parameters.end.erlang</string>
				</dict>
				<key>6</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.directive.end.erlang</string>
				</dict>
			</dict>
			<key>match</key>
			<string>^\s*+(-)\s*+(module)\s*+(\()\s*+([a-z][a-zA-Z\d@_]*+)\s*+(\))\s*+(\.)</string>
			<key>name</key>
			<string>meta.directive.module.erlang</string>
		</dict>
		<key>number</key>
		<dict>
			<key>begin</key>
			<string>(?=\d)</string>
			<key>end</key>
			<string>(?!\d)</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.integer-float.erlang</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.float-exponent.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>\d++(\.)\d++([eE][\+\-]?\d++)?</string>
					<key>name</key>
					<string>constant.numeric.float.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>2(#)([0-1]++_)*[0-1]++</string>
					<key>name</key>
					<string>constant.numeric.integer.binary.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>3(#)([0-2]++_)*[0-2]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-3.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>4(#)([0-3]++_)*[0-3]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-4.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>5(#)([0-4]++_)*[0-4]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-5.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>6(#)([0-5]++_)*[0-5]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-6.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>7(#)([0-6]++_)*[0-6]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-7.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>8(#)([0-7]++_)*[0-7]++</string>
					<key>name</key>
					<string>constant.numeric.integer.octal.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>9(#)([0-8]++_)*[0-8]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-9.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>10(#)(\d++_)*\d++</string>
					<key>name</key>
					<string>constant.numeric.integer.decimal.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>11(#)([\daA]++_)*[\daA]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-11.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>12(#)([\da-bA-B]++_)*[\da-bA-B]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-12.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>13(#)([\da-cA-C]++_)*[\da-cA-C]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-13.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>14(#)([\da-dA-D]++_)*[\da-dA-D]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-14.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>15(#)([\da-eA-E]++_)*[\da-eA-E]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-15.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>16(#)([\da-fA-F]++_)*[\da-fA-F]++</string>
					<key>name</key>
					<string>constant.numeric.integer.hexadecimal.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>17(#)([\da-gA-G]++_)*[\da-gA-G]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-17.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>18(#)([\da-hA-H]++_)*[\da-hA-H]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-18.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>19(#)([\da-iA-I]++_)*[\da-iA-I]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-19.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>20(#)([\da-jA-J]++_)*[\da-jA-J]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-20.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>21(#)([\da-kA-K]++_)*[\da-kA-K]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-21.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>22(#)([\da-lA-L]++_)*[\da-lA-L]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-22.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>23(#)([\da-mA-M]++_)*[\da-mA-M]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-23.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>24(#)([\da-nA-N]++_)*[\da-nA-N]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-24.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>25(#)([\da-oA-O]++_)*[\da-oA-O]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-25.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>26(#)([\da-pA-P]++_)*[\da-pA-P]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-26.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>27(#)([\da-qA-Q]++_)*[\da-qA-Q]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-27.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>28(#)([\da-rA-R]++_)*[\da-rA-R]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-28.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>29(#)([\da-sA-S]++_)*[\da-sA-S]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-29.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>30(#)([\da-tA-T]++_)*[\da-tA-T]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-30.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>31(#)([\da-uA-U]++_)*[\da-uA-U]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-31.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>32(#)([\da-vA-V]++_)*[\da-vA-V]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-32.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>33(#)([\da-wA-W]++_)*[\da-wA-W]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-33.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>34(#)([\da-xA-X]++_)*[\da-xA-X]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-34.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>35(#)([\da-yA-Y]++_)*[\da-yA-Y]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-35.erlang</string>
				</dict>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.base-integer.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>36(#)([\da-zA-Z]++_)*[\da-zA-Z]++</string>
					<key>name</key>
					<string>constant.numeric.integer.base-36.erlang</string>
				</dict>
				<dict>
					<key>match</key>
					<string>\d++#([\da-zA-Z]++_)*[\da-zA-Z]++</string>
					<key>name</key>
					<string>invalid.illegal.integer.erlang</string>
				</dict>
				<dict>
					<key>match</key>
					<string>(\d++_)*\d++</string>
					<key>name</key>
					<string>constant.numeric.integer.decimal.erlang</string>
				</dict>
			</array>
		</dict>
		<key>parenthesized-expression</key>
		<dict>
			<key>begin</key>
			<string>(\()</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.expression.begin.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(\))</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.expression.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>meta.expression.parenthesized</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#everything-else</string>
				</dict>
			</array>
		</dict>
		<key>record-directive</key>
		<dict>
			<key>begin</key>
			<string>^\s*+(-)\s*+(record)\s*+(\()\s*+([a-z][a-zA-Z\d@_]*+|'[^']*+')\s*+(,)</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.directive.begin.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>keyword.control.directive.import.erlang</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.parameters.begin.erlang</string>
				</dict>
				<key>4</key>
				<dict>
					<key>name</key>
					<string>entity.name.type.class.record.definition.erlang</string>
				</dict>
				<key>5</key>
				<dict>
					<key>name</key>
					<string>punctuation.separator.parameters.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(\))\s*+(\.)</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.parameters.end.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.section.directive.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>meta.directive.record.erlang</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#internal-record-body</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#comment</string>
				</dict>
			</array>
		</dict>
		<key>record-usage</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>captures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.operator.record.erlang</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>entity.name.type.class.record.erlang</string>
						</dict>
						<key>3</key>
						<dict>
							<key>name</key>
							<string>punctuation.separator.record-field.erlang</string>
						</dict>
						<key>4</key>
						<dict>
							<key>name</key>
							<string>variable.other.field.erlang</string>
						</dict>
					</dict>
					<key>match</key>
					<string>(#)\s*+([a-z][a-zA-Z\d@_]*+|'[^']*+')\s*+(\.)\s*+([a-z][a-zA-Z\d@_]*+|'[^']*+')</string>
					<key>name</key>
					<string>meta.record-usage.erlang</string>
				</dict>
				<dict>
					<key>begin</key>
					<string>(#)\s*+([a-z][a-zA-Z\d@_]*+|'[^']*+')</string>
					<key>beginCaptures</key>
					<dict>
						<key>1</key>
						<dict>
							<key>name</key>
							<string>keyword.operator.record.erlang</string>
						</dict>
						<key>2</key>
						<dict>
							<key>name</key>
							<string>entity.name.type.class.record.erlang</string>
						</dict>
					</dict>
					<key>end</key>
					<string>(?&lt;=\})</string>
					<key>name</key>
					<string>meta.record-usage.erlang</string>
					<key>patterns</key>
					<array>
						<dict>
							<key>include</key>
							<string>#internal-record-body</string>
						</dict>
					</array>
				</dict>
			</array>
		</dict>
		<key>sigil-docstring</key>
		<dict>
			<key>begin</key>
			<string>(~[bBsS]?)((["]{3,})\s*)(\S.*)?$</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.string.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>meta.string.quoted.triple.begin.erlang</string>
				</dict>
				<key>3</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.erlang</string>
				</dict>
				<key>4</key>
				<dict>
					<key>name</key>
					<string>invalid.illegal.string.erlang</string>
				</dict>
			</dict>
			<key>comment</key>
			<string>Only whitespace characters are allowed after the beggining and before the closing sequences and those cannot be in the same line</string>
			<key>end</key>
			<string>^(\s*(\3))(?!")</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>meta.string.quoted.triple.end.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.tripple.sigil.erlang</string>
		</dict>
		<key>sigil-string</key>
		<dict>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#sigil-string-parenthesis</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#sigil-string-parenthesis-verbatim</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#sigil-string-curly-brackets</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#sigil-string-curly-brackets-verbatim</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#sigil-string-square-brackets</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#sigil-string-square-brackets-verbatim</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#sigil-string-less-greater</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#sigil-string-less-greater-verbatim</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#sigil-string-single-character</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#sigil-string-single-character-verbatim</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#sigil-string-single-quote</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#sigil-string-single-quote-verbatim</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#sigil-string-double-quote</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#sigil-string-double-quote-verbatim</string>
				</dict>
			</array>
		</dict>
		<key>sigil-string-curly-brackets</key>
		<dict>
			<key>begin</key>
			<string>(~[bs]?)([{])</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.string.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>([}])</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.curly-brackets.sigil.erlang</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#internal-string-body</string>
				</dict>
			</array>
		</dict>
		<key>sigil-string-curly-brackets-verbatim</key>
		<dict>
			<key>begin</key>
			<string>(~[BS])([{])</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.string.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>([}])</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.curly-brackets.sigil.erlang</string>
		</dict>
		<key>sigil-string-double-quote</key>
		<dict>
			<key>begin</key>
			<string>(~[bs]?)(")</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.string.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(\2)</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.double.sigil.erlang</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#internal-string-body</string>
				</dict>
			</array>
		</dict>
		<key>sigil-string-double-quote-verbatim</key>
		<dict>
			<key>begin</key>
			<string>(~[BS])(")</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.string.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(\2)</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.double.sigil.erlang</string>
		</dict>
		<key>sigil-string-less-greater</key>
		<dict>
			<key>begin</key>
			<string>(~[bs]?)(&lt;)</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.string.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(&gt;)</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.less-greater.sigil.erlang</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#internal-string-body</string>
				</dict>
			</array>
		</dict>
		<key>sigil-string-less-greater-verbatim</key>
		<dict>
			<key>begin</key>
			<string>(~[BS])(&lt;)</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.string.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(&gt;)</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.less-greater.sigil.erlang</string>
		</dict>
		<key>sigil-string-parenthesis</key>
		<dict>
			<key>begin</key>
			<string>(~[bs]?)([(])</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.string.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>([)])</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.parenthesis.sigil.erlang</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#internal-string-body</string>
				</dict>
			</array>
		</dict>
		<key>sigil-string-parenthesis-verbatim</key>
		<dict>
			<key>begin</key>
			<string>(~[BS])([(])</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.string.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>([)])</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.parenthesis.sigil.erlang</string>
		</dict>
		<key>sigil-string-single-character</key>
		<dict>
			<key>begin</key>
			<string>(~[bs]?)([/\|`#])</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.string.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(\2)</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.other.sigil.erlang</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#internal-string-body</string>
				</dict>
			</array>
		</dict>
		<key>sigil-string-single-character-verbatim</key>
		<dict>
			<key>begin</key>
			<string>(~[BS])([/\|`#])</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.string.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(\2)</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.other.sigil.erlang</string>
		</dict>
		<key>sigil-string-single-quote</key>
		<dict>
			<key>begin</key>
			<string>(~[bs]?)(')</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.string.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(\2)</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.single.sigil.erlang</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#internal-string-body</string>
				</dict>
			</array>
		</dict>
		<key>sigil-string-single-quote-verbatim</key>
		<dict>
			<key>begin</key>
			<string>(~[BS])(')</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.string.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(\2)</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.single.sigil.erlang</string>
		</dict>
		<key>sigil-string-square-brackets</key>
		<dict>
			<key>begin</key>
			<string>(~[bs]?)([\[])</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.string.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>([\]])</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.square-brackets.sigil.erlang</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#internal-string-body</string>
				</dict>
			</array>
		</dict>
		<key>sigil-string-square-brackets-verbatim</key>
		<dict>
			<key>begin</key>
			<string>(~[BS])([\[])</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.string.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>([\]])</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.square-brackets.sigil.erlang</string>
		</dict>
		<key>string</key>
		<dict>
			<key>begin</key>
			<string>(")</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(")</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.double.erlang</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>include</key>
					<string>#internal-string-body</string>
				</dict>
			</array>
		</dict>
		<key>symbolic-operator</key>
		<dict>
			<key>match</key>
			<string>\+\+|\+|--|-|\*|/=|/|=/=|=:=|==|=&lt;|=|&lt;-|&lt;|&gt;=|&gt;|!|::|\?=</string>
			<key>name</key>
			<string>keyword.operator.symbolic.erlang</string>
		</dict>
		<key>textual-operator</key>
		<dict>
			<key>match</key>
			<string>\b(andalso|band|and|bxor|xor|bor|orelse|or|bnot|not|bsl|bsr|div|rem)\b</string>
			<key>name</key>
			<string>keyword.operator.textual.erlang</string>
		</dict>
		<key>tuple</key>
		<dict>
			<key>begin</key>
			<string>(\{)</string>
			<key>beginCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.tuple.begin.erlang</string>
				</dict>
			</dict>
			<key>end</key>
			<string>(\})</string>
			<key>endCaptures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.tuple.end.erlang</string>
				</dict>
			</dict>
			<key>name</key>
			<string>meta.structure.tuple.erlang</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>,</string>
					<key>name</key>
					<string>punctuation.separator.tuple.erlang</string>
				</dict>
				<dict>
					<key>include</key>
					<string>#everything-else</string>
				</dict>
			</array>
		</dict>
		<key>variable</key>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>variable.other.erlang</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>variable.language.omitted.erlang</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(_[a-zA-Z\d@_]++|[A-Z][a-zA-Z\d@_]*+)|(_)</string>
		</dict>
	</dict>
	<key>scopeName</key>
	<string>source.erlang</string>
	<key>uuid</key>
	<string>58EA597D-5158-4BF7-9FB2-B05135D1E166</string>
</dict>
</plist>
