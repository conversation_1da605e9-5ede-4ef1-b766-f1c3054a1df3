{"scopeName": "source.kconfig", "patterns": [{"include": "#comment"}, {"begin": "^\\s*(if)\\s+", "end": "$", "beginCaptures": {"1": {"name": "keyword.control"}}, "patterns": [{"include": "#expression"}, {"include": "#comment"}]}, {"match": "^\\s*(endif|endchoice|endmenu|optional)\\b", "captures": {"1": {"name": "keyword.control"}}}, {"begin": "^\\s*(config|menuconfig|choice|imply)\\s+", "end": "(\\s|$)", "beginCaptures": {"1": {"name": "keyword.control"}}, "patterns": [{"include": "#symbol"}, {"include": "#comment"}]}, {"begin": "^\\s*(range)\\s+", "end": "$", "beginCaptures": {"1": {"name": "keyword.control"}}, "patterns": [{"include": "#if"}, {"include": "#expression"}, {"include": "#comment"}]}, {"begin": "^(\\s*)(---help---|help)\\b", "patterns": [{"include": "#helptext"}], "end": "^(?!\\1\\s+|$)", "beginCaptures": {"2": {"name": "keyword.control"}}}, {"begin": "\\b(option)\\s+(defconfig_list|modules|allnoconfig_y)=?", "end": "$", "beginCaptures": {"1": {"name": "keyword.control"}, "2": {"name": "support.variable"}}, "patterns": [{"include": "#expression"}, {"include": "#comment"}]}, {"begin": "^\\s*(bool|tristate|string|hex|int)\\s+", "end": "$", "beginCaptures": {"1": {"name": "storage.type"}}, "patterns": [{"include": "#string"}, {"include": "#if"}, {"include": "#comment"}]}, {"begin": "^\\s*(visible if|depends on)\\s+", "end": "$", "beginCaptures": {"1": {"name": "keyword.control"}}, "patterns": [{"include": "#expression"}, {"include": "#comment"}]}, {"begin": "^\\s*(select|default|def_bool|def_tristate)\\s+", "end": "$", "beginCaptures": {"1": {"name": "keyword.control"}}, "patterns": [{"include": "#if"}, {"include": "#expression"}, {"include": "#comment"}]}, {"begin": "^\\s*(prompt|source|rsource|osource|orsource|mainmenu|menu|comment)\\s+", "end": "$", "beginCaptures": {"1": {"name": "keyword.control"}}, "patterns": [{"include": "#string"}, {"include": "#if"}, {"include": "#comment"}]}], "repository": {"comment": {"match": "#.*", "name": "comment.line"}, "string": {"begin": "\"", "end": "(?<!\\\\)\"", "name": "string.quoted.double", "patterns": [{"match": "\\\\.", "name": "constant.character.escape"}]}, "symbol": {"match": "[a-zA-Z\\-_0-9]+", "name": "support.variable"}, "expression": {"patterns": [{"include": "#comment"}, {"include": "#string"}, {"include": "#number"}, {"include": "#tristate"}, {"include": "#expression-operators"}, {"include": "#symbol"}, {"include": "#line-continuation"}]}, "expression-operators": {"match": "(=|!=|<|>|<=|>=|!|&&|\\|\\||\\(|\\))", "name": "keyword.operator.logical"}, "number": {"match": "\\b[+-]?(0x[\\da-fA-F]+|[0-9]+)\\b", "name": "constant.numeric"}, "tristate": {"match": "\\b[ymn]\\b", "name": "constant.character"}, "if": {"begin": "\\bif\\s+", "end": "($|#)", "beginCaptures": {"0": {"name": "keyword.control"}}, "patterns": [{"include": "#expression"}]}, "line-continuation": {"begin": "\\\\$", "end": "^", "beginCaptures": {"0": {"name": "constant.character.escape"}}}, "helptext": {"begin": "^(\\s+).*", "end": "^(?!\\1)"}}}