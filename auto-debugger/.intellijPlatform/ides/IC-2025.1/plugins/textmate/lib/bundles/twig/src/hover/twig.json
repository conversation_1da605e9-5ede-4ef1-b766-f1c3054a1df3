{"show": {"prefix": "show", "body": "{{ $1 }}", "description": "{{ }}"}, "execute": {"prefix": "execute", "body": "{% $1 %}", "description": "{% %}"}, "autoescape": {"prefix": "autoescape", "body": ["{% autoescape %}", "\t$1", "{% endautoescape %}"], "description": "Whether automatic escaping is enabled or not, you can mark a section of a template to be escaped or not by using the autoescape tag", "example": "{% autoescape %}\n    Everything will be automatically escaped in this block\n    using the HTML strategy\n{% endautoescape %}\n\n{% autoescape 'html' %}\n    Everything will be automatically escaped in this block\n    using the HTML strategy\n{% endautoescape %}\n\n{% autoescape 'js' %}\n    Everything will be automatically escaped in this block\n    using the js escaping strategy\n{% endautoescape %}\n\n{% autoescape false %}\n    Everything will be outputted as is in this block\n{% endautoescape %}"}, "block": {"prefix": "block", "body": ["{% block ${name} %}", "\t$1", "{% endblock ${name} %}"], "description": "When a template uses inheritance and if you want to print a block multiple times, use the block function"}, "do": {"prefix": "do", "body": ["{% do $1 %}"], "description": "The do tag works exactly like the regular variable expression ({{ ... }}) just that it doesn't print anything", "example": "{% do 1 + 2 %}"}, "embed": {"prefix": "embed", "body": ["{% embed \"${filename}.twig\" %}", "\t$1", "{% endembed  %}"], "description": "The embed tag combines the behaviour of include and extends. It allows you to include another template's contents, just like include does. But it also allows you to override any block defined inside the included template, like when extending a template"}, "extends": {"prefix": "extends", "body": "{% extends \"${filename}.twig\" %}", "description": "Twig snippets"}, "filter": {"prefix": "filter", "body": ["{% filter ${filter name} %}", "\t$1", "{% endfilter  %}"], "description": "Filter sections allow you to apply regular Twig filters on a block of template data. Just wrap the code in the special filter section", "example": "{% filter lower | escape %}\n    <strong>SOME TEXT</strong>\n{% endfilter %}\n\n{# outputs \"&lt;strong&gt;some text&lt;/strong&gt;\" #}"}, "flush": {"prefix": "flush", "body": ["{% flush %}"], "description": "The flush tag tells <PERSON><PERSON> to flush the output buffer", "example": "{% flush %}"}, "for": {"prefix": "for", "body": ["{% for ${row} in ${array} %}", "\t$1", "{% endfor %}"], "description": "Loop over each item in a sequence"}, "for if": {"prefix": "for if", "body": ["{% for ${row} in ${array} if ${condition} %}", "\t$1", "{% endfor %}"], "description": "Loop over each item in a sequence"}, "for else": {"prefix": "for else", "body": ["{% for ${row} in ${array} %}", "\t$1", "{% else %}", "\t$2", "{% endfor %}"], "description": "Loop over each item in a sequence"}, "for if else": {"prefix": "for if else", "body": ["{% for ${row} in ${array} if ${condition} %}", "\t$1", "{% else %}", "\t$2", "{% endfor %}"], "description": "Loop over each item in a sequence"}, "loop": {"prefix": "loop", "body": "loop.", "description": "special variables inside of a for loop block"}, "if": {"prefix": "if", "body": ["{% if ${condition} %}", "\t$1", "{% endif %}"], "description": "The if statement in <PERSON>wig is comparable with the if statements of PHP"}, "if else": {"prefix": "if else", "body": ["{% if ${condition} %}", "\t$1", "{% else %}", "\t$2", "{% endif %}"], "description": "The if statement in <PERSON>wig is comparable with the if statements of PHP"}, "else": {"prefix": "else", "body": "{% else %}", "description": "The if statement in <PERSON>wig is comparable with the if statements of PHP"}, "else if": {"prefix": "else if", "body": "{% elseif ${condition} %}", "description": "The if statement in <PERSON>wig is comparable with the if statements of PHP"}, "import": {"prefix": "import", "body": "{% import \"${filename}.twig\" as ${alias}%}", "description": "Twig supports putting often used code into macros. These macros can go into different templates and get imported from there."}, "_self": {"prefix": "_self", "body": "_self", "description": "To import macros from the current file, use the special _self variable for the source"}, "include": {"prefix": "include", "body": "{% include \"${filename}.twig\" %}", "description": "The include statement includes a template and returns the rendered content of that file into the current namespace"}, "macro": {"prefix": "macro", "body": ["{% macro ${name}($1) %}", "\t$2", "{% endmacro %}"], "description": "Twig snippets"}, "sandbox": {"prefix": "sandbox", "body": ["{% sandbox %}", "\t$1", "{% endsandbox %}"], "description": "The sandbox tag can be used to enable the sandboxing mode for an included template, when sandboxing is not enabled globally for the Twig environment"}, "set": {"prefix": "set", "body": ["{% set ${name} = ${value} %}$1"], "description": "Assign values to variables"}, "set block": {"prefix": "set (block)", "body": ["{% set ${name} %}", "\t$1", "{% endset %}"], "description": "Inside code blocks you can also assign values to variables. Assignments use the set tag and can have multiple targets"}, "spaceless": {"prefix": "spaceless", "body": ["{% spaceless %}", "\t$1", "{% endspaceless %}"], "description": "Use the spaceless tag to remove whitespace between HTML tags, not whitespace within HTML tags or whitespace in plain text"}, "use": {"prefix": "use", "body": "{% use \"${filename}.twig\" %}", "description": "Twig snippets"}, "verbatim": {"prefix": "verbatim", "body": ["{% verbatim %}", "\t$1", "{% endverbatim %}"], "description": "The verbatim tag marks sections as being raw text that should not be parsed. For example to put Twig syntax as example into a template you can use this snippet"}}