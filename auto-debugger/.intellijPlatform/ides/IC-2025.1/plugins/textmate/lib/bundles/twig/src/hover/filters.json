{"abs": {"text": "abs", "body": "abs", "description": "filter returns the absolute value"}, "batch": {"prefix": "batch", "body": "batch(${size}, ${fill})", "text": "batch(size, fill)", "description": "filter \"batches\" items by returning a list of lists with the given number of items. A second parameter can be provided and used to fill in missing items"}, "capitalize": {"text": "capitalize", "body": "capitalize", "description": "filter capitalizes a value. The first character will be uppercase, all others lowercase"}, "convert_encoding": {"prefix": "convert_encoding", "body": "convert_encoding('${to}', '${from}')", "text": "convert_encoding('to', 'from')", "description": "filter converts a string from one encoding to another. The first argument is the expected output charset and the second one is the input charset"}, "date": {"prefix": "date", "body": "date(\"${m/d/Y}\")", "text": "date(\"m/d/Y\")", "description": "filter formats a date to a given format"}, "date_modify": {"prefix": "date_modify", "body": "date_modify(\"${+1 day}\")", "text": "date_modify(\"+1 day\")", "description": "filter modifies a date with a given modifier string"}, "default": {"prefix": "default", "body": "default('${default value}')", "text": "default('default value')", "description": "filter returns the passed default value if the value is undefined or empty, otherwise the value of the variable"}, "escape": {"text": "escape", "body": "escape", "description": "filter escapes a string for safe insertion into the final output. It supports different escaping strategies depending on the template context"}, "first": {"text": "first", "body": "first", "description": "filter returns the first \"element\" of a sequence, a mapping, or a string"}, "format": {"prefix": "format", "body": "format($1)", "text": "format()", "description": "filter formats a given string by replacing the placeholders (placeholders follows the sprintf notation)", "example": "{% set foo = \"foo\" %}\n{{ \"I like %s and %s.\"| format(foo, \"bar\") }}\n\n{# outputs I like foo and bar #}"}, "join": {"prefix": "join", "body": "join${('optional')}", "text": "join", "description": "filter returns a string which is the concatenation of the items of a sequence"}, "json_encode": {"prefix": "json_encode", "body": "json_encode()", "text": "json_encode()", "description": "filter returns the JSON representation of a value. Internally, <PERSON><PERSON> uses the PHP json_encode function."}, "keys": {"text": "keys", "body": "keys", "description": "filter returns the keys of an array. It is useful when you want to iterate over the keys of an array"}, "last": {"text": "last", "body": "last", "description": "filter returns the last \"element\" of a sequence, a mapping, or a string"}, "length": {"text": "length", "body": "length", "description": "filter returns the number of items of a sequence or mapping, or the length of a string"}, "lower": {"text": "lower", "body": "lower", "description": "filter converts a value to lowercase"}, "merge": {"prefix": "merge", "body": "merge(${array})", "text": "merge(array)", "description": "filter merges an array with another array"}, "nl2br": {"text": "nl2br", "body": "nl2br", "description": "filter inserts HTML line breaks before all newlines in a string"}, "number_format": {"prefix": "number_format", "body": "number_format(${0}, '${.}', '${,}')", "text": "number_format", "description": "filter formats numbers. It is a wrapper around PHP's number_format function"}, "raw": {"text": "raw", "body": "raw", "description": "filter marks the value as being \"safe\", which means that in an environment with automatic escaping enabled this variable will not be escaped if raw is the last filter applied to it."}, "replace": {"prefix": "replace", "body": "replace('${search}' : '${replace}')", "text": "replace('search' : 'replace')", "description": "filter formats a given string by replacing the placeholders."}, "reverse": {"text": "reverse", "body": "reverse", "description": "filter reverses a sequence, a mapping, or a string"}, "round": {"prefix": "round", "body": "${0} | round(1, '${floor}')", "text": "round", "description": "filter rounds a number to a given precision"}, "slice": {"prefix": "slice", "body": "slice(${start}, ${length})", "text": "slice(start, length)", "description": "filter extracts a slice of a sequence, a mapping, or a string"}, "slice [] notation": {"prefix": "slice [] notation", "body": "[${start}:${length}]", "description": "filter extracts a slice of a sequence, a mapping, or a string"}, "sort": {"text": "sort", "body": "sort", "description": "filter sorts an array"}, "split": {"prefix": "split", "body": "split('$1')", "text": "split('')", "description": "filter splits a string by the given delimiter and returns a list of strings"}, "striptags": {"text": "striptags", "body": "striptags", "description": "filter strips SGML/XML tags and replace adjacent whitespace by one space"}, "title": {"text": "title", "body": "title", "description": "filter returns a titlecased version of the value. Words will start with uppercase letters, all remaining characters are lowercase"}, "trim": {"text": "trim", "body": "trim", "description": "filter strips whitespace (or other characters) from the beginning and end of a string"}, "trim()": {"prefix": "trim()", "body": "trim('$1')", "description": "filter strips whitespace (or other characters) from the beginning and end of a string"}, "upper": {"text": "upper", "body": "upper", "description": "filter converts a value to uppercase"}, "url_encode": {"text": "url_encode", "body": "url_encode", "description": "filter percent encodes a given string as URL segment or an array as query string"}}