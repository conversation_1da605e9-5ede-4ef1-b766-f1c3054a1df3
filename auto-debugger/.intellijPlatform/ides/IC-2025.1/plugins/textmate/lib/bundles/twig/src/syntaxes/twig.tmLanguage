<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>fileTypes</key>
    <array>
        <string>twig</string>
        <string>html.twig</string>
    </array>
    <key>firstLineMatch</key>
    <string>&lt;!(?i:DOCTYPE)|&lt;(?i:html)|&lt;\?(?i:php)|\{\{|\{%|\{#</string>
    <key>foldingStartMarker</key>
    <string>(?x)
        (&lt;(?i:body|div|dl|fieldset|form|head|li|ol|script|select|style|table|tbody|tfoot|thead|tr|ul)\b.*?&gt;
        |&lt;!--(?!.*--\s*&gt;)
        |^&lt;!--\ \#tminclude\ (?&gt;.*?--&gt;)$
        |\{%\s+(autoescape|block|embed|filter|for|if|macro|raw|sandbox|set|spaceless|trans|verbatim)
        )</string>
    <key>foldingStopMarker</key>
    <string>(?x)
        (&lt;/(?i:body|div|dl|fieldset|form|head|li|ol|script|select|style|table|tbody|tfoot|thead|tr|ul)&gt;
        |^(?!.*?&lt;!--).*?--\s*&gt;
        |^&lt;!--\ end\ tminclude\ --&gt;$
        |\{%\s+end(autoescape|block|embed|filter|for|if|macro|raw|sandbox|set|spaceless|trans|verbatim)
        )</string>
    <key>keyEquivalent</key>
    <string>^~T</string>
    <key>name</key>
    <string>HTML (Twig)</string>
    <key>patterns</key>
    <array>
        <dict>
            <key>begin</key>
            <string>(&lt;)([a-zA-Z0-9:]++)(?=[^&gt;]*&gt;&lt;/\2&gt;)</string>
            <key>beginCaptures</key>
            <dict>
                <key>1</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.tag.html</string>
                </dict>
                <key>2</key>
                <dict>
                    <key>name</key>
                    <string>entity.name.tag.html</string>
                </dict>
            </dict>
            <key>end</key>
            <string>(&gt;(&lt;)/)(\2)(&gt;)</string>
            <key>endCaptures</key>
            <dict>
                <key>1</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.tag.html</string>
                </dict>
                <key>2</key>
                <dict>
                    <key>name</key>
                    <string>meta.scope.between-tag-pair.html</string>
                </dict>
                <key>3</key>
                <dict>
                    <key>name</key>
                    <string>entity.name.tag.html</string>
                </dict>
                <key>4</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.tag.html</string>
                </dict>
            </dict>
            <key>name</key>
            <string>meta.tag.any.html</string>
            <key>patterns</key>
            <array>
                <dict>
                    <key>include</key>
                    <string>#tag-stuff</string>
                </dict>
            </array>
        </dict>
        <dict>
            <key>begin</key>
            <string>(&lt;\?)(xml)</string>
            <key>captures</key>
            <dict>
                <key>1</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.tag.html</string>
                </dict>
                <key>2</key>
                <dict>
                    <key>name</key>
                    <string>entity.name.tag.xml.html</string>
                </dict>
            </dict>
            <key>end</key>
            <string>(\?&gt;)</string>
            <key>name</key>
            <string>meta.tag.preprocessor.xml.html</string>
            <key>patterns</key>
            <array>
                <dict>
                    <key>include</key>
                    <string>#tag-generic-attribute</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#string-double-quoted</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#string-single-quoted</string>
                </dict>
            </array>
        </dict>
        <dict>
            <key>begin</key>
            <string>&lt;!--</string>
            <key>captures</key>
            <dict>
                <key>0</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.comment.html</string>
                </dict>
            </dict>
            <key>end</key>
            <string>--\s*&gt;</string>
            <key>name</key>
            <string>comment.block.html</string>
            <key>patterns</key>
            <array>
                <dict>
                    <key>match</key>
                    <string>--</string>
                    <key>name</key>
                    <string>invalid.illegal.bad-comments-or-CDATA.html</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#embedded-code</string>
                </dict>
            </array>
        </dict>
        <dict>
            <key>begin</key>
            <string>&lt;!</string>
            <key>captures</key>
            <dict>
                <key>0</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.tag.html</string>
                </dict>
            </dict>
            <key>end</key>
            <string>&gt;</string>
            <key>name</key>
            <string>meta.tag.sgml.html</string>
            <key>patterns</key>
            <array>
                <dict>
                    <key>begin</key>
                    <string>(?i:DOCTYPE)</string>
                    <key>captures</key>
                    <dict>
                        <key>1</key>
                        <dict>
                            <key>name</key>
                            <string>entity.name.tag.doctype.html</string>
                        </dict>
                    </dict>
                    <key>end</key>
                    <string>(?=&gt;)</string>
                    <key>name</key>
                    <string>meta.tag.sgml.doctype.html</string>
                    <key>patterns</key>
                    <array>
                        <dict>
                            <key>match</key>
                            <string>"[^"&gt;]*"</string>
                            <key>name</key>
                            <string>string.quoted.double.doctype.identifiers-and-DTDs.html</string>
                        </dict>
                    </array>
                </dict>
                <dict>
                    <key>begin</key>
                    <string>\[CDATA\[</string>
                    <key>end</key>
                    <string>]](?=&gt;)</string>
                    <key>name</key>
                    <string>constant.other.inline-data.html</string>
                </dict>
                <dict>
                    <key>match</key>
                    <string>(\s*)(?!--|&gt;)\S(\s*)</string>
                    <key>name</key>
                    <string>invalid.illegal.bad-comments-or-CDATA.html</string>
                </dict>
            </array>
        </dict>
        <dict>
            <key>include</key>
            <string>#embedded-code</string>
        </dict>
        <dict>
            <key>begin</key>
            <string>(?:^\s+)?(&lt;)((?i:style))\b(?![^&gt;]*/&gt;)</string>
            <key>captures</key>
            <dict>
                <key>1</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.tag.html</string>
                </dict>
                <key>2</key>
                <dict>
                    <key>name</key>
                    <string>entity.name.tag.style.html</string>
                </dict>
                <key>3</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.tag.html</string>
                </dict>
            </dict>
            <key>end</key>
            <string>(&lt;/)((?i:style))(&gt;)(?:\s*\n)?</string>
            <key>name</key>
            <string>source.css.embedded.html</string>
            <key>patterns</key>
            <array>
                <dict>
                    <key>include</key>
                    <string>#tag-stuff</string>
                </dict>
                <dict>
                    <key>begin</key>
                    <string>(&gt;)</string>
                    <key>beginCaptures</key>
                    <dict>
                        <key>1</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.definition.tag.html</string>
                        </dict>
                    </dict>
                    <key>end</key>
                    <string>(?=&lt;/(?i:style))</string>
                    <key>patterns</key>
                    <array>
                        <dict>
                            <key>include</key>
                            <string>#embedded-code</string>
                        </dict>
                        <dict>
                            <key>include</key>
                            <string>source.css</string>
                        </dict>
                    </array>
                </dict>
            </array>
        </dict>
        <dict>
            <key>begin</key>
            <string>(?:^\s+)?(&lt;)((?i:script))\b(?![^&gt;]*/&gt;)</string>
            <key>beginCaptures</key>
            <dict>
                <key>1</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.tag.html</string>
                </dict>
                <key>2</key>
                <dict>
                    <key>name</key>
                    <string>entity.name.tag.script.html</string>
                </dict>
            </dict>
            <key>end</key>
            <string>(?&lt;=&lt;/(script|SCRIPT))(&gt;)(?:\s*\n)?</string>
            <key>endCaptures</key>
            <dict>
                <key>2</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.tag.html</string>
                </dict>
            </dict>
            <key>name</key>
            <string>source.js.embedded.html</string>
            <key>patterns</key>
            <array>
                <dict>
                    <key>include</key>
                    <string>#tag-stuff</string>
                </dict>
                <dict>
                    <key>begin</key>
                    <string>(?&lt;!&lt;/(?:script|SCRIPT))(&gt;)</string>
                    <key>captures</key>
                    <dict>
                        <key>1</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.definition.tag.html</string>
                        </dict>
                        <key>2</key>
                        <dict>
                            <key>name</key>
                            <string>entity.name.tag.script.html</string>
                        </dict>
                    </dict>
                    <key>end</key>
                    <string>(&lt;/)((?i:script))</string>
                    <key>patterns</key>
                    <array>
                        <dict>
                            <key>captures</key>
                            <dict>
                                <key>1</key>
                                <dict>
                                    <key>name</key>
                                    <string>punctuation.definition.comment.js</string>
                                </dict>
                            </dict>
                            <key>match</key>
                            <string>(//).*?((?=&lt;/script)|$\n?)</string>
                            <key>name</key>
                            <string>comment.line.double-slash.js</string>
                        </dict>
                        <dict>
                            <key>begin</key>
                            <string>/\*</string>
                            <key>captures</key>
                            <dict>
                                <key>0</key>
                                <dict>
                                    <key>name</key>
                                    <string>punctuation.definition.comment.js</string>
                                </dict>
                            </dict>
                            <key>end</key>
                            <string>\*/|(?=&lt;/script)</string>
                            <key>name</key>
                            <string>comment.block.js</string>
                        </dict>
                        <dict>
                            <key>include</key>
                            <string>#php</string>
                        </dict>

                        <!-- TWIG START -->
                        <dict>
                            <key>include</key>
                            <string>#twig-print-tag</string>
                        </dict>
                        <dict>
                            <key>include</key>
                            <string>#twig-statement-tag</string>
                        </dict>
                        <dict>
                            <key>include</key>
                            <string>#twig-comment-tag</string>
                        </dict>
                        <!-- TWIG END -->

                        <dict>
                            <key>include</key>
                            <string>source.js</string>
                        </dict>
                    </array>
                </dict>
            </array>
        </dict>
        <dict>
            <key>begin</key>
            <string>(?ix)   # Enable free spacing mode, case insensitive
                        # Make sure our opening js tag has word boundaries
                        (?&lt;=\{\%\sjs\s\%\}|\{\%\sincludejs\s\%\})
                    </string>
            <key>comment</key>
            <string>Add JS support to set tags that use the pattern "css" in their name</string>
            <key>end</key>
            <string>(?ix)(?=\{\%\sendjs\s\%\}|\{\%\sendincludejs\s\%\})</string>
            <key>name</key>
            <string>source.js.embedded.twig</string>
            <key>patterns</key>
            <array>
                <dict>
                <key>include</key>
                <string>source.js</string>
                </dict>
            </array>
        </dict>
        <dict>
            <key>begin</key>
            <string>(?ix)   # Enable free spacing mode, case insensitive
                        (?&lt;=\{\%\scss\s\%\}|\{\%\sincludecss\s\%\}|\{\%\sincludehirescss\s\%\})
                        </string>
            <key>comment</key>
            <string>Add CSS support to set tags that use the pattern "css" in their name</string>
            <key>end</key>
            <string>(?ix)(?=\{\%\sendcss\s\%\}|\{\%\sendincludecss\s\%\}|\{\%\sendincludehirescss\s\%\})</string>
            <key>name</key>
            <string>source.css.embedded.twig</string>
            <key>patterns</key>
            <array>
                <dict>
                    <key>include</key>
                    <string>source.css</string>
                </dict>
            </array>
        </dict>
        <dict>
            <key>begin</key>
            <string>(?ix)   # Enable free spacing mode, case insensitive
                        (?&lt;=\{\%\sscss\s\%\}|\{\%\sincludescss\s\%\}|\{\%\sincludehiresscss\s\%\})
                        </string>
            <key>comment</key>
            <string>Add SCSS support to set tags that use the pattern "scss" in their name</string>
            <key>end</key>
            <string>(?ix)(?=\{\%\sendscss\s\%\}|\{\%\sendincludescss\s\%\}|\{\%\sendincludehiresscss\s\%\})</string>
            <key>name</key>
            <string>source.css.scss.embedded.twig</string>
            <key>patterns</key>
            <array>
                <dict>
                    <key>include</key>
                    <string>source.css.scss</string>
                </dict>
            </array>
        </dict>
        <dict>
            <key>begin</key>
            <string>(&lt;/?)((?i:body|head|html)\b)</string>
            <key>captures</key>
            <dict>
                <key>1</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.tag.html</string>
                </dict>
                <key>2</key>
                <dict>
                    <key>name</key>
                    <string>entity.name.tag.structure.any.html</string>
                </dict>
            </dict>
            <key>end</key>
            <string>(&gt;)</string>
            <key>name</key>
            <string>meta.tag.structure.any.html</string>
            <key>patterns</key>
            <array>
                <dict>
                    <key>include</key>
                    <string>#tag-stuff</string>
                </dict>
            </array>
        </dict>
        <dict>
            <key>begin</key>
            <string>(&lt;/?)((?i:address|blockquote|dd|div|dl|dt|fieldset|form|frame|frameset|h1|h2|h3|h4|h5|h6|iframe|noframes|object|ol|p|ul|applet|center|dir|hr|menu|pre)\b)</string>
            <key>beginCaptures</key>
            <dict>
                <key>1</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.tag.begin.html</string>
                </dict>
                <key>2</key>
                <dict>
                    <key>name</key>
                    <string>entity.name.tag.block.any.html</string>
                </dict>
            </dict>
            <key>end</key>
            <string>(&gt;)</string>
            <key>endCaptures</key>
            <dict>
                <key>1</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.tag.end.html</string>
                </dict>
            </dict>
            <key>name</key>
            <string>meta.tag.block.any.html</string>
            <key>patterns</key>
            <array>
                <dict>
                    <key>include</key>
                    <string>#tag-stuff</string>
                </dict>
            </array>
        </dict>
        <dict>
            <key>begin</key>
            <string>(&lt;/?)((?i:a|abbr|acronym|area|b|base|basefont|bdo|big|br|button|caption|cite|code|col|colgroup|del|dfn|em|font|head|html|i|img|input|ins|isindex|kbd|label|legend|li|link|map|meta|noscript|optgroup|option|param|q|s|samp|script|select|small|span|strike|strong|style|sub|sup|table|tbody|td|textarea|tfoot|th|thead|title|tr|tt|u|var)\b)</string>
            <key>beginCaptures</key>
            <dict>
                <key>1</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.tag.begin.html</string>
                </dict>
                <key>2</key>
                <dict>
                    <key>name</key>
                    <string>entity.name.tag.inline.any.html</string>
                </dict>
            </dict>
            <key>end</key>
            <string>((?: ?/)?&gt;)</string>
            <key>endCaptures</key>
            <dict>
                <key>1</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.tag.end.html</string>
                </dict>
            </dict>
            <key>name</key>
            <string>meta.tag.inline.any.html</string>
            <key>patterns</key>
            <array>
                <dict>
                    <key>include</key>
                    <string>#tag-stuff</string>
                </dict>
            </array>
        </dict>
        <dict>
            <key>begin</key>
            <string>(&lt;/?)([a-zA-Z0-9:]+)</string>
            <key>beginCaptures</key>
            <dict>
                <key>1</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.tag.begin.html</string>
                </dict>
                <key>2</key>
                <dict>
                    <key>name</key>
                    <string>entity.name.tag.other.html</string>
                </dict>
            </dict>
            <key>end</key>
            <string>(&gt;)</string>
            <key>endCaptures</key>
            <dict>
                <key>1</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.tag.end.html</string>
                </dict>
            </dict>
            <key>name</key>
            <string>meta.tag.other.html</string>
            <key>patterns</key>
            <array>
                <dict>
                    <key>include</key>
                    <string>#tag-stuff</string>
                </dict>
            </array>
        </dict>
        <dict>
            <key>include</key>
            <string>#entities</string>
        </dict>
        <dict>
            <key>match</key>
            <string>&lt;&gt;</string>
            <key>name</key>
            <string>invalid.illegal.incomplete.html</string>
        </dict>
        <dict>
            <key>match</key>
            <string>&lt;</string>
            <key>name</key>
            <string>invalid.illegal.bad-angle-bracket.html</string>
        </dict>

        <!-- TWIG START -->
        <dict>
            <key>include</key>
            <string>#twig-print-tag</string>
        </dict>
        <dict>
            <key>include</key>
            <string>#twig-statement-tag</string>
        </dict>
        <dict>
            <key>include</key>
            <string>#twig-comment-tag</string>
        </dict>
        <!-- TWIG END -->
    </array>
    <key>repository</key>
    <dict>
        <key>embedded-code</key>
        <dict>
            <key>patterns</key>
            <array>
                <dict>
                    <key>include</key>
                    <string>#ruby</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#php</string>
                </dict>

                <!-- TWIG START -->
                <dict>
                    <key>include</key>
                    <string>#twig-print-tag</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-statement-tag</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-comment-tag</string>
                </dict>
                <!-- TWIG END -->

                <dict>
                    <key>include</key>
                    <string>#python</string>
                </dict>
            </array>
        </dict>
        <key>entities</key>
        <dict>
            <key>patterns</key>
            <array>
                <dict>
                    <key>captures</key>
                    <dict>
                        <key>1</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.definition.entity.html</string>
                        </dict>
                        <key>3</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.definition.entity.html</string>
                        </dict>
                    </dict>
                    <key>match</key>
                    <string>(&amp;)([a-zA-Z0-9]+|#[0-9]+|#x[0-9a-fA-F]+)(;)</string>
                    <key>name</key>
                    <string>constant.character.entity.html</string>
                </dict>
                <dict>
                    <key>match</key>
                    <string>&amp;</string>
                    <key>name</key>
                    <string>invalid.illegal.bad-ampersand.html</string>
                </dict>
            </array>
        </dict>

        <!-- TWIG START -->

        <!-- PRINT TAG -->
        <key>twig-print-tag</key>
        <dict>
            <key>begin</key>
            <string>\{\{-?</string>
            <key>beginCaptures</key>
            <dict>
                <key>0</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.section.tag.twig</string>
                </dict>
            </dict>
            <key>end</key>
            <string>-?\}\}</string>
            <key>endCaptures</key>
            <dict>
                <key>0</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.section.tag.twig</string>
                </dict>
            </dict>
            <key>name</key>
            <string>meta.tag.template.value.twig</string>
            <key>patterns</key>
            <array>
                <dict>
                    <key>include</key>
                    <string>#twig-constants</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-operators</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-functions-warg</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-functions</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-macros</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-objects</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-properties</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters-warg</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters-warg-ud</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters-ud</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-strings</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-arrays</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-hashes</string>
                </dict>
            </array>
        </dict>

        <!-- STATEMENT TAG -->
        <key>twig-statement-tag</key>
        <dict>
            <key>begin</key>
            <string>\{%-?</string>
            <key>beginCaptures</key>
            <dict>
                <key>0</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.section.tag.twig</string>
                </dict>
            </dict>
            <key>end</key>
            <string>-?%\}</string>
            <key>endCaptures</key>
            <dict>
                <key>0</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.section.tag.twig</string>
                </dict>
            </dict>
            <key>name</key>
            <string>meta.tag.template.block.twig</string>
            <key>patterns</key>
            <array>
                <dict>
                    <key>include</key>
                    <string>#twig-constants</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-keywords</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-operators</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-functions-warg</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-functions</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-macros</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters-warg</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters-warg-ud</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters-ud</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-objects</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-properties</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-strings</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-arrays</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-hashes</string>
                </dict>
            </array>
        </dict>

        <!-- COMMENT TAG -->
        <key>twig-comment-tag</key>
        <dict>
            <key>begin</key>
            <string>\{#-?</string>
            <key>beginCaptures</key>
            <dict>
                <key>0</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.comment.begin.twig</string>
                </dict>
            </dict>
            <key>end</key>
            <string>-?#\}</string>
            <key>endCaptures</key>
            <dict>
                <key>0</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.comment.end.twig</string>
                </dict>
            </dict>
            <key>name</key>
            <string>comment.block.twig</string>
        </dict>

        <!-- CONSTANTS -->
        <key>twig-constants</key>
        <dict>
            <key>patterns</key>
            <array>
                <!-- LANGUAGE -->
                <dict>
                    <key>match</key>
                    <string>(?i)(?&lt;=[\s\[\(\{:,])(?:true|false|null|none)(?=[\s\)\]\}\,])</string>
                    <key>name</key>
                    <string>constant.language.twig</string>
                </dict>
                <!-- NUMERIC -->
                <dict>
                    <key>match</key>
                    <string>(?&lt;=[\s\[\(\{:,]|\.\.|\*\*)[0-9]+(?:\.[0-9]+)?(?=[\s\)\]\}\,]|\.\.|\*\*)</string>
                    <key>name</key>
                    <string>constant.numeric.twig</string>
                </dict>
            </array>
        </dict>

        <!-- OPERATORS -->
        <key>twig-operators</key>
        <dict>
            <key>patterns</key>
            <array>
                <!-- ARITHMETIC -->
                <dict>
                    <key>captures</key>
                    <dict>
                        <key>1</key>
                        <dict>
                            <key>name</key>
                            <string>keyword.operator.arithmetic.twig</string>
                        </dict>
                    </dict>
                    <key>match</key>
                    <string>(?&lt;=\s)(\+|-|//?|%|\*\*?)(?=\s)</string>
                </dict>

                <!-- ASSIGNMENT -->
                <dict>
                    <key>captures</key>
                    <dict>
                        <key>1</key>
                        <dict>
                            <key>name</key>
                            <string>keyword.operator.assignment.twig</string>
                        </dict>
                    </dict>
                    <key>match</key>
                    <string>(?&lt;=\s)(=|~)(?=\s)</string>
                </dict>

                <!-- BITWISE -->
                <dict>
                    <key>captures</key>
                    <dict>
                        <key>1</key>
                        <dict>
                            <key>name</key>
                            <string>keyword.operator.bitwise.twig</string>
                        </dict>
                    </dict>
                    <key>match</key>
                    <string>(?&lt;=\s)(b-(?:and|or|xor))(?=\s)</string>
                </dict>

                <!-- COMPARISON -->
                <dict>
                    <key>captures</key>
                    <dict>
                        <key>1</key>
                        <dict>
                            <key>name</key>
                            <string>keyword.operator.comparison.twig</string>
                        </dict>
                    </dict>
                    <key>match</key>
                    <string>(?&lt;=\s)((?:!|=)=|&lt;=?|&gt;=?|(?:not )?in|is(?: not)?|(?:ends|starts) with|matches)(?=\s)</string>
                </dict>

                <!-- LOGICAL -->
                <dict>
                    <key>captures</key>
                    <dict>
                        <key>1</key>
                        <dict>
                            <key>name</key>
                            <string>keyword.operator.logical.twig</string>
                        </dict>
                    </dict>
                    <key>match</key>
                    <string>(?&lt;=\s)(\?|:|\?:|\?\?|and|not|or)(?=\s)</string>
                </dict>

                <!-- RANGE -->
                <dict>
                    <key>captures</key>
                    <dict>
                        <key>0</key>
                        <dict>
                            <key>name</key>
                            <string>keyword.operator.other.twig</string>
                        </dict>
                    </dict>
                    <key>match</key>
                    <string>(?&lt;=[a-zA-Z0-9_\x{7f}-\x{ff}\]\)'"])\.\.(?=[a-zA-Z0-9_\x{7f}-\x{ff}'"])</string>
                </dict>

                <!-- FILTER -->
                <dict>
                    <key>captures</key>
                    <dict>
                        <key>0</key>
                        <dict>
                            <key>name</key>
                            <string>keyword.operator.other.twig</string>
                        </dict>
                    </dict>
                    <key>match</key>
                    <string>(?&lt;=[a-zA-Z0-9_\x{7f}-\x{ff}\]\}\)'"])\|(?=[a-zA-Z_\x{7f}-\x{ff}])</string>
                </dict>
            </array>
        </dict>

        <!-- OBJECTS -->
        <key>twig-objects</key>
        <dict>
            <key>captures</key>
            <dict>
                <key>1</key>
                <dict>
                    <key>name</key>
                    <string>variable.other.twig</string>
                </dict>
            </dict>
            <key>match</key>
            <string>(?&lt;=[\s\{\[\(:,])([a-zA-Z_\x{7f}-\x{ff}][a-zA-Z0-9_\x{7f}-\x{ff}]*)(?=[\s\}\[\]\(\)\.\|,:])</string>
        </dict>

        <!-- PROPERTIES -->
        <key>twig-properties</key>
        <dict>
            <key>patterns</key>
            <array>
                <!-- DOT NOTATION (properties) -->
                <dict>
                    <key>captures</key>
                    <dict>
                        <key>1</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.separator.property.twig</string>
                        </dict>
                        <key>2</key>
                        <dict>
                            <key>name</key>
                            <string>variable.other.property.twig</string>
                        </dict>
                    </dict>
                    <key>match</key>
                    <string>(?x)
                        (?&lt;=[a-zA-Z0-9_\x{7f}-\x{ff}])
                        (\.)([a-zA-Z_\x{7f}-\x{ff}][a-zA-Z0-9_\x{7f}-\x{ff}]*)
                        (?=[\.\s\|\[\)\]\}:,])
                    </string>
                </dict>

                <!-- DOT NOTATION (methods) -->
                <dict>
                    <key>begin</key>
                    <string>(?x)
                        (?&lt;=[a-zA-Z0-9_\x{7f}-\x{ff}])
                        (\.)([a-zA-Z_\x{7f}-\x{ff}][a-zA-Z0-9_\x{7f}-\x{ff}]*)
                        (\()
                    </string>
                    <key>beginCaptures</key>
                    <dict>
                        <key>1</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.separator.property.twig</string>
                        </dict>
                        <key>2</key>
                        <dict>
                            <key>name</key>
                            <string>variable.other.property.twig</string>
                        </dict>
                        <key>3</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.definition.parameters.begin.twig</string>
                        </dict>
                    </dict>
                    <key>end</key>
                    <string>\)</string>
                    <key>endCaptures</key>
                    <dict>
                        <key>0</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.definition.parameters.end.twig</string>
                        </dict>
                    </dict>
                    <key>patterns</key>
                    <array>
                        <dict>
                            <key>include</key>
                            <string>#twig-constants</string>
                        </dict>
                        <dict>
                            <key>include</key>
                            <string>#twig-functions-warg</string>
                        </dict>
                        <dict>
                            <key>include</key>
                            <string>#twig-functions</string>
                        </dict>
                        <dict>
                            <key>include</key>
                            <string>#twig-macros</string>
                        </dict>
                        <dict>
                            <key>include</key>
                            <string>#twig-objects</string>
                        </dict>
                        <dict>
                            <key>include</key>
                            <string>#twig-properties</string>
                        </dict>
                        <dict>
                            <key>include</key>
                            <string>#twig-filters-warg</string>
                        </dict>
                        <dict>
                            <key>include</key>
                            <string>#twig-filters</string>
                        </dict>
                        <dict>
                            <key>include</key>
                            <string>#twig-filters-warg-ud</string>
                        </dict>
                        <dict>
                            <key>include</key>
                            <string>#twig-filters-ud</string>
                        </dict>
                        <dict>
                            <key>include</key>
                            <string>#twig-strings</string>
                        </dict>
                        <dict>
                            <key>include</key>
                            <string>#twig-arrays</string>
                        </dict>
                    </array>
                    <key>contentName</key>
                    <string>meta.function.arguments.twig</string>
                </dict>

                <!-- ARRAY NOTATION -->
                <dict>
                    <key>captures</key>
                    <dict>
                        <key>1</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.section.array.begin.twig</string>
                        </dict>
                        <key>2</key>
                        <dict>
                            <key>name</key>
                            <string>variable.other.property.twig</string>
                        </dict>
                        <key>3</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.section.array.end.twig</string>
                        </dict>
                        <key>4</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.section.array.begin.twig</string>
                        </dict>
                        <key>5</key>
                        <dict>
                            <key>name</key>
                            <string>variable.other.property.twig</string>
                        </dict>
                        <key>6</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.section.array.end.twig</string>
                        </dict>
                        <key>7</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.section.array.begin.twig</string>
                        </dict>
                        <key>8</key>
                        <dict>
                            <key>name</key>
                            <string>variable.other.property.twig</string>
                        </dict>
                        <key>9</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.section.array.end.twig</string>
                        </dict>
                    </dict>
                    <key>match</key>
                    <string>(?x)
                        (?&lt;=[a-zA-Z0-9_\x{7f}-\x{ff}\]])
                        (?:
                            (\[)('[a-zA-Z_\x{7f}-\x{ff}][a-zA-Z0-9_\x{7f}-\x{ff}]*')(\])
                            |(\[)("[a-zA-Z_\x{7f}-\x{ff}][a-zA-Z0-9_\x{7f}-\x{ff}]*")(\])
                            |(\[)([a-zA-Z_\x{7f}-\x{ff}][a-zA-Z0-9_\x{7f}-\x{ff}]*)(\])
                        )
                    </string>
                </dict>
            </array>
        </dict>

        <!-- STRINGS -->
        <key>twig-strings</key>
        <dict>
            <key>patterns</key>
            <array>
                <!-- SINGLE QUOTED STRINGS -->
                <dict>
                    <key>begin</key>
                    <string>(?:(?&lt;!\\)|(?&lt;=\\\\))'</string>
                    <key>beginCaptures</key>
                    <dict>
                        <key>0</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.definition.string.begin.twig</string>
                        </dict>
                    </dict>
                    <key>end</key>
                    <string>(?:(?&lt;!\\)|(?&lt;=\\\\))'</string>
                    <key>endCaptures</key>
                    <dict>
                        <key>0</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.definition.string.end.twig</string>
                        </dict>
                    </dict>
                    <key>name</key>
                    <string>string.quoted.single.twig</string>
                </dict>

                <!-- DOUBLE QUOTED STRINGS -->
                <dict>
                    <key>begin</key>
                    <string>(?:(?&lt;!\\)|(?&lt;=\\\\))"</string>
                    <key>beginCaptures</key>
                    <dict>
                        <key>0</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.definition.string.begin.twig</string>
                        </dict>
                    </dict>
                    <key>end</key>
                    <string>(?:(?&lt;!\\)|(?&lt;=\\\\))"</string>
                    <key>endCaptures</key>
                    <dict>
                        <key>0</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.definition.string.end.twig</string>
                        </dict>
                    </dict>
                    <key>name</key>
                    <string>string.quoted.double.twig</string>
                </dict>
            </array>
        </dict>

        <!-- ARRAYS -->
        <key>twig-arrays</key>
        <dict>
            <key>begin</key>
            <string>(?&lt;=[\s\(\{\[:,])\[</string>
            <key>beginCaptures</key>
            <dict>
                <key>0</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.section.array.begin.twig</string>
                </dict>
            </dict>
            <key>end</key>
            <string>\]</string>
            <key>endCaptures</key>
            <dict>
                <key>0</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.section.array.end.twig</string>
                </dict>
            </dict>
            <key>patterns</key>
            <array>
                <dict>
                    <key>include</key>
                    <string>#twig-arrays</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-hashes</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-constants</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-operators</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-strings</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-functions-warg</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-functions</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-macros</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-objects</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-properties</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters-warg</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters-warg-ud</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters-ud</string>
                </dict>
                <dict>
                    <key>match</key>
                    <string>,</string>
                    <key>name</key>
                    <string>punctuation.separator.object.twig</string>
                </dict>
            </array>
            <key>name</key>
            <string>meta.array.twig</string>
        </dict>

        <!-- HASHES -->
        <key>twig-hashes</key>
        <dict>
            <key>begin</key>
            <string>(?&lt;=[\s\(\{\[:,])\{</string>
            <key>beginCaptures</key>
            <dict>
                <key>0</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.section.hash.begin.twig</string>
                </dict>
            </dict>
            <key>end</key>
            <string>\}</string>
            <key>endCaptures</key>
            <dict>
                <key>0</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.section.hash.end.twig</string>
                </dict>
            </dict>
            <key>patterns</key>
            <array>
                <dict>
                    <key>include</key>
                    <string>#twig-hashes</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-arrays</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-constants</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-operators</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-strings</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-functions-warg</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-functions</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-macros</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-objects</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-properties</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters-warg</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters-warg-ud</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters-ud</string>
                </dict>
                <dict>
                    <key>match</key>
                    <string>:</string>
                    <key>name</key>
                    <string>punctuation.separator.key-value.twig</string>
                </dict>
                <dict>
                    <key>match</key>
                    <string>,</string>
                    <key>name</key>
                    <string>punctuation.separator.object.twig</string>
                </dict>
            </array>
            <key>name</key>
            <string>meta.hash.twig</string>
        </dict>

        <!-- KEYWORDS -->
        <key>twig-keywords</key>
        <dict>
            <key>match</key>
            <string>(?&lt;=\s)((?:end)?(?:autoescape|block|embed|filter|for|if|macro|raw|sandbox|set|spaceless|trans|verbatim)|as|do|else|elseif|extends|flush|from|ignore missing|import|include|only|use|with)(?=\s)</string>
            <key>name</key>
            <string>keyword.control.twig</string>
        </dict>

        <!-- FUNCTIONS (w/ arguments) -->
        <key>twig-functions-warg</key>
        <dict>
            <key>begin</key>
            <string>(?&lt;=[\s\(\[\{:,])(attribute|block|constant|cycle|date|divisible by|dump|include|max|min|parent|random|range|same as|source|template_from_string)(\()</string>
            <key>beginCaptures</key>
            <dict>
                <key>1</key>
                <dict>
                    <key>name</key>
                    <string>support.function.twig</string>
                </dict>
                <key>2</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.parameters.begin.twig</string>
                </dict>
            </dict>
            <key>end</key>
            <string>\)</string>
            <key>endCaptures</key>
            <dict>
                <key>0</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.parameters.end.twig</string>
                </dict>
            </dict>
            <key>patterns</key>
            <array>
                <dict>
                    <key>include</key>
                    <string>#twig-constants</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-functions-warg</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-functions</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-macros</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-objects</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-properties</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters-warg</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters-warg-ud</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters-ud</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-strings</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-arrays</string>
                </dict>
            </array>
            <key>contentName</key>
            <string>meta.function.arguments.twig</string>
        </dict>

        <!-- FUNCTIONS -->
        <key>twig-functions</key>
        <dict>
            <key>captures</key>
            <dict>
                <key>1</key>
                <dict>
                    <key>name</key>
                    <string>support.function.twig</string>
                </dict>
            </dict>
            <key>match</key>
            <string>(?&lt;=is\s)(defined|empty|even|iterable|odd)</string>
        </dict>

        <!-- MACROS -->
        <key>twig-macros</key>
        <dict>
            <key>begin</key>
            <string>(?x)
                    (?&lt;=[\s\(\[\{:,])
                    ([a-zA-Z_\x{7f}-\x{ff}][a-zA-Z0-9_\x{7f}-\x{ff}]*)
                    (?:
                        (\.)([a-zA-Z_\x{7f}-\x{ff}][a-zA-Z0-9_\x{7f}-\x{ff}]*)
                    )?
                    (\()
            </string>
            <key>beginCaptures</key>
            <dict>
                <key>1</key>
                <dict>
                    <key>name</key>
                    <string>meta.function-call.twig</string>
                </dict>
                <key>2</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.separator.property.twig</string>
                </dict>
                <key>3</key>
                <dict>
                    <key>name</key>
                    <string>variable.other.property.twig</string>
                </dict>
                <key>4</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.parameters.begin.twig</string>
                </dict>
            </dict>
            <key>end</key>
            <string>\)</string>
            <key>endCaptures</key>
            <dict>
                <key>0</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.parameters.end.twig</string>
                </dict>
            </dict>
            <key>patterns</key>
            <array>
                <dict>
                    <key>include</key>
                    <string>#twig-constants</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-operators</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-functions-warg</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-functions</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-macros</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-objects</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-properties</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters-warg</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters-warg-ud</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters-ud</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-strings</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-arrays</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-hashes</string>
                </dict>
            </array>
            <key>contentName</key>
            <string>meta.function.arguments.twig</string>

        </dict>

        <!-- FILTERS (w/ arguments) -->
        <key>twig-filters-warg</key>
        <dict>
            <key>begin</key>
            <string>(?&lt;=(?:[a-zA-Z0-9_\x{7f}-\x{ff}\]\)\'\"]\|)|\{%\sfilter\s)(batch|convert_encoding|date|date_modify|default|e(?:scape)?|format|join|merge|number_format|replace|round|slice|split|trim)(\()</string>
            <key>beginCaptures</key>
            <dict>
                <key>1</key>
                <dict>
                    <key>name</key>
                    <string>support.function.twig</string>
                </dict>
                <key>2</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.parameters.begin.twig</string>
                </dict>
            </dict>
            <key>end</key>
            <string>\)</string>
            <key>endCaptures</key>
            <dict>
                <key>0</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.parameters.end.twig</string>
                </dict>
            </dict>
            <key>patterns</key>
            <array>
                <dict>
                    <key>include</key>
                    <string>#twig-constants</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-operators</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-functions-warg</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-functions</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-macros</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-objects</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-properties</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters-warg</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters-warg-ud</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters-ud</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-strings</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-arrays</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-hashes</string>
                </dict>
            </array>
            <key>contentName</key>
            <string>meta.function.arguments.twig</string>
        </dict>

        <!-- FILTERS -->
        <key>twig-filters</key>
        <dict>
            <key>captures</key>
            <dict>
                <key>1</key>
                <dict>
                    <key>name</key>
                    <string>support.function.twig</string>
                </dict>
            </dict>
            <key>match</key>
            <string>(?&lt;=(?:[a-zA-Z0-9_\x{7f}-\x{ff}\]\)\'\"]\|)|\{%\sfilter\s)(abs|capitalize|e(?:scape)?|first|join|(?:json|url)_encode|keys|last|length|lower|nl2br|number_format|raw|reverse|round|sort|striptags|title|trim|upper)(?=[\s\|\]\}\):,]|\.\.|\*\*)</string>
        </dict>

        <!-- FILTERS (w/ arguments & user-defined) -->
        <key>twig-filters-warg-ud</key>
        <dict>
            <key>begin</key>
            <string>(?&lt;=(?:[a-zA-Z0-9_\x{7f}-\x{ff}\]\)\'\"]\|)|\{%\sfilter\s)([a-zA-Z_\x{7f}-\x{ff}][a-zA-Z0-9_\x{7f}-\x{ff}]*)(\()</string>
            <key>beginCaptures</key>
            <dict>
                <key>1</key>
                <dict>
                    <key>name</key>
                    <string>meta.function-call.other.twig</string>
                </dict>
                <key>2</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.parameters.begin.twig</string>
                </dict>
            </dict>
            <key>end</key>
            <string>\)</string>
            <key>endCaptures</key>
            <dict>
                <key>0</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.parameters.end.twig</string>
                </dict>
            </dict>
            <key>patterns</key>
            <array>
                <dict>
                    <key>include</key>
                    <string>#twig-constants</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-functions-warg</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-functions</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-macros</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-objects</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-properties</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters-warg</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters-warg-ud</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-filters-ud</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-strings</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-arrays</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#twig-hashes</string>
                </dict>
            </array>
            <key>contentName</key>
            <string>meta.function.arguments.twig</string>
        </dict>

        <!-- FILTERS (user-defined) -->
        <key>twig-filters-ud</key>
        <dict>
            <key>captures</key>
            <dict>
                <key>1</key>
                <dict>
                    <key>name</key>
                    <string>meta.function-call.other.twig</string>
                </dict>
            </dict>
            <key>match</key>
            <string>(?&lt;=(?:[a-zA-Z0-9_\x{7f}-\x{ff}\]\)\'\"]\|)|\{%\sfilter\s)([a-zA-Z_\x{7f}-\x{ff}][a-zA-Z0-9_\x{7f}-\x{ff}]*)</string>
        </dict>

        <!-- TWIG END -->

        <key>php</key>
        <dict>
            <key>begin</key>
            <string>(?=(^\s*)?&lt;\?)</string>
            <key>end</key>
            <string>(?!(^\s*)?&lt;\?)</string>
            <key>patterns</key>
            <array>
                <dict>
                    <key>include</key>
                    <string>source.php</string>
                </dict>
            </array>
        </dict>
        <key>python</key>
        <dict>
            <key>begin</key>
            <string>(?:^\s*)&lt;\?python(?!.*\?&gt;)</string>
            <key>end</key>
            <string>\?&gt;(?:\s*$\n)?</string>
            <key>name</key>
            <string>source.python.embedded.html</string>
            <key>patterns</key>
            <array>
                <dict>
                    <key>include</key>
                    <string>source.python</string>
                </dict>
            </array>
        </dict>
        <key>ruby</key>
        <dict>
            <key>patterns</key>
            <array>
                <dict>
                    <key>begin</key>
                    <string>&lt;%+#</string>
                    <key>captures</key>
                    <dict>
                        <key>0</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.definition.comment.erb</string>
                        </dict>
                    </dict>
                    <key>end</key>
                    <string>%&gt;</string>
                    <key>name</key>
                    <string>comment.block.erb</string>
                </dict>
                <dict>
                    <key>begin</key>
                    <string>&lt;%+(?!&gt;)=?</string>
                    <key>captures</key>
                    <dict>
                        <key>0</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.section.embedded.ruby</string>
                        </dict>
                    </dict>
                    <key>end</key>
                    <string>-?%&gt;</string>
                    <key>name</key>
                    <string>source.ruby.embedded.html</string>
                    <key>patterns</key>
                    <array>
                        <dict>
                            <key>captures</key>
                            <dict>
                                <key>1</key>
                                <dict>
                                    <key>name</key>
                                    <string>punctuation.definition.comment.ruby</string>
                                </dict>
                            </dict>
                            <key>match</key>
                            <string>(#).*?(?=-?%&gt;)</string>
                            <key>name</key>
                            <string>comment.line.number-sign.ruby</string>
                        </dict>
                        <dict>
                            <key>include</key>
                            <string>source.ruby</string>
                        </dict>
                    </array>
                </dict>
                <dict>
                    <key>begin</key>
                    <string>&lt;\?r(?!&gt;)=?</string>
                    <key>captures</key>
                    <dict>
                        <key>0</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.section.embedded.ruby.nitro</string>
                        </dict>
                    </dict>
                    <key>end</key>
                    <string>-?\?&gt;</string>
                    <key>name</key>
                    <string>source.ruby.nitro.embedded.html</string>
                    <key>patterns</key>
                    <array>
                        <dict>
                            <key>captures</key>
                            <dict>
                                <key>1</key>
                                <dict>
                                    <key>name</key>
                                    <string>punctuation.definition.comment.ruby.nitro</string>
                                </dict>
                            </dict>
                            <key>match</key>
                            <string>(#).*?(?=-?\?&gt;)</string>
                            <key>name</key>
                            <string>comment.line.number-sign.ruby.nitro</string>
                        </dict>
                        <dict>
                            <key>include</key>
                            <string>source.ruby</string>
                        </dict>
                    </array>
                </dict>
            </array>
        </dict>
        <key>string-double-quoted</key>
        <dict>
            <key>begin</key>
            <string>"</string>
            <key>beginCaptures</key>
            <dict>
                <key>0</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.string.begin.html</string>
                </dict>
            </dict>
            <key>end</key>
            <string>"</string>
            <key>endCaptures</key>
            <dict>
                <key>0</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.string.end.html</string>
                </dict>
            </dict>
            <key>name</key>
            <string>string.quoted.double.html</string>
            <key>patterns</key>
            <array>
                <dict>
                    <key>include</key>
                    <string>#embedded-code</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#entities</string>
                </dict>
            </array>
        </dict>
        <key>string-single-quoted</key>
        <dict>
            <key>begin</key>
            <string>'</string>
            <key>beginCaptures</key>
            <dict>
                <key>0</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.string.begin.html</string>
                </dict>
            </dict>
            <key>end</key>
            <string>'</string>
            <key>endCaptures</key>
            <dict>
                <key>0</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.definition.string.end.html</string>
                </dict>
            </dict>
            <key>name</key>
            <string>string.quoted.single.html</string>
            <key>patterns</key>
            <array>
                <dict>
                    <key>include</key>
                    <string>#embedded-code</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#entities</string>
                </dict>
            </array>
        </dict>
        <key>tag-generic-attribute</key>
        <dict>
            <key>match</key>
            <string>\b([a-zA-Z\-:]+)</string>
            <key>name</key>
            <string>entity.other.attribute-name.html</string>
        </dict>
        <key>tag-id-attribute</key>
        <dict>
            <key>begin</key>
            <string>\b(id)\b\s*(=)</string>
            <key>captures</key>
            <dict>
                <key>1</key>
                <dict>
                    <key>name</key>
                    <string>entity.other.attribute-name.id.html</string>
                </dict>
                <key>2</key>
                <dict>
                    <key>name</key>
                    <string>punctuation.separator.key-value.html</string>
                </dict>
            </dict>
            <key>end</key>
            <string>(?&lt;='|")</string>
            <key>name</key>
            <string>meta.attribute-with-value.id.html</string>
            <key>patterns</key>
            <array>
                <dict>
                    <key>begin</key>
                    <string>"</string>
                    <key>beginCaptures</key>
                    <dict>
                        <key>0</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.definition.string.begin.html</string>
                        </dict>
                    </dict>
                    <key>contentName</key>
                    <string>meta.toc-list.id.html</string>
                    <key>end</key>
                    <string>"</string>
                    <key>endCaptures</key>
                    <dict>
                        <key>0</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.definition.string.end.html</string>
                        </dict>
                    </dict>
                    <key>name</key>
                    <string>string.quoted.double.html</string>
                    <key>patterns</key>
                    <array>
                        <dict>
                            <key>include</key>
                            <string>#embedded-code</string>
                        </dict>
                        <dict>
                            <key>include</key>
                            <string>#entities</string>
                        </dict>
                    </array>
                </dict>
                <dict>
                    <key>begin</key>
                    <string>'</string>
                    <key>beginCaptures</key>
                    <dict>
                        <key>0</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.definition.string.begin.html</string>
                        </dict>
                    </dict>
                    <key>contentName</key>
                    <string>meta.toc-list.id.html</string>
                    <key>end</key>
                    <string>'</string>
                    <key>endCaptures</key>
                    <dict>
                        <key>0</key>
                        <dict>
                            <key>name</key>
                            <string>punctuation.definition.string.end.html</string>
                        </dict>
                    </dict>
                    <key>name</key>
                    <string>string.quoted.single.html</string>
                    <key>patterns</key>
                    <array>
                        <dict>
                            <key>include</key>
                            <string>#embedded-code</string>
                        </dict>
                        <dict>
                            <key>include</key>
                            <string>#entities</string>
                        </dict>
                    </array>
                </dict>
            </array>
        </dict>
        <key>tag-stuff</key>
        <dict>
            <key>patterns</key>
            <array>
                <dict>
                    <key>include</key>
                    <string>#tag-id-attribute</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#tag-generic-attribute</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#string-double-quoted</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#string-single-quoted</string>
                </dict>
                <dict>
                    <key>include</key>
                    <string>#embedded-code</string>
                </dict>
            </array>
        </dict>
    </dict>
    <key>scopeName</key>
    <string>text.html.twig</string>
    <key>uuid</key>
    <string>C220B028-86FF-44CB-8A59-27937FC83730</string>
</dict>
</plist>
