<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Resources/LICENSE.txt</key>
		<data>
		K4uBUimqimHkg/tLoFiLi2xJGJA=
		</data>
		<key>Resources/NOTICE.txt</key>
		<data>
		q2viEXtQ+hl948q8V/Sy3saK0is=
		</data>
		<key>Resources/build.txt</key>
		<data>
		FbZnfKXROQ6zmEuywdbMoPO+0xs=
		</data>
		<key>Resources/en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			E3gk0IqpErOQM/by383+iQ+nvfg=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/idea.icns</key>
		<data>
		GMGL4i86POQQ5zuxFVn+XiMtEZ8=
		</data>
		<key>Resources/ja.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			QZ5W2MZuGpyBRSmBWVWBpHJ4bEk=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			yVbuQvY1evU8A6fG9Q1YuGcMAQE=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/product-info.json</key>
		<data>
		tjXDGaM1QFO5H74IyAsDID16aXI=
		</data>
		<key>Resources/zh-Hans.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7vZkhiGsJQ86BRltYqSh7ZiZEPI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh-Hant.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash</key>
			<data>
			7vZkhiGsJQ86BRltYqSh7ZiZEPI=
			</data>
			<key>optional</key>
			<true/>
		</dict>
	</dict>
	<key>files2</key>
	<dict>
		<key>Resources/LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>Resources/NOTICE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			g5A6vevjs92hoXj7PwOsce8xxFA7eSJrZu8n8aqdOZM=
			</data>
		</dict>
		<key>Resources/build.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			Z0CBaCPfPBYccYULXgSxcSqqmqQxUZtZ7rZkvGCPm/k=
			</data>
		</dict>
		<key>Resources/en.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			G89ZBVrEV1LRFi/PtLAG7+1BfUJsryz0cbONRyn8H4w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/idea.icns</key>
		<dict>
			<key>hash2</key>
			<data>
			HsAvnSlT5hyy7Mq3zDD0Cv1DS2z0KIq6njW5yfsB9Mw=
			</data>
		</dict>
		<key>Resources/ja.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			AJstW4fRS7nl/D1gVWgKrVrB+f5nE/qz/kWGhXGmK7w=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/ko.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			QKcFNVXo5RjdjTEvan9zLKB8mh/04SlKMXfXwIPe/f0=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/product-info.json</key>
		<dict>
			<key>hash2</key>
			<data>
			O102uNU+Bh7jt24ADCSceQ+6xdfh1B0vvlduM9laU4k=
			</data>
		</dict>
		<key>Resources/zh-Hans.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			wnoVbfcHkY0DTUIeKvk9//MJLqUWqXAglNA2PP/XanU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>Resources/zh-Hant.lproj/InfoPlist.strings</key>
		<dict>
			<key>hash2</key>
			<data>
			wnoVbfcHkY0DTUIeKvk9//MJLqUWqXAglNA2PP/XanU=
			</data>
			<key>optional</key>
			<true/>
		</dict>
		<key>bin/appletviewer.policy</key>
		<dict>
			<key>hash2</key>
			<data>
			yWE9KMzktuoZ5E1tRiDwABOPcC4FU7CZ03kxsYCNSVc=
			</data>
		</dict>
		<key>bin/brokenPlugins.db</key>
		<dict>
			<key>hash2</key>
			<data>
			eDm+iYN5hUxF9mPhWEeOf+EN1oaVLr6DjJuXlReMlDg=
			</data>
		</dict>
		<key>bin/format.sh</key>
		<dict>
			<key>hash2</key>
			<data>
			QqydxK7884cVFCjDyiqkB8kIM28l54uO3asYBFsaPCw=
			</data>
		</dict>
		<key>bin/fsnotifier</key>
		<dict>
			<key>hash2</key>
			<data>
			a/rhVRGqHnF6a9EdV+xvi3/2CTUZxVq7aVYFV+tuG14=
			</data>
		</dict>
		<key>bin/idea.properties</key>
		<dict>
			<key>hash2</key>
			<data>
			eWzXZfnEc362KXrsl9RZAXQUHDzv9ZGHC+RGkUlAIrc=
			</data>
		</dict>
		<key>bin/idea.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			Y86TH++SgXSh+yBKlE74vOZZwP095+MlY7yIGggGiaw=
			</data>
		</dict>
		<key>bin/idea.vmoptions</key>
		<dict>
			<key>hash2</key>
			<data>
			Th+AZ/a8J1ddE/hZa/s9MqWgpwij2QGpdbVoChH9Y4s=
			</data>
		</dict>
		<key>bin/inspect.sh</key>
		<dict>
			<key>hash2</key>
			<data>
			lxjud3YB/DsvA+DPwzAQEwJBI9qdehhlQvv2j2A9Mwk=
			</data>
		</dict>
		<key>bin/jetbrains_client.vmoptions</key>
		<dict>
			<key>hash2</key>
			<data>
			Jngmli4iJ/+lOFX6tCK6Af3NIrhGImiBz/9mntDVbP0=
			</data>
		</dict>
		<key>bin/libmacscreenmenu64.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			n+gqgikIREH7C0S8vMM5eYMq+2Zuqz0FmtGT/kZw680=
			</data>
		</dict>
		<key>bin/libnst64.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			uBtUboz9nc4F4va18FW9QYZ19TxMOSboJX7lCWV0J/w=
			</data>
		</dict>
		<key>bin/ltedit.sh</key>
		<dict>
			<key>hash2</key>
			<data>
			do2BAAfPDX0l4j6D28//2tBD62bh9UG/ZiHDDjXZBSU=
			</data>
		</dict>
		<key>bin/printenv</key>
		<dict>
			<key>hash2</key>
			<data>
			DRaEM//hlK7zoIiI3aE1hxTKtIFXmuWgF1CsUrOjjiE=
			</data>
		</dict>
		<key>bin/restarter</key>
		<dict>
			<key>hash2</key>
			<data>
			rTBfsF6XK5xumQLpM8UGaNeE9T9w32dAzew059uQnYs=
			</data>
		</dict>
		<key>jbr/Contents/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			QY2IIN1x2qZd+OVwfibhJhiL/MhH4Zc3OykQ8dwnxng=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Chromium Embedded Framework</key>
		<dict>
			<key>hash2</key>
			<data>
			/WS2v2JHQZ49OL2brVTns56vdkagn2Fc3MuOnLyh+q8=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Libraries/libEGL.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			HSr69GGivqbx+bFYEIUbqlAqAXUuryEwEFM9BoNTY6Y=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Libraries/libGLESv2.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			aqck7fgfXKTCmRSR08d+87pBt9SZV48p7UbHjC3/KPs=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Libraries/libvk_swiftshader.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			usAS/oq+9y08ylpYKgQ8IFq7twDMyYlQBapMF3/ki+E=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Libraries/vk_swiftshader_icd.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1xfZFeMefCeUi4CzarNOLYl4iBFMXH0K+DX5PrU+WPU=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			b/yvyDTEKhhmH0frDueOWf9dXPRbPKqdckDqhZ3U/88=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/af.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			qip2o7dfhJ1UFtt5y399sZDlmYEMSYnxTuPZrmdmjCo=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/am.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			1ae1Bk1AjCb6HJhpvUez5DVp/qThFZSuiBT+soET7to=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/ar.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			b3g5RS2nr+PY8a9x3p75nAR7KE6LcWrCdWysKGyZSbw=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/bg.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			J0NpahFIy3uj/yjZqE/TO2hnEJnX+zzq2w0iapejcMc=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/bn.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			w98u2VGaEM6k/dYm8oFrWmfpACjYaPk3D20aqso2ZJ8=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/ca.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			ztBQApUJ4WSiJkJueHSvxLp5Y6xyA2zNTxwO5PDZSqc=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/chrome_100_percent.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			fKXiigc7JTxon95TyNLVSE4JKAnAjGkxmScbsJmQfv4=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/chrome_200_percent.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			B7VmvrQqaA/hgqL3sHrNVMG/JpruKK6gWoDpmPG2IdU=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/cs.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			w+4BZ3DFoLMQA+ZBFT9XW5K41OcFaXvMy2IAW9DPLjE=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/da.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			r1a44QIh0qHVVaxZG7KWgYVJ/BlLf+DEYzjPOPkeJXA=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/de.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			k5A2HBZBaGO04H92JQx5u7i4Z4RVZsjWx0K4bmBMSjg=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/el.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			Wmc8wbmsg0Yi2q/T5t+TBa7so8lYhxWjio4/3HRUcU4=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/en.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			K1FGfX8J/Bg4KlJG3u0z0pcGgVaGrjPeGHVxOb+vjqg=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/en_GB.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			PxFIRLy+wWlx1/3VO51b8ZC7WjurkN8VjxCsdFbaYrc=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/es.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			d01Qtwb5kvg7VVCXaxpVtjVUTymGYC2ivU2rnUmtsO0=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/es_419.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			/N2ENBylTVM2dCfHk7Nhd44Wd5WE5N7gA2/YSkMgDao=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/et.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			kHmcDFl5BYi6JuC8YAdGWk60Ytw4Z8wZU+at3AwqdCg=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/fa.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			RUX98PtBun8f4+jfqcupIjW7tcgI4Hm4FynGcwQD4No=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/fi.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			q2Udy+/chEWJQdcYRACWM92ngJa6O3guLqJkiRliaGs=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/fil.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			m1xetgfMQwueJD3mFtQ+qf2UtR3dfXnABTaIoqPJ+gI=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/fr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			ZGzBgpGZznnbar+8nlB4y7kxnAqdQsC3YmYh8fPeLos=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/gpu_shader_cache.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			0/w6J8HZplH11HDg9u8uBglOxa1YxotJeyKzEkQv240=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/gu.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			SdDvKf/yfDGW1GNM1R53Zh29qLxnbzs9ZsfmVwK7Txo=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/he.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			utSo9xOSxBcS0nPBpfPhSgVOPoIqscY1qCRMgza+Foo=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/hi.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			sWv8dyfnTwx+/TgRvZA35ctHoEX9i/aOwhCrZQDfDV4=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/hr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			wZdmZ55cdZI/KM4n/k92D8g+laO0UZ+SDXjouFZW3OY=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/hu.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			McB4Ql2PvHTbGwtNf07eqOhXsWUGp5E+L8gprNsa3To=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/icudtl.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			9RcKorOI0jvr+YeE3UiKm8t0FHA4SmqajXomONdo3vs=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/id.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			XjMozNgZR5HpV9bj60WzwcJ8I3ZsHbND/opWTM0/Kcc=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/it.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			Gz3jN8XlcZm1Zqdt2//WC+196t+TlEFGcS3UD2FKLiI=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/ja.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			AsUaJNzkNB4T9Itzpg6esGAmdjX5nOVSzDggfFpbSio=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/kn.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			+uyiyElvaxrHO6H1zTfHCu9q7knBX0ALrFw0naxxKL4=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/ko.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			+GS0puq3LsOSba1gicVVDfaGi3D3y5Pp2teCJp8dNPQ=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/lt.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			ppQXN9oOffho3PA3xif8zT5Z4xDxfOoY/s3ROt/DcTU=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/lv.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			cgW52cmx9STQElz+6f8jJCLeRA+2tYnKKIczdeWxpGI=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/ml.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			WmHwy9MgpZoKoVlEijT8qjbfyVfrD8zXUwfD4/hclCo=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/mr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			duzxyeONP7qWbzOANqASaR5Kpt+aNxJfpF/fIpz80MA=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/ms.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			Kic5MfLLATTmoVTIxgLGk8MgIAP0LIvxckZQ9lkyUZo=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/nb.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			37feIoUj1L4J53YiMDjgcIDmin5oNnxhVHHLbmQEc88=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/nl.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			nHuPSOunAgfN0TQbfsLseOInw37SBzGzP3vqajRiUYk=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/pl.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			9VLawM4hhXsC5kw5kUtJ4etrGTh9xlwzPvH3mKXjJQY=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/pt_BR.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			jIksYiTGTWZOLdyxBAYZ1XRdTUtVymMbiv7HZIDpUuM=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/pt_PT.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			hyeGd0RNbxnGI/cQ5ndV8d71/t/0//9Cd2AqC3Tu6Vs=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/resources.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			tidifc0f7sCYENMITpK0d1JtkSb7/YDB9dL9tGwueMk=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/ro.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			xJxYe5OKQ6t5Mmw8wjAX+m87FbGTm6iM2A4cxptJj7w=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/ru.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			C6kR6CHjLTprNipeJ5ja0R5t3PTf9NG0pq1jUYcP9m4=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/sk.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			ZNmL7slOgeoUISWgRlx3CjhMOMwCFciIfpJkD5SpSAQ=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/sl.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			9D4Dm5Nb/fikBxTjF5oTVVaBut5z9TIwfyT7SanoEkY=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/snapshot_blob.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			tDjNcJBkfDgeAVv2Sy6+w6vjinr7Z6XipIuuvVokd1w=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/sr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			LkRESJ2XuKAruoPz80K5TdCozHpt7qSpBT1KwZwcVCY=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/sv.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			30kJTggBbBixJQAhoTOpXPHsV4vAR/1F4T5KnmPtSy4=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/sw.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			ZP5/1uZpUOlaWfBGgIyYClmxwmSyMHgn5/CS8iUTibc=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/ta.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			zxL/9zxt8qn2EEkDzrxmvO8sgZ9YsV+bfdmI+8fzxLg=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/te.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			rxIoUGgCdi1SmE+KOHAqfe/JqdbHHeb0+jAjJ4hW5CM=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/th.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			RgT9S3YHeJ/Cdzp6DIXj+0DQ8KuLeVz6JQWCKQZFC9Y=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/tr.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			r5Q97BxnDZ+uXHhYp0K+B2NiImAPUGtPI1+bLdaeAiU=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/uk.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			GjczSPK8N6tZ21jeTBEDzg16HkTHfKN4nO5N7Mkc8mo=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/ur.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			6RmDZn/2hjshdKUrL0ejpLEwU2ic6K2OOtmkjzF7qj0=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/v8_context_snapshot.arm64.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			ZKWjQ1sXAlXbwrPi0inr2jQyhty0gavQjLa6h+mw294=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/vi.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			XHaCKpA6khFdw42sRMe3ktPjCR22cfFkSSgKKUGW8FA=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/zh_CN.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			+Z2tOmZ9WJYpCsjtFems+3Deo6d2lyc10NAzCilJU/I=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/Resources/zh_TW.lproj/locale.pak</key>
		<dict>
			<key>hash2</key>
			<data>
			tqMGXJSr2RWa4yjkjBDF0lNCaJA/1pWd84hmLCyiQFg=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/Chromium Embedded Framework.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			IpyTBSpQ4nzsAewl0/FimqBf4A7Uu8IbpoR6N5Kb/PY=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/cef_server.app/Contents/Frameworks/cef_server Helper (Alerts).app/Contents/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			YwaEfndG9t4bkvnYl9Mjozz1nHqV2X6W0/KO/t2aaz0=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/cef_server.app/Contents/Frameworks/cef_server Helper (Alerts).app/Contents/MacOS/cef_server Helper (Alerts)</key>
		<dict>
			<key>hash2</key>
			<data>
			YzC9z0IoRmgn3Q56D+vSQ7MkPmrZWdf3zunvhMHvmTA=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/cef_server.app/Contents/Frameworks/cef_server Helper (Alerts).app/Contents/PkgInfo</key>
		<dict>
			<key>hash2</key>
			<data>
			glAhkclISwTWhTdPmHmgBmBpxJuKyuegSwHTjQfo7KA=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/cef_server.app/Contents/Frameworks/cef_server Helper (Alerts).app/Contents/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			ZobeEKKKL+EbNsu4bcusyCfPxOoRa02r8YReWu5inps=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/cef_server.app/Contents/Frameworks/cef_server Helper (GPU).app/Contents/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Hb/MoUC98Q782mzi5KthNwcYRzo1FSw/4IjGnbTXgvU=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/cef_server.app/Contents/Frameworks/cef_server Helper (GPU).app/Contents/MacOS/cef_server Helper (GPU)</key>
		<dict>
			<key>hash2</key>
			<data>
			a93ZzPHmsY9jM6D7i+gmry/AKxsZ8EGihHie9MR4aT8=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/cef_server.app/Contents/Frameworks/cef_server Helper (GPU).app/Contents/PkgInfo</key>
		<dict>
			<key>hash2</key>
			<data>
			glAhkclISwTWhTdPmHmgBmBpxJuKyuegSwHTjQfo7KA=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/cef_server.app/Contents/Frameworks/cef_server Helper (GPU).app/Contents/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			ZobeEKKKL+EbNsu4bcusyCfPxOoRa02r8YReWu5inps=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/cef_server.app/Contents/Frameworks/cef_server Helper (Plugin).app/Contents/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			4bWeyORYBpdHIrIM/MOmStFLkawFW9hto+08/IP2O98=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/cef_server.app/Contents/Frameworks/cef_server Helper (Plugin).app/Contents/MacOS/cef_server Helper (Plugin)</key>
		<dict>
			<key>hash2</key>
			<data>
			Wv6ZZjwoQzvaWGdE7ojTfI1CG6w/P973gsp/J3y1nP4=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/cef_server.app/Contents/Frameworks/cef_server Helper (Plugin).app/Contents/PkgInfo</key>
		<dict>
			<key>hash2</key>
			<data>
			glAhkclISwTWhTdPmHmgBmBpxJuKyuegSwHTjQfo7KA=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/cef_server.app/Contents/Frameworks/cef_server Helper (Plugin).app/Contents/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			ZobeEKKKL+EbNsu4bcusyCfPxOoRa02r8YReWu5inps=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/cef_server.app/Contents/Frameworks/cef_server Helper (Renderer).app/Contents/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			DLCuSXu/fEaEaCdoDBrHHB84RoqZaYzdQlmHtZfdMzQ=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/cef_server.app/Contents/Frameworks/cef_server Helper (Renderer).app/Contents/MacOS/cef_server Helper (Renderer)</key>
		<dict>
			<key>hash2</key>
			<data>
			8CzMJBd2NvVSD7AxgezKdQrGeTFVDtJ0sFN3D13bis8=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/cef_server.app/Contents/Frameworks/cef_server Helper (Renderer).app/Contents/PkgInfo</key>
		<dict>
			<key>hash2</key>
			<data>
			glAhkclISwTWhTdPmHmgBmBpxJuKyuegSwHTjQfo7KA=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/cef_server.app/Contents/Frameworks/cef_server Helper (Renderer).app/Contents/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			ZobeEKKKL+EbNsu4bcusyCfPxOoRa02r8YReWu5inps=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/cef_server.app/Contents/Frameworks/cef_server Helper.app/Contents/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			WWlqOqJgAazPv9qfKIH5t1UeRiJIlw6YoAFg8F5eXFE=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/cef_server.app/Contents/Frameworks/cef_server Helper.app/Contents/MacOS/cef_server Helper</key>
		<dict>
			<key>hash2</key>
			<data>
			BlflAASkEgObxSTAKCWJa60T/1rbbIarO2oHoug9ZpE=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/cef_server.app/Contents/Frameworks/cef_server Helper.app/Contents/PkgInfo</key>
		<dict>
			<key>hash2</key>
			<data>
			glAhkclISwTWhTdPmHmgBmBpxJuKyuegSwHTjQfo7KA=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/cef_server.app/Contents/Frameworks/cef_server Helper.app/Contents/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			ZobeEKKKL+EbNsu4bcusyCfPxOoRa02r8YReWu5inps=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/cef_server.app/Contents/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			I+cuOjuKuCkziFG1Tr87T/PfHOpPrym83jpBvIGJFCo=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/cef_server.app/Contents/MacOS/cef_server</key>
		<dict>
			<key>hash2</key>
			<data>
			ZMiwKhxCvA+D6eyaV8CbViRvyXma7Bv1+a1dXwm6V9M=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/cef_server.app/Contents/PkgInfo</key>
		<dict>
			<key>hash2</key>
			<data>
			glAhkclISwTWhTdPmHmgBmBpxJuKyuegSwHTjQfo7KA=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/cef_server.app/Contents/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			lZPssydX7ZVP+aVL135GmHnPBmojUKZQNmvF6KGxwoA=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/jcef Helper (Alerts).app/Contents/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			iZB9d0LYAFb6sQs5pDOoQyI0xJF+j1G95dE701JFTdw=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/jcef Helper (Alerts).app/Contents/MacOS/jcef Helper (Alerts)</key>
		<dict>
			<key>hash2</key>
			<data>
			ZifSHKx48Qi4a9xwpWotpO/bfP3I/TFF/tLK8U6Mmho=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/jcef Helper (Alerts).app/Contents/PkgInfo</key>
		<dict>
			<key>hash2</key>
			<data>
			glAhkclISwTWhTdPmHmgBmBpxJuKyuegSwHTjQfo7KA=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/jcef Helper (Alerts).app/Contents/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			ZobeEKKKL+EbNsu4bcusyCfPxOoRa02r8YReWu5inps=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/jcef Helper (GPU).app/Contents/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			M+OSGDqtS+vL9G6RNqFDCng24iXrWynmD7+fxA+oxO0=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/jcef Helper (GPU).app/Contents/MacOS/jcef Helper (GPU)</key>
		<dict>
			<key>hash2</key>
			<data>
			4+B8vMLU8eDud+YO5KTcCSBBV9HJ+BkZawwRE4lUyMk=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/jcef Helper (GPU).app/Contents/PkgInfo</key>
		<dict>
			<key>hash2</key>
			<data>
			glAhkclISwTWhTdPmHmgBmBpxJuKyuegSwHTjQfo7KA=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/jcef Helper (GPU).app/Contents/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			ZobeEKKKL+EbNsu4bcusyCfPxOoRa02r8YReWu5inps=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/jcef Helper (Plugin).app/Contents/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			G8Gyjo2ARcKoC2TIfpB6korz52Dn3YxtGU0vtvLlOoY=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/jcef Helper (Plugin).app/Contents/MacOS/jcef Helper (Plugin)</key>
		<dict>
			<key>hash2</key>
			<data>
			Vbg+fjzpIXDoNVmAwid96MBQN35qXqpFUcG1FL1GqrM=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/jcef Helper (Plugin).app/Contents/PkgInfo</key>
		<dict>
			<key>hash2</key>
			<data>
			glAhkclISwTWhTdPmHmgBmBpxJuKyuegSwHTjQfo7KA=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/jcef Helper (Plugin).app/Contents/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			ZobeEKKKL+EbNsu4bcusyCfPxOoRa02r8YReWu5inps=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/jcef Helper (Renderer).app/Contents/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			snGf1gobr708AzmrLB+F72s1QOHnk3Lwh1fi4gQvvSU=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/jcef Helper (Renderer).app/Contents/MacOS/jcef Helper (Renderer)</key>
		<dict>
			<key>hash2</key>
			<data>
			qP4VcSHp+gJxqRADFZ/7Iw5czDAiej0OJv49S58ZJYc=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/jcef Helper (Renderer).app/Contents/PkgInfo</key>
		<dict>
			<key>hash2</key>
			<data>
			glAhkclISwTWhTdPmHmgBmBpxJuKyuegSwHTjQfo7KA=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/jcef Helper (Renderer).app/Contents/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			ZobeEKKKL+EbNsu4bcusyCfPxOoRa02r8YReWu5inps=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/jcef Helper.app/Contents/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			R5lep5KqG8xuLWseQIuvJzSPV8UT6x7K1XAQYBgx/rY=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/jcef Helper.app/Contents/MacOS/jcef Helper</key>
		<dict>
			<key>hash2</key>
			<data>
			b58bFkUHY+1KElVXt7B5MRWvqcDKiYahoQS7krw5abk=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/jcef Helper.app/Contents/PkgInfo</key>
		<dict>
			<key>hash2</key>
			<data>
			glAhkclISwTWhTdPmHmgBmBpxJuKyuegSwHTjQfo7KA=
			</data>
		</dict>
		<key>jbr/Contents/Frameworks/jcef Helper.app/Contents/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			ZobeEKKKL+EbNsu4bcusyCfPxOoRa02r8YReWu5inps=
			</data>
		</dict>
		<key>jbr/Contents/Home/bin/java</key>
		<dict>
			<key>hash2</key>
			<data>
			M5gtMfTocusgNaf4+XwZtZbmHwUJhNLi2US3EHn/sqU=
			</data>
		</dict>
		<key>jbr/Contents/Home/bin/javac</key>
		<dict>
			<key>hash2</key>
			<data>
			vfc/Boy2etyhUC0xEG2wWwBhtzURgCZrdreQw0+PKhM=
			</data>
		</dict>
		<key>jbr/Contents/Home/bin/javadoc</key>
		<dict>
			<key>hash2</key>
			<data>
			xapKtSL3o5ZdYALi0Zz9dRACiGeXZJaGYZfLCbfEN4Q=
			</data>
		</dict>
		<key>jbr/Contents/Home/bin/jcmd</key>
		<dict>
			<key>hash2</key>
			<data>
			dmnPySEoYV6VJ2p7kZeH3SpJ9hTvZxSRxluKih2xb3E=
			</data>
		</dict>
		<key>jbr/Contents/Home/bin/jdb</key>
		<dict>
			<key>hash2</key>
			<data>
			E4lV2lp9Kyr3LE5EiDcY1+cELojfJNAvUWYQQQTD1kw=
			</data>
		</dict>
		<key>jbr/Contents/Home/bin/jfr</key>
		<dict>
			<key>hash2</key>
			<data>
			EAkD7ZBOIu04cwrWAWGrk+L5+G21DvSv0liQg5s347Y=
			</data>
		</dict>
		<key>jbr/Contents/Home/bin/jhsdb</key>
		<dict>
			<key>hash2</key>
			<data>
			l+Pm9tn7ikoNFYHrID1cW2RQ+PCitsoEqi8r3oyKZfI=
			</data>
		</dict>
		<key>jbr/Contents/Home/bin/jinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			C/9innhhzrDo8Y6h5J00N0OeXNeqytxzKayjdNrso6Q=
			</data>
		</dict>
		<key>jbr/Contents/Home/bin/jmap</key>
		<dict>
			<key>hash2</key>
			<data>
			260NJpQIHkYDLxiN5UlaQr5kzwhQUQwUlE0hncIVhP4=
			</data>
		</dict>
		<key>jbr/Contents/Home/bin/jps</key>
		<dict>
			<key>hash2</key>
			<data>
			fGjiDj11ydocGTXlcYWHqOuPWbkM087xYV2VuoVkgeE=
			</data>
		</dict>
		<key>jbr/Contents/Home/bin/jrunscript</key>
		<dict>
			<key>hash2</key>
			<data>
			V3Ga+IF8vSqFsOi1wYv+xvjVCdBLpuwfLNdi24CIMLk=
			</data>
		</dict>
		<key>jbr/Contents/Home/bin/jstack</key>
		<dict>
			<key>hash2</key>
			<data>
			gLHXsrIoT8n0+52YIiNEIYyPL7+kcdD1708Y0FNAl6Q=
			</data>
		</dict>
		<key>jbr/Contents/Home/bin/jstat</key>
		<dict>
			<key>hash2</key>
			<data>
			84k0XN1g4l6HS9BJIEu6mEo+msxhmW/S4eFiuf8GVVc=
			</data>
		</dict>
		<key>jbr/Contents/Home/bin/jwebserver</key>
		<dict>
			<key>hash2</key>
			<data>
			mXISQAJ4oT+V67r9/qxxfMdTVj9UOhKRy4RpVux9YMc=
			</data>
		</dict>
		<key>jbr/Contents/Home/bin/keytool</key>
		<dict>
			<key>hash2</key>
			<data>
			ZT1lNfr7AwPeNS4R132H0mEz5ZF7YMf07KXHBiSFXao=
			</data>
		</dict>
		<key>jbr/Contents/Home/bin/rmiregistry</key>
		<dict>
			<key>hash2</key>
			<data>
			0HzYHPr68onT2BH3AkZfLCu7wBYxmZuVZwFPZdTGq8o=
			</data>
		</dict>
		<key>jbr/Contents/Home/bin/serialver</key>
		<dict>
			<key>hash2</key>
			<data>
			51p+cWILFF3C/c2Z8XvwfH+nNcMdwmde+ECjpmvOivs=
			</data>
		</dict>
		<key>jbr/Contents/Home/conf/jaxp.properties</key>
		<dict>
			<key>hash2</key>
			<data>
			fZXElGXQyDbWCNAoVtOwl5NN+13EvSJ5r/rzN9BFtwg=
			</data>
		</dict>
		<key>jbr/Contents/Home/conf/logging.properties</key>
		<dict>
			<key>hash2</key>
			<data>
			ti0nM6uZVWsQihlR2JTFqNdrGsegDALDiPnrm+BGxW8=
			</data>
		</dict>
		<key>jbr/Contents/Home/conf/management/jmxremote.access</key>
		<dict>
			<key>hash2</key>
			<data>
			DCXSbuISyh6MM/Z8PEYNQ/6EnDodI9vjQRSFF2ArKAw=
			</data>
		</dict>
		<key>jbr/Contents/Home/conf/management/jmxremote.password.template</key>
		<dict>
			<key>hash2</key>
			<data>
			AnO2prniDmzlTFrucBZAKOA5UGOyt9OQYKQLZJVUPb8=
			</data>
		</dict>
		<key>jbr/Contents/Home/conf/management/management.properties</key>
		<dict>
			<key>hash2</key>
			<data>
			B9/92FsBwZv0bKMgppmrpI3WsBBD6wvWqVKMeZMxK60=
			</data>
		</dict>
		<key>jbr/Contents/Home/conf/net.properties</key>
		<dict>
			<key>hash2</key>
			<data>
			8Ak5BoIMMOB5y/eb10TXYarzKukXWYx85x+sfq4W+G0=
			</data>
		</dict>
		<key>jbr/Contents/Home/conf/security/java.policy</key>
		<dict>
			<key>hash2</key>
			<data>
			1RvKt+0wHK7/N3ml53fmAZhkzs7l4qvBAu+ZGw3nevI=
			</data>
		</dict>
		<key>jbr/Contents/Home/conf/security/java.security</key>
		<dict>
			<key>hash2</key>
			<data>
			Dv+FEOqHeUejaFoGPP4ZmnQgaLRhZ6upNZynSz2oK/8=
			</data>
		</dict>
		<key>jbr/Contents/Home/conf/security/policy/README.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			baB0czSw/qdZL9kmFLK7yLEmU14Smx/uSDd02RTpjrU=
			</data>
		</dict>
		<key>jbr/Contents/Home/conf/security/policy/limited/default_US_export.policy</key>
		<dict>
			<key>hash2</key>
			<data>
			dYuTClJvxnCrdTf4wmMhUnBQox9fQhSaLdpiPFagoak=
			</data>
		</dict>
		<key>jbr/Contents/Home/conf/security/policy/limited/default_local.policy</key>
		<dict>
			<key>hash2</key>
			<data>
			KyYnVI5hMWFQ1H/8PmytRlygWzzM1Hhet9Iap7qg9EE=
			</data>
		</dict>
		<key>jbr/Contents/Home/conf/security/policy/limited/exempt_local.policy</key>
		<dict>
			<key>hash2</key>
			<data>
			jD12SKvNlaJyzhLbhwCCk39Nf2h41zDYPLf7sx64ssk=
			</data>
		</dict>
		<key>jbr/Contents/Home/conf/security/policy/unlimited/default_US_export.policy</key>
		<dict>
			<key>hash2</key>
			<data>
			dYuTClJvxnCrdTf4wmMhUnBQox9fQhSaLdpiPFagoak=
			</data>
		</dict>
		<key>jbr/Contents/Home/conf/security/policy/unlimited/default_local.policy</key>
		<dict>
			<key>hash2</key>
			<data>
			jYoxjm2Q39fiZhLStjhapwT2hsphNMVR+JKEGNkrhRo=
			</data>
		</dict>
		<key>jbr/Contents/Home/conf/sound.properties</key>
		<dict>
			<key>hash2</key>
			<data>
			KZwjYLYVXrKJkOxJzSF1P5fkNEL+j6sD4E8+IT30OmY=
			</data>
		</dict>
		<key>jbr/Contents/Home/include/classfile_constants.h</key>
		<dict>
			<key>hash2</key>
			<data>
			cFYmMZfGjrhJP1PYgCCqVQmF5kgWr9d25QnWv4zwtKA=
			</data>
		</dict>
		<key>jbr/Contents/Home/include/darwin/jawt_md.h</key>
		<dict>
			<key>hash2</key>
			<data>
			SvEJJ+vP39+/IYu/kMOXhW0lGjEsH+j0RpsFYjXaTfs=
			</data>
		</dict>
		<key>jbr/Contents/Home/include/darwin/jni_md.h</key>
		<dict>
			<key>hash2</key>
			<data>
			iMtcM+MGkA3TWnjVpDkIcSO46RsJhrtay0LMm9L8xC4=
			</data>
		</dict>
		<key>jbr/Contents/Home/include/jawt.h</key>
		<dict>
			<key>hash2</key>
			<data>
			hRAdB5KKWJrM/vnXwmGFD6qNWvyO8mKvfslzQAj28vU=
			</data>
		</dict>
		<key>jbr/Contents/Home/include/jdwpTransport.h</key>
		<dict>
			<key>hash2</key>
			<data>
			xCJW+FlsvztZqiW+VLvy7OR6Xe511OYVN5HcViDP8sg=
			</data>
		</dict>
		<key>jbr/Contents/Home/include/jni.h</key>
		<dict>
			<key>hash2</key>
			<data>
			meZOu+dJ5t8oT4UvEbPHP26pe68VEgQo9A+If+BhbmE=
			</data>
		</dict>
		<key>jbr/Contents/Home/include/jvmti.h</key>
		<dict>
			<key>hash2</key>
			<data>
			eNzKJon0KTfDteVOr37S5GdM2AjR1v1fvRD4OCnld+g=
			</data>
		</dict>
		<key>jbr/Contents/Home/include/jvmticmlr.h</key>
		<dict>
			<key>hash2</key>
			<data>
			buPlLSS9tPTQMS37qz1HvVJMvaxVQKTXkMrAYgxZs8g=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.base/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>hash2</key>
			<data>
			ppvOJ1uno1cK9lecsPVWgs11/t/NSeDo6QIicMRHyRY=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.base/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>hash2</key>
			<data>
			dSkvA78j09t8mFrswZECm5OIMgByHtI+00ouYBRj3zM=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.base/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			S5q+vEM4BIp8LcGE6fgA3rNJNmvfKOsjwmd6d7TIdyY=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.base/aes.md</key>
		<dict>
			<key>hash2</key>
			<data>
			RcbU2kgyXt+/89z3HHBOUEwFeQRDXtI8bVcEbVUetp0=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.base/asm.md</key>
		<dict>
			<key>hash2</key>
			<data>
			aDvhVpW9JIJy1g9bf75eEmqTXqa/IxpiSpqhZHM+HR0=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.base/c-libutl.md</key>
		<dict>
			<key>hash2</key>
			<data>
			vvQGeZItb9+35N2yI61nIjAPYFS6c3u/YYjWD87FF/k=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.base/cldr.md</key>
		<dict>
			<key>hash2</key>
			<data>
			GVFeFKJA4CJkDpW2G1CVEn+paQdVlQtKKwoC54PggWM=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.base/icu.md</key>
		<dict>
			<key>hash2</key>
			<data>
			G/KEWcbgr580KfT4vs0WaNZUQFX43yQCd0VrxLPYp1I=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.base/public_suffix.md</key>
		<dict>
			<key>hash2</key>
			<data>
			14GOAuv8TlzYJhPgA+e6a+Lp1ZSepKoLqI1NL3ymmZk=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.base/siphash.md</key>
		<dict>
			<key>hash2</key>
			<data>
			WnkrWnStKl89anrYt6hBEW5Yp3LBi8bjkjIKNlsiLHY=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.base/unicode.md</key>
		<dict>
			<key>hash2</key>
			<data>
			b3LxDRZrLC6KOV4D5zTFr8hStZrspzztEk9rnJYmjVM=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.base/zlib.md</key>
		<dict>
			<key>hash2</key>
			<data>
			gJtiumSOAjAvfZ6mtohsENUlOshq1SgDilDHPq2l/OI=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.compiler/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.compiler/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.compiler/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.datatransfer/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.datatransfer/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.datatransfer/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.desktop/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.desktop/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.desktop/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.desktop/colorimaging.md</key>
		<dict>
			<key>hash2</key>
			<data>
			BNYePo5x3UUuvlIAivU3jZ9mQNFFeK61FdxTdZc7AYk=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.desktop/freetype.md</key>
		<dict>
			<key>hash2</key>
			<data>
			6kvsVw7TXrFjr/lCD3zgj2+TeMEVvc94Zdwc0dTU6kg=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.desktop/giflib.md</key>
		<dict>
			<key>hash2</key>
			<data>
			bNlxcw0wR+pX9oZbe9yiUJqYdq4k1cDtDE4y3vX5EH4=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.desktop/harfbuzz.md</key>
		<dict>
			<key>hash2</key>
			<data>
			ch18WiS9DApZ/l7h/eHBdUGnRcA444g9SxhKIXefTkg=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.desktop/jpeg.md</key>
		<dict>
			<key>hash2</key>
			<data>
			wd+5cZpxrZ+GH4coVQVC1oHiXI70DmOTYG5uKgwdZTo=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.desktop/lcms.md</key>
		<dict>
			<key>hash2</key>
			<data>
			6fXzdMtBFqy9guw5sKH5OrH1rf2MIISIuo+X3mXoZEY=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.desktop/libpng.md</key>
		<dict>
			<key>hash2</key>
			<data>
			JkKcHrZdtBysgZmb69cFpgpduh2DdmTkzJT1TVhn2Bg=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.desktop/mesa3d.md</key>
		<dict>
			<key>hash2</key>
			<data>
			Y/Tm91yuu8y5XZA/tD5GrHERs2JNCjTxRrJ219nnsVI=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.desktop/pipewire.md</key>
		<dict>
			<key>hash2</key>
			<data>
			fASU016F5fM7saawYahLe3RZBHZMO3usfX2LgXpsotY=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.desktop/xwd.md</key>
		<dict>
			<key>hash2</key>
			<data>
			HU/6k8h/NQhLAqeqkKIQhLQBnbT+EAPC5c53W0o4T1k=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.instrument/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.instrument/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.instrument/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.logging/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.logging/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.logging/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.management.rmi/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.management.rmi/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.management.rmi/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.management/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.management/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.management/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.naming/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.naming/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.naming/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.net.http/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.net.http/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.net.http/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.prefs/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.prefs/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.prefs/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.rmi/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.rmi/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.rmi/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.scripting/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.scripting/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.scripting/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.se/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.se/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.se/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.security.jgss/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.security.jgss/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.security.jgss/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.security.sasl/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.security.sasl/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.security.sasl/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.smartcardio/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.smartcardio/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.smartcardio/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.smartcardio/pcsclite.md</key>
		<dict>
			<key>hash2</key>
			<data>
			s5zjY8KB7TbpN/nmwDMR19vwsg02FN3ghBMMKhCQlpI=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.sql.rowset/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.sql.rowset/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.sql.rowset/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.sql/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.sql/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.sql/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.transaction.xa/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.transaction.xa/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.transaction.xa/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.xml.crypto/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.xml.crypto/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.xml.crypto/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.xml.crypto/santuario.md</key>
		<dict>
			<key>hash2</key>
			<data>
			t3ZLYXMdTulWewkPNNAiN6/PsDd+XRE2x60+80XMSTc=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.xml/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.xml/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.xml/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/java.xml/bcel.md</key>
		<dict>
			<key>hash2</key>
			<data>
			hToefOOXuxDeDis73ghEvMZR8X2YPezQfS0APAMEwxE=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.xml/dom.md</key>
		<dict>
			<key>hash2</key>
			<data>
			Zoboh3ZnWEo6fAc0S6rcoaA+KfZ3Fi2Hw8CBHpkNEUg=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.xml/jcup.md</key>
		<dict>
			<key>hash2</key>
			<data>
			jV3P31BFWjw0x1OpjyHpUySK8gBBWpCE4/ECy2xDuL8=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.xml/xalan.md</key>
		<dict>
			<key>hash2</key>
			<data>
			wn64ddpL5oPU10Ir6Ybl4w9jbt4xlY/x05+c1hCeegA=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/java.xml/xerces.md</key>
		<dict>
			<key>hash2</key>
			<data>
			Smv2s2cZPuaGgcstn+0w/8XWLdLUd71i4CcXB9cbMkQ=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.accessibility/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.accessibility/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.accessibility/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.attach/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.attach/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.attach/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.charsets/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.charsets/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.charsets/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.compiler/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.compiler/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.compiler/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.crypto.cryptoki/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.crypto.cryptoki/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.crypto.cryptoki/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.crypto.cryptoki/pkcs11cryptotoken.md</key>
		<dict>
			<key>hash2</key>
			<data>
			JigC4IF2CzizdIyLGUNT00Djm8k2rCLheru3FY2JWBE=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.crypto.cryptoki/pkcs11wrapper.md</key>
		<dict>
			<key>hash2</key>
			<data>
			Nxl0sfyjdEo4ksfuH8xZO4tCgfwhj0yv0vcJ6d9f2B0=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.crypto.ec/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.crypto.ec/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.crypto.ec/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.dynalink/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.dynalink/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.dynalink/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.dynalink/dynalink.md</key>
		<dict>
			<key>hash2</key>
			<data>
			FzElkcq+4+9sNO2Il9kuTjYbqc6kHsANzWGjIqj8LNs=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.hotspot.agent/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.hotspot.agent/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.hotspot.agent/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.httpserver/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.httpserver/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.httpserver/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.internal.ed/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.internal.ed/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.internal.ed/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.internal.jvmstat/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.internal.jvmstat/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.internal.jvmstat/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.internal.le/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.internal.le/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.internal.le/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.internal.le/jline.md</key>
		<dict>
			<key>hash2</key>
			<data>
			mMMOcpRpQnvuATHBZobVF4aU0QSHr3ftK5KVjzFW5Es=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.internal.opt/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.internal.opt/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.internal.opt/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.internal.opt/jopt-simple.md</key>
		<dict>
			<key>hash2</key>
			<data>
			mbxn+Tz1fW0g5gR3Mck/uyZ9cPvdQRXRGeD4XG7+XAU=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.internal.vm.ci/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.internal.vm.ci/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.internal.vm.ci/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.javadoc/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.javadoc/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.javadoc/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.javadoc/jquery.md</key>
		<dict>
			<key>hash2</key>
			<data>
			to9FTCvViVnIYtlL8PFsP3ii1Tc4jKBg01Q0TbgO5pU=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.javadoc/jqueryUI.md</key>
		<dict>
			<key>hash2</key>
			<data>
			uwoOievYJN9xRRa/ZLkQHGIIHks3bwD5KaWMCVVb8RE=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.jcmd/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.jcmd/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.jcmd/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.jdi/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.jdi/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.jdi/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.jdwp.agent/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.jdwp.agent/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.jdwp.agent/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.jfr/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.jfr/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.jfr/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.jsobject/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.jsobject/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.jsobject/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.localedata/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.localedata/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.localedata/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.localedata/cldr.md</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/cldr.md</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.localedata/thaidict.md</key>
		<dict>
			<key>hash2</key>
			<data>
			wyYUSiNRyWCPpwi119PFo9oD6CtmR5sSjp20lpU5gko=
			</data>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.management.agent/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.management.agent/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.management.agent/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.management.jfr/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.management.jfr/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.management.jfr/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.management/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.management/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.management/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.naming.dns/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.naming.dns/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.naming.dns/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.naming.rmi/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.naming.rmi/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.naming.rmi/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.net/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.net/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.net/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.sctp/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.sctp/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.sctp/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.security.auth/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.security.auth/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.security.auth/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.security.jgss/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.security.jgss/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.security.jgss/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.unsupported.desktop/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.unsupported.desktop/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.unsupported.desktop/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.unsupported/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.unsupported/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.unsupported/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.xml.dom/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.xml.dom/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.xml.dom/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.zipfs/ADDITIONAL_LICENSE_INFO</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ADDITIONAL_LICENSE_INFO</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.zipfs/ASSEMBLY_EXCEPTION</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/ASSEMBLY_EXCEPTION</string>
		</dict>
		<key>jbr/Contents/Home/legal/jdk.zipfs/LICENSE</key>
		<dict>
			<key>symlink</key>
			<string>../java.base/LICENSE</string>
		</dict>
		<key>jbr/Contents/Home/lib/classlist</key>
		<dict>
			<key>hash2</key>
			<data>
			zLH43O9FVCAAU1qr8OLj//u9m1VWWjhJ0NcYNQXlaU8=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/ct.sym</key>
		<dict>
			<key>hash2</key>
			<data>
			dle2brJxmSMBq87odyNoN4itXlb8RY58Fpu5GLplJmw=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fontconfig.bfc</key>
		<dict>
			<key>hash2</key>
			<data>
			VAmVKiGBJsXBHPDkljZlcRruq1D1ZIJk1whm/KcFqVA=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fontconfig.properties.src</key>
		<dict>
			<key>hash2</key>
			<data>
			yNIKSjkkBmUlIwa909vI1x4eJGyM2MeYWzLkEBZYoUg=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/DroidSans-Bold.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			L1KaPmDAB5edldKXlMNmBpQhf7iCQp+zORnSJF/paek=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/DroidSans.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			9RuIlF9MGyNvRLjVWi0wQxaGkSfpUkjENcI/HkFCp9s=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/DroidSansMono.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			ErVS3nZdwSZdZPn1VmZJkw3eTboH2gJR2fkoAecKEEc=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/DroidSansMonoDotted.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			v4xea2Yt8XevJSJuYcNNbPauI7s5k5UwNHqBpbW7/8g=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/DroidSansMonoSlashed.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			AVUNZ7THqMET2iIqJvDnfznZ/iJkNpx8wtj2liaCvjU=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/DroidSerif-Bold.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			x0bGODoD7/vK1sywKOSznocHH/GhRqJPshEKI+BXdrs=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/DroidSerif-BoldItalic.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			GrqZQhsxr+iQGCZjJwwovGGfANh0YjTaPdoFTNW/CbA=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/DroidSerif-Italic.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			AsAQjepYPjk/vL7DnHCT6aMJWgnWXjf6//mpt6iRI6Q=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/DroidSerif-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			rjIUAmXb4N/eJLmr0iK+khClMYiPAU/5sjJqui1v13c=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/FiraCode-Bold.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			N6YJt+J1Fs4M9Vy3VQ7dGhy9jNW8AoQVodUgxCbBA1c=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/FiraCode-Light.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			lleGsBV6jwYHcBB34iasq516ZrKzLwGEjEa7VLJLy5o=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/FiraCode-Medium.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			+U2fof+0ZGPm2P2hIEEIg5et6hxH3nxQqPn1fOmm14g=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/FiraCode-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			KMOuIahT8ddGczhMeg1iCrsOh3uMbNi2QXOpVRJHaCQ=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/FiraCode-Retina.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			6KrYFA3SVLc7rHEgXsTgzD8cORSqc/UU4sxLPPk3010=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/Inconsolata.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			aRt4EaBStRehsjyDspqrOsLqC9GaPwgWzzKqHxsXk7I=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/Inter-Italic.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			Rez+bLrf1IA95iFOAsM2qVFjp3A5c7yKXOesLRdOyvc=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/Inter-Regular.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			p+eR6PWg+wK2VmP3/Kc+HRypVD93KtSAy9dvTj/j+Mw=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/Inter-SemiBold.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			jBmQtgEiVOorSHFhaX0Qc1fdDuVYEc/ZHIwRInu+9Fc=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/Inter-SemiBoldItalic.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			QEJR+v/UxV38tTAH56+51cKJFYTIQhZeNQUDRauXdEs=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/JetBrainsMono-Bold.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			VZCZDILglzl1F/J19DCvRUbhxFz/QIveQlXa0UJHncs=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/JetBrainsMono-BoldItalic.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			QDnVzg7SJb+ciyyMZDYpCuLzVrfpDXD6ZmInI4Mkqjs=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/JetBrainsMono-ExtraBold.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			jlAdOmqIPoPqT3hSgE+wiUzr3Wd1G7EAazekds7zTNY=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/JetBrainsMono-ExtraBoldItalic.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			7DqGD6h9CjsUUSd9HC8wcvW6GdI2euxU9xadVxCHJhA=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/JetBrainsMono-ExtraLight.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			g5Hn7BPounWMGDj1a8zZcyKMz03HSqW//pUluRR7Evg=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/JetBrainsMono-ExtraLightItalic.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			PH/uV1OPGLYcxfB4SoEz/l7pX+tB9jPo9vfvYJ5iB+w=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/JetBrainsMono-Italic.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			nQofenCOavGD8Rk7foHUDaKU9cZ2gsCF2EAcYKrI3tQ=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/JetBrainsMono-Light.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			YMGNfdWNgbO70S6M4ydEqHcb/itSgFdAgrDq7UbGDSQ=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/JetBrainsMono-LightItalic.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			GP+tuR+nEbRf6uAn3f1WHn+XrOgF7EuvmQUEbPRQvvs=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/JetBrainsMono-Medium.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			McktAaighSi3GKQ63fCtPfCvLKS3sykKRS9w81jhTT0=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/JetBrainsMono-MediumItalic.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			RHf9pr1HLvlrEbwQgzcPf8P/QnvcgH5oLO1YGePe6d8=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/JetBrainsMono-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			oL9g7w+Dxe1NenXUWDhUix9oczct+siPcYBEkYmNE48=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/JetBrainsMono-SemiBold.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			Gzv6HtVmWkzj+f62jS1OQOcL+LS32aPt1BjzIbThZqA=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/JetBrainsMono-SemiBoldItalic.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			OzAAUHpyhYcjld27TlOijweRDb9JT7DV4UId1gtdhDY=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/JetBrainsMono-Thin.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			B1bo6M8dZfp1GXdnZEebOXPvsVr09BnV1ydNwnrhtwI=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/JetBrainsMono-ThinItalic.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			683RMRm2vrdFfi92sxCUy4zxEI5WcDD4QCsk3eAY52o=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/Roboto-Light.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			sXZnzn4TWB2xBXd/mG4UEWgjHoio7xbRPlgcfBUl8Us=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/Roboto-Thin.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			EdvYv0+MYdZl9PMVcCe5ZD2yRU1dhNr/vmOF1w6L8TE=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/SourceCodePro-Bold.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			cvNXiLntAJtPLpyUdEfmsx+90Au41YPlZ1ZgoOK8QN0=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/SourceCodePro-BoldIt.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			skLPMySztv0mi7mK7bb9HEukfgnNcsyTgPto3lJEYqk=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/SourceCodePro-It.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			BMD7M4IByf7SghF2RhFl0nz3AYK9uapH/7deHd/PVMw=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/SourceCodePro-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			726mK2hJvBU1qulB0y3o17OtFOq4fWgd0jfRJeNq6+w=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/font.conf</key>
		<dict>
			<key>hash2</key>
			<data>
			EL5pLZbKWzq8e/92aFx3fmLT6NteFijYPdyrvBQd8qU=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/fonts/fonts.dir</key>
		<dict>
			<key>hash2</key>
			<data>
			a0HZ5le/n4NdEv0GvMNa9/OHRFn0TI9WzK0Fij5GYjs=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/jfr/default.jfc</key>
		<dict>
			<key>hash2</key>
			<data>
			sXZdVqDsxXEzYAtyhLt7O5dzZsQ2W3rP/Yb4FXc21T4=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/jfr/profile.jfc</key>
		<dict>
			<key>hash2</key>
			<data>
			SF+5Db7O6alQxFJHRkNRFisesMNTh/u5KfACxgCAclE=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/jrt-fs.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Eb52dxDBwx6NQg5NW6NQPgc2K0ghFRtNavXDsoM7a2w=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/jspawnhelper</key>
		<dict>
			<key>hash2</key>
			<data>
			pOlIN7tqgyeIuKIL9gjmRrVbV3nLBi3rpzFVeobPUIw=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/jvm.cfg</key>
		<dict>
			<key>hash2</key>
			<data>
			qp77lpREwUhOKa3sq1WhIkWAkGFudmsvEjDvBbw4Z+A=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libattach.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			U2SRtL4+7WH5ZGITWLSCti5+tjxNGFOecoNXyRldh+c=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libawt.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			6Xbgoz6wmedbudwTHePiiPUG8J5ZIYPsFYUlUMIdo58=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libawt_lwawt.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			abEa7DjVeADF/quF7x7dxz7wRHj5Pm55QHNUI23W8wE=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libdt_socket.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			mAKyZfa4YXxXS3GLq5ZheU6bNVCCqasIV9tempkhS6o=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libextnet.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			JdXNpyxISwTmqX6/KD4WQQ34e/I4rXvXjA25zD/RRpc=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libfontmanager.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			irvoM/0ou/l+L5D2UBG8GsS9QIh2FtgLOBDA6nPeMUM=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libfreetype.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			w5K3lDMfhzWlZhEos7UM2ui/Ed30DzZEewwP4cXeYIg=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libgluegen_rt.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			mfjI0pDFxa5hGxCqfQ/6jiee0akgsh0Pg/7/6jV4M/8=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libinstrument.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			JI9jlBMCfbx6hE9qnCihQjUa9F6cZ3sAJyxURJjUbZU=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libj2gss.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			ABPaVFbUO9XqC4PwhC1YR0hNYjSzkGzaW3EK2/xIZwQ=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libj2pcsc.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			VycBpkGzQfIMcmMfvXjkIXZH0QtpQdfainCCcbA1u3M=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libj2pkcs11.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			da4wmZ6YDNhlFkLz+8xFaeZNLvuKges6KZ18S3ZRyuY=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libjaas.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			yQeNyroPzLpPB/qVfRLYxnDfYFtNYmIwhPqvsYENgWI=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libjava.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			rrOtQYuJA+kLvss4g0ls2qADILzNDRpJUoW6xUadhK8=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libjavajpeg.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			YTZ7bFHzbfdGUprJgLnGasfFoIFW8QirWgPz3Rq/Ymo=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libjawt.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			mmQsYnBOnrTsEzcw2p47sEus1j6dfDOecXqoH42iGTE=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libjcef.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			TmlrUnxeP+GH7laxjsMF6OlxyOyCTzci81UgK9fszm4=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libjdwp.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			H1tHUZTQD6q/08b3US9LZt6DYM1SV2WU+P039ADglaI=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libjimage.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			u0cbJbYVppxdjP7BcoDIxvOCyoTy2X/XsN8QXWXYDfo=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libjli.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			cXkvT/2qY3k/yJTPgZL7XM/3tGCG7Str/lz4RWgojMo=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libjogl_desktop.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			IGkyxitxZ886eu3qkN+GEwbgDbNwYJPX9fILPnOF+ew=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libjogl_mobile.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			YGuBtaSuVjczWAVUoyi7DbTA1nhgPn8ebP0v0wm33tQ=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libjsig.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			9duwxD8GAd55v+0VXCxeY7XY6cU4m1Ry4gnJK33T/M0=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libjsound.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			Hvur0iFJpB2LZjwuuTrrmlRNokHuMLBcJYmW+w5L72k=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/liblcms.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			ZehR45qnn7XRf/FLF1AWgZ3QIo/zCnui29HWhdEkxdk=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/lible.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			/yFAL6WjNo1YONCqNDp0axR4qzIeaJWHOFhAtiDd/UM=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libmanagement.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			STEFL1AqBBVDFT3Gq+ybv7L1ephfsn3MxWzYl7OA7G4=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libmanagement_agent.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			Kxfd06jneAX5Qwrm8t51qBtuJy5Q2ZFNUr3hLns/T+o=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libmanagement_ext.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			LkYePK3R4ampHwoX6+zZyO4l5/RdaFsKI7ztYIJUWyk=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libmlib_image.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			GoznlJgsudErRyQ+RGKlfqo0Y0mf9Gxya5SI+YOfvFk=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libnativewindow_awt.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			JS8MYvnZe3kgP8a3YJktICitICFryl3G+5t9NSs4Buk=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libnativewindow_macosx.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			V04K7PA0mocTkRrRWrJkNNjoH3NHaH4t/EUV2DlG8L0=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libnet.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			QR7QZYyfG3LVbqyWk6k85PWaTP5Rda9pq8v93e33Luk=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libnewt_head.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			Rhv0lIAdn5ONUxdo3xCQdfj+9V/eW4WU7QAzCjPaIQI=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libnio.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			3/sHMt1E48TgFchy9NucLj5Cvl+OZRkmeJXQOcJLrSA=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libosx.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			i+LCCq/r0InIxOPWXD62Kd+B5WNH++whpKiiFkH11TM=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libosxapp.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			cqQWJ+gY95Mhr7aBupburWJqJf73WQcHArCBDshoErI=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libosxkrb5.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			DaH6w0Fr3Yu0D5HwSUbprZpbqrVhLFAcsHjKOWxqOKM=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libosxsecurity.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			DmrRT0BSjKEVlXSqyTWa8euLrperBKGvOJekDqwIlC0=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libosxui.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			ek7EowbowoSpFY0PcVHIj4UcU19kH7ePqCaoZJhf300=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libprefs.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			dsfzWcnxI5IBG8X9oZa/Og+3KRv5UV7EVvT490F0kpk=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/librmi.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			vo97ktMIvG1s2sn+xl5/rihMp7UGgsyfVZmxE8tjqW8=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libsaproc.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			8ldNn9vAo0aOIWR/WPAOhY6UVLvcl9vZgMs7vs9a9z8=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libshared_mem_helper.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			kDFSnuiy+hahSvAo6hd1A40ZI2aLzLTYa2PONkNMW+k=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libsplashscreen.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			xawW/xWx4IZbE4trc5erF0ZtZL9BMXdQLaYftGBPyFI=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libsyslookup.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			zgkWYk6twB5UvYP8fqa/cCpfDyGNyJNaHBGGLy+3sFY=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libverify.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			RsgR9y2yZni2ys3m2wZJi/sZJVyvVwiylRzI0Ninfrs=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/libzip.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			7WuYw7HXUiC5Ebgd2zkORklLLQYMS0c6v1J2fk/Xg0s=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/modules</key>
		<dict>
			<key>hash2</key>
			<data>
			YUbLJeYEqQp0ixm1vMmQCkvafrQdIlke6YtfyuZH2ic=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/psfont.properties.ja</key>
		<dict>
			<key>hash2</key>
			<data>
			WkvVG5ab8Yf/htlPSnH9+/pgJ2KXX6PHPSZLRXX3x48=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/psfontj2d.properties</key>
		<dict>
			<key>hash2</key>
			<data>
			eAxWXVrz7m9ouIe3XAQc30agWS9nAS8S7raRKD6SYwo=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/security/blocked.certs</key>
		<dict>
			<key>hash2</key>
			<data>
			llcvJD8xwu+BpuYnVC5Zb2qSlc/zx64JXBtZXLFFfe0=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/security/cacerts</key>
		<dict>
			<key>hash2</key>
			<data>
			IWjnFluUI9JgXLvyr8FmXOw2vCD/XFSvkdEsOK5V0yc=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/security/default.policy</key>
		<dict>
			<key>hash2</key>
			<data>
			K9QYqrMLCRsTaWL4C+fd853LqF8IKlWKaZOEiuZTZq4=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/security/public_suffix_list.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			WjpXHMLgFv7hDCIQNs8dK1Lui6OSiO8gq+JmeCjKMLQ=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/server/classes.jsa</key>
		<dict>
			<key>hash2</key>
			<data>
			WstFrCWfX0hbpIDh6FKwxoiH2Aeq8xR4UERQwxEUAbA=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/server/classes_nocoops.jsa</key>
		<dict>
			<key>hash2</key>
			<data>
			JtC/n0paHWCZzgyy+LPrnapmF9to8iRff7lFLE2NQeU=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/server/libjsig.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			H/iJLln6nbiEu8wQA5ZHxEZTdaCydoPSvDfVrDXivJg=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/server/libjvm.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			WRsBX0uaaM6kYKpxLCypfZvuTu3DVCSSMAGAxqfZtFw=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/shaders.metallib</key>
		<dict>
			<key>hash2</key>
			<data>
			wM+UL8Y4dN1P8jBD2YLVYmBsTz9WTlI/3QWJWMGUGNI=
			</data>
		</dict>
		<key>jbr/Contents/Home/lib/tzdb.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			hwIuta6UZddXYt5vsqZoxg1BHBOU5QDCRlGJVoEigUg=
			</data>
		</dict>
		<key>jbr/Contents/Home/release</key>
		<dict>
			<key>hash2</key>
			<data>
			V102XidmYxcxK+8UT0Tv5FlZagcfhutvYYQNuYPiiaY=
			</data>
		</dict>
		<key>jbr/Contents/Info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			uSlinxHlOJHZ7X4aSNIJN+wbXwQQi7YfmuxF5b3eVmY=
			</data>
		</dict>
		<key>jbr/Contents/MacOS/libjli.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			0y6YXE4qEofL2pnR0KU7SD+u5yiW4doOSM18j3mxtYM=
			</data>
		</dict>
		<key>jbr/Contents/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash2</key>
			<data>
			D/rVRTbNr0cVmOK3qMm3cdPI/iH9NJCPcLYHbYcqt+k=
			</data>
		</dict>
		<key>lib/annotations.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			adr72EqFSCUEH8PD3C8eOxCXbiF5Y9YHi2TK426/Gu0=
			</data>
		</dict>
		<key>lib/app-client.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			mn49KE2lmcAiFbgOWn1ZZ194CEqqdUFgxZoDMR29v30=
			</data>
		</dict>
		<key>lib/app.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			1EyBv45B+rF1hiIVeZDRqPza9lc4flkNW6moQcABc48=
			</data>
		</dict>
		<key>lib/bouncy-castle.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			iYFAPHIgsRLyGksuiLIxejy/1gIkylG+FE87c0HLnJU=
			</data>
		</dict>
		<key>lib/external-system-rt.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			rXyyWLV6lTUt+ccqYZ1fHQT1mvedT/vTQDMQYrR8fY8=
			</data>
		</dict>
		<key>lib/externalProcess-rt.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			OiFDDS/jzSq8rZsKM5rafyJBFQh2vXBFk4Ru/MRCBr4=
			</data>
		</dict>
		<key>lib/forms_rt.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			+1C+bag4EuQ8YrDS9dbAwF7K0R0CtRlKX6NSxxBkQ0s=
			</data>
		</dict>
		<key>lib/frontend-split/frontend-split-customization.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			g6qoumlkbOna00ZBe14QvcdaS0MhFey6VCHeTfaE5MA=
			</data>
		</dict>
		<key>lib/frontend-split/frontend-split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			YRJzcLbutf1sjL7j/Ee7sW5Fv/5QRivw7xQprYlBArc=
			</data>
		</dict>
		<key>lib/groovy.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			HMmDXs073eueDUggsSABX52Ma7WuZxOCFlbqPw4HdRk=
			</data>
		</dict>
		<key>lib/idea_rt.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			A3I8Huj3hSpENNi5yu1v5VRpnBS/6UuLQvMGVOE30cw=
			</data>
		</dict>
		<key>lib/intellij-test-discovery.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			xnUx3OE89VaT7eQJXcBbcI4ZiwPcZ1Ui/5sMInIlgWY=
			</data>
		</dict>
		<key>lib/jna/aarch64/libjnidispatch.jnilib</key>
		<dict>
			<key>hash2</key>
			<data>
			xUAGTJaB2iJrmAekYxE2DSMvBRmz4YMjWF+pQujqQ9c=
			</data>
		</dict>
		<key>lib/jps-model.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			HWB0qPoLGhHeCUhx6vtTAfdkwQLrfW3t1mhu/SVWnqU=
			</data>
		</dict>
		<key>lib/jsch-agent.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			SPpL8/mKaATd+x00hUpWkxRSs66zfNdyn+9Hwd9JNG8=
			</data>
		</dict>
		<key>lib/kotlinx-coroutines-slf4j-1.8.0-intellij.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			qHwv4KCVvDBkkEX4z+lPWbyXKF4POMxpo1f7RN1SM8Q=
			</data>
		</dict>
		<key>lib/lib-client.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			0kgaV94T3eZeNoobukwt5aoylaDeZaCFMnMn3ibd0pQ=
			</data>
		</dict>
		<key>lib/lib.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			1SwH7JTQ/sTVpVSejW4XLboL7KpIas7Q8EllCxQSiOw=
			</data>
		</dict>
		<key>lib/modules/intellij.grid.core.impl.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			bGWdG3XSI0IoIFcWCXXkmBvtRWjc/icYDuNogGWwBd8=
			</data>
		</dict>
		<key>lib/modules/intellij.grid.csv.core.impl.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			DIGyr+p6vKIQphZz+cKs+5E9ndS1GYdmpgyX4f5w8JA=
			</data>
		</dict>
		<key>lib/modules/intellij.grid.impl.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			n1TyiOraBj5NZVXfMgH60UxXmZfnGKNKPcwvkUk/2GQ=
			</data>
		</dict>
		<key>lib/modules/intellij.grid.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			0kb2ogTFHhg2Dhpr9n6Baqw1mf+JayDGr+CPYWGrY/g=
			</data>
		</dict>
		<key>lib/modules/intellij.grid.types.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			yomCaG3NqneB9YSBTKSeHIQVR8So/gn682l6lxHZv64=
			</data>
		</dict>
		<key>lib/modules/intellij.ide.startup.importSettings.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			jt4v177KmNMJN5T27L14cHqJcfiDq+YoijfJWx/gEXY=
			</data>
		</dict>
		<key>lib/modules/intellij.idea.customization.base.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			eN1951pxSWky2jJzqFpeIjKx1KPOBrqrtGxlr5lud/8=
			</data>
		</dict>
		<key>lib/modules/intellij.kotlin.onboarding-promoter.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			m7P93toq2FpBz5hvjS5mQ4MNMN95pLIaZnMva6MfbUo=
			</data>
		</dict>
		<key>lib/modules/intellij.libraries.compose.foundation.desktop.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			honvrslA120ViyyprHvozMeGmpYSBgwFthH41rGtUHc=
			</data>
		</dict>
		<key>lib/modules/intellij.libraries.ktor.client.cio.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			h03ynwQadJaSLI7mfxzD6VHs1Wv6vsa7HFsWunpnJd8=
			</data>
		</dict>
		<key>lib/modules/intellij.libraries.ktor.client.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			bc0GIcsrvDT54j7uZ219CeI//0BV1wqrXX59p2A/JMs=
			</data>
		</dict>
		<key>lib/modules/intellij.libraries.microba.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			kfMopr7W3mYB/h1xKAXR3d36UWc3uzb5oyKhvicEahk=
			</data>
		</dict>
		<key>lib/modules/intellij.libraries.skiko.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			7fhPzQVXp68Yu8A4KixTugBWFQjoe+XFy4LmdkZ1xo4=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.bookmarks.backend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			pSL7FASRSRhmek7Ls5Q8iq+DcHPATSWHoUAXF+MAOIE=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.bookmarks.frontend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			E0qBOEhHOeBZQ0n+dp031TgYzTOCEuoZHLSAHNctWmM=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.clouds.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			EfXcT2yWaMSKikjYDT3/EpUe1LOf0qZ2b9GrcdIj9w4=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.collaborationTools.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			7JsQ5yEJspLmqNcBq9h7hyMX2eHo1wy/SsEMLyjQBuU=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.compose.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			tRtJYqPrjcKJw3ikdaVHubreAifV8Snn+c1FhNcOfL0=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.coverage.agent.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			g0BYj+Ev1t0HZK4sggc6uvzf8T5udMca7MioMhT1q1A=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.coverage.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			1/xs/+2QfGcDkzxdGreL3cGNzb451R/ySASQ8lgvF+I=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.debugger.impl.backend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			CSSBcxW+CMTyEpHUXxn5gvK98CNhsFjT2KXJWvBSHGQ=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.debugger.impl.frontend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			c3B1SfD/aDOA9Z/p9VDkZoNHnMYW04YZMbzFR/e9htc=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.diagnostic.freezeAnalyzer.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Lnv2QnZMHjcqtxrv8N3UkabJJnrnMzTmPqKqnQ364nU=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.diagnostic.freezes.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			EfRHUX7i9oED7jyi1Bc2G/Pp6QAwabwXp2UX9lOHFho=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.editor.backend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			FBQwgMe+hfmWweTp/eZ3E1eVmDKpsO4eBXM3Fknp4NM=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.editor.frontend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			WmfNOjAyZ9XJaODBSFMbkdLjbz+TyTASZsZ3Ot6HkrY=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.editor.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			ucnzZKzQL5nralQEbvEAW49x0J3ZXd6+pcVerajVlhE=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.execution.dashboard.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			vi3chBm6TLodiAErurF71N8VSijx6XkVjyEixEYzEPw=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.execution.serviceView.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			nMR7JsHqQGkGu2B4I9QBCavAzy6O3hD8dRmQxKzE4eU=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.frontend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			5cdWhtOHeh30MMEVgsmo2W0C9/qLRImr18SRMDyPIug=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.ide.newUiOnboarding.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			xw6o0RO3FSiij1Ui+Lkweh4TfAQFI9QM4p9gRjg6oBU=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.ide.newUsersOnboarding.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			OkmxCwBwtCCXx82TT8CxrhIc9EbC4VppWTrcTkwtKi4=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.images.backend.svg.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			9yK/vK+CN+1iRng8FE1qTGFn09GK+KschrbBLL0AdRI=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.images.copyright.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			6o/B1AXruO3ofRXI67Hd+HLFvaZ5EBlmWq8lW6yymIM=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.inline.completion.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			50brE0zBBvYLO0Ht6gmFT/QmL26LMArm5+bqBnQXLWA=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.jewel.foundation.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			vtjdVDxAr3UV4AVf1stGafQLIPJp4reWUT2SGIYroUg=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.jewel.ideLafBridge.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			rbdsnT2Lft6M7FqvOqzYSgnt3OOxDw05WPlx/xLpLJY=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.jewel.markdown.core.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			dgaB2oKR1H7noqLoj0SadnErcaA1CuVDHIJSLVWe8hM=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.jewel.markdown.extension.gfmAlerts.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			QLM7N2h2ssXJnzRFLsfjxrjdt5rjfk87dnMJJlP2R+A=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.jewel.markdown.extension.gfmTables.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			6kOnrdG/YePtCe8TSp7WomP1cpD+NxN8YxQm66FD9ng=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.jewel.markdown.ideLafBridgeStyling.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			nd9M/wFdqZSuqw4fSvbwtcTCfHnIdbKz3L3ijCshKBE=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.jewel.ui.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			G9s4s8ZTcBSBHHwENza8pex/4Y6w26lQPSgFeHY7hfU=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.kernel.backend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			AQ8WHUGfKeoViIy6dKr2KgpoJPs6kbPp5dOcHnFR8Eg=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.lvcs.impl.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			L6tFW2DPBaHlyIpR/xv4vcpPt5SE4uRpzQLYal4eYvI=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.navbar.backend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Y2iEJ7bG48V3E7qOBin2VjEuodDEfXZ+6F/6xTO30V8=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.navbar.frontend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			7qw61veaMohbQM8cHFV76cjxnw/Sf40sIirh6MN5rL0=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.navbar.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			N2KapVJlLe7kQenpnsjrtCZRwem1os5a3xxjXSMvakI=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.navbar.monolith.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Bk0RkeLPRbvt58RJOvSq790d46jRUwefq2kIlshHcws=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.progress.backend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			7UXS0Q3A1ZWGUDnzFoLhO+Imc5P23TSP9cYStY7lo5I=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.project.backend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			VsMqlyNKQ7R/YnVNOxjg6OiCcWfxBvoof9Fnstu6oVM=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.recentFiles.backend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			jjSe8FaJ9pTHmbsWLDdkqDXypnLHFAsrEY55QJOdDq8=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.recentFiles.frontend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			jlCnnuo5Xh9U1s9zM70K3JLrecgwB9qZ1M8SOsO5ohk=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.recentFiles.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			EJr13nM9KDK7ymMDo9DZbYHhOeY2eGtrPHE7cFoC8cw=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.registry.cloud.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			OhKjzYis3RL6/NXAJg3vtDnrkUmUdvyQvkdl91xCoRQ=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.rpc.backend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			RydLj8kcl+cKKoUAgxSQ5n4M4RcMxJL9P9wRW4s7H3A=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.searchEverywhere.backend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			l9ceML21PxBePG/W6ZNnwyORTD7Ji8pHpBYDGTy4NU8=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.searchEverywhere.frontend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			BuLBUqqPGlEd8B/ynMKjtgvWWKTHz2tXXphuVH5BaLQ=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.searchEverywhere.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			8Ly2r9tlS1viAZ154Enz2UZjc4X8YiWARTZ/qKHvoOk=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.settings.local.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			K8l8Nz8JvShi20L6+muuNcmGlWC5hKqQK3go2ucLaXc=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.smRunner.vcs.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			jhdpV7VV0F2tVvGpxDtpnfGRYnrF7IWQRwXnejITuVk=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.tips.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			xOUpydTy4lIKYGSASSLsgR0/EXXw9FZIvy1OW+zhLjY=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.vcs.dvcs.impl.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			puDMDbSVsPOLClHCXYlogsExyx3rog9C8TC7vu+KyRg=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.vcs.dvcs.impl.shared.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			tCPCv+YSBZ40uxqrXUBZH+JwHcIr/NENHvC/ygHSv80=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.vcs.impl.backend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			l0Vf1Fqf9RF5G1O8f66KjmkRmpsCTX1UXQ9C/VZOh9k=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.vcs.impl.exec.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			uDfBdEgWYlQwC1gk6Te4ZpjU3jndmwTmd1KO9/7J7Cs=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.vcs.impl.frontend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			5oBuU5bFm4rLlz0F2AuYQF5/QVGir3NOXekKd5r+ZK8=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.vcs.impl.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			UBM2UaKosGnbJPKOkH4yClM1TRy2zACxjfo7WSmXrbw=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.vcs.impl.lang.actions.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			5RkTEsh7VR5lm+E46w39v6+pCRnmwi7sdH8cFbwbC8o=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.vcs.impl.lang.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Y7xApBaYDFJX7VDv0FGbpCr/+cxZm3EQAuFml3/PqAQ=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.vcs.impl.shared.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			R4PlxzT5DKuYtgeF3q60UGSHEjI/qoqi//MpTqxnxa4=
			</data>
		</dict>
		<key>lib/modules/intellij.platform.vcs.log.impl.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			heAZ+sIfr7hWO7dxNSQl2fnzwOhvHMx7YnV/0OT4tyU=
			</data>
		</dict>
		<key>lib/modules/intellij.settingsSync.core.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			neXzbP8uxyMncDlHPt8ZL4DcIX8jhcKwSDcBy3AFCB8=
			</data>
		</dict>
		<key>lib/modules/intellij.xml.xmlbeans.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			zNnOjXKxvF8vS7Nkqc013tbpQSfCLhXPYI2o6hTnxVM=
			</data>
		</dict>
		<key>lib/native/mac-aarch64/libsqliteij.jnilib</key>
		<dict>
			<key>hash2</key>
			<data>
			Kj6jtIn4Gj/lBjAUYKU66bgJ3QiFm2jMYvm+gbx5FCE=
			</data>
		</dict>
		<key>lib/nio-fs.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			0WRcUyu26tXiv0HkJCyhePsAngqaEYVKLmHVH1TDauk=
			</data>
		</dict>
		<key>lib/opentelemetry.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			UM87GyMYGXVl2aHaXo3kgdkJqU/qBu95izTtEr5Cr5Q=
			</data>
		</dict>
		<key>lib/platform-loader.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			3YAQMh46LmXJ4DYBJ2TV6u8nq8QSsoCHGLMnCMVbZfs=
			</data>
		</dict>
		<key>lib/protobuf.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			w1EEJj/8PoyAvbr8kmwyjhcF1wOXdfLij/H0bECvoks=
			</data>
		</dict>
		<key>lib/pty4j/darwin/libpty.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			mjQ+wucyE9OIBob0dClLpj7ICLmXqqbzkSW0/f7U11Y=
			</data>
		</dict>
		<key>lib/pty4j/darwin/pty4j-unix-spawn-helper</key>
		<dict>
			<key>hash2</key>
			<data>
			CnT188iXSwhqbRv9NRsV0X8YgJ8JkI6Fzhe119BTO84=
			</data>
		</dict>
		<key>lib/rd.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			11T+uxa9vOa39gDeyudoYLC+YYTUHQLm/D4OwxGFiyI=
			</data>
		</dict>
		<key>lib/stats.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			uYIvyZI15aCaDq30rVaTaFqkbZwgb5eYcsDo88yX2JU=
			</data>
		</dict>
		<key>lib/testFramework.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			z8Hxj9U67IL5ARVJwOy78jxh0ZzpGpZc32JEARmt+44=
			</data>
		</dict>
		<key>lib/trove.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			dtk6zcR1+3haPHMtyII+E4ehiEhVZm/0rwP3FikNi8E=
			</data>
		</dict>
		<key>lib/util-8.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			C7Ls9jE8balVLKsPWOa+P6AGFpbjdXd5tRfQ/psBmyg=
			</data>
		</dict>
		<key>lib/util.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			VRr53lk23eKyNGO1v93u5/fuJYk7SinNtGHzghGSrMM=
			</data>
		</dict>
		<key>lib/util_rt.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			LgIkI1L85SI0S24KrEhDLwezMxOl3iZagK+UZF38wyk=
			</data>
		</dict>
		<key>license/javahelp_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			Gwh60oLLPNChHk4WAxjqtP8Jlarn0i5qwNMDZ+GWxuM=
			</data>
		</dict>
		<key>license/javolution_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			3BTusvYrhB5iSoHWpUJ22rOwRzvNyRYS501bsKWA6TU=
			</data>
		</dict>
		<key>license/launcher-third-party-libraries.html</key>
		<dict>
			<key>hash2</key>
			<data>
			I5ioIlMr4EqgI3CVGvBfY/6iLHugNfv48/X7Ixn1HEQ=
			</data>
		</dict>
		<key>license/saxon-conditions.html</key>
		<dict>
			<key>hash2</key>
			<data>
			Hu3wMk+V5lnHjqyBXiEbGH+wJNr7RbstnMrAiOy+ceY=
			</data>
		</dict>
		<key>license/third-party-libraries.html</key>
		<dict>
			<key>hash2</key>
			<data>
			CPNQNvWf72JDKqGdgZo4I/dAaIrCH/YNLs/1/cIAIw4=
			</data>
		</dict>
		<key>license/third-party-libraries.json</key>
		<dict>
			<key>hash2</key>
			<data>
			y+DLldYSaWsKGnctb+JNXwQ99z6e6NbIy12ZXZxSHCg=
			</data>
		</dict>
		<key>license/yourkit-license-redist.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			VnClqGCQXEQGHFVGAp1uMvzZ5MkxECnFepNa42wlvFE=
			</data>
		</dict>
		<key>modules/module-descriptors.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			PSqd6W05PkLiUh7xukZKdDRaFdS7MIApvbi71bDQkfM=
			</data>
		</dict>
		<key>plugins/Groovy/lib/Groovy.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			II3SUnFDPBj7Hy5B5F7oe5TdtM4stMHPW7Z9RVhtUGM=
			</data>
		</dict>
		<key>plugins/Groovy/lib/agent/gragent.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			WcxJ0zqfxQHcvNUL9k3IftwLspSbgbqdiLbx42TSvMg=
			</data>
		</dict>
		<key>plugins/Groovy/lib/console.groovy</key>
		<dict>
			<key>hash2</key>
			<data>
			ssFq9GPSCi9ooZdg4dxsGSixV96BHJnMXiylBsV2wsA=
			</data>
		</dict>
		<key>plugins/Groovy/lib/groovy-constants-rt.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Cl2ldJzrpqsC6LT1CXjs6PuLxCtTDmx9MtKPknFfK8I=
			</data>
		</dict>
		<key>plugins/Groovy/lib/groovy-jps.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			62YaAmfIRTGx+3MxdyAW7d8O5dRs5QYZ8ZV/TCrwpsE=
			</data>
		</dict>
		<key>plugins/Groovy/lib/groovy-rt-class-loader.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			HvMaNKA4wleJuzdV0vYp8H5vUo7sQ8MFa1n9ZvNSiQY=
			</data>
		</dict>
		<key>plugins/Groovy/lib/groovy-rt.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			fC6NMSeOCAAthbJ59o9I3P3jYNiQZ5vEv2oJ8ZCaveY=
			</data>
		</dict>
		<key>plugins/Groovy/lib/groovy-spock-rt.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			lKQUMLxEY9st8iALxnItk/NHlKiouEpA1xb+T8sPWQg=
			</data>
		</dict>
		<key>plugins/Groovy/lib/standardDsls/categoryTransform.gdsl</key>
		<dict>
			<key>hash2</key>
			<data>
			lqUJtzs7GzgzRE19KF1NdwiXzCQ17c+GGL5yqujNa/Q=
			</data>
		</dict>
		<key>plugins/Groovy/lib/standardDsls/closuresInMethod.gdsl</key>
		<dict>
			<key>hash2</key>
			<data>
			mQ5IWUXGELzPqhQvOouHKfZkRcFKzhwrcwtbU49+/8Q=
			</data>
		</dict>
		<key>plugins/Groovy/lib/standardDsls/defaultMethods.gdsl</key>
		<dict>
			<key>hash2</key>
			<data>
			C6YnhxDs+bOK+qylebfHPpHAJo9qwi7kjJgAXqMyZ1w=
			</data>
		</dict>
		<key>plugins/Groovy/lib/standardDsls/gantScript.gdsl</key>
		<dict>
			<key>hash2</key>
			<data>
			z8LLSjwIVPo8oDUoWvqM1NKYP9qj0gzGT301Pv8Q/Ts=
			</data>
		</dict>
		<key>plugins/Groovy/lib/standardDsls/metaDsl.gdsl</key>
		<dict>
			<key>hash2</key>
			<data>
			L+A5qA5PkiAtyvCyf5ypu99o8gl8+/Maou18CAftUkU=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/META-INF/MANIFEST.MF</key>
		<dict>
			<key>hash2</key>
			<data>
			VmrRqAIgAm0FCZViZFzpaP8OfDbN4iY0MyYFuzTMPv8=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/bin/kapt</key>
		<dict>
			<key>hash2</key>
			<data>
			bQsRWhZPTMKiU+O+fJ7s9h1r6Ccy41Cyqwq/z8+OLjE=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/bin/kapt.bat</key>
		<dict>
			<key>hash2</key>
			<data>
			wvNr1vI/5yLngtTLNT2ToU/ojskG9d8AF30Vo3YKun4=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/bin/kotlin</key>
		<dict>
			<key>hash2</key>
			<data>
			SdSo32SUXMerj4adYvLpaRfMa5FVz+xXvURxUt4SZZk=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/bin/kotlin.bat</key>
		<dict>
			<key>hash2</key>
			<data>
			Cjk7IJndxNU9f6ObmMZyeRV6hPMfBe6Euc+HwiFgB1w=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/bin/kotlinc</key>
		<dict>
			<key>hash2</key>
			<data>
			ZNYqGX++xfMvd8ZyTM4z6tQDSy7D+HPAVs3hn+APMk4=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/bin/kotlinc-js</key>
		<dict>
			<key>hash2</key>
			<data>
			h7YHmSCUzJdUXRKuzsPs1vKwdtyuBi/CiC5flZQGXqw=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/bin/kotlinc-js.bat</key>
		<dict>
			<key>hash2</key>
			<data>
			h7w8SQC6hc0zDNz+g1c5/KsOMKgPQDl+Tx4BHVv/EQQ=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/bin/kotlinc-jvm</key>
		<dict>
			<key>hash2</key>
			<data>
			mfKcz2OY2KspoYzi68Pr2BzT1fQSnqypL7OwomPr5Mw=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/bin/kotlinc-jvm.bat</key>
		<dict>
			<key>hash2</key>
			<data>
			H9iErgKWEugPlpskaiH9JPO4Mv9mYLuBguuK9kGOPTY=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/bin/kotlinc.bat</key>
		<dict>
			<key>hash2</key>
			<data>
			cOk3j5WIPn2Y4egL8fH1E/KSqJrgH978CTTtnz+iMzU=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/build.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			7aWzQBBTNghh57pTvgC5rNlIcXXt4xUP/W7tO+EFmQM=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/allopen-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			BEHu9hkhFnQeG6fL4bJ4VFdqRBtN7GtXA6j4SG+nzXQ=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/android-extensions-compiler.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			8TipoaJPUG2HkDbwAwJ3o3gnyEQd7H1bbyEG7czat38=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/android-extensions-runtime.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			UCDfiury+6L6n/V3UaU9D7/KM4PQSJIOsj01q/019Go=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/annotations-13.0.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			rOKhDcji1f00kl7KwD5JiLLA+FFlDJS4zvSbob0RFHg=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/assignment-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			YvDbTb5GZSiG9MJGU7HORGzlsDKUAQQm53sr2p6uQbw=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/compose-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			RaucZ6yUV2PrYTHnfxOb717vLHLK5i8WcDprp6pnfig=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/jvm-abi-gen.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			0DirQK9wL7CarXmuagdNNRqth23W19UbN1i77i0X0rs=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-annotation-processing-cli.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			IbFeCgKhAFIFoxpyGZW3b5fXC71hKcKZ5lv0i/LTTf8=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-annotation-processing-runtime.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			zG21EEpk+RHZSteNaSxvY5pnNkYKR0zQBlsyueJLaBw=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-annotation-processing.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			xL0eZYHUR2yAdHSvynLE9PHYbyFkopYmcgkFaCdAaQI=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-annotations-jvm-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			v1lNDjo1whsfBwl9nhC2q1JK5bsv/bkuqbH3nyjoqwk=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-annotations-jvm.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			YFQjBZnIQMaa82V8KSt2jytYXCX7qR0mzVrYyZJ1ewM=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-ant.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			nUlB8yJQGjdhtsg4UgfpeDRP+qzrp8CmjDX5nDc8UzE=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-compiler.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			33/C/WcUlwiMLfDnXqorIVNmxOnq1EH3I/2D3VE+Okk=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-daemon-client.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			3o73nFqUvina+SCtea3cMF1fGzAy1zs7n1eStCLFWxk=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-daemon.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			jRzp7iVn1Pkn7etM4uwXMETvonYz1OH2BSO2EP0PvLw=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-imports-dumper-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Km6AFf0u9gQNT7EFWFg47Kf6lslH9zXAFvz/MDXZbG8=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-main-kts.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			4dcQKXQ+1+mgmOkef6EkWSkvIzWfum9UYhih8LelmcI=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-metadata-jvm-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			zE0m0xuMCYhTxzkydqWP0PGBVWLwE8gEIvBZ5e9CfWU=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-metadata-jvm.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			37t0C7VaSOSesQQiVCH5KoBXaNCTeJGEhpKtaeSnwWM=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-preloader.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			rYPFOQ+LXnw7Jg5CAaxEnMCaYMspb68EK63E1NssVwE=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-reflect-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			LaWgm9sGqCniXPdSDgc13INc3V3IGQPv9+Y7hNifFqY=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-reflect.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			6sJddl7feBezldtvYgfko7X7+woKvKOJWMlooanZf5Y=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-runner.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			GgWrMNMWQzTQ6JZF96JJBx2VJjE3zJJps3gQupF6r/c=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-script-runtime-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			2da0Fr8SZ9XZov/7MJyNAvpwdggVTXsNfdMY0t19qpY=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-script-runtime.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			4EvQSIX0n/PV510buy2jeZ7FYybBuRUIz9BkbSkkopY=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-scripting-common.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			/SE1HH3lv7Zoxxc4vzhCLGolDfMbbmRcD6uFra5/sKk=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-scripting-compiler-impl.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			U7h0wHoe/mVvKwcH29ox6QuJj+ZJYOPvN7uz0tJl2sg=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-scripting-compiler.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			H8WlxrjNpsWlJcy1gRf/lJXzPwjJopRPRsNWdGpwREo=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-scripting-jvm.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			CK0ruYPEuHTTd/Gcg1zPrRW4duHxnOemSXlkadJxrl4=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-serialization-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			jyCh9YSeX38et0orWJavYcu2qJNZaSY/K29C1s+wYkI=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-stdlib-jdk7-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			JTTIkIQy4G3nMXdQmQPUBbVfQj3UwvdH4WuSohYmEeY=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-stdlib-jdk7.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			EnhEfDfYZcGgW+TRpg1GuThv5L1yOsUrmu02Pq3t6Vc=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-stdlib-jdk8-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			PLaJUFSgmFu6WRwWVQP+TdY6IVr1MmO2egcczcJCv24=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-stdlib-jdk8.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			EOgsxw3t7cTQru+D/Qo7asG/U0JcXJVM9/EI8krvTKQ=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-stdlib-js-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			XMQhfvz8Rg59soc8CSvb3z6zrAGZbVxG8qzptunv8W0=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-stdlib-js.klib</key>
		<dict>
			<key>hash2</key>
			<data>
			bFUQLYbe+fC8GT7klrHQDHKAZiEfvhBAclyanbl+KXI=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-stdlib-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			GaxjU72jOXpSSF73eCoPicUVvgxMr244YjN6VqlpTW0=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-stdlib.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			9GE/JWQPaN+QK97LYu11z4UpoUE6i9omrdC5p2TKhpA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-test-js-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			55q2+NQlCb8CKgIt1KJlfvoeSOoFUpEEw4iv3pgpp3o=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-test-js.klib</key>
		<dict>
			<key>hash2</key>
			<data>
			UwaZ2PYyEYrjC/kD2U0JH2nHkti7XJdSXo4d1I392tc=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-test-junit-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			xBGg2tEhjz9lK2YZ8ij+/uSYkD7m4Rw2yZD7w/6CPxg=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-test-junit.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			+EF0vlk5J/pqJTxoPxz2zJbO7Ok6+SG8W9qMGtRzHcI=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-test-junit5-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			iRxW2Bg9LNPpEs8lCt7KiJ26UdArB/zjAVMh4I9ytKI=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-test-junit5.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			xG7TCp9y/YP1WvesyZ40M9FXToSBLcHH+x6SFjfjjnU=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-test-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			S/pH6+scbnIkNNsEWL1br8BxXHhgQoKBaIdRflQpEsY=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-test-testng-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			dINmNl0LPphSiaq3Aq7KuE0L2O9t0QRlnobB8xNZxao=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-test-testng.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			xCIDV3bvieVjYzGXJveveu1fzc76NU1sGIT7m2rP65A=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlin-test.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			6DftRn5bZeDmaYGXqEiiSJHhairwKUsnGDkQbvUVjHo=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlinx-coroutines-core-jvm.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			mGCQahk3SQv187BtLw4Q70UeZblbJp8i2vaKPR9QZcU=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/kotlinx-serialization-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			jyCh9YSeX38et0orWJavYcu2qJNZaSY/K29C1s+wYkI=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/lombok-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			a6ccLjYHuezNPnDcDHHzZ/4Dyb3uX4ao87kPHjt9LRs=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/mutability-annotations-compat.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			jHZpP94fNLqur1KY4ZU/ykicjpDObAZflt0g2XjCADs=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/noarg-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			pzu9DHnQ8BucXIw55Rqsb7wLbluYRBuIZOQiDl69yD8=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/parcelize-compiler.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			hGDzkHffd/KwpFa7L1EKdWyR+KHFd8u+cQLJW8XMBeI=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/parcelize-runtime.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			qdvVqV+6Rih4gpRA2Df+IkFl7lRL9Od8d39F2fOJcCs=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/power-assert-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			fxwtOLQCaz4w0YyvFeNA2M7Ndt1smGSbwKuExmvZLDo=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/sam-with-receiver-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			8YBASOzh8srlt1fHRSqjajGtVaE071xsOnRrB6kBi5o=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/scripting-compiler.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			H8WlxrjNpsWlJcy1gRf/lJXzPwjJopRPRsNWdGpwREo=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/lib/trove4j.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			xf1yW/+rUYRr88d9sTg8YKquv+G3/i8A0j/ht98KQ50=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/COPYRIGHT.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			jxoXAK21SQYExglPsTe/c/bCjT7lVLWPP/jvl1p5Zy0=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/COPYRIGHT_HEADER.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			4gR+1xd7caDgnUv72PgSotFWejbYxXeNjTHd2ppEDdo=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/NOTICE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			CwmoPT73eVx97GXoFYZll8BmxTiY8oUqdlmbj1VSlBo=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/README.md</key>
		<dict>
			<key>hash2</key>
			<data>
			jLqD/N7W0Wb8Q5jrjLnBoSrYuH/2JKQElWJC0iIx+8k=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/aalto_xml_licence.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/aether_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			7Y16JHaaydV9CHabX4vidRndiBHQawmV2+jKZxTpbQg=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/aosp_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/args4j_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			cpt87xJwFVPAJyu/0ucKR6uTBl875vhG2B6ToJHO80A=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/asm_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			v35ddyyK3zjRYwbLox3mFR4YhlYNCIiL8l3vvorfclo=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/asmble_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			mAjO/+RQ7xY64Eug1CFJw3QqVd3Y2lgMJHHyOn95pAU=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/assemblyscript_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			xazLvYVG6Uw0rtJK/miaYXYn0Y7tWmxIJ35I21fCOFE=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/boost_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			jYKRyvHO4m0jrPPrZ8n5otWPHGgbFqT76Mv7njwLWps=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/caffeine_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			WNHhf/5RCaeuKWyq/K39vmp9F28LxKsB4SpomwSZ2L0=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/closure-compiler_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/compose_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			gJ+h7SFFD1mCfR6a7HILvEtodDT6Iig8bLXdgqR6ucA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/dart_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			pXKHlVG4AR2xT+shKQbX1pCrvlingD+y1vJqviGXqws=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/fastutil_licence</key>
		<dict>
			<key>hash2</key>
			<data>
			+0EeEwWf42SbJjDWuU96Ax0Imop45JJWAzxOPz2j8fQ=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/gradle-node-plugin_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			bcDgaNzzpbyOBUIFuFt3IOHUkmW7xkv1FdLPeRl99po=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/gradle_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			9eZh3GNcgqLa1aa9kscbuJUAAghl4Lhv+tS992T3jkQ=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/guava_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/gwt_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/jgit_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			XCD7UpXCYq7GqMM+1vzWFkZiSlePNt+ta3N+uW1cUeg=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/jquery_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			31fWn7px0uvKBIpc434W+Ou94zS3m8sqFdK1ignWQkE=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/jshashtable_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			gLglsNbeZ6kjCR3ZsO5nsSs+F8ATVsdTrtSUHAJuUfg=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/karma-teamcity-reporter_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			ruxVkZyqWzvJXNTIpV8vlMAA2yy0Pxaiq2LZJctkaVs=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/karma_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			D/mBBeGjbovjUtA9ON9+x9duLyERjZkwjVj4hKGYVfs=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/lodash_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			2geZkwkTgeS1ghZHWAMlMpvzzNxP3ozeF5fxRIPBO8w=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/lombok_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			YQ+MC+E+3i7RBJUpI7pjUG73BUPXKUGiX61eF/kIFKI=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/maven_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/mocha-teamcity-reporter_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			rY8n42AWvV1I37E5g9H3Ql6Pjf1Wf6Y/0YxE1KkjXZA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/okhttp_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			5mbqTNqOcSIyG+yOofGHBfRpqoWbr5Ia63c/VJcJ/Mk=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/opentelemetry_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			xx0jnfkXJvxRnG63LTGOxlggYnIysveWIZ6H3PNdCrQ=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/power_assert_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/prototype_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			G7wDYr8ngJsNWs/QMLlVBW/rA8eB88W4dpqvBug4qaw=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/rhino_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			j/TQGr5LDNFEt4lhlZJOQhvEGgH/x5CAx7Xyx9dcFjI=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/scala_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			iykQeFbcWEz5Do6nzFtIRzOexpB9Yer7xbGoQ6gy+TU=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/sl4f_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			sDrvp8eF1M7rWn66nt8AaZeKX3IK9TiyKG1Ixo0ClJA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/stax2-api.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			fzMiHcbHbz6OpBEQiOyQXKS6Wc36vWfifhlTqU0HrjQ=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/sun_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			EQCruMnUHrShf/ViM+aLSfrx7cYxT5taMt3prNZiL2Q=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/teamcity-service-messages_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			P6BRgeAp3+hm4z+CXWh58r29dIk2jbNn47GphqgQ5yw=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/testdata/dagger_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/testdata/eclipse_distribution_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			1wVvfXsHVKukRzS0iyxum3NMNQhRdlwH1yS7X7NOybM=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/testdata/eclipse_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			Dbqc1byDPWpA9fWjTbcON7bOHNM328LwbM8dvFIdIHk=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/testdata/findbugs_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			3GJlINzVOiL3J68+5Cx3DlbJemT+OtsGN5nYqwMv5VE=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/testdata/jspecify_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/testdata/lombok_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			XQFV6yIfzVolvLDMR2EFepdRsjdWFeBDW3Yw/8LwuBA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/testdata/rxjava_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/testdata/spring_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/threetenbp_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			0bxTtJOjqzh7QnF+1cSxl2pQSJlvgRVCeBAL/4bTkzE=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/trove_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			38fDzlDughdQa+zYjrU0rRH6KSVk/Fs+8/TMrUD5XVw=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc.ide/license/third_party/trove_readme_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			cIjZwHMX6U8fm3lOfscT+Z9l0DYU3RBbcHVKCefEbfE=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/META-INF/MANIFEST.MF</key>
		<dict>
			<key>hash2</key>
			<data>
			VmrRqAIgAm0FCZViZFzpaP8OfDbN4iY0MyYFuzTMPv8=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/bin/kapt</key>
		<dict>
			<key>hash2</key>
			<data>
			bQsRWhZPTMKiU+O+fJ7s9h1r6Ccy41Cyqwq/z8+OLjE=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/bin/kapt.bat</key>
		<dict>
			<key>hash2</key>
			<data>
			wvNr1vI/5yLngtTLNT2ToU/ojskG9d8AF30Vo3YKun4=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/bin/kotlin</key>
		<dict>
			<key>hash2</key>
			<data>
			SdSo32SUXMerj4adYvLpaRfMa5FVz+xXvURxUt4SZZk=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/bin/kotlin.bat</key>
		<dict>
			<key>hash2</key>
			<data>
			Cjk7IJndxNU9f6ObmMZyeRV6hPMfBe6Euc+HwiFgB1w=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/bin/kotlinc</key>
		<dict>
			<key>hash2</key>
			<data>
			ZNYqGX++xfMvd8ZyTM4z6tQDSy7D+HPAVs3hn+APMk4=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/bin/kotlinc-js</key>
		<dict>
			<key>hash2</key>
			<data>
			h7YHmSCUzJdUXRKuzsPs1vKwdtyuBi/CiC5flZQGXqw=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/bin/kotlinc-js.bat</key>
		<dict>
			<key>hash2</key>
			<data>
			h7w8SQC6hc0zDNz+g1c5/KsOMKgPQDl+Tx4BHVv/EQQ=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/bin/kotlinc-jvm</key>
		<dict>
			<key>hash2</key>
			<data>
			mfKcz2OY2KspoYzi68Pr2BzT1fQSnqypL7OwomPr5Mw=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/bin/kotlinc-jvm.bat</key>
		<dict>
			<key>hash2</key>
			<data>
			H9iErgKWEugPlpskaiH9JPO4Mv9mYLuBguuK9kGOPTY=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/bin/kotlinc.bat</key>
		<dict>
			<key>hash2</key>
			<data>
			cOk3j5WIPn2Y4egL8fH1E/KSqJrgH978CTTtnz+iMzU=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/build.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			72gI9+lEXLHrZ1eIjifWVPvxP8DWgr6jW7I/7UGZzUo=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/allopen-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			nn0MKh1SZvp2NOwEqS3a7wKdnZ2lx0fRPEgjOnARsRk=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/android-extensions-compiler.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			MFQ1SNiXUPi+Tn8DIMJOAwII9HIe1389PEBfV74uSV4=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/android-extensions-runtime.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			FIcMpy7KrsGAsZJg0GaM24b+iNx5n1ZeXWTKFCO1pUs=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/annotations-13.0.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			rOKhDcji1f00kl7KwD5JiLLA+FFlDJS4zvSbob0RFHg=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/assignment-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			2qZk/3g+RgTTv3W8oimJL9pkreCUACopUT8hRz+sEB0=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/compose-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			yJ4xLUuzcG4IKx4afEhNoD/x1t+vbbHOYAPWrGahgjo=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/js.engines.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			aMUB9C58xcACcj4/H4EOZrgWuadcjqNc2Mxr9MpNTtA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/jvm-abi-gen.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			LPUOxWCfrBfAV1kzayK60LJRY5IXr+4FMmhsCb5dOYE=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-annotation-processing-cli.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			riiEqGKluUFozBgkMhjQfYm7hgkXXS0gzQ8nnSVHS44=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-annotation-processing-compiler.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			+dzGK7oxBVpXjUg4xZld35bO50SDcB/geRXAWXludB8=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-annotation-processing-runtime.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			LB1GpkzOy0i4U9q/+tS7RZDFhWYfzokNlkYb9blNOV8=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-annotation-processing.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			e2Dgsttt7JLBmCLKVrboRe7IDjWxoRLSaTQgUeRUd4g=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-annotations-jvm-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			v1lNDjo1whsfBwl9nhC2q1JK5bsv/bkuqbH3nyjoqwk=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-annotations-jvm.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			YFQjBZnIQMaa82V8KSt2jytYXCX7qR0mzVrYyZJ1ewM=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-ant.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			+NrgqQO631CEXyEtsQAQseZo0VuKdmkp8rithuZDdvk=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-compiler.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			VpCq31v3hFJPotDAdGHusWdFBYPdq62cIpg5tBtYhv4=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-daemon-client.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			zxinYYIDWuLUjoJZllBSTh4VHnND6KsP0G0GB8QUXNE=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-daemon.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			YEVXaKgXgylrdAl98KKPubKxdQNE4hq+6Qsgg8SIvFs=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-imports-dumper-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			lzegGU1L7L2X3TIrlxdjxowjET64Ner0GN8PsNVA4Ok=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-main-kts.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			vtPy21eusYT/Au7es8J/CgO37OHP5om0icVTRSUEVCw=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-preloader.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			+k80YVs5Ew00KQoKG2MbZQpxNpjzCBb0l5tsfsboGt0=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-reflect-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			8sNIe1EJ62ftwGXpZCH8ANWHzH9RvcUBKfQhj1p8d5A=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-reflect.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			0BnT1hXz5VouoSmbYZwmpz4ieyUDduUprqHhrbxlpOQ=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-runner.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			68QMbRLrFwIYVO46uVQixzlMQTlxwkA9pbotg7YitPA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-script-runtime-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			2da0Fr8SZ9XZov/7MJyNAvpwdggVTXsNfdMY0t19qpY=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-script-runtime.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			4kTA5/ZkERdV8ubpd4Ybcabro7T0UWJwSDPkbfqIOug=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-scripting-common.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			R3VyCGt1QtldsIyGY3rA7bm/DWsiq5uhNA5BoEiUEHk=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-scripting-compiler-impl.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			C2iRtTH6Fe9Ux2DaUOtg+amx+d6yVeOQQdJsfgPAlxk=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-scripting-compiler.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			soJbR8xWmD69RY/fEq4HL+rHy5DPo5tW4zDTm4JrRg0=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-scripting-jvm.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			lUZtpQ9CjAG9g2WE0wxVpomGHJOz2Xd4dKY2PRnPLdw=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-serialization-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			GROM13pL3xrNEmV07cEhPUfslNwuBngScdObqdbh0r8=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-stdlib-jdk7-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			JTTIkIQy4G3nMXdQmQPUBbVfQj3UwvdH4WuSohYmEeY=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-stdlib-jdk7.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			L4dvs1tz2YBuyFAHAyZavL1ap8PIH0GiylSoVahkmzQ=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-stdlib-jdk8-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			PLaJUFSgmFu6WRwWVQP+TdY6IVr1MmO2egcczcJCv24=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-stdlib-jdk8.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			sJJg6kgo3Gz8Z9JhJ9rOOK4ru+ilZdLQKXJegcLSWOw=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-stdlib-js-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			s6MkM8UyUar8KY7lHSJ2L2es6UH/+gqLmlL7wYwZxTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-stdlib-js.klib</key>
		<dict>
			<key>hash2</key>
			<data>
			WS7GvQXTlMvaYM6v2or4BmNG3PGl6DuJ0YcwVT5R8oY=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-stdlib-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Ve7uk7WQ+V494ct6fLVdtZC7hutk/cZKjpq3xUVhF78=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-stdlib.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			XyrByo3Is3o/QxTnFtNpaevwInp1GB0yaZ0Kj2RbHCE=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-test-js-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			55q2+NQlCb8CKgIt1KJlfvoeSOoFUpEEw4iv3pgpp3o=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-test-js.klib</key>
		<dict>
			<key>hash2</key>
			<data>
			fxCTsB8+7V3r2BPy3fnqj3JLthYSlAWme1/1DTlIZaQ=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-test-junit-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			xBGg2tEhjz9lK2YZ8ij+/uSYkD7m4Rw2yZD7w/6CPxg=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-test-junit.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			G8L04LrXOi0EvMZ/Rqtj+xDMjiiPd/lLXl3Ac2HvML4=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-test-junit5-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			iRxW2Bg9LNPpEs8lCt7KiJ26UdArB/zjAVMh4I9ytKI=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-test-junit5.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			o5tR1L4H4vV6nzaDhnlNx3SWqUhklOZF6hG+zqm+ss4=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-test-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			S/pH6+scbnIkNNsEWL1br8BxXHhgQoKBaIdRflQpEsY=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-test-testng-sources.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			dINmNl0LPphSiaq3Aq7KuE0L2O9t0QRlnobB8xNZxao=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-test-testng.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			KukQdAmMBuxZR3Zu0ftZ/rNYBj7/K7FPjiXMZZyXxAQ=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlin-test.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			lA6q7MxDTHn8WvyzCT9H5GRpZhuzUOefgvTZKsbpYMU=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlinx-coroutines-core-jvm.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			wkyLsnuzIMSpOHFQGn5eDGFgdjiQexl672dVE9TIIL4=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/kotlinx-serialization-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			GROM13pL3xrNEmV07cEhPUfslNwuBngScdObqdbh0r8=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/lombok-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			FanD88/qxqOSZHV1kNSNVB+2QS16P2QGpY8I/lDDVmg=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/mutability-annotations-compat.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			jHZpP94fNLqur1KY4ZU/ykicjpDObAZflt0g2XjCADs=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/noarg-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			nnwEyY4wM3PYA8jhFd7vngUedYjnq29xFwYWQ4bOkvI=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/parcelize-compiler.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			cQtdT6Cmofw2DlerJyjkWg3p/kCkl4XZ67SYCLLF4FU=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/parcelize-runtime.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			UhPc4wUxJAQSHV1BVJ6GyZEwYVwSPiG1DXq2oOMR8Pc=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/power-assert-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			L/UVAQHZ/yMa8Yn2/F3FWxPbG5LQyHx9W/cbCU5DCSU=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/sam-with-receiver-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			gFZbTnq4Fsg6iRkid9bbCry0K2PbL0E+bK9OTERdHAI=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/scripting-compiler.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			soJbR8xWmD69RY/fEq4HL+rHy5DPo5tW4zDTm4JrRg0=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/lib/trove4j.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			xf1yW/+rUYRr88d9sTg8YKquv+G3/i8A0j/ht98KQ50=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/COPYRIGHT.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			jxoXAK21SQYExglPsTe/c/bCjT7lVLWPP/jvl1p5Zy0=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/COPYRIGHT_HEADER.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			4gR+1xd7caDgnUv72PgSotFWejbYxXeNjTHd2ppEDdo=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/NOTICE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			CwmoPT73eVx97GXoFYZll8BmxTiY8oUqdlmbj1VSlBo=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/README.md</key>
		<dict>
			<key>hash2</key>
			<data>
			O+2rIaxwbL/Aza/0cX/lHaw96S3nBulklKPzm0AS6Rc=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/aalto_xml_licence.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/aether_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			7Y16JHaaydV9CHabX4vidRndiBHQawmV2+jKZxTpbQg=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/aosp_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/args4j_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			cpt87xJwFVPAJyu/0ucKR6uTBl875vhG2B6ToJHO80A=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/asm_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			v35ddyyK3zjRYwbLox3mFR4YhlYNCIiL8l3vvorfclo=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/asmble_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			mAjO/+RQ7xY64Eug1CFJw3QqVd3Y2lgMJHHyOn95pAU=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/assemblyscript_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			xazLvYVG6Uw0rtJK/miaYXYn0Y7tWmxIJ35I21fCOFE=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/boost_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			jYKRyvHO4m0jrPPrZ8n5otWPHGgbFqT76Mv7njwLWps=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/caffeine_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			WNHhf/5RCaeuKWyq/K39vmp9F28LxKsB4SpomwSZ2L0=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/closure-compiler_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/compose_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			gJ+h7SFFD1mCfR6a7HILvEtodDT6Iig8bLXdgqR6ucA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/dart_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			pXKHlVG4AR2xT+shKQbX1pCrvlingD+y1vJqviGXqws=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/fastutil_licence</key>
		<dict>
			<key>hash2</key>
			<data>
			+0EeEwWf42SbJjDWuU96Ax0Imop45JJWAzxOPz2j8fQ=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/gradle-node-plugin_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			bcDgaNzzpbyOBUIFuFt3IOHUkmW7xkv1FdLPeRl99po=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/gradle_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			9eZh3GNcgqLa1aa9kscbuJUAAghl4Lhv+tS992T3jkQ=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/guava_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/gwt_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/jgit_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			XCD7UpXCYq7GqMM+1vzWFkZiSlePNt+ta3N+uW1cUeg=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/jquery_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			31fWn7px0uvKBIpc434W+Ou94zS3m8sqFdK1ignWQkE=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/jshashtable_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			gLglsNbeZ6kjCR3ZsO5nsSs+F8ATVsdTrtSUHAJuUfg=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/karma-teamcity-reporter_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			ruxVkZyqWzvJXNTIpV8vlMAA2yy0Pxaiq2LZJctkaVs=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/karma_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			D/mBBeGjbovjUtA9ON9+x9duLyERjZkwjVj4hKGYVfs=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/lodash_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			2geZkwkTgeS1ghZHWAMlMpvzzNxP3ozeF5fxRIPBO8w=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/lombok_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			YQ+MC+E+3i7RBJUpI7pjUG73BUPXKUGiX61eF/kIFKI=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/maven_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/mocha-teamcity-reporter_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			rY8n42AWvV1I37E5g9H3Ql6Pjf1Wf6Y/0YxE1KkjXZA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/okhttp_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			5mbqTNqOcSIyG+yOofGHBfRpqoWbr5Ia63c/VJcJ/Mk=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/power_assert_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/prototype_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			G7wDYr8ngJsNWs/QMLlVBW/rA8eB88W4dpqvBug4qaw=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/qunit_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			y7r8FHjpSVs5lS4odbpCTSTQc5tGS2opdymYyxVE8Ho=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/rhino_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			j/TQGr5LDNFEt4lhlZJOQhvEGgH/x5CAx7Xyx9dcFjI=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/scala_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			iykQeFbcWEz5Do6nzFtIRzOexpB9Yer7xbGoQ6gy+TU=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/sl4f_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			sDrvp8eF1M7rWn66nt8AaZeKX3IK9TiyKG1Ixo0ClJA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/stax2-api.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			fzMiHcbHbz6OpBEQiOyQXKS6Wc36vWfifhlTqU0HrjQ=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/sun_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			EQCruMnUHrShf/ViM+aLSfrx7cYxT5taMt3prNZiL2Q=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/teamcity-service-messages_LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			P6BRgeAp3+hm4z+CXWh58r29dIk2jbNn47GphqgQ5yw=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/testdata/dagger_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/testdata/eclipse_distribution_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			1wVvfXsHVKukRzS0iyxum3NMNQhRdlwH1yS7X7NOybM=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/testdata/eclipse_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			Dbqc1byDPWpA9fWjTbcON7bOHNM328LwbM8dvFIdIHk=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/testdata/findbugs_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			3GJlINzVOiL3J68+5Cx3DlbJemT+OtsGN5nYqwMv5VE=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/testdata/jspecify_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/testdata/lombok_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			XQFV6yIfzVolvLDMR2EFepdRsjdWFeBDW3Yw/8LwuBA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/testdata/rxjava_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/testdata/spring_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/trove_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			38fDzlDughdQa+zYjrU0rRH6KSVk/Fs+8/TMrUD5XVw=
			</data>
		</dict>
		<key>plugins/Kotlin/kotlinc/license/third_party/trove_readme_license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			cIjZwHMX6U8fm3lOfscT+Z9l0DYU3RBbcHVKCefEbfE=
			</data>
		</dict>
		<key>plugins/Kotlin/lib/jps/kotlin-jps-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Pk70/G6ctGEJFgbk4fC8blstEx5/X82/rujzusv8irU=
			</data>
		</dict>
		<key>plugins/Kotlin/lib/kotlin-base-jps.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			/eNY89KkjpqhZ67OR61EdvyDeLKGtN5eyVppcRfVb6I=
			</data>
		</dict>
		<key>plugins/Kotlin/lib/kotlin-gradle-tooling.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			w69mczCT1Ag2JbUWp6MenQbsjEfO9oZxn2a84Owcyk8=
			</data>
		</dict>
		<key>plugins/Kotlin/lib/kotlin-plugin-shared.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			hAP6hauWRuERHnno8VtwpwPOqX/2ozwcwWjS2URmq94=
			</data>
		</dict>
		<key>plugins/Kotlin/lib/kotlin-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			THfx5EEq64rI0fgJ+gvA6rWyFHOL82MH0CJhzGo7s8U=
			</data>
		</dict>
		<key>plugins/Kotlin/lib/kotlinc.allopen-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			2jmO3R0GCa5nTfn8ZL8cHezZ5FAq2stavnjsxvFRJmM=
			</data>
		</dict>
		<key>plugins/Kotlin/lib/kotlinc.android-extensions-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			qWkH/WratF/Hmfj2PpJZYBYccDRgLqHwkgrpASC2nq8=
			</data>
		</dict>
		<key>plugins/Kotlin/lib/kotlinc.assignment-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			NRyiyhnXxmHhFoNu6iylUXwU6dsWMKyeFzlWY65jTio=
			</data>
		</dict>
		<key>plugins/Kotlin/lib/kotlinc.compose-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			HdgmdW0lmDnOC5VLj5gduY78dbjsfz/NwSudy/PK2Ew=
			</data>
		</dict>
		<key>plugins/Kotlin/lib/kotlinc.js-plain-objects-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			PCJ9rJ466dK6yQCoGWmfHTyUBmGWR2JOPUZn9FJ8jk4=
			</data>
		</dict>
		<key>plugins/Kotlin/lib/kotlinc.kotlin-compiler-common.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			4NPAeSrtK0Ct+4+sJ6n+/uX6F7u+OuEoA20jFBhjDcY=
			</data>
		</dict>
		<key>plugins/Kotlin/lib/kotlinc.kotlin-compiler-fe10.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			oxY0lKJ5tfQXjY5KbG0I6WZIomGoRKSWzaj75BCuzJc=
			</data>
		</dict>
		<key>plugins/Kotlin/lib/kotlinc.kotlin-compiler-ir.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			m3tRnhvdUs5vEprRtpwvWunq+n9SQ1aaglj/MSxuLYg=
			</data>
		</dict>
		<key>plugins/Kotlin/lib/kotlinc.kotlin-jps-common.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			SaP//Z2iVUt0AWTJNNVEB1m+S13mi2DLfQ9Q6+NXuH0=
			</data>
		</dict>
		<key>plugins/Kotlin/lib/kotlinc.kotlinx-serialization-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			vb+fHZzUQC+LBtGRCHKsciUWol0Kl5dPzBT2iNoZeZo=
			</data>
		</dict>
		<key>plugins/Kotlin/lib/kotlinc.lombok-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			REGx60ayd9fKbkVe/cDDbaGYVSIQywIiCgJ98lP+1d0=
			</data>
		</dict>
		<key>plugins/Kotlin/lib/kotlinc.noarg-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			ZyKVWhpGe7cxBmijIwuEh9M61EfGya9x+M/1P6gIq0Y=
			</data>
		</dict>
		<key>plugins/Kotlin/lib/kotlinc.parcelize-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			R15aXcdaPPef2LHBQ+HJvR5XEKJiRSVCgE7TaRNpJ4U=
			</data>
		</dict>
		<key>plugins/Kotlin/lib/kotlinc.sam-with-receiver-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			+x5SONGaIELrEq9YLwHUSEWWzh1HDOQ5xg1g4Ji1kdU=
			</data>
		</dict>
		<key>plugins/Kotlin/lib/kotlinc.scripting-compiler-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			TwGPw1X6lG8v30cVqEPI9W+7hiigyXYFHL+SXtIP3d8=
			</data>
		</dict>
		<key>plugins/android-gradle-declarative-lang-ide/lib/android-gradle-declarative-lang-ide.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			ISnyaecYAnv22SAwf1gKwzxrZOSalrnz+NLaBTFlr1Q=
			</data>
		</dict>
		<key>plugins/android-gradle-dsl/lib/android-gradle-dsl.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			7TLFvboxdsddhu6Eu3zNnrIQ8Dyg3tYGQW/KpUanK6g=
			</data>
		</dict>
		<key>plugins/android-gradle-dsl/lib/modules/intellij.android.gradle.dsl.flags.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			X4NebkrUyXWDB3w1vyJF0MGBDNNkWRsD3Gkd4jH0Zkw=
			</data>
		</dict>
		<key>plugins/completionMlRanking/lib/completionMlRanking.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			6ErpQgeSpBdVXwTtn4knnGI3RIqUnoGa6JJypI31WbI=
			</data>
		</dict>
		<key>plugins/compose-ide-plugin/lib/compose-ide-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			0VtjhacvwYH4vUEuMG80S/Q1/prYJKxC/zeiH9ec3EA=
			</data>
		</dict>
		<key>plugins/configurationScript/lib/configurationScript.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			UXlwdwnAdjfXuVQ23RjYLIv+Fzn9CpVcwatNi7fPN8M=
			</data>
		</dict>
		<key>plugins/copyright/lib/copyright.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			3/LUVM3tpTK86bF51X1zgIu5MrS7lbsVFDO/RNKn7RE=
			</data>
		</dict>
		<key>plugins/cwm-plugin/jre-build.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			C9FWP/m04hPoynfIgmWIl5/UjaCgt7p1wFIhL4lB4hU=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/backend-split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			WUglNg/yNp0lx+cmGeL37KDyU3Rxi3wDoWwOYfBflmw=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/bouncy-castle-pgp.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			mpY/bBWBKcUdFyDFa1GTgob+Vhj9MYnPOG0L+mOLdcE=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/cwm-plugin-android.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Ozm8PjY2YrfjpTm5n6MMldkmdG0WQXryOtbVu9WkRkc=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/cwm-plugin-backend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			LRBidPOJYe2ITAL/c8D7hBN0JmQOhw/MORj/HFA4BJM=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/cwm-plugin-common.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			2v4CLe6U6+falM9A3B4LRnIyFvQLi5PHOvEYBD99ei8=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/cwm-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			79VnKl+34wavbX1rUuAQR3QtUnnf89xr0nwGwaK7uN8=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/frontend-split/cwm-frontend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			J1ADQGIniRykq6cVxrxk4eOzSzKVlVOC7fdqVg0LRHk=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/frontend-split/driver-frontend-split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			xIwqcrO6N7cLmVKr2Yr4JiVL2bEMVTlSxIF9xIb5DaA=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/frontend-split/frontend-split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			RZPRZzSdwACngPcnuRrieHVSWm5Z991RuLUu18WORpU=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/frontend-split/markdown-frontend-split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			siSdQixhhzvPAcDI0UW7tCC8RXClT7iZIxtewNzHGE4=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/frontend-split/performance-testing-frontend-split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			t4VenIYtAS1VBJsh2TZUlb2SuMHvVA1+zozXA7+YT9M=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/frontend-split/platform-ssh.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			q/GBqHh7pVuvM6een/rsWZwsyyXF1bNd95/4iOa+Lg0=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/frontend-split/rd-client.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			XIm9yVrTMPzsc/1gJytLEORv37qfUm79kkNmTX4APOM=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/frontend-split/terminal-frontend-split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			ClIbGyV/zzdjOcJiC9GOTJ48BObaH6lSz6kYAMqk0OI=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/grpc-netty-shaded.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			YoTdPcFQCla17sPFvh216b+oRk1OmiYqkLXAeOIobJM=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/ice4j.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Cn3I7hw8pdDe02yLwHhzMkmAQV7l+wTJJtJIBpvvwzg=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/java-websocket.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			fl9zYAx9iPnNKKY/fAyc1k77VJBgH6bxomljgwy7pmQ=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/jitsi-utils.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			R3aEw/Pjt4WxTqLeWxxW5xNXa+TdOyGdNfZe7QcXyy0=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/jnaerator-runtime.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			e871tRPOZTIh9HkmR9dUKWUGi0bhaHkzKqyizojfXIA=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/jstun.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			9NsojoXSNS+rIb/Ga28nvz/1FSOzSXWdYV7UuMAW/vo=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/modules/intellij.cwm.vcs.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			UiJ8aGl4TS+sSo/pwiNS9PE6TrdBvQdOSSlDL1ThFuw=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/modules/intellij.java.backend.split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Tmem1KMCbbMOzWFNPco/QEHqA6SGlRHrJOCv1ds2jmQ=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/modules/intellij.performanceTesting.backend.split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			cXNfVuTzX7EODxx9//EnPUcvPiQe8xOQRzVC0lPaBKE=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/modules/intellij.platform.execution.frontend.split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			C6P3B/L8lpo7Hloza8KbWqn4NJPHqn0eemUjxi8V/4A=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/modules/intellij.platform.inline.completion.backend.split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			KXiTog1t3+3mOOdyHVd2rNOnUA77AchkX2TfgyXlmfY=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/modules/intellij.platform.inline.completion.frontend.split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			3VIXhQ/6j9AJZB2jQoZ7iNu0eW8hnD8+O2mLLHYyG2k=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/modules/intellij.platform.inline.completion.split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			kVW0MYNeASG3EDeNZvalChRglGd+Ka5IO6bMlii3Fv4=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/modules/intellij.platform.kernel.backend.split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			+hrFzlyQ7uvn7V/uH/mZjGxw3ZIbaMVXNcdab6w1y9c=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/modules/intellij.platform.navbar.backend.split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			prm0aVWuU34nJFoIOjyFBSopjxW2o9EryY/yLoMXRHY=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/modules/intellij.platform.progress.frontend.split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			GcPbhAQRo5v6fhrMwAhTxmwBzLZWh/BaD3cz9RHUl88=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/modules/intellij.platform.project.frontend.split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			NdJ0QVHB0rQjwu24WiwmUYcFR4drjdOgcKk410TIZ0w=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/modules/intellij.platform.rpc.split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			B8Xa8uVHkYTtBTFyXxgJJcmitjjtkCMjVPpmRzV7a1s=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/modules/intellij.platform.vcs.backend.split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			oGo4eogaStcHFGlmjQbZSnzbShC2i9mI+a7oDaaTwGo=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/modules/intellij.platform.vcs.common.split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			cvlEocQlAjekaYcr8cryZXHs4uO4unczKrEC8iznqHQ=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/modules/intellij.platform.vcs.frontend.split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			3QkbEeZDsCqRPVMb+IfI2KYG+ObL0rCBUHBy/Yl68DY=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/modules/intellij.terminal.backend.split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			GVmF6iri68OGUEcq5U/aj+AEsnw5+jyFOzZf6kd+/FM=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/quiche-jna-stubs.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			WUYQW+kfPUO4C8J1haIwsTQWgRw24LgPYex6oKHVWCE=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/rd.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			i8yVHJPSZLDFJnta76zQq6dPw/TMVc6Ha3vuag5tzRI=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/remote-controller-backend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			zsUc09Z07bVKSzUbfu6i+FmN6lXaV5/Ebi3CkT4Cpj8=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/remote-controller.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Z5usqsDTGDsiOoObz88loA+2sdDx+A64nQfFCu1wC5U=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/split-protocol.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			0RSeUYNSqY/crT7/joUIPO43IX9h5urH1+oUUpzxvag=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			OD89Rcl3yLas4UfmqraMjIrq5GsmquLiO/cMwRr768A=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/tls-channel.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			9S7fFUfEr4i8a/CNi5mxOHKx4Of/wZLbGe/Ell1Ql/g=
			</data>
		</dict>
		<key>plugins/cwm-plugin/lib/vcs-protocol.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			lbEesohGeyoUIYpop1dyCBjIIyPROkOdowookuCY8vY=
			</data>
		</dict>
		<key>plugins/cwm-plugin/quiche-native/darwin-aarch64/libquiche.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			jUFmh4yQv1k1rjZvuhLngjt4FwHec0k7hmW2122Gxio=
			</data>
		</dict>
		<key>plugins/debugger-streams-core/lib/debugger-streams-core.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			OzGQEvLSJB+2lY3J+rHWHPNsYifhxTQ6tuBBLbJydzM=
			</data>
		</dict>
		<key>plugins/dev/lib/dev.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			52FQAHL5l02U2ZflHwqbrAoplu0m2BwOqTeV/E/Yp7U=
			</data>
		</dict>
		<key>plugins/eclipse/lib/eclipse-common.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			CiUGtx/Cvu8qF/cPdxixRrk6UKRKebzb/UNN7nQhX0s=
			</data>
		</dict>
		<key>plugins/eclipse/lib/eclipse-jps.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			JMkbY/NJf+nbrKTbHu6Ocg0rgZJRgXe+mggqJdTgExU=
			</data>
		</dict>
		<key>plugins/eclipse/lib/eclipse.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			8Iid9gg6hALI98gJunD0vJ6tA4Hy17ID7SXQjlcWFyk=
			</data>
		</dict>
		<key>plugins/editorconfig/lib/editorconfig.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			AQaaZuSo9VDKmqVcCEL46XkDvxfeua4eTq+FgUXEj0M=
			</data>
		</dict>
		<key>plugins/featuresTrainer/lib/featuresTrainer.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			FjKR7JCqnREKO5ap7qRnzf/Xxe4ZA1ylF+qNphv8ZM4=
			</data>
		</dict>
		<key>plugins/fullLine/full-line-native-server-macos-arm_64.zip</key>
		<dict>
			<key>hash2</key>
			<data>
			YM9sHckrsET2IhJXaNCNrtkPlbAbqDMWa/VWdEMElhc=
			</data>
		</dict>
		<key>plugins/fullLine/lib/fullLine.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			9o+SN2hU6onVK685aMCuAga7+YaYELorjgTdl7G1FVQ=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.fullLine.css.local.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			NoQaZWE07YDCLxCa7owTttZz44WeqowAEVPdVfJUeus=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.fullLine.go.local.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			S0GR7U4/XA4Ao1zsZyuB28lMPFY2eRVVJgagHwxKatA=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.fullLine.html.local.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			GHI1C8BVPSvDVrHteSRFfXYlQ9xwXvuXytn8qM2hebg=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.fullLine.java.local.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			m62lVuT30ua33a9vWqR5UblKVrli7ST7rNsVogtmExg=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.fullLine.js.local.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			YLsXM4k2ojA4+WdP9dWtnQLSj84ZNU4f/NpCH8OyA1g=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.fullLine.kotlin.local.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			N7dZzWRqb381YddcfcDMTeG2tTeAIgBkm7TrhIASceQ=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.fullLine.local.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			LMl7nhif7X3Ciz+WOgPguP1VuPH7CybzaAJ7ihMXdBA=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.fullLine.php.local.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			OuOgLj6h0uAk1ajPI8P4IpKgt5+g3nzEQUIJWJpOvKM=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.fullLine.python.local.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			eOMry2ogEG+ROVEkrn7p0LH3rsctYUQA4wORKIrnTTE=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.fullLine.rider.cpp.local.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			KGdXXLpiXrD+FHDse6qz5KoJsTSyjikhc3KHizQZILg=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.fullLine.rider.csharp.local.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			kPZ72DZbr5WuczU553M11xpncNzCpV59zRdW3QiFOps=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.fullLine.ruby.local.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			+upv4pGz4nDMlM7wtPkPV/w5phRjQemGOAG6x9Pw9tE=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.fullLine.rust.local.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			qB+txePr+0wtI6wPl5if2hEWY61d15TuhlW8kEaSUyM=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.fullLine.terraform.local.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			dgaKhyN+IVQ6FleP0gtX+tD9Z4H2WNvA82PqHzuM9Qg=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.ml.llm.cpp.completion.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			WFFtetOHluu4EWBsb9pyKu3o8PYoorkep/+ZO3FGccI=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.ml.llm.css.completion.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			jadMnM3JaFgopGQK+k/rDV+RpCTGa6Vi9YvEKRaplew=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.ml.llm.go.completion.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			fct4B0GGhijwLER7epDPQDJbyx5s/o/bZgwPt3N2mBI=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.ml.llm.html.completion.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			qdi7IVqixUhEa2nfPCwouYa35wJJTkXndKmDekvLbuA=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.ml.llm.java.completion.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			68mxyjw/T2Yw7rCJKTYkHIlvmtRoxYjSvTpIfW4TuYQ=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.ml.llm.javascript.completion.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			7Lnr0/VBB54NUy55wHrTz5B+hzXZo3ufqtCZeXOxHtw=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.ml.llm.kotlin.completion.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			SotgKNIg2YYIMzCp99hXvSNFf7P+byay6I8mHyCjFmU=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.ml.llm.php.completion.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			gxHNguvQ94p5B+VmLK/h3/6SME+Y8YxStPiM9mBp0ks=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.ml.llm.python.completion.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			695u89+x8G4/i1Ozzu1p16F1won/QcR3PrpSs8t3tW4=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.ml.llm.rider.cpp.completion.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			3r2ZusrKVwYJ1XO/QTez2KgNeyDrZhosEg3/dU3DzGQ=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.ml.llm.rider.csharp.completion.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			7Km1BISRKyisIxsoZaz9gOxq2nH3D5TI502QdsHVFk0=
			</data>
		</dict>
		<key>plugins/fullLine/lib/modules/intellij.ml.llm.ruby.completion.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			mExUM358MoCnFPujJdJU6o/wbo9TyZNxDp9pEfeph1Y=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Aqua/AIEnterprise.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jfwrTvze26FCO4sSO76PSNWBL3gm8XTGdRLtDMFtxkc=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Aqua/CloudCompletion.png</key>
		<dict>
			<key>hash2</key>
			<data>
			/MNRqJIWguzPWLWhHQIZgTBfbB/D6y+gXp6FmrcowsU=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Aqua/CloudCompletion_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			/JZFKNJNE6LQDsUlqdcV1apMGuH2qmWIRk4gw3BwP5M=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Aqua/CommandGenerationInTerminal.png</key>
		<dict>
			<key>hash2</key>
			<data>
			xdWgeTroC75oZ5w3P3U0AySPWFP09atDXkQruGJ3mGs=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Aqua/CommandGenerationInTerminal_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			TD22hE0juQT4Cjo3ur4jzyxGhTiZJ9iVEUXySKydruI=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Aqua/ContextAwareChat.png</key>
		<dict>
			<key>hash2</key>
			<data>
			FdZkb+/1Op4CsUeEhHBT0TEtrI36bQ6F8O1hIkZvqGQ=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Aqua/ContextAwareChat_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			FG49cgb93WWjtD3BqRV8pzUfmIwxFz68jItdv6+BPZo=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Aqua/FixWithAI.png</key>
		<dict>
			<key>hash2</key>
			<data>
			PALPhDrt2dWnOj7BQ6zb8A5KVOvkp+ClAs4/xuhPUTM=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Aqua/FixWithAI_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			HXOsF9rqyuuR9Gx61M89BEXsadUJvWWvvks4h70ynwU=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Aqua/GenerateTests.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Hl74E2FSW8/FwKcTYS+s6pgXlAcJsWteH89O2guY7dA=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Aqua/GenerateTests_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			h8rD3w473R+ujEaUMbaRDw9zVNHPUPO3Zwe8OLpA66Y=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Aqua/LocalCompletion.png</key>
		<dict>
			<key>hash2</key>
			<data>
			R/eCCSD+MrH8rD+N32ymIcl6JjUxNFwr7TjGYDnXnwA=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Aqua/LocalCompletion_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			FkGUDGpTvEvf6mccffKgt+vKLrOW/jJ4n4qqg80RVBc=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Aqua/MergeWithAI.png</key>
		<dict>
			<key>hash2</key>
			<data>
			XO1r+Jr9zAWrg2k8XgGo0YfVFVm3Uu+mc/0cwgKmKRg=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Aqua/MergeWithAI_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			M1RXQqG1rFvRBPb2en9ltbLhavU2uheqTlPVeNcoHys=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Aqua/content.json</key>
		<dict>
			<key>hash2</key>
			<data>
			IuWLPONrxMtYSJTdksWY7Rc0VMaF5zhDQPN61eE7Ch8=
			</data>
		</dict>
		<key>plugins/fullLine/promo/CLion/AIEnterprise.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jfwrTvze26FCO4sSO76PSNWBL3gm8XTGdRLtDMFtxkc=
			</data>
		</dict>
		<key>plugins/fullLine/promo/CLion/CloudCompletion.png</key>
		<dict>
			<key>hash2</key>
			<data>
			iaYhCpNHrYzmLGBzk/26EghSkVQN2YRxkVi+iMQ0EXA=
			</data>
		</dict>
		<key>plugins/fullLine/promo/CLion/CloudCompletion_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			q8SC+0BSwBMNoD0df7O8ex2kVocj++BKTmgDSfW3kmA=
			</data>
		</dict>
		<key>plugins/fullLine/promo/CLion/CommandGenerationInTerminal.png</key>
		<dict>
			<key>hash2</key>
			<data>
			w7HK1K/7HujxfrlghUtayyKLyqIhQHQh32j1scwneSw=
			</data>
		</dict>
		<key>plugins/fullLine/promo/CLion/CommandGenerationInTerminal_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			mplY8Pjc1BgpI04J5AvZtqdHnAu8LUcqLWGoEEKmIyE=
			</data>
		</dict>
		<key>plugins/fullLine/promo/CLion/ContextAwareChat.png</key>
		<dict>
			<key>hash2</key>
			<data>
			G04TYhB22x8R/us+8TgY9Yi5ZKmumO/K7Qo4t8Gt56k=
			</data>
		</dict>
		<key>plugins/fullLine/promo/CLion/ContextAwareChat_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			HBTtnYNq43J2Dotjk9UArZWnawF70BtO6j/dBFa+wl0=
			</data>
		</dict>
		<key>plugins/fullLine/promo/CLion/FixWithAI.png</key>
		<dict>
			<key>hash2</key>
			<data>
			rzg84a5f4GNlJAkOxtND1IOGlIoTY2GEyaHnemWuxXY=
			</data>
		</dict>
		<key>plugins/fullLine/promo/CLion/FixWithAI_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			c+4HktP1NWUgBJ1T+bUlF4J+pk09d1LiyrB9PS3EK/c=
			</data>
		</dict>
		<key>plugins/fullLine/promo/CLion/LocalCompletion.png</key>
		<dict>
			<key>hash2</key>
			<data>
			vZEwCFlVcFArvkzVMCDVVjh4Q9jf+GhehrdrlbXntF8=
			</data>
		</dict>
		<key>plugins/fullLine/promo/CLion/LocalCompletion_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			38Ggb8zpAbyFcMyOFzLgNwnPBDOOgIJcHMZLHN5X3G8=
			</data>
		</dict>
		<key>plugins/fullLine/promo/CLion/MergeWithAI.png</key>
		<dict>
			<key>hash2</key>
			<data>
			b6BqNsrbaoMbhZeGazh74pKe33y0VhCp2r3yqM1wC2g=
			</data>
		</dict>
		<key>plugins/fullLine/promo/CLion/MergeWithAI_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Ss8XlAaXeW+FkZStfPQ7Z82An7M+U57+gmy36jXpMLQ=
			</data>
		</dict>
		<key>plugins/fullLine/promo/CLion/content.json</key>
		<dict>
			<key>hash2</key>
			<data>
			77VETt7hV2Uind8zooJeUDVCxY5xekhV0Q+KWxEcYtY=
			</data>
		</dict>
		<key>plugins/fullLine/promo/DataGrip/AIEnterprise.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jfwrTvze26FCO4sSO76PSNWBL3gm8XTGdRLtDMFtxkc=
			</data>
		</dict>
		<key>plugins/fullLine/promo/DataGrip/ContextAwareChat.png</key>
		<dict>
			<key>hash2</key>
			<data>
			QptYEOGIUn6ZB8xXuDN3OrigK/qq9aGyvWDTqE6Uazs=
			</data>
		</dict>
		<key>plugins/fullLine/promo/DataGrip/ContextAwareChat_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			dAj5mrP9L8nkr+OSpiB58FscWb0pumilCBPb4ouPxyk=
			</data>
		</dict>
		<key>plugins/fullLine/promo/DataGrip/FixWithAI.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ca5dQ3WCM4timVDx86aCY0EsaHzHMuNu/m6YtPrR2Kg=
			</data>
		</dict>
		<key>plugins/fullLine/promo/DataGrip/FixWithAI_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			leN6beoslkz8HEg2CO+UcfHyyisD/RDRQIuQUViIwV0=
			</data>
		</dict>
		<key>plugins/fullLine/promo/DataGrip/MergeWithAI.png</key>
		<dict>
			<key>hash2</key>
			<data>
			VXNtwjTI+3WhKy8+scT37IFCiVNhdEcAGFb1lUZF5RE=
			</data>
		</dict>
		<key>plugins/fullLine/promo/DataGrip/MergeWithAI_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			N9E6+H0tzpLhIGs4h9JLaLSjQDekCEIs0pKmaQAFgeY=
			</data>
		</dict>
		<key>plugins/fullLine/promo/DataGrip/content.json</key>
		<dict>
			<key>hash2</key>
			<data>
			TXZ8PRySClyTZ4yhzFq5X/aatqu6uY1N8TNcTcFefbo=
			</data>
		</dict>
		<key>plugins/fullLine/promo/DataSpell/AIEnterprise.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jfwrTvze26FCO4sSO76PSNWBL3gm8XTGdRLtDMFtxkc=
			</data>
		</dict>
		<key>plugins/fullLine/promo/DataSpell/CloudCompletion.png</key>
		<dict>
			<key>hash2</key>
			<data>
			R/chTKy1+1jGtevJgGuV0ILd3amMoYKgp69WoTTBrnM=
			</data>
		</dict>
		<key>plugins/fullLine/promo/DataSpell/CloudCompletion_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			3GpE1Z786g2P52KF7V7MLhaCM6ngH2lSN7uWpdtwPk0=
			</data>
		</dict>
		<key>plugins/fullLine/promo/DataSpell/ContextAwareChat.png</key>
		<dict>
			<key>hash2</key>
			<data>
			qBVdwpZf0VRFrqIG5m8aiQE4bWg8Uufw8MJPkLexWNk=
			</data>
		</dict>
		<key>plugins/fullLine/promo/DataSpell/ContextAwareChat_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			0FJKrbVzJOPLT9UNzaW8RE0cVyD1TQ5w8cDMDGIehDI=
			</data>
		</dict>
		<key>plugins/fullLine/promo/DataSpell/FixWithAI.png</key>
		<dict>
			<key>hash2</key>
			<data>
			9QNAn43F3Vuj8XAZ4wPv/qZvOowdpP3kNPrpQaZmVb0=
			</data>
		</dict>
		<key>plugins/fullLine/promo/DataSpell/FixWithAI_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			1LDiyaiF9LMJbX8JIXMtZUQpMyHXYMP61Cw2oOm/Nr0=
			</data>
		</dict>
		<key>plugins/fullLine/promo/DataSpell/LocalCompletion.png</key>
		<dict>
			<key>hash2</key>
			<data>
			WExGr6NV+IsEC2ITaekelPtwJQ67YgW+dfQa2wHLkr0=
			</data>
		</dict>
		<key>plugins/fullLine/promo/DataSpell/LocalCompletion_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			wxcek0FO6FtSg2mo3Z2EbI6kcmoHYOhUOpp4tQ1OKCM=
			</data>
		</dict>
		<key>plugins/fullLine/promo/DataSpell/MergeWithAI.png</key>
		<dict>
			<key>hash2</key>
			<data>
			spqI9ZoXazffMAxbGg6F6q9Yv62N8C92cKGnadO+ec8=
			</data>
		</dict>
		<key>plugins/fullLine/promo/DataSpell/MergeWithAI_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			fl0BdoA9eErGt6PYefzzX3YLORUwsTaizv4ySe1nGK8=
			</data>
		</dict>
		<key>plugins/fullLine/promo/DataSpell/content.json</key>
		<dict>
			<key>hash2</key>
			<data>
			nbiK+al1sj2mHmcdaNzzR3MvBOl9sdOadXhhunbnOiA=
			</data>
		</dict>
		<key>plugins/fullLine/promo/GoLand/AIEnterprise.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jfwrTvze26FCO4sSO76PSNWBL3gm8XTGdRLtDMFtxkc=
			</data>
		</dict>
		<key>plugins/fullLine/promo/GoLand/CloudCompletion.png</key>
		<dict>
			<key>hash2</key>
			<data>
			4KiLX7nXtLRfwqyg0c3fcl1gI1hfoYEGQuItgYQQlMM=
			</data>
		</dict>
		<key>plugins/fullLine/promo/GoLand/CloudCompletion_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			h2XGgSW9kmRKlcdGe3EVO+xLqxarzGVCoL6IPLZjk/k=
			</data>
		</dict>
		<key>plugins/fullLine/promo/GoLand/CommandGenerationInTerminal.png</key>
		<dict>
			<key>hash2</key>
			<data>
			tXTry3esRB/7QQFVlQQRCMCBMUu9WozOtH3k5G67ZRs=
			</data>
		</dict>
		<key>plugins/fullLine/promo/GoLand/CommandGenerationInTerminal_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			OsyQVsaWaTJV3rIhWDWzhiCVAaL7nnyuUhwVkl4wRto=
			</data>
		</dict>
		<key>plugins/fullLine/promo/GoLand/ContextAwareChat.png</key>
		<dict>
			<key>hash2</key>
			<data>
			zfRejh1io+FUoSWsYDqvnJ+PKajOaLaSfLa1gyY5frI=
			</data>
		</dict>
		<key>plugins/fullLine/promo/GoLand/ContextAwareChat_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			RWwyy1ewl484hhk7K2o22LfotYXQhvUYrLIjA55nJek=
			</data>
		</dict>
		<key>plugins/fullLine/promo/GoLand/FixWithAI.png</key>
		<dict>
			<key>hash2</key>
			<data>
			3ZSjghu4yKGY+Q6HPPCtMvYLXqZMYL40890rZYdehbQ=
			</data>
		</dict>
		<key>plugins/fullLine/promo/GoLand/FixWithAI_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			OTRfXTk1RUZZL9w9bdaJgXmbqktH/CEaXz+67U/B5AA=
			</data>
		</dict>
		<key>plugins/fullLine/promo/GoLand/LocalCompletion.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ZHsH6oqKLk0Uf6oELylU+cKTEvuLkboS1lGtRJ4myrk=
			</data>
		</dict>
		<key>plugins/fullLine/promo/GoLand/LocalCompletion_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			KdapyjuLZP41CBIbmR59QuTdsWDuc5iiCUaedzKekXo=
			</data>
		</dict>
		<key>plugins/fullLine/promo/GoLand/content.json</key>
		<dict>
			<key>hash2</key>
			<data>
			lUMkCdjzc5GWNX69mpU3idO0Oav8sxcgoHOHPDPJyak=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Idea/AIEnterprise.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jfwrTvze26FCO4sSO76PSNWBL3gm8XTGdRLtDMFtxkc=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Idea/CloudCompletion.png</key>
		<dict>
			<key>hash2</key>
			<data>
			WblnvHpJz+oQ8Z3CiFKe0owOMKQY9+JFMFLplIMshA8=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Idea/CloudCompletion_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			4c7apoSm1F8S6ElgJw7319sZLdlUbSA9uXmP4OzO/50=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Idea/CommandGenerationInTerminal.png</key>
		<dict>
			<key>hash2</key>
			<data>
			hopHNJ2zNwW9r10nwV5SQkpwz+4wj0VBVtzH9fk+pzY=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Idea/CommandGenerationInTerminal_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ssP1/mJCA3u633P339ssteqMSQ3Eixg/IAtM7wNPY5c=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Idea/ContextAwareChat.png</key>
		<dict>
			<key>hash2</key>
			<data>
			VCzh4uH9t45Ipr3eJM1E6SZcvLkdyOFuNTCTyl8jZLc=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Idea/ContextAwareChat_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			bfAQ2FQ8MHo8TzdMf69eopXOdvuGHJVxICinMLy4URI=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Idea/FixWithAI.png</key>
		<dict>
			<key>hash2</key>
			<data>
			iltB6HacRPFW4ErJ/CGqNZbrqqM36P7NESatNywgqLo=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Idea/FixWithAI_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			U/JvS9xp+xjRjbDKEF13yAbMPz2Wl4A4nPc93rNnFS8=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Idea/GenerateTests.png</key>
		<dict>
			<key>hash2</key>
			<data>
			T0ztQYC0RNtPBlKr/qaCfj1aH9DxB9iJHggIvjOgMU8=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Idea/GenerateTests_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			qJjusuJ5DNZTCQg4zdx8OzSeEYoHRtFdSM+/UkBWjJc=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Idea/LocalCompletion.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ntP6/Imi6RuNIkjZ9L83p/C1R2tWIoV3DYJ/L4Huom8=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Idea/LocalCompletion_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			1YZkHdS8tCDAfpSgpslnu7aAv5DmwrEAw4QA5klY8SA=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Idea/MergeWithAI.png</key>
		<dict>
			<key>hash2</key>
			<data>
			+LVtmjsC5EpEeYKbWVIkBLGv3yEg4fZmbc61ApHpJdI=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Idea/MergeWithAI_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			lQHhuR6DIGxC9nGEhmulVQJe4iA8P1FnIIJaOuOz8uU=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Idea/content.json</key>
		<dict>
			<key>hash2</key>
			<data>
			IuWLPONrxMtYSJTdksWY7Rc0VMaF5zhDQPN61eE7Ch8=
			</data>
		</dict>
		<key>plugins/fullLine/promo/PhpStorm/AIEnterprise.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jfwrTvze26FCO4sSO76PSNWBL3gm8XTGdRLtDMFtxkc=
			</data>
		</dict>
		<key>plugins/fullLine/promo/PhpStorm/CloudCompletion.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Mau/SU12xVLb2FvVOV4o3Ml2rPZ80ccRD3vkd57Tewg=
			</data>
		</dict>
		<key>plugins/fullLine/promo/PhpStorm/CloudCompletion_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			mhyWZ5ylKaoLoQEiBMlitYCY+Gq0rE9GKXRXLvvHVmw=
			</data>
		</dict>
		<key>plugins/fullLine/promo/PhpStorm/CommandGenerationInTerminal.png</key>
		<dict>
			<key>hash2</key>
			<data>
			UKvM9IsCGKvYuI6YOOAhN+l/sOrg7xATuJke/HxwV7M=
			</data>
		</dict>
		<key>plugins/fullLine/promo/PhpStorm/CommandGenerationInTerminal_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			2Hp6ylpXvjYiVjYufydGColCuZR+zrNr3OHEkcWN6Is=
			</data>
		</dict>
		<key>plugins/fullLine/promo/PhpStorm/ContextAwareChat.png</key>
		<dict>
			<key>hash2</key>
			<data>
			569kBAnkyziHMcm1lp28hf/fbiP2f+J/T9Uyf5o/o0A=
			</data>
		</dict>
		<key>plugins/fullLine/promo/PhpStorm/ContextAwareChat_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			b8L2lPoDg92z1QL/eUmG1pGzZ39b3LfajVEaEOvWFL0=
			</data>
		</dict>
		<key>plugins/fullLine/promo/PhpStorm/FixWithAI.png</key>
		<dict>
			<key>hash2</key>
			<data>
			3b3C75elLzIf1JIyRWq6OGWGUZeumO+dfXJQ5BWWj7Q=
			</data>
		</dict>
		<key>plugins/fullLine/promo/PhpStorm/FixWithAI_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			WrytmNXTZop5E8sTrwezfkeif3cu7GEIfSL5Do9ilg8=
			</data>
		</dict>
		<key>plugins/fullLine/promo/PhpStorm/GenerateTests.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Tc86qEnnloqPciea8wzCRinxPDES736mHsQVBtXFTaU=
			</data>
		</dict>
		<key>plugins/fullLine/promo/PhpStorm/GenerateTests_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			YgtYRoSumftQsY6IPj/HNIvfsiB/3F3De/RADEpPogU=
			</data>
		</dict>
		<key>plugins/fullLine/promo/PhpStorm/LocalCompletion.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Unl1zwJa5ZQpKPVJU7c+3C16xeMivunJB6RH1M6zRSs=
			</data>
		</dict>
		<key>plugins/fullLine/promo/PhpStorm/LocalCompletion_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			YPbN2uJqR88iRIVvRu+/VyMv01gxUTNGtRBaQ+eg0jw=
			</data>
		</dict>
		<key>plugins/fullLine/promo/PhpStorm/MergeWithAI.png</key>
		<dict>
			<key>hash2</key>
			<data>
			qz+pSO/ZsgBGkY4c1Hu3+azhvMfgkUYl4Wa0jsYW5So=
			</data>
		</dict>
		<key>plugins/fullLine/promo/PhpStorm/MergeWithAI_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			F0U8HKarXTSfr6a5ymA5TYRZ6amLo7x6N42p5J4qU60=
			</data>
		</dict>
		<key>plugins/fullLine/promo/PhpStorm/content.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1dpVX/BJldQehdTi1bOixhlmvB6krxCQ05jttqWz01o=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Python/AIEnterprise.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jfwrTvze26FCO4sSO76PSNWBL3gm8XTGdRLtDMFtxkc=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Python/CloudCompletion.png</key>
		<dict>
			<key>hash2</key>
			<data>
			R/chTKy1+1jGtevJgGuV0ILd3amMoYKgp69WoTTBrnM=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Python/CloudCompletion_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			3GpE1Z786g2P52KF7V7MLhaCM6ngH2lSN7uWpdtwPk0=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Python/CommandGenerationInTerminal.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Tx6W0EdBfdG3Fc4ZbQY+ECdVFFeUFcP44nzKwmBgIrQ=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Python/CommandGenerationInTerminal_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			2SeG4S0+UI0gtpglkJHGrYlxd7aFLHBVDGxgP6W+e7U=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Python/ContextAwareChat.png</key>
		<dict>
			<key>hash2</key>
			<data>
			qBVdwpZf0VRFrqIG5m8aiQE4bWg8Uufw8MJPkLexWNk=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Python/ContextAwareChat_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			0FJKrbVzJOPLT9UNzaW8RE0cVyD1TQ5w8cDMDGIehDI=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Python/FixWithAI.png</key>
		<dict>
			<key>hash2</key>
			<data>
			9QNAn43F3Vuj8XAZ4wPv/qZvOowdpP3kNPrpQaZmVb0=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Python/FixWithAI_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			1LDiyaiF9LMJbX8JIXMtZUQpMyHXYMP61Cw2oOm/Nr0=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Python/GenerateTests.png</key>
		<dict>
			<key>hash2</key>
			<data>
			tFbdhnUb7BJwfjnPCgsQMp0fXGSQpEUWFck3uMAQBV0=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Python/GenerateTests_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			xmOf2XeVktwNvEPOOPkfP05KiyMFpi4OCLcy6GpXwhc=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Python/LocalCompletion.png</key>
		<dict>
			<key>hash2</key>
			<data>
			WExGr6NV+IsEC2ITaekelPtwJQ67YgW+dfQa2wHLkr0=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Python/LocalCompletion_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			wxcek0FO6FtSg2mo3Z2EbI6kcmoHYOhUOpp4tQ1OKCM=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Python/MergeWithAI.png</key>
		<dict>
			<key>hash2</key>
			<data>
			spqI9ZoXazffMAxbGg6F6q9Yv62N8C92cKGnadO+ec8=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Python/MergeWithAI_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			fl0BdoA9eErGt6PYefzzX3YLORUwsTaizv4ySe1nGK8=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Python/content.json</key>
		<dict>
			<key>hash2</key>
			<data>
			IuWLPONrxMtYSJTdksWY7Rc0VMaF5zhDQPN61eE7Ch8=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Rider/AIEnterprise.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jfwrTvze26FCO4sSO76PSNWBL3gm8XTGdRLtDMFtxkc=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Rider/CloudCompletion.png</key>
		<dict>
			<key>hash2</key>
			<data>
			zaze/84T+QBaH9nSXpCJhnOx8JNJAA/P6T0S46BZR2E=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Rider/CloudCompletion_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			cttv35yx3wvtqsh62G1w/oIWUEHjbePW7npK9YyhDMo=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Rider/CommandGenerationInTerminal.png</key>
		<dict>
			<key>hash2</key>
			<data>
			fn1AZ9i6iX50CROUQHxOC6t/ocpcJuFWS6CdQUXeuAY=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Rider/CommandGenerationInTerminal_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			grTioXX7hGX5YGXb59G3lcAEIOZBTeo8ph8LzFbFPuA=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Rider/ContextAwareChat.png</key>
		<dict>
			<key>hash2</key>
			<data>
			QYBmU69PNbk2k5IIQM9XHFfpfKP51Q6zJVziToxHFx0=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Rider/ContextAwareChat_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			xozKP9IEW7MhPZ+UATPX5jI0hsNPSGvujFGkSXXR3RQ=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Rider/FixWithAI.png</key>
		<dict>
			<key>hash2</key>
			<data>
			8FhBOXLtqHfKnuXk5lC1hkSfXmW2jqqEsnmo4vXJhMU=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Rider/FixWithAI_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			yRJa/0VDfoyiK/wtfNpMqjh+kC9ODvAMvWIww/ueaGo=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Rider/LocalCompletion.png</key>
		<dict>
			<key>hash2</key>
			<data>
			kVIjkBqGr5i8HAbeD7ngiy30ZbDHZhlkxlkwkOJN14Y=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Rider/LocalCompletion_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			EqJcGemSZR+yS2Z0UgIoJA6LphekfMBA2RWseBSEIgQ=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Rider/MergeWithAI.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ZoTwOzzSCotCsgpUyGNACrsYc6ZohbKHZRjUS355rF4=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Rider/MergeWithAI_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			M9mCiy7+MSCZnzuxuwY7p8yJIpHlgi348kRgrmKKuIQ=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Rider/content.json</key>
		<dict>
			<key>hash2</key>
			<data>
			3WuAAuUUx79hw1jqMzxXHXigLQAEdvr6VudBk6BlkI0=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Ruby/AIEnterprise.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jfwrTvze26FCO4sSO76PSNWBL3gm8XTGdRLtDMFtxkc=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Ruby/CloudCompletion.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Y2JKpDxfAzjhTK/rXhBuKJ4ZAAfSUzI3qsFCQTfGQFM=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Ruby/CloudCompletion_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			pAtMcsikdRWZ72+xnWSR1pWf3/gNGA5LUoHRCSKGPr0=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Ruby/CommandGenerationInTerminal.png</key>
		<dict>
			<key>hash2</key>
			<data>
			oHu5uOi/ysmOC1XGiiHZcTFyBwK2VgpnF5sJQ66Fpz0=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Ruby/CommandGenerationInTerminal_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			4PpvOmiDcoCCx3nwQyCsK4EJ1OR7xta2lFQs3C9ZnLk=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Ruby/ContextAwareChat.png</key>
		<dict>
			<key>hash2</key>
			<data>
			aJIziSayZ0zFFQY76NXs5Ln/jr/K8Qvzl/kCO6k0rFY=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Ruby/ContextAwareChat_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			20dCh5UlbeGRbJ5+HHdZq7iwCF0nzGw1aAw/oli+acg=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Ruby/FixWithAI.png</key>
		<dict>
			<key>hash2</key>
			<data>
			2IxOH8BK/Ocx90qqUaNMfMEw/ceF4tdXYQQqLpMN60E=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Ruby/FixWithAI_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			RiqAtjCgkiqPD97hg1M/e0LFfYtEe/tbDjA4gYwn0T4=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Ruby/GenerateTests.png</key>
		<dict>
			<key>hash2</key>
			<data>
			JRhc/g1B4z71eugpHOIBt1yYkxK+YNxhhAorgbR3alE=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Ruby/GenerateTests_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			kgMvblQ3hCrR9Hrk2lhgl/ZRk/CcaDILimFfkKnk83I=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Ruby/LocalCompletion.png</key>
		<dict>
			<key>hash2</key>
			<data>
			MJd0GyJGvXIK+qah8JgZvmXGp8nIlPSHkUlixhcAWd0=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Ruby/LocalCompletion_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			MAcqZjyqv2+rPm/ZjaIUrdMPHle5LLsLLVOeAf8jiAo=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Ruby/MergeWithAI.png</key>
		<dict>
			<key>hash2</key>
			<data>
			SjQdCdJzhd8MAaqW4otcd5NMdPOp3l8YzvE1XLBfzBQ=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Ruby/MergeWithAI_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			iUAgb2arv9bHMZZEWSqeFzkLceKngTKeOG/xv8A9FSo=
			</data>
		</dict>
		<key>plugins/fullLine/promo/Ruby/content.json</key>
		<dict>
			<key>hash2</key>
			<data>
			StXTYDpsxYmdFiyd8AGVtCuAAQEPbJEHdkNBh6//b20=
			</data>
		</dict>
		<key>plugins/fullLine/promo/RustRover/AIEnterprise.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jfwrTvze26FCO4sSO76PSNWBL3gm8XTGdRLtDMFtxkc=
			</data>
		</dict>
		<key>plugins/fullLine/promo/RustRover/CloudCompletion.png</key>
		<dict>
			<key>hash2</key>
			<data>
			3+wdgSCogfBWJ40FDXlGEqK11xRcvm3DgaNhGM6hFro=
			</data>
		</dict>
		<key>plugins/fullLine/promo/RustRover/CloudCompletion_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			2lMAgt4N0QGC4t+rujkk349LTb+ECxj432Z+FW6hOo8=
			</data>
		</dict>
		<key>plugins/fullLine/promo/RustRover/CommandGenerationInTerminal.png</key>
		<dict>
			<key>hash2</key>
			<data>
			RbvtWsMhTnNEPdavhKXbxVabzfJejnEujNHXqrGwV0A=
			</data>
		</dict>
		<key>plugins/fullLine/promo/RustRover/CommandGenerationInTerminal_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Id1I5m6za9fO1UFv3nH5gSHCGaQBfyCO8ilhz3XUWJk=
			</data>
		</dict>
		<key>plugins/fullLine/promo/RustRover/ContextAwareChat.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jJH9OV6D8+IWMMucDpXej/kGaa4MJpl6EXfQsfQy/VQ=
			</data>
		</dict>
		<key>plugins/fullLine/promo/RustRover/ContextAwareChat_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			pTP9OTFBfyQT9qHFk3s5NnyuwKJShMguALR57qOq570=
			</data>
		</dict>
		<key>plugins/fullLine/promo/RustRover/FixWithAI.png</key>
		<dict>
			<key>hash2</key>
			<data>
			14BXXF6JD+1Y4bzEQeKwOcQFefDFTTUC9itE+19P1Ow=
			</data>
		</dict>
		<key>plugins/fullLine/promo/RustRover/FixWithAI_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			x/K8+t+qZGck/PT+i7/PMG1675EPFVNSfZ530O4nG7k=
			</data>
		</dict>
		<key>plugins/fullLine/promo/RustRover/LocalCompletion.png</key>
		<dict>
			<key>hash2</key>
			<data>
			2WLYyNThLbzIuJv+nOdP41ZswTT79HgDsZgzRvvnvXM=
			</data>
		</dict>
		<key>plugins/fullLine/promo/RustRover/LocalCompletion_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			b4OT/qZPi5uwp7WqB0MAUQYCATE/qq0rrN0x6FMe2nk=
			</data>
		</dict>
		<key>plugins/fullLine/promo/RustRover/MergeWithAI.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Q0BRtbFymFFTUGgv67tuE/2uHZln49XtVBEidYgV/3w=
			</data>
		</dict>
		<key>plugins/fullLine/promo/RustRover/MergeWithAI_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			s0bbhbN7bEOikU19Vdd3I6Zz0VrRMbOijNrf2oJ+4Ag=
			</data>
		</dict>
		<key>plugins/fullLine/promo/RustRover/content.json</key>
		<dict>
			<key>hash2</key>
			<data>
			KV2Zl/2zm5dkEzbMuv7ZIq/NX4J/TJ8O5v6aqC89zmE=
			</data>
		</dict>
		<key>plugins/fullLine/promo/WebStorm/AIEnterprise.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jfwrTvze26FCO4sSO76PSNWBL3gm8XTGdRLtDMFtxkc=
			</data>
		</dict>
		<key>plugins/fullLine/promo/WebStorm/CloudCompletion.png</key>
		<dict>
			<key>hash2</key>
			<data>
			V9ywqO/EJirofDtTEGW2/Do3fHQrhPDp5111QYhLObs=
			</data>
		</dict>
		<key>plugins/fullLine/promo/WebStorm/CloudCompletion_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			4MkKaQ0pp5etveNYcvuXU3PFzdNG+PIIveMSZWWtTrI=
			</data>
		</dict>
		<key>plugins/fullLine/promo/WebStorm/CommandGenerationInTerminal.png</key>
		<dict>
			<key>hash2</key>
			<data>
			+62m5jmtp54/IvZvT3Bh0eXM5WrkFrsAJLtBTixqoYA=
			</data>
		</dict>
		<key>plugins/fullLine/promo/WebStorm/CommandGenerationInTerminal_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Jxi5z38zUtQFWaM4s1CMtGNWRyyNSzDgn0genIzCDQQ=
			</data>
		</dict>
		<key>plugins/fullLine/promo/WebStorm/ContextAwareChat.png</key>
		<dict>
			<key>hash2</key>
			<data>
			kIaMBIk2kFlG2WPQSUOk//k3dSjIO8AMWPrSmwpQibU=
			</data>
		</dict>
		<key>plugins/fullLine/promo/WebStorm/ContextAwareChat_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			LiokcZ2TsPdyakGWo7HmTEYb3y/xWr/JTeCDDKpjrsc=
			</data>
		</dict>
		<key>plugins/fullLine/promo/WebStorm/FixWithAI.png</key>
		<dict>
			<key>hash2</key>
			<data>
			VcElqwZtYNvWM7LAZotno4UoM3Lx7AXK3CwdVYB9wTI=
			</data>
		</dict>
		<key>plugins/fullLine/promo/WebStorm/FixWithAI_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			2JZCkk/KThyKkour049d1Cg/KjlvIgeFk/aaEYducSQ=
			</data>
		</dict>
		<key>plugins/fullLine/promo/WebStorm/GenerateTests.png</key>
		<dict>
			<key>hash2</key>
			<data>
			o9BdICKyCPyRl29WDY1mDD16mAF/LA+Sn8MehjuKYdU=
			</data>
		</dict>
		<key>plugins/fullLine/promo/WebStorm/GenerateTests_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			59FohwAatOKYMabcZWk1oYhyoNXfDEREqmAfog37B6w=
			</data>
		</dict>
		<key>plugins/fullLine/promo/WebStorm/LocalCompletion.png</key>
		<dict>
			<key>hash2</key>
			<data>
			G5MWffxlDZtxFb7gcE3A21IBcJdoWVmdjhzEeAtwaUo=
			</data>
		</dict>
		<key>plugins/fullLine/promo/WebStorm/LocalCompletion_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			rApvq9XcGfUF3dtIzxCrdlkaojvbIif2hPnoD6fZUCw=
			</data>
		</dict>
		<key>plugins/fullLine/promo/WebStorm/MergeWithAI.png</key>
		<dict>
			<key>hash2</key>
			<data>
			kSLfQZJTk76ho10y26NLEr25KXASA49K/3YPuqKRU7A=
			</data>
		</dict>
		<key>plugins/fullLine/promo/WebStorm/MergeWithAI_dark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			krtl2iwtUuZkOvxhgvo5UlvGdg/qbT0VkNZKDl80pWc=
			</data>
		</dict>
		<key>plugins/fullLine/promo/WebStorm/content.json</key>
		<dict>
			<key>hash2</key>
			<data>
			StXTYDpsxYmdFiyd8AGVtCuAAQEPbJEHdkNBh6//b20=
			</data>
		</dict>
		<key>plugins/gradle-analysis/lib/gradle-analysis.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			nk2jQ8xO59/H1q6mt2KHnzoqY4Jd8lsa6JRfR1DN06E=
			</data>
		</dict>
		<key>plugins/gradle-dependencyUpdater/lib/gradle-dependencyUpdater.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			3j4xfrzc5dJxLibeyDFFwGWf0pIvXGE4WSFBUN9EG4I=
			</data>
		</dict>
		<key>plugins/gradle-java-maven/lib/gradle-java-maven.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			5qn7nhl3pRL+zWdZz5zEHNYyHYdJPdH0fjQCN7VaSPI=
			</data>
		</dict>
		<key>plugins/gradle-java/lib/gradle-java.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			NkingrNj2afmkng4GEXR/EPmRRG+xWFohX1sKZNIrLA=
			</data>
		</dict>
		<key>plugins/gradle-java/lib/gradle-jps.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			XbGpkhqbzHXSRdsH/ZDI+kSHKOK4JoEl0wnk+CZI16g=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-antlr.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			CJDYN3Ecq4L1rqOCmBrAekcdQEQc1sfuosF7yyhwHxs=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-apache-bcel.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			XbruIVqrGXNu2QPyQlISvg59kJwz2SXqjRPSJ5jUOR0=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-apache-bsf.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			tT555O/XIbDWNHLQBxcR0gkrEG+GtTkP2p0Qz0J4nRA=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-apache-log4j.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			m/hYt2f90i3pG4aBf4X6SSAJ2lyo1Y88g1y0cKR8XWw=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-apache-oro.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			gevoQSPS1ze5Wz5Wc+oLgCaSR1V75x5Z/w4GEofwlt4=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-apache-regexp.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			+2oArIHFJGCrLQ6qqtL6SQQrH90y2gbBJ2PNqB1L2KA=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-apache-resolver.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			dJaidgjudpQ1/VLr3QzUL+fcLnskCqTcONTm1gdHBNU=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-apache-xalan2.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			GWTb6OTdxyXyi67jv9PkBJkQm9HZ2jCeuNhdu3J56JI=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-commons-logging.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			CJVfJbh2mErbHR/nSlSwyKnmRQXg2tzHzVEk4LzG7eQ=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-commons-net.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			w3p9wbi5Yi1y1+13sPiQAXNPOvGDxHB74zb6pS6N8hQ=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-imageio.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Ar8HbC4LU80//tx6nlsAkmgdLuyAh+w9hrdOOCiZ1Ls=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-jai.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Fi6gbtHBylWXk6T23Oe7y86Ojogz4xwT5RGpdbunCdQ=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-jakartamail.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			skRzf1WuONhKQyDJ1tFW1xxRg2vvbrd20rwNQPWZzp0=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-javamail.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			MAMZD2DGy3Ds2/QpNxkvFKoK3lAlJPQ88wsRPW2HWJ4=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-jdepend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			QvFKpiALmZnq/kfJtfCzHCYHN1KuxzAX9/K7+uHwQYE=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-jmf.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			dbvAi/Vkyd38bmaa0I1etL5J4r/GuIT7LGSvzqPm22I=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-jsch.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			BsVaWwCI+D2c22+q3Bc99ya0aqGMQ5ShoFDeyCzISyU=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-junit.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			rU3Q1Erg9Gl9htf0BzlQmDheOxdvTyP+Cnlobi4NAGI=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-junit4.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			vxaSMxPPYtAG0I/bZdpNM9ngT0U6mmj3JdEERSzz00I=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-junitlauncher.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			wX9yIB1l60zTtmz6JeAJsoZKZfliPLfLwHGCjvGVxP4=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-launcher.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			sLAURhld4nhwsLIWxjGpsk+NnVCqaLr67cbh6JbQRjs=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-netrexx.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			w2eBXQ2vWv4lNYDG/eQaStJZj6xvl44bFPFwfwY+iIc=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-swing.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			9aUMN69xKLOSDl2oeL3JQ4b10Rf22/oCWbml0Pp3B8Q=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-testutil.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			p15Yra/1aZSm8eIw4JHr8hlQ9gymYrQm3KKK8d9n19o=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant-xz.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			R+22EZZN3HJNLnn3xmT0KW75BJ4o75olu9vdd9Uduag=
			</data>
		</dict>
		<key>plugins/gradle/lib/ant/ant.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			9olFxE+9iqdBVCuDa1QUjIvRLh094o2mD3fl/fOA4ks=
			</data>
		</dict>
		<key>plugins/gradle/lib/gradle-api-8.12.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			VVUDihkCMKvnHEIy9GeslAkbb/ncw+HcSeG3GNvYbPE=
			</data>
		</dict>
		<key>plugins/gradle/lib/gradle-tooling-extension-api.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			yK1Dxh6lHVOXGlWxJ4WqqEFKZkC63GvKVYsIF8Hcd1Y=
			</data>
		</dict>
		<key>plugins/gradle/lib/gradle-tooling-extension-impl.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			FSmiqp4oap98ZATV7ZrqIhs1Vuq05fmzqWQtTBdCOAk=
			</data>
		</dict>
		<key>plugins/gradle/lib/gradle.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			il/OprEqajeG2sehl1u7MNce/hrbB9sjuRCHWnrFhfA=
			</data>
		</dict>
		<key>plugins/grazie/lib/grazie.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			XCnXGNT6pmdu50u0kvEiG8lnTUy/vu+ZmPNCu8gy2a4=
			</data>
		</dict>
		<key>plugins/html-tools/lib/html-tools.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			jqGo/KP08yToyMXaF90aAyNPdCzOIHfoWdWU77gyGLQ=
			</data>
		</dict>
		<key>plugins/indexing-shared/lib/indexing-shared.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			UWg18mKuu1pLtuHz9hLzCNpV2LCXSV3WWBKqt3llmVM=
			</data>
		</dict>
		<key>plugins/java-byteCodeViewer/lib/java-byteCodeViewer.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			JmmlQw1M8ixQMVzJVu/CIrDQkUubjryyhzQfc1+GM/s=
			</data>
		</dict>
		<key>plugins/java-coverage/lib/jacoco.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Ph9qVPAflahcpQ2OKOjbq7k17Dyz8sqZg/15L7OVWGo=
			</data>
		</dict>
		<key>plugins/java-coverage/lib/java-coverage-rt.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			ALJbDnlPiZYgjASTUueadtbwB+0OzYiRcYluqHJJLvE=
			</data>
		</dict>
		<key>plugins/java-coverage/lib/java-coverage.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			ZPASoshT3lCbYL6URtjx6wn6QnRHTb+Zy4W5ueU0DtE=
			</data>
		</dict>
		<key>plugins/java-debugger-streams/lib/java-debugger-streams.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			4uRDI6xsibxWQYIgGTWGmfha4nWuRbLy0Q6KPGDXlGo=
			</data>
		</dict>
		<key>plugins/java-decompiler/lib/java-decompiler.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			x00iTenbxVi/R23ZXxtPRZ1LD3fHjRhXyu6q/2HHwdk=
			</data>
		</dict>
		<key>plugins/java-i18n/lib/java-i18n.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			oKdmOwAXvzyAnYnrw/P4jjsh2ST/FOSr6Fm5mxKylP0=
			</data>
		</dict>
		<key>plugins/java-ide-customization/lib/java-ide-customization.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			zUbNHrVwBxJYdGpaFidKyXyzLBD5es7OSQSLgMbcj5k=
			</data>
		</dict>
		<key>plugins/java/lib/aether-dependency-resolver.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			JkcXgXpG40ltR2n4Vbnt0TaiRdhjTtnf9RUPKpRT7fY=
			</data>
		</dict>
		<key>plugins/java/lib/debugger-memory-agent.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			PpA8ElfsyeSRMkxey1L1zKlgkAQs8u0gWEYxUVMpRbA=
			</data>
		</dict>
		<key>plugins/java/lib/ecj/eclipse.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			87gT79ERVNrc36b45mCbFJqM1b0bMXlhrDkaoY00CSA=
			</data>
		</dict>
		<key>plugins/java/lib/frontend-split/java-frontend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			33Ppcd0NAznD8+rNJOo1l1DtslgCWVKGSitCeYVSuJY=
			</data>
		</dict>
		<key>plugins/java/lib/java-frontback.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			cHvOLfwIYT6M2BtJmRFbIxr+tzibdlqCQTms8G3zC7M=
			</data>
		</dict>
		<key>plugins/java/lib/java-impl.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			pvyxi49bNRmfEvyN2Z6iRgx5KXhEkFme6BxBUhnKyJQ=
			</data>
		</dict>
		<key>plugins/java/lib/javac2.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			oEbopx44CgOTnWuwQd+GU7Jmgd5zQKSBe/PuE10rQJw=
			</data>
		</dict>
		<key>plugins/java/lib/jb-jdi.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			b5lZt9eCLTs+ytalCcxbNuDYaaRI/04GgPIAmOgkAVI=
			</data>
		</dict>
		<key>plugins/java/lib/jgoodies-common.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			n4S8uEMQMjtt0KdBqF9gWbH8jLV0U1CrBu1fP6F9BTY=
			</data>
		</dict>
		<key>plugins/java/lib/jps-builders-6.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			aGCL0hn73rma18egsd0RFE2yBX30I022YuEwdNxAeno=
			</data>
		</dict>
		<key>plugins/java/lib/jps-builders.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			CiqM52rwamOih+tPEIhaFdrYKMUHbA1l6dJDafV7URo=
			</data>
		</dict>
		<key>plugins/java/lib/jps-javac-extension.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			JrDCnwAINP64cVT0bHlluuH6KkyWJXE813ioMDyID0s=
			</data>
		</dict>
		<key>plugins/java/lib/jps-launcher.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			k7mmu4UqouJlwMyrUQeTmjcFwf6mHavUN7RxsfJPUWc=
			</data>
		</dict>
		<key>plugins/java/lib/jps/java-compiler-charts-jps.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			lVHb80LF2I7PW+9qLGrTXDiAXiSjlNtsIcm7wIVT+PI=
			</data>
		</dict>
		<key>plugins/java/lib/jshell-frontend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			yB7OFxGUUxgswt01ws6axEGJ2k8jv5mqGeaNLM21AxY=
			</data>
		</dict>
		<key>plugins/java/lib/jshell-protocol.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			C3kyZ9MlJwULCrsYoQBD/T02iZjOCFDEpqO+GfMTSd0=
			</data>
		</dict>
		<key>plugins/java/lib/kotlin-metadata.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Pp4W8pvTudZqzDe+XGaPQbBtchqlFrC0oWLBS2yKL28=
			</data>
		</dict>
		<key>plugins/java/lib/maven-resolver-connector-basic.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			4H2yVnkxIjvWS+pm+iL1oATmrBHVa3eKmgW/JtbgtYo=
			</data>
		</dict>
		<key>plugins/java/lib/maven-resolver-transport-file.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			i9H0SChhdJrY/YAPfYvcZQP5GNICmXBs5jpfAEjuBjg=
			</data>
		</dict>
		<key>plugins/java/lib/maven-resolver-transport-http.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			4M/ttg572bV32jx8TESLvWZ4qB4JrN1sCqSpI1aE5X4=
			</data>
		</dict>
		<key>plugins/java/lib/modules/intellij.java.debugger.impl.backend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			iMh3qkxE5nPBXqE8+7Og5ssH9oYQAG2AHbbZpjwaL/o=
			</data>
		</dict>
		<key>plugins/java/lib/modules/intellij.java.debugger.impl.frontend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			E6dyqLas3ut53h8y2oBpnkmgQ3SONyW8oyaz4LC9agw=
			</data>
		</dict>
		<key>plugins/java/lib/modules/intellij.java.featuresTrainer.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			jNxCpM+wQ2CeKn3v8mPsLHet9PWb1v7MHyL9ARvHmh4=
			</data>
		</dict>
		<key>plugins/java/lib/modules/intellij.java.structuralSearch.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			u6BJTl+OJmeLU5YIMHeG7DZUeLAsiFs7aYMyF6V2x+o=
			</data>
		</dict>
		<key>plugins/java/lib/modules/intellij.java.unscramble.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			bFjK0zf1f8TSaJvG6NroWzNC8keZW4054XoLvqgQ0Yg=
			</data>
		</dict>
		<key>plugins/java/lib/modules/intellij.java.vcs.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			yBwfTIq4OwGptQ+Z70SyXmKcUxEvOUW7I9HzdxVSWlk=
			</data>
		</dict>
		<key>plugins/java/lib/modules/intellij.jvm.analysis.impl.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			9TsJ4nZ5e7BrxvSG5vg4a4LvDleOWQXG+sSAm+avmVY=
			</data>
		</dict>
		<key>plugins/java/lib/resources/jdkAnnotations.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			6qb1BNtLQeqlQ7QYJ1fXHgZAKXlpuw7sxJ4OnSGEp5s=
			</data>
		</dict>
		<key>plugins/java/lib/rt/debugger-agent.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			JylyGIFtC4TRc+4TeCn82WqMkChvx247H2ds9jjqPww=
			</data>
		</dict>
		<key>plugins/java/lib/rt/netty-jps.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			M23PJz0dt2rcjmysm7rjBirBio2Sr0gUyP1tbmrTRn4=
			</data>
		</dict>
		<key>plugins/java/lib/sa-jdwp.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			oz8bxkUOByzM8SWxIMY9+hsxd0g0j4FSTSJTIs/qtas=
			</data>
		</dict>
		<key>plugins/javaFX/lib/javaFX-common.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Db6WbM7ux2QZUxjix60Mde2w0cANip0RmtpWs+GRN+k=
			</data>
		</dict>
		<key>plugins/javaFX/lib/javaFX-jps.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			irXiAROgVhWQZARYJ7cEaR8Os3qlZx0h/TJ4vmhLkds=
			</data>
		</dict>
		<key>plugins/javaFX/lib/javaFX.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			PYJ3A0pA2f2ODy1HzP5H05SRj/fA18ePQokkvTdF9yY=
			</data>
		</dict>
		<key>plugins/javaFX/lib/modules/intellij.javaFX.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			3tWTpGwLey2NyJ6kCZVYYybrIzWApYaqxj3dARvZH+0=
			</data>
		</dict>
		<key>plugins/javaFX/lib/rt/sceneBuilderBridge.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			QLx2lVs3rG6ybvzvPU+B9GlV+va1NR5+EKVAT1CBUms=
			</data>
		</dict>
		<key>plugins/json/lib/frontend-split/json-frontend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			9clOPpZPTCHyG2P34Mhy1LUB6OxpdJISU1nHDaNUcTM=
			</data>
		</dict>
		<key>plugins/json/lib/json.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			drHEupXYm3gQt84x/bReHbHSD5UEvRYb1QA9hskZYM4=
			</data>
		</dict>
		<key>plugins/json/lib/modules/intellij.json.split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			fhbvtiDS0WEF0i9NUUXkmfVw02fecsorUHwTJQeY2mc=
			</data>
		</dict>
		<key>plugins/junit/lib/junit-rt.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			72RtAP82e8ZM+NwHltT1A0SPk0Gq5h6Bv1KDiUJVinM=
			</data>
		</dict>
		<key>plugins/junit/lib/junit.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			mqOzDPnJ7LSNpCmgZPQ3gRxV7TCW4Sc0j8fOJ1BFGXg=
			</data>
		</dict>
		<key>plugins/junit/lib/junit5-rt.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			ZG4xsZFyWFixtzje91ded3oK17nacSxPBxbCaoe1YGw=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/1030.js</key>
		<dict>
			<key>hash2</key>
			<data>
			GVF3RMRXuBRvhOczbzfnrc7rcBps1wovQfIqbOpDyNA=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/1066.js</key>
		<dict>
			<key>hash2</key>
			<data>
			CiMKVdO1ZaXoIXzdIk7X8ZU/56f/gOnCSFGhQAW7Wuo=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/1186.js</key>
		<dict>
			<key>hash2</key>
			<data>
			2oEzUSz5yfQU80icc53hB9s9xmsrQ7qvH03AtWcO4mU=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/1203.js</key>
		<dict>
			<key>hash2</key>
			<data>
			rKxrpzbmku5uDngtJSvEXCDO5Enwh59jAJtzKJMUufs=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/1283.js</key>
		<dict>
			<key>hash2</key>
			<data>
			PMZhwLyJm8abTSt2D8GdeLp/q1tCr1MT6aZZt9vS0fU=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/1326.js</key>
		<dict>
			<key>hash2</key>
			<data>
			TFSRHDTW2f01Z55O9LF8cMZff9ybbPSyCKky1X7pCMo=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/1408.js</key>
		<dict>
			<key>hash2</key>
			<data>
			ZNjXKnG7zBFKcDYeGdJeVnirFU//k/BkI9eFTpKz3y4=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/1671.js</key>
		<dict>
			<key>hash2</key>
			<data>
			pQsu/gnXi52syQ/qXNi3FF4W2gNOVyxZxofhz2zD1yQ=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/1773.js</key>
		<dict>
			<key>hash2</key>
			<data>
			ir9Pc7U/t9Len+cj7OU8tzSL14t4zN5eKojLbhXY84k=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/1778.js</key>
		<dict>
			<key>hash2</key>
			<data>
			yumbzgBk+oApNNI3355dN0GmSNTlXoodrso6rkaS2uA=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/1780.js</key>
		<dict>
			<key>hash2</key>
			<data>
			qUiIRe77e6YPdHBZ9RA/ZZPxI6k+p3cIudx0j318A5M=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/1809.js</key>
		<dict>
			<key>hash2</key>
			<data>
			08GqpYDjOZSAYUs3OialVOoKecY1lLFGOlFAi/X9zpU=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/1926.js</key>
		<dict>
			<key>hash2</key>
			<data>
			22K79gxB8hd+xWZwuwp3GSRq5nYQgnIT3RUuk6WGcbI=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/1927.js</key>
		<dict>
			<key>hash2</key>
			<data>
			M0dMoZw/zE2z9jYDNXxc/1xsvk14Y6OoU1xq74jRIxc=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/1974.js</key>
		<dict>
			<key>hash2</key>
			<data>
			66d1bE4S4Ui2nRdLLq/OEcdZjYdEU6Oy4xm/yv6dnEc=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/2175.js</key>
		<dict>
			<key>hash2</key>
			<data>
			ihmeEvN5iQwI5fDqc1U6/ypsK6u9cpW4/aagAY7+YEY=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/2196.js</key>
		<dict>
			<key>hash2</key>
			<data>
			KbuL7Jx/gt0abJwzLCa+vF5lXMrvqqCXD3KWJ5bL6M8=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/2237.js</key>
		<dict>
			<key>hash2</key>
			<data>
			jprYen3JbAj6TN041CfQoOJq98yUQUkINz9pbkUKT78=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/2287.js</key>
		<dict>
			<key>hash2</key>
			<data>
			DJHlmV8WK2nnl1EVPUH81kITy+S8jIzr5cYJK7ewQDo=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/2313.js</key>
		<dict>
			<key>hash2</key>
			<data>
			TVWTeXqWEqNEhg0e0cfbbuDQ8PqCbfDt/PDLNxFE7zU=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/2424.js</key>
		<dict>
			<key>hash2</key>
			<data>
			GKnsl8liE88HrS/335BzF5oOu9NZTbK9DpwAOIWh3wo=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/2459.js</key>
		<dict>
			<key>hash2</key>
			<data>
			6MeG128wNqQCtpuHscOXXUcpoI9ZMbhg1p1v4WZLTLU=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/2496.js</key>
		<dict>
			<key>hash2</key>
			<data>
			1z9dQl+JLHi0P+nqhZMH0p+n4giIC1Z0wt8ua/JGy9I=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/2578.js</key>
		<dict>
			<key>hash2</key>
			<data>
			xp0oUp83+AKdrS6i0W6r1dN97vbNBw99rRfWkX4qbrU=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/2580.js</key>
		<dict>
			<key>hash2</key>
			<data>
			Axvh22qZF1xSCXHeRKYM06IQEYQ8OrPKnw0y750wWxI=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/2698.js</key>
		<dict>
			<key>hash2</key>
			<data>
			unS3tEh21TuR77z8jK/0StZ9RNQ+50AzMovlALao+R8=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/2735.js</key>
		<dict>
			<key>hash2</key>
			<data>
			oOKMFnGCO6/62KeytoLX1l+FhXnGYfIQUsyv1afuxaA=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/281.js</key>
		<dict>
			<key>hash2</key>
			<data>
			/VRClQjJwH4a7HUSeTjHl7Fop6qi5HvAMuy3lMXysbw=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/2830.js</key>
		<dict>
			<key>hash2</key>
			<data>
			Qs+r5a+c6pyJE+NnSTMPZEd3dLmtssId/q824txWLRM=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/2855.js</key>
		<dict>
			<key>hash2</key>
			<data>
			rWURUsWaNnrz9BIlwl8+1Pmo3v+i2YPBm05UzuGgL+g=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/2866.js</key>
		<dict>
			<key>hash2</key>
			<data>
			g0zJ6H0vSm+SzKSapj+eZ2E2SPahtRO3yXlQEPmex+E=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/295.js</key>
		<dict>
			<key>hash2</key>
			<data>
			v9EOSDrjaVyIlJV7g+Zl07yx+WoGS/r+dnJXh/edFCQ=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/2958.js</key>
		<dict>
			<key>hash2</key>
			<data>
			Gt82KczFFeQtehU62S94TnZSjrbb5TBTCzdwvgPaV/8=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/2995.js</key>
		<dict>
			<key>hash2</key>
			<data>
			mnhiN6HVVZ+2PwsvJ2TxuBKMojH67wL7yzPItd4/lms=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/3085.js</key>
		<dict>
			<key>hash2</key>
			<data>
			cQ0eUwUgAac/SGdc80c+EMJexUg8jnSW8M13DGF+Z3g=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/31.js</key>
		<dict>
			<key>hash2</key>
			<data>
			16zVr+unO9r+gzol99MzkFOj6yZ074wZM/Rqrdm16U4=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/310.js</key>
		<dict>
			<key>hash2</key>
			<data>
			C7ildoqiy1f4ffb0QAMl/gKoceNQe4PgVigXJsbTSqg=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/3282.js</key>
		<dict>
			<key>hash2</key>
			<data>
			JQVJ1FM0YSgmSgTXajOA54wgdYk0XoqeNCzb5+uShHw=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/3420.js</key>
		<dict>
			<key>hash2</key>
			<data>
			da2/I8+zL4LWRINBH3SjfAUmNqJkBl+BmIl09uag+cg=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/3471.js</key>
		<dict>
			<key>hash2</key>
			<data>
			a6uY30Fq9LFoNjYgYSQp42biFWWKVrwkZXGPyJkLDGA=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/3671.js</key>
		<dict>
			<key>hash2</key>
			<data>
			K2hD8o7Tls/5uQn3NtHdVwfMC9MepWVHhbDT+ePDQ+E=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/3746.js</key>
		<dict>
			<key>hash2</key>
			<data>
			4/boxwxindiWkL4Q/PpiBps8kzOH4PqbGybQizwWDYI=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/3887.js</key>
		<dict>
			<key>hash2</key>
			<data>
			Yn3Juqnc0vSpVQsiuN+18Pw95G+PZmn532Q4u2IATaA=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/3rdpartylicenses.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			mwfrCp9CWdCdHdTe5VRs6LUh62rGZPRsE/3NQS4peEI=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/4014.js</key>
		<dict>
			<key>hash2</key>
			<data>
			7gI/7N7Z2NHV76Z7PRDaUzSMP0T54ffCqNJ2HDyO+sg=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/402.js</key>
		<dict>
			<key>hash2</key>
			<data>
			dtCE8uxRrJqDer5BUfzrv31WYh4Zu1gT3s/jsvjY2SU=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/4093.js</key>
		<dict>
			<key>hash2</key>
			<data>
			YLFPTpTThvfZ62NCfyLGR0l5020UH1uLwOLD+B6/Bzs=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/4126.js</key>
		<dict>
			<key>hash2</key>
			<data>
			lghlNvLG0nVHPV+HKST5xprNcWa2bOM7r1UvI8X7hms=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/4176.js</key>
		<dict>
			<key>hash2</key>
			<data>
			XpnE76IZY0y+zJTABXe5Sp6jji5wiKuzGWBQn820rxE=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/426.js</key>
		<dict>
			<key>hash2</key>
			<data>
			85SB8w4u9PcIKfK6RVfsDLYrSAIXaXpK8mb+g/u8ttc=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/4334.js</key>
		<dict>
			<key>hash2</key>
			<data>
			2qQ+FPOoxJPGjbkiQlb+odcovJKxGc0amcKkmxJixwk=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/4430.js</key>
		<dict>
			<key>hash2</key>
			<data>
			xHMcnJlGV9mHgg8Z6PVMmVcFjsyKcpbYHilbdVzQBr4=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/4527.js</key>
		<dict>
			<key>hash2</key>
			<data>
			7vezm0BMGDaWLhGbpquatvBgzwi5v9sbJ3D2k0i0ZIY=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/4780.js</key>
		<dict>
			<key>hash2</key>
			<data>
			pj8altga2CQbwRgrhVQuhEI+qsqqvIeDVLcZqZx1K/M=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/4789.js</key>
		<dict>
			<key>hash2</key>
			<data>
			w9nI+x4k4PqZoCaEMF3V/qFl2NW8iLN79jGKs2NPWx0=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/4801.js</key>
		<dict>
			<key>hash2</key>
			<data>
			0gALMXVeI4ow8duGHqjmxvCen8QbfEpv38bFMSA/neA=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/4970.js</key>
		<dict>
			<key>hash2</key>
			<data>
			qgt1EgtBRTr6Dpn08jcsp/IwEHP9USEQHMGzYF0rlFY=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/5134.js</key>
		<dict>
			<key>hash2</key>
			<data>
			rMr7XFI0I/3GcjglsxQMy7zU2rAZ3RQJ9sySfMdWfv4=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/5228.js</key>
		<dict>
			<key>hash2</key>
			<data>
			VyQ+alE92SrdUb1GKkB2owNiJUpydADeai0tqtwKQEA=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/5249.js</key>
		<dict>
			<key>hash2</key>
			<data>
			/h0laYiJjO/22kqgye2tZn2nm0bLnEVKJvfvBVgMYdU=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/525.js</key>
		<dict>
			<key>hash2</key>
			<data>
			6SG8H5mPaSjE3doLGCs+F7PAQ7IhJtj8vOZ6yTDm+ps=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/5365.js</key>
		<dict>
			<key>hash2</key>
			<data>
			h39ZxtWRK/NaRF8djhdGIfIOW53y1W8ufMm28/xkKGw=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/5373.js</key>
		<dict>
			<key>hash2</key>
			<data>
			khnpL0Y5t8wcF0QDLgS6a11JfZ0Sbp/aZHBXuK3Wr9Y=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/5375.js</key>
		<dict>
			<key>hash2</key>
			<data>
			AaI7BD6BMBOSlFbU4kBq3PJ28lGehKdpmqewk6YwkaA=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/5403.js</key>
		<dict>
			<key>hash2</key>
			<data>
			DHBrQUS3snXFTJQ7UfpsuQe+bgQuO5e7KOAYj+TUEgw=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/5408.js</key>
		<dict>
			<key>hash2</key>
			<data>
			zsvgLHFsDNjeqzrwqubmMZOat0Pn+9NsqzMhPq4/ndE=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/5462.js</key>
		<dict>
			<key>hash2</key>
			<data>
			z1OoCKXX2dyLMv/mkF8ntGRkTSQ6Qup0d5JicFPO09E=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/5530.js</key>
		<dict>
			<key>hash2</key>
			<data>
			Og80tP20zPTi2MYLOAzX6dFaJzy6WI2xmXMlVpxNrp4=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/5576.js</key>
		<dict>
			<key>hash2</key>
			<data>
			80lewoUAr4LnQ6IqwGIs/BukLFHGNBNqmQ31KVef1t4=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/5594.js</key>
		<dict>
			<key>hash2</key>
			<data>
			LWT4kO3f7HFjacjFl4UMY4W8B/deVuIWT31pvzdiQxc=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/5740.js</key>
		<dict>
			<key>hash2</key>
			<data>
			7bK2PbP5YhT4SkUSoIxWtUuAf08nmbV6c0W7TzZeaoU=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/5760.js</key>
		<dict>
			<key>hash2</key>
			<data>
			QytKbfIJXGtJtUAys8tN21UvO0Hjr8gZHqg/40b2uOU=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/5850.js</key>
		<dict>
			<key>hash2</key>
			<data>
			q266ItFJ3KUh/bF/R9keF1LMMwccr2YMCtGmUyXM36E=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/5998.js</key>
		<dict>
			<key>hash2</key>
			<data>
			N9VYkg9fmqod8BujQgtTX4XhDR2j6BsnxgmcGlY2iis=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/6001.js</key>
		<dict>
			<key>hash2</key>
			<data>
			+O1KE6c9uKGoRT5kT7h7OvWinb+RPJ8pfpOfW8I1uEU=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/6054.js</key>
		<dict>
			<key>hash2</key>
			<data>
			7Jo5+24RUZXJzy0xJo/eUsBfgaT9vc+tM5hrJXy6JEo=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/6074.js</key>
		<dict>
			<key>hash2</key>
			<data>
			xXsq4aXnu22JhNJa5bIMculRRjRNwj5GuA1tKM/T7Wg=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/6082.js</key>
		<dict>
			<key>hash2</key>
			<data>
			QI6NTpddP7UWsXEvhBbKd865ik4tWI3s7L9LlErsVuU=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/6104.js</key>
		<dict>
			<key>hash2</key>
			<data>
			I65iuz+4MljcAQtx5srvD1qZo74diDUFBDPMbOnkZm8=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/6113.js</key>
		<dict>
			<key>hash2</key>
			<data>
			xF+pZPyXWE7rZqgZPCpQN6qDtauDkB39Gyx3PVoVkT4=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/6199.js</key>
		<dict>
			<key>hash2</key>
			<data>
			BWUW9WZhdMZoy+CaN7+1s4wrxqdthfRkBb0Jl0g0W/g=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/6247.js</key>
		<dict>
			<key>hash2</key>
			<data>
			Oiuh3GdvX9gO1id3vM6S2NFMzA8d3EQnDecG/ivNraI=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/6392.js</key>
		<dict>
			<key>hash2</key>
			<data>
			wnwpEoNe9VlBbCBncxV8DaBAnvX15M11K9HXknYfgJ8=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/6407.js</key>
		<dict>
			<key>hash2</key>
			<data>
			u3hnwiBigbaHOeuoqMypxq8MkvCo7swKpAFuTA9HOyY=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/6531.js</key>
		<dict>
			<key>hash2</key>
			<data>
			1qWmY0iG7d2KK6g5niMd6GW3l7EWiM9N7606H8hopiI=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/6689.js</key>
		<dict>
			<key>hash2</key>
			<data>
			L4bMxiZ/tj+OEh/0T3cF2jH26ar03+shicUpcBNibso=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/6735.js</key>
		<dict>
			<key>hash2</key>
			<data>
			0Aj48nzhrSWZCZ/V20FD+7BRpq5SQHu3ewUGBxvg+YM=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/674.js</key>
		<dict>
			<key>hash2</key>
			<data>
			RU2Rvxh032bpFP6I5wfNpzAIsQw8v6WTLWa6kfvwbyk=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/6740.js</key>
		<dict>
			<key>hash2</key>
			<data>
			7RFozvQBust05d9zXU51TyIBdw9cEWMGNsZLQFTN3EA=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/6745.js</key>
		<dict>
			<key>hash2</key>
			<data>
			ez4SOpzKNpuVRLkbqeJj5Xbt8NQ35qSLzqbEdB4NVKI=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/6852.js</key>
		<dict>
			<key>hash2</key>
			<data>
			ZVoH5GoQFh1EZtmj92T4P8JUqGefobIy4qZBQNtbQfo=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/6854.js</key>
		<dict>
			<key>hash2</key>
			<data>
			QWxIQIMkzzHHTfVdEwR78c7kZDFO2LDkljqLSpo1guk=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/6932.js</key>
		<dict>
			<key>hash2</key>
			<data>
			s2Kf0/96vLkLIwNrkGM5phoGLlqPeeW7MI0PIb2YNlc=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/7061.js</key>
		<dict>
			<key>hash2</key>
			<data>
			IaQEAN4t23MZI3Y4iAMkn90y8dZUbO/mJHNaH3CZkwo=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/7225.js</key>
		<dict>
			<key>hash2</key>
			<data>
			ustqjIuKVIqesodrX46/8TgqzOQ193C6cRQ4X5+u7Pw=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/7347.js</key>
		<dict>
			<key>hash2</key>
			<data>
			vVC6Z5YepzFCkTKyJguu8aB8JH6Ov2NcvwT/Lij8Clg=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/7366.js</key>
		<dict>
			<key>hash2</key>
			<data>
			FxW7aYk36KXBpZbYW4cC6Xjahbi/HXtSoWWfzqACE6E=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/7401.js</key>
		<dict>
			<key>hash2</key>
			<data>
			/gTVdAo4sWp7UaVC8NRcddl4kUyB+kcX8JxoQzscRPo=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/8023.js</key>
		<dict>
			<key>hash2</key>
			<data>
			Qyy12hGNiNK0atcuogp0W5TfYOx7PXCU6OmnrF3bY2k=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/8097.js</key>
		<dict>
			<key>hash2</key>
			<data>
			wDXC2nAZ/IGpfKsLMPOFFPXYTQPKf36f81HlUA68lZs=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/8109.js</key>
		<dict>
			<key>hash2</key>
			<data>
			fJL2pcDgQcRI6abJ0YMBJBVq2ZHVDjk8v5fmf4T5h4A=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/8112.js</key>
		<dict>
			<key>hash2</key>
			<data>
			mhRLwU1NpASITTqBKsUfbPjymD7O6X8DQnLa2UYbtZM=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/8132.js</key>
		<dict>
			<key>hash2</key>
			<data>
			A8guXVn5ipkBSNuvlhDoZOEU4Lm5bfVs51U9YHFCkls=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/8239.js</key>
		<dict>
			<key>hash2</key>
			<data>
			YtpYrq4xjQZH1wzboGrHiuDjRz4Qy4LkK4X5bG7mEBI=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/834.js</key>
		<dict>
			<key>hash2</key>
			<data>
			w9cDFvOx68lBNxmGyWgwFeiz028gZnxUXunOkvpyLNU=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/8366.js</key>
		<dict>
			<key>hash2</key>
			<data>
			oMWa5f6CvOhy0ST+zscXP1rZ6R8hcvqP1+ZQq/cASqo=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/8370.js</key>
		<dict>
			<key>hash2</key>
			<data>
			FkMK8lLXQJlj6fB2JlI9qCX3ZaHXzw93TxOi3me+M+U=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/84.js</key>
		<dict>
			<key>hash2</key>
			<data>
			829AN1E2GEPwVRAUQWUcWYSfA7Z0ppN2G0LlAyORmdY=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/8409.js</key>
		<dict>
			<key>hash2</key>
			<data>
			NjEcJ8oHtfFf5qBRO1f2XBxwubZbVe4TkiDpEqhbvgE=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/8416.js</key>
		<dict>
			<key>hash2</key>
			<data>
			30uZ0YnMBEdtdCzkVKouR4EoGn7lCjRZhKI8Faeu17c=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/8454.js</key>
		<dict>
			<key>hash2</key>
			<data>
			gxjm36Rxv6DBqCPSt2gp+pEsbdRaA1oR+4cis6ahBWA=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/8457.js</key>
		<dict>
			<key>hash2</key>
			<data>
			64CbXmGH+HcA+/9SoU23knMM9NFoKVy7B/j3Gl2u2Gs=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/8461.js</key>
		<dict>
			<key>hash2</key>
			<data>
			9h3DKJfNu4GNM7PlqHjhihhsaF01eapy4MX/uhMnVMQ=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/8597.js</key>
		<dict>
			<key>hash2</key>
			<data>
			EmRiJilIPYALuWpiFPAqWIIk79GFiHzID1OwoORJ+PE=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/873.js</key>
		<dict>
			<key>hash2</key>
			<data>
			6LDJFFAsbfc5r///yUKO4mxIv8EwzvbYHHkDX7LLoTM=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/8778.js</key>
		<dict>
			<key>hash2</key>
			<data>
			hjZndpZWThPI5gD9DEXs8YVzMQbcJcrlUPmjlIo21/E=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/8844.js</key>
		<dict>
			<key>hash2</key>
			<data>
			mRMqvCO/m9hqTHZp2Y8/ZMaRdVlZzZbB8QHf+TIDVaU=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/8955.js</key>
		<dict>
			<key>hash2</key>
			<data>
			HdpKEKNc7cAsKjRiL4kWfWkQh/De06/ApmOicZ+bsAs=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/9002.js</key>
		<dict>
			<key>hash2</key>
			<data>
			giT/ZOvDReTr+5aLws1ortIy7Hqi9WNO42artH3TkCQ=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/9103.js</key>
		<dict>
			<key>hash2</key>
			<data>
			llLq6ocu4yUPfCWxnfilIMs3WKelDsZLajGjvmgf0Dw=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/911.js</key>
		<dict>
			<key>hash2</key>
			<data>
			ICZkyFywLrfsr+j7dd9jIo2E1HD61IRBDJVyO+OYf+s=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/9307.js</key>
		<dict>
			<key>hash2</key>
			<data>
			BzTjN8JQ240Sp7JwYYMuJPw5sB8P5Al+uwuT7wOG3ZM=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/9377.js</key>
		<dict>
			<key>hash2</key>
			<data>
			7xpVX54gE6OAcYLXK/ueLatN7yjaO6P/s4uRb0eGyew=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/9421.js</key>
		<dict>
			<key>hash2</key>
			<data>
			9GDTvp00a+W8zO3P48BK21XAt8C9g0QsMVeL3D5d+1I=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/9523.js</key>
		<dict>
			<key>hash2</key>
			<data>
			zzMw+lfLjyN49V44uuGuq3h/Jv5olel6zZY3s2I/Ifk=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/9653.js</key>
		<dict>
			<key>hash2</key>
			<data>
			Ci3lYgDUFodz5JIXWO8a9RiGFpPKZdLeM58aoAnNf+I=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/9681.js</key>
		<dict>
			<key>hash2</key>
			<data>
			F9wfz6OdBp6wwEmzBH5Grlo5fehzEOEYRnKUkcVgQSI=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/9733.js</key>
		<dict>
			<key>hash2</key>
			<data>
			d9DVlbi7b5+4Y8UebmJ0KM5uIDEZfVH+iJrDgcMi6Ac=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/9861.js</key>
		<dict>
			<key>hash2</key>
			<data>
			vl+JeSwKmMqIY7ckQvVeoMWHbqKqw0OvfLDR8F4igGQ=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/MathJax_AMS-Regular.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			PeeE0HufqPEEwQkoqHjuh5zzMFyuUZXLpmPJwrsBles=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/MathJax_Calligraphic-Bold.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			rwRUKynqrARVChQMXxdgpkl4OYlCbyVAhVv0FXgZNn0=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/MathJax_Calligraphic-Regular.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			Jmg78gH7JYoiN9l1RhbenU7PTMHNOd0ZAkdt99dfHRY=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/MathJax_Fraktur-Bold.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			chkhurDQAev/AgbCTLXebKE2RnvxhDp6oyAwugYdHpI=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/MathJax_Fraktur-Regular.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			hwZz33LnD4fJGloxfVWMLDtUOSJkrXm7rcbUJK6HZf4=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/MathJax_Main-Bold.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			iLmMrTaIkV5Q2lVVP/atGF4NzhNLR/F26RsQD4qbF1w=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/MathJax_Main-Italic.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			NVJU25yhCgmjtfCSnXTrRnD0T76GTAdSagYhPgoMr2w=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/MathJax_Main-Regular.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			HLHDnqZC8mpN/tIwtK6hw8IYaJQh9unAp8GBFpPE+gc=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/MathJax_Math-BoldItalic.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			jqjbsbAub3MPVbTLXUE7eFufXDmAfQoPp9ogaggkpFc=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/MathJax_Math-Italic.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			oAm+pAT3pQDe1I+LmtnPFuElBLMZXdniWXUom4JWsPA=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/MathJax_SansSerif-Bold.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			MnkhBLXvae3tkFttFZjtBdgIdoTTjnqU1S48OLoW9H4=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/MathJax_SansSerif-Italic.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			/G3fXfQCsmPPsViu2OiZclQsNLcZzYex2zBGGYX3vVs=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/MathJax_SansSerif-Regular.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			tBgTbjs4S6qt7LcL08SKjamCUhATCyiX24CMRO/Ig8s=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/MathJax_Script-Regular.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			r5b2fXrM9f0qSmgtC5+LM5+Opv40wxDBaUyLp/bdyW8=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/MathJax_Size1-Regular.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			xJgQtT7MDYfYAodixRiSQZfdnT+QWwj5nqJBMBCFucs=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/MathJax_Size2-Regular.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			MOiJtYy8Ua37sDirGpbcQCWqNUKjz/dxL8VezlEGdeI=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/MathJax_Size3-Regular.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			XNpBVjoJW9cMeOLdoT0PjLkixxyQ//0KAETGUXOmboM=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/MathJax_Size4-Regular.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			O8bsqufs9vjX+OoHvdusqGAeLLbonWrvWOeNn22KOY8=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/MathJax_Typewriter-Regular.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			xW2o1p8aAgi44HA2VsMmS23XSL1FJSTIKwOFtg9qaME=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/MathJax_Vector-Bold.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			NuDXLYp6/GlqPnpcc2mAdjRkDemwIle8pEe98SYiGic=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/MathJax_Vector-Regular.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			crxXM4bdHUjFu9KGMCyp40AMXrDymOfPv5RbnQj8aI8=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/MathJax_Zero.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			SB45BCUIrjE6YGGK8eNxRquT6TJMmOTHi48X/lXUHgs=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/add-above.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			vwk29waY5ymJa+0KxoN5vwLcZjT2KTuUcSh4uhSyERg=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/add-below.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			hecbwRDckzkp53VnXVJsahhp5dnpkUM++YUzJveod/c=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/add.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			ntTuqBLdbq/JfE1B83ynyi499Dp3sVA1D/j13jNrLFQ=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/bell.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			bbfxg3owvsMtppb+QI0aqpE1QqL6AyDEnlq1VAdEyI0=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/bug-dot.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			FGKpI/4IVJBG2g8ApPMSMm8MAHSbGWg639WRibufzPE=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/bug.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			3W6jX/mJ9RU0LvFr7g9+2ikp+CnqzBDkG6+pWkjtHP0=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/build.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			9SOxWNZAxcho+B3iu5EYKLVR+yNtbbLO4L8qbTJXRcE=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/caret-down-empty-thin.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			vXZQDRGCe5Ercrg6xUbteMY3Z71itB2uhfU3XGZbCmc=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/caret-down-empty.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			sNkPBnfMupsG2sik5dD72aG7iFt2RT4pH5qR8vjpoec=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/caret-down.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			wIMqA04YcKdkIJ4G78vDvHwhkbM6KVQ3wZl6A/d0aHc=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/caret-left.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			ghx+skKXdHpw/AwSMnIrE1Izh03XetI90um/Xz0MmRQ=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/caret-right.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			Id0kXIcy78awiZeW/8+d/iz6i+0Lm2R2iWm80HKBwWs=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/caret-up-empty-thin.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			0nFXDgN8dG5inRv4Cg60s5ac29ZmMf1tz5TDa29637A=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/caret-up.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			OULMxcXjCuE5TVf88VQMdadhH+l7xelNBOHql9JfUH8=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/case-sensitive.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			UTBAJQol3nu0TbUJzOlsVsLvkUKtBSTKGqjhvvoR5J4=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/check.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			xwOgK7xETa4oc62a76PeT5VljHCmcNgvgOPI9bLoXds=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/circle-empty.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			I4IZ1njfLfWbtIRVtS3Xft8EdO/9MjzaNy3+bZV+cJs=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/circle.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			T+l6+v03Wv9rmShkfWjEpPANZaDY0wIglOdegSt/ih0=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/clear.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			K1ay0GFufWWTX23z6cK05DIVXOh3nVsAxho1yNVSEAI=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/close.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			RLCfkIOEk/B47ZMLOMc3p9IMPTlnPzKKMHMY+iQ8k8s=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/code-check.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			t0PoMseg9ZXkFxuJkd2IFmu9gceUkcD/weU94wAeb9c=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/code.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			2jZQS8qIQ3HWl5uVuA8CGtNkRiWcBNUafrj5ct8+QRQ=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/collapse-all.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			JFc8nS346qUHsqcwogHQn3KwgVBNUaEpZF4o80oNhSE=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/collapse.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			uOV+JqsOeLTEJHiUeDgLY0f8PiGePllKSZjts5ZgGGo=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/common.js</key>
		<dict>
			<key>hash2</key>
			<data>
			SkbHWfpXEa8twIzVMr7OAbcNvATMWiHzHu8CgFCRBYs=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/console.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			DcXyLr0tAFoVUxNwz5+tmrslqWT8T4WyZ81NP9v4eSk=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/copy.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			GFAswkv1RK9VhKHu4i/1/8qjXshDao05qbfDteuC23U=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/copyright.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			uq+kCZ1AQHNcDK+QByIlhh+paKDm5KTIZtlleV62RrM=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/cut.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			xAKf60J1WBChVz2/3TJ4+j81awJYjYgzMHIAHH6hFgQ=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/darcula-new.css</key>
		<dict>
			<key>hash2</key>
			<data>
			bCJfEAdHzq8A4sgbmpH4J6Fohk2Ly77AlshOTP7sLec=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/darcula-old.css</key>
		<dict>
			<key>hash2</key>
			<data>
			MFAZ+yN+7lz9fxKAlS0uzswAaObTdMxnls5ESWSwBE0=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/delete.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			HfCiJaC8dMfHIsEEsHUeQEnkYehcHgu1Zgdg1f4PmGk=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/download.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			ykosC/SIti4ZzFSVTqbU5+eaaZk64LtEyaQVPWylNcQ=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/duplicate.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			mBxKrrRXJRILFQRWc1lq5EEVjt0uXel42y1SFO2Xubo=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/edit.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			N694fqb2iEj4TZ5tOsEiMVi6lqh98HNlYSz2phL+PCw=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/ellipses.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			33m0KnMkL8vGUJQLZudc33Q4lRHG+2NaypqcNAPr6vk=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/error.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			HSUY/6YmBVeyshApTabJbFb6voV4xj5fEAxnKRfX9iM=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/expand-all.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			l9bDeMdUQgF+12Mfl/UnjNhNE7xKf0h5lHPpX0NOjew=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/expand.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			vGvAfhIcX/fvje8W0cdpIUVNr3gIJ3+MEqv9qFCWdqI=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/extension.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			ikCMUXuDH/wQUT6S6kSlrFtBG5Nr81KnSWT/LphUY4s=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/fa-brands-400.eot</key>
		<dict>
			<key>hash2</key>
			<data>
			5CmUZOewEpaO7WOsLbHJUJ9WvKQJ759x8pJqjDyAsqk=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/fa-brands-400.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			o7mBd4AhTK8B6K7CC83CMFof80oV+ugezQkj35zVzQo=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/fa-brands-400.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			zaWdbv/6aFgw/ZW1X2SunLUSec00skELafhMfsMBV9k=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/fa-brands-400.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			+SF/ZodLDAHNjBC2opXbxPYJrLb1rcQcN9pGZBtX6wI=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/fa-brands-400.woff2</key>
		<dict>
			<key>hash2</key>
			<data>
			jqh5F1SRWomKMQDmPjKXim0XY75t+Oc6OdOpDWkc3u8=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/fa-regular-400.eot</key>
		<dict>
			<key>hash2</key>
			<data>
			edCIBkvrOCYFT7iBZUFiNYl6hWypUvyhSYscWbFqqkg=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/fa-regular-400.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			vgoISWLYBmiE9/6b0n7BblH1qTtypQLJLFok3IfrLrw=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/fa-regular-400.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			6HEbu4ca/Y6d6mDhbTDwDH5IN7vJgHBlAXR1uEn6IxM=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/fa-regular-400.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			y56eaTGSQTzeKx8hwdwdRLb+eyfMK0WOizWdGPn/j04=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/fa-regular-400.woff2</key>
		<dict>
			<key>hash2</key>
			<data>
			5CqIRERIrD1gVJzHwf8sipyschA0wHPYChSkTnlzDMo=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/fa-solid-900.eot</key>
		<dict>
			<key>hash2</key>
			<data>
			NzwE/SQY9cd+6knVFHMQWPGQepT/O05dfD5XZ+i1PYs=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/fa-solid-900.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			lnTrG9VQRxeQODcJOmdmjqiPLtAG2RNn0NS3qh+SEfw=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/fa-solid-900.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			r2OXUD/O+9YTl2whrVweNymMGLvgfQltsDzNOvbgW6g=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/fa-solid-900.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			P200iM9lN09vZ2wxU0CwrCvoMr1VJAyAlEjjbvm5YyY=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/fa-solid-900.woff2</key>
		<dict>
			<key>hash2</key>
			<data>
			mDS4KtJuKjdYPSJnahLdLrD+fIA1aiEU0NsaqLOJlTc=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/fast-forward.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			duxbMW5PjqcjQBdLozsvg+TUhghm3qAlF3+qRsaoS/Q=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/file-upload.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			MtdWzbKoOfEUrDK2mhcmGKH/g3tNBDUgo3N9tmdj+XY=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/file.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			W89LnDJefCiokfdYDCvkFUcDXjHeMw21onEWQUI5nBo=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/filter-dot.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			8rtGJPLJPfQG+PGzyKQVM8xD1Y5Ka6AIJa768JFjWQc=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/filter-list.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			m5Ekev0sa6ajFT03JjiirP7Xyt0mMsPWfCaTc+xzy3s=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/filter.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			2ah16AwP37GqJqeP4aT8eAA75cmuLv2weQtzsOwdP3M=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/folder-favorite.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			aJIoTByXC15qnhb/OC/A7PTubUpHDLVACwRLz+ZcSwg=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/folder.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			pBhDi87Fu2VgL0G3JHRxHHfE8fcWT7pubY6kO+tZoqw=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/fontawesome-webfont.eot</key>
		<dict>
			<key>hash2</key>
			<data>
			e/yrbbmdXPvxcFygU23ceFhUMsxfpBu9etDwCQM7KXk=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/fontawesome-webfont.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			rWFXkmwWIrpOHQPUePFUE2hSS/xG9R5C/g2UX37zI+Q=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/fontawesome-webfont.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			qljzPyOaD7AvXHpsRcBD16msmgkzNYBmlOzW1O3A1qg=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/fontawesome-webfont.woff</key>
		<dict>
			<key>hash2</key>
			<data>
			ugxZ3rVFD1y0Gz+TYJ7i0NmVQVh33foiPoqKdTNHTwc=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/fontawesome-webfont.woff2</key>
		<dict>
			<key>hash2</key>
			<data>
			Kt78vAQefRj88tQXh53FoJmXqmTWdbejxLbOM9oT8/4=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/history.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			5ZLegrQXoGkyTGxtXoCQ+OA2kh5+UYmVm00TzauICxI=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/home.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			5+qkSuiwkdsd+maWIMYsYCel5GEG2j4VbN6WTnWqP50=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/html5.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			5IdisCraJlPO/goC4S+EZbMNQRDZOzdVoAyDaTWAx4o=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/image.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			X2QsscC1ue/Xr0E+Kvrx1Xe6y8usAC15QicNVqkuvAw=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/index.html</key>
		<dict>
			<key>hash2</key>
			<data>
			A/+2lWvYZqDHVx0rsGlUmnS5WAXDO3mhIPrR8CSNT5o=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/info.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			SFKFrQvvPxQZhEPHVtaqhQ0hWG1s5dvCQQJR2QzGGOc=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/inspector.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			rNOsw6hOKxePgfE5Cw47B62yERv+Ki5J0ucTM0fn/bU=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/intellij-new.css</key>
		<dict>
			<key>hash2</key>
			<data>
			sg518CTwSi1lPkMfPDvd8APnXb+4qq8FYYZCJQnTD5g=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/intellij-old.css</key>
		<dict>
			<key>hash2</key>
			<data>
			pAvK09/HjEV2AOO2UBZPSdrk9Ol7nxKUemrBViY/hKM=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/json.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			DrKmzX7aRhJFjPYm2zHFgfe8qt5BImxhhFxFpfaCPfI=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/julia.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			Vk0Ys3Ggu6sFn0Wr/4t1tW7NynnVQRkXSNQBxGuyWVo=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/jupyter-favicon.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			SiosfZhnZaiw5Z5hfiifiRsz10qS2F0DwjwtRiByBHA=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/jupyter.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			etTCdYcQneXWCKTxJQdQNUbDScNpgGYRr4x+4moUUII=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/jupyterlab-wordmark.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			AQfBtlU4EQGiqCkXHARFhi0gBvg/XyKjDRmL6L9gIKA=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/kernel.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			mLlz9MZIFoy6Z0ywRO3RHgSnjJFHZLwGRwf2e6xgVYY=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/keyboard.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			1QRzlPZPADDKBAN5VLVL/b1RY1ky2DpSHVwJK+iKXpc=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/launch.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			JxL2ijJORGtkoTFdFVhbJHvyfqjwFGaOKOqxc+B6sJk=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/launcher.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			akjhH4t5Yebt+BDoQxjExKhX6XybDou3iN+JkU2V75E=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/licenses.json</key>
		<dict>
			<key>hash2</key>
			<data>
			XP5B1UAkvevtOsQ1v8WuXsYllWIPGuQqQg7DepBaFgc=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/line-form.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			V58Yea7zPGjM5eTAV61nvcfH/bNbiiM68fCo7lELpAM=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/link.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			CvwB48+gE67xhPf0APSa7+NEGm8fNZWl5MdHy7q/ftU=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/list.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			Mk7mu/AdfxIM81dw0felQ6rqGCYuLG8ehfpmkYQFGlI=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/lock.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			oRJZW/pKkdhds81zfUFBq3TSw2I2ADWaFex93mrLzzk=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/main.js</key>
		<dict>
			<key>hash2</key>
			<data>
			6Xhs1Oo34PPs0N96Qv7seAqSCu5GzlJby561pXn7zKM=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/markdown.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			wvdtQgx8+r8YYTvgoRSc/pMTdmiRnQPQtzZyPDnIRAI=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/mermaid.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			lcGJbHmnduV/kamr0eVIx5RNRkjO5+xiHqqUXQy4QI4=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/move-down.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			/b+uXk/gfIN3+JKN8PI0nUcLawglCLPv9OgBh/GFTh4=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/move-up.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			84N1gLfIfa+b97uV7pQLiDVnUMtGQ7uE7/sfspXrheo=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/new-folder.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			0FhtfyHKpgzYBlKcCahqiCQY6tAFWXuzcLG5atnMuUs=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/not-trusted.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			JCXtHdEGJI5WBIY/MpEMEKQdj7WF2wbEzXLohtgne9s=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/notebook.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			FfjClnVSPAtGS6r1VNecBDBJp6JExdFqrYm84+4Moeg=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/numbering.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			de8vY6fBMxHwOAOJjWWemNOn1iV+FrycnuY1rz80k04=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/offline-bolt.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			z4ncWyoyv1mzrar6zFUc7pBayMGEWWn5xyxkpIKf0bA=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/palette.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			TvYrhGnSrfnTWBHxd6PbKcsYw9VbmoMAKHItONxG60U=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/paste.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			oL7AVS+MI4fNKbqmSUY+wks3zNDsWtOtFmmorMispCA=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/pdf.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			2YTj/z8BnDWxAlJJQeIRBO9s8lM9w/0nPqyh6f25CoM=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/polyfills.js</key>
		<dict>
			<key>hash2</key>
			<data>
			wnEqrgwrWZ1RHrmMUNtzG2cCgqoOoC6qnKqwQd08wiE=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/python.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			hXbJRZo4ib3J1rphPV4eJO4aYn1qusX3tclytp0Ilc8=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/r-kernel.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			Yslb80dcMao+YDQEGoYeX1Jlod9hLDOHssjZ9Qp7rOo=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/react.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			LVxKwwLpe5rQmWy6EdWbPFjro83cthVPn7YTgJKPxr4=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/redo.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			nBK96cfjJzCkCDAwOyIeca5HGBFyE9/x3NZQk6EYUEY=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/refresh.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			ZX+SOkKtrWo8+e3xYThAm9AeCc5x2sX4jSaD8bUkSGE=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/regex.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			kI3dwZalPwZEV+GIo9HOC+2//yZ20KXi3PXWaYrSLkg=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/run.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			NrybsQj2RKCKY1MY8i4hcjVkclXu8nOyvqcjND47JM0=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/running.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			0K0T7oQ6A3Q6wyI+lcGTAQ11Cp4kfWD4+e0C21D+vd8=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/runtime.js</key>
		<dict>
			<key>hash2</key>
			<data>
			WheH98uwjjRDN8GvkfuFvYjdeLSalMFglOTgDt27mhI=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/save.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			4KnyNyGpctqxjOinV5uxz9rlGH0unrwp6BypN6SnI5U=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/search.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			h0jnMKO35INaXNzasOj3p1Y679/lRAUsBxbaq6JxFlA=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/settings.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			lY8oSbAqt+g0s8Pflrv3IqDqKzekxB5hagG54f1HtNY=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/share.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			2jolADcAOSNtfz1ElBl0YvOUM0QQs7XtOKn9l/73f50=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/spreadsheet.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			FgKvmmwWWu23VSMBjCB0oASEqZ3mCMuLdZzpUndOPz4=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/stop.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			+HbpArdYX4tLFouLCElWyx0LpgOe9F4/T4GRL/kLvBI=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/tab.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			G0DN8p37Z+7ymg5dTukkE5ULq+eNGn/v/BlpobneNZc=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/table-rows.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			0nwT+OLHgR5EMDp3zJCNpc8Zm3g6Ji8JAP+FMIdkM3Q=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/tag.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			QsTRZzCa2InHDEVSgVn6lU7rXG6uoXZDidwYbZumNYk=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/terminal.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			xX9LFdJ9ZhGW2GN6ipXfP3MPcp9onZJDoTts6kudbdY=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/text-editor.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			fuPWUCGPAmAbRcXvWmZUZWWZPahiE5bhCgu6N/Ni9dI=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/toc.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			oswAZsG0zOp1Pm0a3g6sM6FJZKIu3U+GukqAGmbXl7g=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/tree-view.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			sOUT/vhTb6sxJxaX+w8fxXW/VsBjmmoRo51Pay5Gjhg=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/trusted.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			FejVrDaQaoGoXRSN6cRBgOeMJUP88ygiUk1tmdJxKHk=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/undo.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			BNTwrzAlXVmlRdiikiqeBns+JVExDC42Q7wU8fQHE9Y=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/user.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			v7bgKWEvWSCbRPt+HUqPCcuhAuojylAWPFkJ7yXa/5E=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/users.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			SyFc1QmYlDUO5Eu3Kv/LfFaul21M5lDK8yQrk7/Wpe8=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/vega.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			PEmH4Rxsf+YZxQWu0HaAnMMC02KA+gJOL3kkLLNKgm0=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/word.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			5eBQ72e8rBRBgtvyY/MwuVpN9WxS4UkdXJJPp4ue8Wk=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/jupyter-web/yaml.svg</key>
		<dict>
			<key>hash2</key>
			<data>
			hV8aXPuEWSoawjpRIdiAp0CuggOMJ7Ih6jg5np64gH8=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/lib/frontend-split/jupyter-frontend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			gCJdcRhu1N5eKv9FIthomsd1aZFMIn8+0S3aWzUlyec=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/lib/jupyter-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			qEx/62oLR5p9buWpk/AQTm5fEKsykJjPeVCnI0jySSY=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/lib/modules/intellij.jupyter.core.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			CwqhWVDiUkZGNd9I1wfxVg6D16W8OTmxzKB7XqZsOpc=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/lib/modules/intellij.jupyter.psi.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			6AVHW/tWQ8gtJPDbrXNg0KGZtKfyXFpWlWcPhX/UIKQ=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/lib/modules/intellij.jupyter.py.completion.ml.ranking.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			gxa7yqrrBFvESNuOEtqJ7y/eftA517mcR1XnFA3KtfQ=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/lib/modules/intellij.jupyter.py.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			D3YH23hS3NezjgkLSpKTjG4mJBQFn2t5/xlNTMh3z98=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/lib/modules/intellij.jupyter.py.psi.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			O7fkWwJ8+p5ud5WDKym1C6L9wltiO0mIRFp3stdyAbs=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/lib/modules/intellij.jupyter.split.common.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			YbrlmHPfu3LHXICWg96kUG6K44NVJq6n3F56xN42N0U=
			</data>
		</dict>
		<key>plugins/jupyter-plugin/lib/modules/intellij.jupyter.tables.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			gfpL+zN+y7Pp1HIQzUMGaRe5UQEv7fXapvCGTfxpOzo=
			</data>
		</dict>
		<key>plugins/keymap-eclipse/lib/keymap-eclipse.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			LfpE6CtvNPBbZ+FpsY/wND8F459S0HaIa/TxWtClSsw=
			</data>
		</dict>
		<key>plugins/keymap-netbeans/lib/keymap-netbeans.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Vfa9fp7Jx5yIcEE29JkFf3q12NooXpo3qzABQGQ4ywE=
			</data>
		</dict>
		<key>plugins/keymap-visualStudio/lib/keymap-visualStudio.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			hX/WKoEh2P1DXtrspeGF8VZQuws7DwDNz7btvCHUayM=
			</data>
		</dict>
		<key>plugins/kotlin-jupyter-plugin/lib/kotlin-jupyter-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			6JRwUJ9v/fvve3sEv/FicDPA3WOwH9B2ezONLB6HxR4=
			</data>
		</dict>
		<key>plugins/kotlin-jupyter-plugin/lib/modules/intellij.kotlin.jupyter.plots.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			QWBCfILWLbWf9Lf1qwsOiBZqOvugdB0k9G3Yo917c8Q=
			</data>
		</dict>
		<key>plugins/localization-ja/lib/localization-ja.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			XPwzWcfN8DviwrrMVuaIIC/JrUAXWl1FR330HJh2N64=
			</data>
		</dict>
		<key>plugins/localization-ko/lib/localization-ko.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			rZItxkkqdHoSaW6UqqLSRb6tkZ4LfmDES99RmIe9NSk=
			</data>
		</dict>
		<key>plugins/localization-zh/lib/localization-zh.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			6JHomBivTFeERRp3Uk7EBF/SvEdff6gX9RP170DOHSs=
			</data>
		</dict>
		<key>plugins/markdown/lib/markdown.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			oaA0Dvltge3HuknMijacxNbkM4K1VxuS76gZCSWYkbg=
			</data>
		</dict>
		<key>plugins/marketplace/lib/boot/marketplace-bootstrap.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			j6yH7K3zB9nymxrSMKA32pdfuRWKEdtcgOWmoxIQPv4=
			</data>
		</dict>
		<key>plugins/marketplace/lib/boot/marketplace-impl.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			66+wkfc2eM4XQz+ORiqhdvzQHQTsvJZ3X21A+L6mLUk=
			</data>
		</dict>
		<key>plugins/marketplace/lib/marketplace.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Z4TSvcSP4IZ6nd6KFCOrWxTd5FnnEFLj6XiaUBS8YUY=
			</data>
		</dict>
		<key>plugins/marketplace/platform-build.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			TgIV/VIIBaWI6lacn9dSpUIW6URGysTGMkEXqiIq4WA=
			</data>
		</dict>
		<key>plugins/marketplaceMl/lib/marketplaceMl.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			QmqRVXnMPq1YGg8V0HTIvmT4PK4QFwHDGNSd9lliKtw=
			</data>
		</dict>
		<key>plugins/maven-model/lib/maven-model.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			+pUu2xqQ47qRfwWLiRiwW3vLGemuDOF+wxdc9ieTbt4=
			</data>
		</dict>
		<key>plugins/maven-server/lib/maven-server.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			KHKqzFJ0Lrf+cQY/ei3/fi6ngC9inKBSQ0wJYtNKDr8=
			</data>
		</dict>
		<key>plugins/maven/lib/artifact-resolver-m31.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Sv3K3sWhrdpcWGcWLXRwYdkZH/GWGi4VX9V15nLd+kQ=
			</data>
		</dict>
		<key>plugins/maven/lib/intellij.maven.server.indexer/lib/apache.maven.archetype.catalog-no-trans-321.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			I0y9xNKwBD6tRLXFCW9zYK0RGI+aXL54VXgYwMzObm8=
			</data>
		</dict>
		<key>plugins/maven/lib/intellij.maven.server.indexer/lib/apache.maven.archetype.common-no-trans-3.2.1.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Tl4lCfXDmru4UY2Txh6aovnny6H8476ntAmWga9rHfs=
			</data>
		</dict>
		<key>plugins/maven/lib/intellij.maven.server.indexer/lib/apache.maven.core-3.8.3.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			iJvKvscWyYNffg7nvjjCrxZpOgfjOhxD9VaHgWUneps=
			</data>
		</dict>
		<key>plugins/maven/lib/intellij.maven.server.indexer/lib/apache.maven.wagon.provider.api-3.5.2.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			h/PkdlmXWjYp7RxX9I+EYLjRQIyt1mIZ60PN/A9Zv5E=
			</data>
		</dict>
		<key>plugins/maven/lib/maven-event-listener.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			c0CqTFkkWU0sDvMbKNJOvFRGyd0TjA2dnEj6VBQ75cg=
			</data>
		</dict>
		<key>plugins/maven/lib/maven-jps.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			dto+MpStERD10Q0q2L1LrUL56vTpI+fjyIhsrdMpBFs=
			</data>
		</dict>
		<key>plugins/maven/lib/maven-server-indexer.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			GdstGMJr+ydd6pB36OfHLD6pcjGU8pQpdIk7wY7dP5E=
			</data>
		</dict>
		<key>plugins/maven/lib/maven-server-telemetry.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			oiP3Q8EO1D4YwFMwpgAIbtocY7lT4dGvZ6HvYS5K27M=
			</data>
		</dict>
		<key>plugins/maven/lib/maven-telemetry-lib/jackson-core-2.16.0.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			ZuLN5Mx+Vl1fziopmLZOmRd3v5+tTSIHNcUl/Y0Bwxo=
			</data>
		</dict>
		<key>plugins/maven/lib/maven.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			2LdmkRYJ2O8rT7FlnmqxC5WcPQAESR6/cNrPf3XlK80=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3-server-common.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			q15qSQJ5tsFotTIcBTQbPAUv9M5aDKMWCyo5/eiEywM=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3-server-lib/archetype-catalog-2.2.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			ES4G5qzBQFrTulGEQug10dQnWO8x8Lu9syqh4VoX0Q8=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3-server-lib/archetype-common-2.2.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			OgCngVeoL/93gzR2QlVkJYUSdEPgs9pWvNtvig+RAiA=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3-server-lib/archetype-descriptor-2.2.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Fi/wzIBEUwfeBfXgzpwBTozSLPvS7WEK45gG5FksUlU=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3-server-lib/lucene-core-2.4.1.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			uF1sKmtMKekPp8XZSyH+eW+caFeHAmi1aQR2USa65xg=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3-server-lib/maven-dependency-tree-1.2.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			27jFPMwLFqndg3DW595jECRoyu2sHl+i60GDGaaHUpM=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3-server-lib/nexus-indexer-3.0.4.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			nUOijQdYkU/c6iikNmAawm0Go7VdKDu2vgMhzNNjGlg=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3-server-lib/nexus-indexer-artifact-1.0.1.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			FxA1dAMi+WvYwVXPtgUJYt1h15dqSUwKrG1Gs8gxdMQ=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3-server.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			j2sONR/aJFU+bVExa5g51e76zGdF16X1mEJWZbs2N5w=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			6OarihC2AE11qc/h8CTzo9vuCkyzb+7BixYQKjwlRTU=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/NOTICE</key>
		<dict>
			<key>hash2</key>
			<data>
			/e7bCy5l4mF9thGxXyCDwCkkCPLM2PJoVmgTN8m6g0A=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/README.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			UCBIWMj5L0KLa96NQgzZa+uvI2IqaK/Pz5UxBIfTDDU=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/bin/m2.conf</key>
		<dict>
			<key>hash2</key>
			<data>
			4zZ2m/k6kCuqej6Ce6VeTO995K8u0alUGkJhCU10i6k=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/bin/mvn</key>
		<dict>
			<key>hash2</key>
			<data>
			zv1JIhrLp+u73JK2+K+TlfMCQBnRQxs87XyI0iLQ3EY=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/bin/mvn.cmd</key>
		<dict>
			<key>hash2</key>
			<data>
			w+nmB6N84uQvKzuWwVT18RwOnK9Zve7iNlJ98xEK8f0=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/bin/mvnDebug</key>
		<dict>
			<key>hash2</key>
			<data>
			gwwvbo1VlD6t236vrgw47SZR/5M7LKe3+P43QN3rvFk=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/bin/mvnDebug.cmd</key>
		<dict>
			<key>hash2</key>
			<data>
			eegilWH4CPK+WU99O+nVCdwGu6K3DIdCG1jyUsHHnAY=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/bin/mvnyjp</key>
		<dict>
			<key>hash2</key>
			<data>
			yCyo5NqMNDwP7o54lQNPAVRYukkDxhKbf9Wp70pg6OM=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/boot/plexus-classworlds-2.8.0.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			CBtA4OqwM81axy0lAb//T1/So+74JwUREXMOoVJoHHI=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/boot/plexus-classworlds.license</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/conf/logging/simplelogger.properties</key>
		<dict>
			<key>hash2</key>
			<data>
			QhQPbsE1gnvsJVouvfoRIROHQC0TKWeS/0DlapbO3fg=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/conf/settings.xml</key>
		<dict>
			<key>hash2</key>
			<data>
			L1UjJa7dmpySaN9SC+Is/jqoYmC3ag+DDSeW0vDiK9Q=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/conf/toolchains.xml</key>
		<dict>
			<key>hash2</key>
			<data>
			cWpuc+SwExpyxzKc96nL+3WvwwQu1zLNGGK9/NicExE=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/aopalliance-1.0.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Ct3sZw/tzT8RPFyAkdeDKA0j9146y4QbYanNsHk3agg=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/aopalliance.license</key>
		<dict>
			<key>hash2</key>
			<data>
			9pYL4bcdYCNS19nedqVk9U89q1ULI/pWdASfhCEWylU=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/commons-cli-1.8.0.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			+UyYu8+hwdrGlW1sP0lOxAJlxnv1Td78ld9P/dc65tU=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/commons-cli.license</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/commons-codec-1.17.1.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			+fbLED8t3DyZqdgK2irnvwaFER/Wv/zLcgM9HaTm/yM=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/commons-codec.license</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/ext/README.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			43422H9mqcHMExUZvRj7Xokxj0LFhYkJbqU5+o5BH28=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/ext/hazelcast/README.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			mvHpVKpUrQzp/6FnfenYL0Xrj7Cw8euEkH3HVS3ulWU=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/ext/redisson/README.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			QH8yQMXCiEIscURAyLebW1ElIls2i+RcuDLkGOOc8lU=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/failureaccess-1.0.2.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			io+Bz5s1nj9t+mkaHndphcBh7y8iPJssgHU+G0WOgGQ=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/failureaccess.license</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/guava-33.2.1-jre.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			RSstl4e302b6jPXtmhxAQEVC0F7/p6WY2gO7u7dtnzE=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/guava.license</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/guice-5.1.0.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			QTDlC/rEgJnIYPDZA7kYYMgaJJyQ84JF+P7Vj8gXvCY=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/guice.license</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/httpclient-4.5.14.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			yLx+HFGm1M5y9A0uu6vxxLaL/nbnMhBLBDgbSTR46dY=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/httpclient.license</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/httpcore-4.4.16.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			bJs90UKgncRo4jrTmq1vdaDyuFElEERp8CblKkdORk8=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/httpcore.license</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/jansi-2.4.1.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			NitdMTx1VtuBIQI+X+EA0j8kKoZTD3L5qgMUmJ+iL1Q=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/jansi-native/README.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			BoGB4dLmvcSVwcOJvXTriTLT2pknMdjGMIR0hsDX7AI=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/jansi-native/Windows/arm64/libjansi.so</key>
		<dict>
			<key>hash2</key>
			<data>
			WayqkRyiqiiQb1SRYTlTka8Elvha13vx5a7ZN/9/GzM=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/jansi-native/Windows/x86/jansi.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			nmQJwyZKbzP1+XxYJlwG6Vv5GolujheeuuuCBMXRntM=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/jansi-native/Windows/x86_64/jansi.dll</key>
		<dict>
			<key>hash2</key>
			<data>
			cCAmDI21EWBCMV6nNxSsNq2UrNwzCFIoDoiSElBghcA=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/jansi.license</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/javax.annotation-api-1.3.2.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			4EulGVvNVV3JVlD3zGFNFR5LzVLSmhC4qiGX86uJq5s=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/javax.annotation-api.license</key>
		<dict>
			<key>hash2</key>
			<data>
			Gwh60oLLPNChHk4WAxjqtP8Jlarn0i5qwNMDZ+GWxuM=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/javax.inject-1.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			kcdwRKUMSBY2wy2Rb9ickRinIZU5BFLIEGUID5V95/8=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/javax.inject.license</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/jcl-over-slf4j-1.7.36.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			q1fKj9IjdywXNl0SH1npTsvwrlnQjAOjy1uBBxwBkZU=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/jcl-over-slf4j.license</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/maven-artifact-3.9.9.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			MPAV0cGjk+GcGM1PQ1MgicNtTKMoYIzj3aeLdNPTFRU=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/maven-builder-support-3.9.9.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			LKSpZ73RKp6F1A4BI3T4bmPUoQMMGZ2kgy49ChxncNg=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/maven-compat-3.9.9.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			ETirBfG+cDS5AlHdMq1YJilw+S0uxNwUIwHdF++0YSE=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/maven-core-3.9.9.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			f6s3/GBE8grgBDdquEFDc2Ns9R4mrQse+ms/HNK+xQM=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/maven-embedder-3.9.9.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			IJNWXoIl6+vNwCJ60MVtPofIkt74e1gx3RdXoO7mWXQ=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/maven-model-3.9.9.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			j1mwoW/pyTO+dJpgrgcFoMsze7WrrziAG0C3QP93Vyc=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/maven-model-builder-3.9.9.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			pDdxgqwuWt/ha+OzyBmBpezdqwFBhN5yrh5SLwSndgI=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/maven-plugin-api-3.9.9.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			K0kdONtFsOju9SLo94iaM2blRuWLN2sH/LVuNMQk6TI=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/maven-repository-metadata-3.9.9.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			E3wpfmpS1Im3ZmPIIyTVTkD11Jio/AFcAgP9kd+GI7A=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/maven-resolver-api-1.9.22.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Y/X2ZeRKCe9VRjs7kf2gt4/wfdJLEGDVbnnBC24yy/s=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/maven-resolver-connector-basic-1.9.22.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			SraL3sl+7DGLKjvSfnyVTjFviQ35LVRLaK/Qv2ZslYg=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/maven-resolver-impl-1.9.22.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			5Nr7iswT1zY3fALSFw2GlDjddLmLhgdFkJ0jhya6vLs=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/maven-resolver-named-locks-1.9.22.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			BoXynsO1SNm2kXxSfxPGZ2haM5S5Vaqlsl0FWYGLf8U=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/maven-resolver-provider-3.9.9.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			XeoFBJyU+VL0jOK/4BEa/fmGrMWR/MEdI/47jctwKR4=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/maven-resolver-spi-1.9.22.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			ma1yHkYx2b0MT54pyGlnJXfGbypnSlcjzjjv8Tx1y/0=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/maven-resolver-transport-file-1.9.22.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			TyqFfYuDJJS66e9tfbe7hAk3iyiqvTYV8D+uvkKkrR0=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/maven-resolver-transport-http-1.9.22.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			HPKojJhODq6LPxPh7skE7t6tLwdtHzwdXLUQOiFTi9E=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/maven-resolver-transport-wagon-1.9.22.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			JB6diNHTriQrC3/m6oIiYFkNetkFv/GHITj7zeecsho=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/maven-resolver-util-1.9.22.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Sq6hWEw5KUypJvxHRyPZaERzYJ70SQxOsWnW6n2sprU=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/maven-settings-3.9.9.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			aO3xtRDg11nsUBJxpdBeOm5CVGL7uEEmwW6Kb4mr2to=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/maven-settings-builder-3.9.9.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			CUZA8/3ORyUMsGlooUP0DE4vHCK+l5xzyqwvSfPDg3M=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/maven-shared-utils-3.4.2.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			thM1fhutTfwd6tgBaRyUYPlYX+fGtGa8JRhiEtfRhIc=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/maven-slf4j-provider-3.9.9.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			HFivaDW/gDJYmQVd8EvPc/tdAAN3+QF3lWU1C6o+exM=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/org.eclipse.sisu.inject-0.9.0.M3.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			FTNcTc8IL1mfuO3c+1jWp+mpyX3iiDwlcImkebmyRSI=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/org.eclipse.sisu.inject.license</key>
		<dict>
			<key>hash2</key>
			<data>
			IJ/iS/VWd7v4HCsEgcFAMgH6tXs7TGCZceuk7IFiuZw=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/org.eclipse.sisu.plexus-0.9.0.M3.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			yZZ003c+JhVIhWYXEfC21jqlAI9cyZInojZ1bUrZ3l4=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/org.eclipse.sisu.plexus.license</key>
		<dict>
			<key>hash2</key>
			<data>
			IJ/iS/VWd7v4HCsEgcFAMgH6tXs7TGCZceuk7IFiuZw=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/plexus-cipher-2.0.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			mn8bXFqe/9Yerf2HMUUqL3ao55ER+sOR73XqgBvqIDo=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/plexus-cipher.license</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/plexus-component-annotations-2.1.0.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			veNhfOm1vPlYQSYEYIAEOvaks7rqQKOxU/Aue7wyrKw=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/plexus-component-annotations.license</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/plexus-interpolation-1.27.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			P7T7YUP9+WQCTDy3OFUVJLnqhOXCEc1mDFWa0HA+UjA=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/plexus-interpolation.license</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/plexus-sec-dispatcher-2.0.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			hzE5lgxMeAF23aWAsAOixL+CGIvc5buZI04iTves/Os=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/plexus-sec-dispatcher.license</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/plexus-utils-3.5.1.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			huAlXUyHnGG0gz7X8TEk6LtnnfR967EnMm59t91JoHs=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/plexus-utils.license</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/plexus-xml-3.0.1.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			waUQqHpivS10rBRy3THD+emwuLhWjzfXfA8TVBW+vQU=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/plexus-xml.license</key>
		<dict>
			<key>hash2</key>
			<data>
			z8d0m5b2O9McPEK1xHG/dWgUBT6EfBDz6wA0F7xSPTA=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/slf4j-api-1.7.36.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			0+9XXj5JeWeNwBvx3M5RAhSTtNEft/G+itmCh3wWocA=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/slf4j-api.license</key>
		<dict>
			<key>hash2</key>
			<data>
			b74ur0Sxk7ikDu2SCPUoSFciJK2NdnLdCUGKoXSEfnM=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/wagon-file-3.5.3.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			r8khb6l7eNrSJ7So1NZ7mJe/ETpX+AWY1imThBET4QM=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/wagon-http-3.5.3.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			0rbkjJ/L5XnhhYxiLRRGQBH/Jl+m4o55QASmiCFUpQk=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/wagon-http-shared-3.5.3.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			jn2nZvVRZP3od5qqoSWDJQbChIyrSHa1MFE4hz4oA38=
			</data>
		</dict>
		<key>plugins/maven/lib/maven3/lib/wagon-provider-api-3.5.3.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			XnIAAziUXtPpb45PV40dBnLhr34ZwOkBQZeuWzGvPvQ=
			</data>
		</dict>
		<key>plugins/maven/lib/maven36-server.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			x6t6a9+t2NVDirLmvlPSQXsQ1ButBE13AEXaj85gmE0=
			</data>
		</dict>
		<key>plugins/maven/lib/maven40-server.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			5VhnrK99rGg5sq1sokTpWyrXYJbEzN9k4AKuDTmbZRo=
			</data>
		</dict>
		<key>plugins/notebooks-plugin/lib/notebooks-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			5hxKx+V0JA0/E+ese7OZmfWaGichwIkvbLCXyiSP1JA=
			</data>
		</dict>
		<key>plugins/packageChecker/lib/packageChecker.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			L4LsUC4hy4Im8T7CdYP7SAbZnA39FRmAtZM40dkkd7Q=
			</data>
		</dict>
		<key>plugins/performanceTesting-async/lib/async-profiler.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			tb+RwlsbAUuDF8mZYbA8Tc9epyq3FRn9z+Yy2ZHFxBU=
			</data>
		</dict>
		<key>plugins/performanceTesting-async/lib/modules/intellij.profiler.asyncOne.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			bNaLNWFc07D+4Imc6BqUuRQwT//iBiR9kCt2L36qiik=
			</data>
		</dict>
		<key>plugins/performanceTesting-async/lib/performanceTesting-async.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			FZlgGpF3V59uGyAZ4Qo4NR5ufi5ZLk/82f4viMn16d8=
			</data>
		</dict>
		<key>plugins/performanceTesting/lib/performanceTesting.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			CSVWjWK9r6nEV30j2leyjK1RgGKo2PEyNYxTbzQTZwM=
			</data>
		</dict>
		<key>plugins/platform-ijent-impl/ijent-aarch64-unknown-linux-musl-release</key>
		<dict>
			<key>hash2</key>
			<data>
			WYi3L0DqsTRw0WDLozypPbImzNzC/fE2C+6SmCB8fhU=
			</data>
		</dict>
		<key>plugins/platform-ijent-impl/ijent-x86_64-unknown-linux-musl-release</key>
		<dict>
			<key>hash2</key>
			<data>
			K1EkkDmR+t0WW6UnN4f6vNNyroQZfvaVAcn5gcb/UPQ=
			</data>
		</dict>
		<key>plugins/platform-ijent-impl/lib/platform-ijent-impl.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			qvKMj7xRUvFniaE/+pT/HY+acDohW0JY69MTJZimVL8=
			</data>
		</dict>
		<key>plugins/platform-images/lib/platform-images.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			qSB2YmGmbtbwR1HdUbeOC9LFPLAjz7NSB1EUnqZT6xQ=
			</data>
		</dict>
		<key>plugins/platform-langInjection/lib/java-langInjection-jps.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			+nOU7459IG+CfTdxgncN03GqP57IHo++jXYhSTK/ZXU=
			</data>
		</dict>
		<key>plugins/platform-langInjection/lib/platform-langInjection.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			vVulNnqUcj7UiycV1KRDcAp2YpupuI5T+nHYqie2E0w=
			</data>
		</dict>
		<key>plugins/plugin-classpath.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			LwUvRbmjxswiIms8bsBlUhGbeGknocslZaOX1Q0ENBc=
			</data>
		</dict>
		<key>plugins/properties/lib/properties-frontend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			N9lSYNdO540TDyQUBQ4oPSVy5q8dD33gf+MItVv/MbQ=
			</data>
		</dict>
		<key>plugins/properties/lib/properties.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			znSJ0/yHfiWA+AnroriDuJsYFIYB7YIuwNu6pxV8NTY=
			</data>
		</dict>
		<key>plugins/qodana/lib/qodana.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			ERslu5qKYDz7ge62CdSClvr+hvg0vgu1JK3lUL91Gmo=
			</data>
		</dict>
		<key>plugins/repository-search/lib/repository-search.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Jrv6yy16qXCu+CQCfTitEDmHuzzCVACliqeMlThg4XI=
			</data>
		</dict>
		<key>plugins/searchEverywhereMl/lib/modules/intellij.searchEverywhereMl.ranking.core.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			K6kM0p0pm/OfpHoOkeWuTPwNvEFETNVBgrZ83bMuRhQ=
			</data>
		</dict>
		<key>plugins/searchEverywhereMl/lib/searchEverywhereMl.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Hyhc3p+qzppHRxgly2EapfHZ0qgEiXmptqxdqHsnIGU=
			</data>
		</dict>
		<key>plugins/settingsSync/lib/settingsSync.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			CP2mX851gyyG94+U3D9Z6mxsgTP6HLxC97uRXpU+unc=
			</data>
		</dict>
		<key>plugins/sh/lib/sh.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			sGf1tT7LnrIjl2VdrUBqimycF3pQN2uB7h+BAv7vXI4=
			</data>
		</dict>
		<key>plugins/station-plugin/lib/modules/intellij.station.frontend.split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			VHXwqUPt2tb0abviHPSpC5e34DXaDWgp7/hTk1Lvbuo=
			</data>
		</dict>
		<key>plugins/station-plugin/lib/station-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			zgSf30GB65NSEt5cqpuryVZgULGGtIb3IeTMBfhlIKA=
			</data>
		</dict>
		<key>plugins/tasks/lib/tasks-core.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			w/XdQbTEthtbl3E5k0U79Pe3Zj3Ya8b5ZRmg4Oopz40=
			</data>
		</dict>
		<key>plugins/terminal/lib/terminal.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			rNAnwku4FgC9qfP7YESDvL7yKZ0RH6t7LjdoGRIDN9k=
			</data>
		</dict>
		<key>plugins/terminal/shell-integrations/bash/bash-fig.bash</key>
		<dict>
			<key>hash2</key>
			<data>
			LCUQ7W913VtGQOxUjvu4uefK5ZUioAZTcwlO9qCyW+0=
			</data>
		</dict>
		<key>plugins/terminal/shell-integrations/bash/bash-integration.bash</key>
		<dict>
			<key>hash2</key>
			<data>
			8HeeT8e3NmsA1B398tQoly3TpyI5LlqSdkzZsxZVXOg=
			</data>
		</dict>
		<key>plugins/terminal/shell-integrations/bash/bash-preexec.bash</key>
		<dict>
			<key>hash2</key>
			<data>
			qpEteXZxv+L5aXOuJjnLRMKoilQ2e+5bV5SOWP99lH4=
			</data>
		</dict>
		<key>plugins/terminal/shell-integrations/bash/command-block-support-reworked.bash</key>
		<dict>
			<key>hash2</key>
			<data>
			SuPzkm1evCXkqAQi3EBUcX6qmf1OOeypxYlp9KcSSB8=
			</data>
		</dict>
		<key>plugins/terminal/shell-integrations/bash/command-block-support.bash</key>
		<dict>
			<key>hash2</key>
			<data>
			bXSf1tJqHhnvXOasulRNbQCLPYfQtFWIT4VzDkI7DRs=
			</data>
		</dict>
		<key>plugins/terminal/shell-integrations/fish/command-block-support-reworked.fish</key>
		<dict>
			<key>hash2</key>
			<data>
			FuuoKvGiZQGmCcqg5JmB3yO3Yefeotcb1KoKpvA967I=
			</data>
		</dict>
		<key>plugins/terminal/shell-integrations/fish/command-block-support.fish</key>
		<dict>
			<key>hash2</key>
			<data>
			ohpuVReFVv2+m2hygDa/+NFbHbvC1KWjv+lKTsV46Fw=
			</data>
		</dict>
		<key>plugins/terminal/shell-integrations/fish/fish-integration.fish</key>
		<dict>
			<key>hash2</key>
			<data>
			5XpAnbzYG8fWiFwznNkZl7pXeyMrBf5h4EpzQPcbwD0=
			</data>
		</dict>
		<key>plugins/terminal/shell-integrations/powershell/command-block-support-reworked.ps1</key>
		<dict>
			<key>hash2</key>
			<data>
			k7010kx7pCLbRZvuBE4Gyr9Nf2deL/DpSTtXsXMzlCk=
			</data>
		</dict>
		<key>plugins/terminal/shell-integrations/powershell/command-block-support.ps1</key>
		<dict>
			<key>hash2</key>
			<data>
			Eiz6rDP4Llg0jtmlQZ2nXh3xSf5E1Ed6lFDZSFtIR7o=
			</data>
		</dict>
		<key>plugins/terminal/shell-integrations/powershell/powershell-integration.ps1</key>
		<dict>
			<key>hash2</key>
			<data>
			TpN8J6CDwsmeyNOoogIjkZCIjuim+QuKdTK211miwKQ=
			</data>
		</dict>
		<key>plugins/terminal/shell-integrations/zsh/command-block-support-reworked.zsh</key>
		<dict>
			<key>hash2</key>
			<data>
			FlHE1Ume88doVJvgYpEP4j88NCCbtQHzESEYwFo13YM=
			</data>
		</dict>
		<key>plugins/terminal/shell-integrations/zsh/command-block-support.zsh</key>
		<dict>
			<key>hash2</key>
			<data>
			J5HgnjtTuU/Ki7u1VK5jEN7G+bJRO6KOTfYcQbe3Eak=
			</data>
		</dict>
		<key>plugins/terminal/shell-integrations/zsh/zdotdir/.zlogin</key>
		<dict>
			<key>hash2</key>
			<data>
			9tWQ9lVo5s0P33CcITgc96v/jKb/qRQeaaK3ao5mqdA=
			</data>
		</dict>
		<key>plugins/terminal/shell-integrations/zsh/zdotdir/.zprofile</key>
		<dict>
			<key>hash2</key>
			<data>
			h2E5wJFHWZiPRpsuzCfOTQ05uDVZtPtxnfYdQXPUpE0=
			</data>
		</dict>
		<key>plugins/terminal/shell-integrations/zsh/zdotdir/.zshenv</key>
		<dict>
			<key>hash2</key>
			<data>
			7Xxw3E6Lzw/GOzrOkJ30INX414bSLFcWBaarHK09u4w=
			</data>
		</dict>
		<key>plugins/terminal/shell-integrations/zsh/zdotdir/.zshrc</key>
		<dict>
			<key>hash2</key>
			<data>
			f26U5FvWNd0vJp+cg0WoPwawogSGY/JjxL8R1TOBx8o=
			</data>
		</dict>
		<key>plugins/terminal/shell-integrations/zsh/zsh-integration.zsh</key>
		<dict>
			<key>hash2</key>
			<data>
			JKABttQpwI6At3ryTZhwTaVbPR4NAe6Qgu+l6TzuH4k=
			</data>
		</dict>
		<key>plugins/testng/lib/testng-plugin.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			PuOMyicXOzsk9Hg0t1aExxuKQNxiftctV74sq5MCVNY=
			</data>
		</dict>
		<key>plugins/testng/lib/testng-rt.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			AHaMEEVLkXbJM0IvsDJ6U/lvEd9hHH3Gteq2u76Wgh8=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/adoc/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			1zpiaHmaAxjy6wtkODOTl9fx8N8DF/OXqAbGqiN5nHI=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/adoc/README.md</key>
		<dict>
			<key>hash2</key>
			<data>
			pfnl1O4xSDHyA30echFcG8RDNYqhVC08g3a1SpiVgXs=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/adoc/asciidoc-language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			6vVIYiOOtpk2RZbSaNTtdy63Sh9YVX0wmzY8FL8qHu8=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/adoc/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			s2tvK89QQGgDAQkVGZ3jicACgf7uxuNX/5GRhqysm1Q=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/adoc/snippets/snippets.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zWLtZyo5vJikpZnPqJVWA2emfaR2f8u+VnndPMHEoGA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/adoc/syntaxes/Asciidoctor.json</key>
		<dict>
			<key>hash2</key>
			<data>
			B4H1peofKMhGdTjgD4TGN4N2k27QYpzet+SfIh7rpA4=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/bat/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/bat/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			L6csu8zQKUnd8xvOAO7Gzy+im11WIUryoWGtTeSEUnk=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/bat/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			IVgFCZ9U1l9j/pltF+eLuWcdUd3k0inZveuYd2qxfss=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/bat/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			xL8Gd1XOW4mvO9QKD2JJRqTn6qUGfuhg9DlSx+Eegto=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/bat/snippets/batchfile.code-snippets</key>
		<dict>
			<key>hash2</key>
			<data>
			lWC94vcxBeoYve5uxS6KObpPS4A8F6BDYS6aZbRHHEU=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/bat/syntaxes/batchfile.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			M2YKm15qkYjWTc/fEi/WQi37QOwxQfYorumtzhfmWWE=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/bicep/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			oV+8dqcFBXSuyumQWBzl4kE1bUC7mZBJII38QC41Xvg=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/bicep/syntaxes/bicep.codeblock.json</key>
		<dict>
			<key>hash2</key>
			<data>
			VjSsPoCf4ZIGljA4iioF6dZFT16dFCQxDXGy5RMgdp0=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/bicep/syntaxes/bicep.tmlanguage</key>
		<dict>
			<key>hash2</key>
			<data>
			bUsZizZzDVmKxIkLpatqc62lsaOVThniW/e0gUd4FhA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/bicep/syntaxes/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			azlhCQyV6A7YlbMvpTEQt3AhBIR54C9Du6JLULOyW/s=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/bicepparam/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			irxEYCotxSwxU4aQa5SBHy/4EoqVZu0pPChlTL1Mbrw=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/clojure/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/clojure/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qx8PLGOLfIHhUZAIc7JxRhgXLPy4BNhwSUUuLmnw8Ws=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/clojure/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			d4qrD7+D7C8u3N0GU/SfC8d4kC+iJxIQAc9Ju2DExs0=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/clojure/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			bXekFMhWDDdfJUO4cIQqeAeRPhCcVzfDZFUuuWB9J2I=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/clojure/syntaxes/clojure.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			C6aks+AirxJ1RwBmkszDvJfuPykVUCM+7CKdxhTZRZU=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/cmake/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			RDmm33t+wOA4pR2mtbwrrI88o6fvL8LtoY38wyXFxnI=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/cmake/README.md</key>
		<dict>
			<key>hash2</key>
			<data>
			nOYkqvFL9OZT3lCTF8pYzPtXimY+Z3ubwEgBudSB9gU=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/cmake/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			P/jbZM0xEZDk++qHttEQlcGgkhR+9TFK0DIH7YQ9YOg=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/cmake/syntaxes/CMake.tmLanguage</key>
		<dict>
			<key>hash2</key>
			<data>
			t3LM2axj0+nAppF4P1UP+eZAnnICceJJyrfxTsh7uPM=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/cmake/syntaxes/CMakeCache.tmLanguage</key>
		<dict>
			<key>hash2</key>
			<data>
			Jw4cPaXdnOoHbwTEVksa3fPowxNT07qBVmkfIK1HZLM=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/coffeescript/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/coffeescript/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			kBijnau0hPa60pp2wB8mWNqYUnfpkvytPVrhHlD0X8I=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/coffeescript/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			JbXmA2bL8pr+lNftsFr3aRKkFLRj6i79DmvB7p8JtvA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/coffeescript/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			eskZsHCsmZq2lFmagWuPZQPAcBODqzpJtKLktNcxyqg=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/coffeescript/snippets/coffeescript.code-snippets</key>
		<dict>
			<key>hash2</key>
			<data>
			DzB0FjXNCzGyC73LPUd8+KGQNJDQvsK9ZMJi9Pf1PWA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/coffeescript/syntaxes/coffeescript.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			CXxNpxQ6LnEKkhKKwnXfmFXFO1KceQLZ665p66mr/u8=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/cpp/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			PIyTx7DwHGl5VGIrRW7kylQWoeX28jl2zJnehbgVjUY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/cpp/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zLGyC/6Dl+f7SzI/XcM7QTXXzs3uAiLxyOskg+d2L3w=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/cpp/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			jZDBz27kZtvo8OMmEKgjLcoQUXQUVfbDgkzbQGZkolc=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/cpp/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			fdi8Bcgn1fXITw2yDxa3qiKJx5ltc8EWgewUAGB2+cU=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/cpp/snippets/c.code-snippets</key>
		<dict>
			<key>hash2</key>
			<data>
			rxxl9/FXnvw+TVMc5si3n2Dosee23TmJkPgtdWdmVUQ=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/cpp/snippets/cpp.code-snippets</key>
		<dict>
			<key>hash2</key>
			<data>
			rxxl9/FXnvw+TVMc5si3n2Dosee23TmJkPgtdWdmVUQ=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/cpp/syntaxes/c.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			nQowW8ZZVSb/wGgOzL8joSUS3tmaYy2fHeqzzoG2KVc=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/cpp/syntaxes/cpp.embedded.macro.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Xup2DdD+jxDfPMOa+9AcW1/uq1u4D9FalWAeOGgoyQM=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/cpp/syntaxes/cpp.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			grOcjyrsCVtFooerFV/PhowZeyFrqNb+DJVnS4KGMYI=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/cpp/syntaxes/cuda-cpp.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Ta5h7u8869nRzET9xhI+eGFWvhy/vVsZIwyQVOhZAvA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/cpp/syntaxes/platform.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			nnPj7PAJxiQ2Fcg9v70EHaHVqMBJc50cmuEflfLFbDs=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/csharp/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/csharp/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			N2zgRizaadYcbDZY7UUQwmMC292emtp8+f1FOcUfYN4=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/csharp/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Wb409r2lfa1zYBujv3wHvRR3Tlasczb4aZHxnP23pjE=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/csharp/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			DlGl6GRUjhIBHCY409hG4HT6BKFAYwHYZ3VfqNCSBjQ=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/csharp/snippets/csharp.code-snippets</key>
		<dict>
			<key>hash2</key>
			<data>
			OTbMCfRjXVFriu+zoLRRvg4ykAuFFbG3eI68ELH1a0U=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/csharp/syntaxes/csharp.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			lir7aypVKxZ99Q4Az0FgWjUe/rtQmRStS/D30amcGhI=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/css/.vscode/launch.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Litfp9wVCwOprGk0zrkxFkCj9DcwcjWb1V9+9uMKlro=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/css/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			7bWV6smJ8dZ14QDC2PngkRfcU2QEe/CyuRLyziFfuyU=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/css/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			l4kfsNUVqhJMDqsU6WGu67UOoMfTY04BhT9vQc5h8o0=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/css/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			JfMWbQwWNhHYzQgTPu1EaoD6Ny5uSkt7g858Z/1Lgww=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/css/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			le3pSFk5yTYgu8rh0ZfAaERGJH7PmMf4Tmd8lJw0a5c=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/css/syntaxes/css.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zi3Q3BokHWQyHmDW/jo3VYeW+wPn09EOESRB8dwVTGM=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/dart/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			1bT2YOa5DkPSwx3NgwrOz91A3zxzSkHZLg+BAe0xrno=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/dart/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			fM0uXk/3opsNKJcGfNXGbQpASJKIbBYr1a+BI0rtOi4=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/dart/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			bNgcDnbLXyI91BYjxNlOlUDxj6a0/dPoQvCne6BJEWg=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/dart/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			boXvScpsJONMTgbxeqbGGRKxuCHCYS+6GRuJBTUEaVk=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/dart/syntaxes/dart.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			3QbPtdKZ7LoD2KTO+uFDvTwYO4bpecmCYRLNJ2Z8Q5s=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/diff/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			1bT2YOa5DkPSwx3NgwrOz91A3zxzSkHZLg+BAe0xrno=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/diff/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			xvVnw0aQIDmjkeEgtsUjkKkNiPwjWmOrtoXNhBCTssc=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/diff/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			cx8Mv11df2/o90BOpiEqZGWivXztU+x4OgeYPwIoW2M=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/diff/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qj+LDUpoJ9XO8T3i2d9Fg3kWA6qbsM4hBOq/931BTqU=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/diff/syntaxes/diff.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			y6bbAwNkK1IYp6PvjpRt8F0XdF7OOBqXj/Fx0ppR4vA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/docker/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/docker/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			RtVS35/QsxFFEKXBtvF5frG9yA71aXvpaUS8DZdEQVc=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/docker/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			0VwL3yjYpk3EYo/cSQnnIiJ5+u4obYV9tCr3GmkRZs0=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/docker/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			SbZSk7qPvAdPp2VXbflsaVgsTC4s8IAmFZUMrLLrum4=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/docker/syntaxes/docker.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ma5hc66RUgNONeF94JL04RJzrO+4IyBZK7+mJ6KbsDw=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/erlang/grammar/Erlang.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			qco5culq8GatQJwvcC99HMOuQji6xzivlkHfZe3rWoA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/erlang/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			aOuKWwRDqXmR6egymCFMek0b6BwMf+2wF9G+J+5++H0=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/erlang/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			AnfVTLE5jhkvyyc+LGSww2ZHniYId3oVTPqgO09IQyA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/fsharp/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/fsharp/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			nWrZG2F5iwd6+mYzdLcmqa9XWxluV+mMzGWE+f5vQao=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/fsharp/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ReMiPZmika3TriZpYZxbJxlaBvGaPZzL7o7rnzsVO/U=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/fsharp/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			9snwuSi1ig34B2L5WQ1YLgDGR5vpW240SZpajezsysg=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/fsharp/snippets/fsharp.code-snippets</key>
		<dict>
			<key>hash2</key>
			<data>
			VFHv4J2hAmcVa8LItF097c5GOZ2g6GEdRrbv7RnDTHE=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/fsharp/syntaxes/fsharp.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			KFeKv+/jHt8W8Tb+UxL9UDtd/4IF9PJUBl77gTD/oOI=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/git-base/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			GcbhjaeWYmKx79ipEaGvPUBbf/AJG6VbhD2Mnb8E+Dg=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/git-base/README.md</key>
		<dict>
			<key>hash2</key>
			<data>
			UTrEnXFdSLYOarv4grjGJpdv4/llr4z3x418j/HjMiY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/git-base/languages/git-commit.language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			EMF6fGLTYKD5e86CHiY+gJ7Ut1XM+8oclVuanA0LHhw=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/git-base/languages/git-rebase.language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			EMF6fGLTYKD5e86CHiY+gJ7Ut1XM+8oclVuanA0LHhw=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/git-base/languages/ignore.language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+xp52quXYpYYPBn3eYh4hkmJoGbbolWi36VCaBojqGc=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/git-base/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			cJREhaWkGgZofEWiCdhYEmSaWGgFsfvZRqY8QyBHQn8=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/git-base/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			A4+11AE65uh3zumYYpNaW1xuESI4+9iURpZ+XrmQFkA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/git-base/syntaxes/git-commit.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			qyKy2rUHba9qj5kPAr32qbnbmbtkfA2LvPFePrMfEAU=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/git-base/syntaxes/git-rebase.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			PEXMIb4K+aWZ+yWzUgfbnipDioTqoX5UgSh1I1oiZvA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/git-base/syntaxes/ignore.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Gj7SI+komcRpVBGDiflB3SVnv4+M/bmeWR5O10B586o=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/git-base/tsconfig.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zCts4IOEe8iDMgp3usw5CD/2QczTduAPnZOl2lHG9+4=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/go/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/go/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			SPQuq2kT8M+/3zTFVi9nXxUazGpVx4pg8eEhVR05tmQ=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/go/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			3LdG3JCXsqW+vosqDLBNL7ReBzlrMR8nhIJyMSL6heI=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/go/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			d7QqB96mifp3qFid4HnmEvblYcr52hH+4rjqLFA6VG4=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/go/syntaxes/go.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			GCrl1f1m+H3Ft8R1wPz2WFTNO3p7KooXtKS6z1p1I4U=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/groovy/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/groovy/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			CIwaGX7pf6k7tw/xupSgZR6xOukhzsaiJJpW9RHBsQQ=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/groovy/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			NKgs9ihzTyfQ1HwemmLnwsbnffUtCNoHIQ8Sx9oTr1E=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/groovy/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			6873bQg5FK0v46GaQBarbKx5QlRXEehhUGFOUOwc8nk=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/groovy/snippets/groovy.code-snippets</key>
		<dict>
			<key>hash2</key>
			<data>
			BHr205okYHGu9zRbXZJS2rREnvBjrNtwpge1tj5sM3k=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/groovy/syntaxes/groovy.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			fmHcZkao6THYOND8Yvw0CHSfIa7rmJQxuH1JxRtVm18=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/handlebars/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/handlebars/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			tMUUxvxRUrjysJPJlpJ1A4WTNb6s+NqtN3civ0m/4b0=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/handlebars/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			0ngmPxew+tyTv7ppda/7a/mg+ExYw3dQZjfzkvpzNsM=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/handlebars/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			aT+9e0okKZOMN7K9732BtmseNYiDH2LrHDdZFVENDBQ=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/handlebars/syntaxes/Handlebars.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			E/GXYhh7KOIeuPEcrkKvhz8LY4NbAw7rbG31OP9DPY8=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/hcl/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			HWtpGE9K5lyEN94c1NZ5J/M8w/wNTe3VplUREQO/hVA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/hcl/README.md</key>
		<dict>
			<key>hash2</key>
			<data>
			egZGFDUJhYNhX4Q6g1RMWym9OC+0IrlWlZc/2pdA0us=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/hcl/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			PKhAUZxxRW5xI5lVAZnB8Wh71B2wUzjjOpicD7VUu/k=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/hcl/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Qolw5EPwAHN2hWHie6Hy4jfOLLc5R5VvEj4bBGKQWJE=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/hcl/syntaxes/hcl.tmGrammar.json</key>
		<dict>
			<key>hash2</key>
			<data>
			o5ytZgVWkDEdQZiuOUFguvAUWFnGPT8OQpnw9N2PEY4=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/hlsl/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/hlsl/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			KHmdjKRqZgSBtgY24V0Qa/mAkyuwRWI4/lqf6bcFxWQ=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/hlsl/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			bUqPTn1lydmie9IXbYLbQmJF+A313ofcNT4Bkr0rvg8=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/hlsl/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			pGG+qq79vzkWdn6dU0FdxakcOUL3w41kJRNlIkUIFuQ=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/hlsl/syntaxes/hlsl.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ixItZ72ua70cWB2+Ej6eg/NBMGLI/HRHEtBCnObDCiw=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/html/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			PIyTx7DwHGl5VGIrRW7kylQWoeX28jl2zJnehbgVjUY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/html/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			/mDIOzbPvHDHPUhAX2rLT4hWInO0PbLoj0W8XB11Uk8=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/html/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			P8cDNQ/XgyPgpjV3EMko/cni7/RO03Uo5+ReTngCeyU=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/html/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zpFFKbV8Lo3w20zvesL5hDs97WgZ7+D1kDHd4LaIUSQ=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/html/snippets/html.code-snippets</key>
		<dict>
			<key>hash2</key>
			<data>
			NxxH5UlXtrutbQXHYrbw2rPj2qJEQVgSOdlRoeVtY0s=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/html/syntaxes/html-derivative.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			V8a1OgFP6HMB+HFsr0559peW5VYGjTNkGw7Z/3fbcMU=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/html/syntaxes/html.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			gN7fT7J+iIiayPtydjlUptJmBQLGhvRBUgjYyNADUs0=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/ini/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/ini/ini.language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			l+IsfQvM6ugK2XDQFN0EEtn9xE1FGtaRj+UFixfbeXM=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/ini/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			olhS8Mgz4t318ye9lJRDbLux0295XUjovSurUyEovCk=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/ini/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			0Qr3pRM0AHF4uuIUvdry8xdTiChhu7l6qyRb7CIli9o=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/ini/properties.language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			AEuVJEnZJgPaK8NC3o3UfoIdTAtB3INCH1t96S3wCac=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/ini/syntaxes/ini.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			pGYTvZk3OWlctivgTSPF13l1S86f2opurWHs1GFvlf0=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/java/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/java/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1wi3NgEGw41fjXbCz9LRZ3UxLr1wel4aUjZsG7bRWwM=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/java/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			sjQGSkEoAIV2zXAsAnwl4b3R68PRmFY14L/O1KVgNvo=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/java/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			bKaNPTjYuVLeloLKIdqtjxRQNGFLoEMq1x33t8WP4e0=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/java/snippets/java.code-snippets</key>
		<dict>
			<key>hash2</key>
			<data>
			HSnY2V+6pGD1aEHvE+XkJXujG++AYhwi/2ITzVZnQvI=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/java/syntaxes/java.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			cVK1BEb8l7Kr94dVlY7Y0ncptTNlRl7Ym27evBpum6M=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/javascript/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			6lq0bOrCSFv7NL5XjT75MQ4CTjw2vEjHTXdljbEqWSk=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/javascript/javascript-language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			jn0lfJbbYrNKfciK24trWeZYjJT/FEU3ATGacAcboO8=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/javascript/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Vx4QoQfAEdHsFD531Tc6Z53Qr5wfd/o98d3DqNEr84A=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/javascript/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			tF5zgPcJCCK/+ypc+3q3zVNq07x8EWCa7lZvaNmEcME=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/javascript/snippets/javascript.code-snippets</key>
		<dict>
			<key>hash2</key>
			<data>
			7RQFooLwQnDmJ2U+stH38hRSLxmZncCz+15PjBHTOcw=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/javascript/syntaxes/JavaScript.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			/o3wOKZZpMxJm3IblRlnf0VVnQQyxPZfK2VDQrfTiJM=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/javascript/syntaxes/JavaScriptReact.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			6koYs73J1sheORgxBN4/paUySA7KgBNTLVZwF8OW91E=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/javascript/syntaxes/Readme.md</key>
		<dict>
			<key>hash2</key>
			<data>
			jaZj76QNbtOc+TagfHxTRQtOJi8qDc/Gnz4ILA8x7DM=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/javascript/syntaxes/Regular Expressions (JavaScript).tmLanguage</key>
		<dict>
			<key>hash2</key>
			<data>
			ABCMQeSaqDEvDpTh2pYVgNeckrZOqw1gj5Bx0QgXGO8=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/javascript/tags-language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			/pHWq49Yh4Z8VpYkGjFV7LUOSuZgbDDm/umluSICwiY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/json/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			PIyTx7DwHGl5VGIrRW7kylQWoeX28jl2zJnehbgVjUY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/json/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Gw0WGRbR41u8+lRcw1VNHnV+7WMiCPmcJCU1I1wGxgA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/json/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			qXCRDz+k4baEWbTGoIy2ND65f4BNz4Kzf9QkaKxEZhc=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/json/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+Tf21onRNY8idsy9voeFeBDaBZyakxwqgAUA1r9/ZaI=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/json/syntaxes/JSON.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1yOPHMkDOZO5gWsJRcq0u46hCN4DmWY8VR8o+RPpTHA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/json/syntaxes/JSONC.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			n5J7s6pT5m78PKzp0voRhYIrKt+j9KkpnnwHjxNuHuI=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/json/syntaxes/JSONL.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Q5k8OT9hQa88vIweiy/c8vf4j8fmNBWsXzara7BboQM=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/json/syntaxes/snippets.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			D8GvJ4hXr3D4+XrJWagkWwg0wrCtiY0sAyQQP39RloU=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/jsp/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			AUVSWIF+XD5iWJi5iE+22qNp6GbHCLohQfldqoYvQqI=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/jsp/README.md</key>
		<dict>
			<key>hash2</key>
			<data>
			4qXvhtY+2Tri9bF3YT2Ai+bEhl/Hh1nzJMOtczGzyJM=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/jsp/jsp-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			2gPhr9Z1uaoU3nC6s/rKy7CVMhCVPXhjppji1Kfw9oU=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/jsp/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			xQvjZoAdEvvjHZzYzN5kxXrbENVSlwzQLWzqfpQxPGI=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/jsp/syntaxes/jsp.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			uEQoXEL2myqdxbfOpKdzmR2ZrSFqZlmgNsmuQ7XLYe8=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/julia/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			1bT2YOa5DkPSwx3NgwrOz91A3zxzSkHZLg+BAe0xrno=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/julia/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			42+wOr/J+Dqc2hwuxBwBW+XaZ+CGh8jMcfQeH23DkTo=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/julia/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			5nyEPVBzd29hplLV3xaSpFKPoSbesb2P6/6SEfhHslk=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/julia/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			orEede/qto/s0s12tsGLVM007rYG17vyxgONdUvnXt4=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/julia/syntaxes/julia.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			JvKDdjJkQ4prtsVhpf+7aGMoBUv+pxVHcaE2rXZ0mbs=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/kconfig/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			rg45FeuX6fMH7KcxbYzCt28RX+sB3sievzMZ3Hf0Gsk=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/kconfig/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			cjr6rJdplRF3RWJtDmCom9XWFLT4iMjJuP6nGXZmRqQ=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/kconfig/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			yAFuzbN1MH0Ton7KALH4SoUEtDJLx4jnt3EJS/iLxQk=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/kconfig/syntaxes/kconfig.tmGrammar.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UYdXJekZT+9zz0FF/8gTogFw9rqJ7sbiL4nyFjofiNM=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/kotlin/info.plist</key>
		<dict>
			<key>hash2</key>
			<data>
			Vh9x/WOW8AK8RxYdYuRRkfx/XbL1rQnwFBZX6ok60mc=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/kotlin/snippets/class.tmSnippet</key>
		<dict>
			<key>hash2</key>
			<data>
			qz+qumn3uTRtc30XKskspWMUkqLUsraMvKvbyGdzKjI=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/kotlin/snippets/println.tmSnippet</key>
		<dict>
			<key>hash2</key>
			<data>
			SddFkmwcOcI+T44xzvcyGHf0gaNl+DQ8g9XS9XoW/1M=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/kotlin/syntaxes/Kotlin.tmLanguage</key>
		<dict>
			<key>hash2</key>
			<data>
			PdY/vHgs4jBRlk0IZqaCpMrqjure9HdqES+C/BbxVqI=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/latex/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			hYeZCDxe2MIJBZk4kH9irdr3QPJJN6YwmgRGTPkrBhE=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/latex/cpp-bailout-license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			tdTshppbb5IRl2Wof3k2adcw9IBifnCn9dyfdCZfUEo=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/latex/latex-cpp-embedded-language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			w06RKrGxy6qoAAkxwNoDnYoZkuOdxA4S++ESBpiinD8=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/latex/latex-language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			bCeig4qqLsnstBAP0QbhHv2qSSf/BYi4dRPKi87zLHY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/latex/markdown-latex-combined-language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			fM5Y5U7zYBMjpkHZeg5vNf62rwWcJiv9Ke4mBH00bck=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/latex/markdown-latex-combined-license.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			YEt/xZ4w2MkJv6wT8vILxDoOAYEkTIwd5iEIUFAJ+Zg=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/latex/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			E2JjBjt0d2iwLAfj2Jk0DXxwuf5eTcicmlBVb8uYkvE=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/latex/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			hi1lLuXLLYPcd1JLbuAmdsHLOiEkNiTbZT7XYNpfOiA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/latex/syntaxes/Bibtex.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Q7PzoSbfuMTt6F98l+9x7kiLfNNye3h1dZTH/4ANKP8=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/latex/syntaxes/LaTeX.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Gfu4FKcK11SeqzSS5PLFMbcJkeI0YEosR9y5TriI/LA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/latex/syntaxes/TeX.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			xuFNTaYfukPgASrx8u3RFfHWKsbyZc7CqYf1JFJoawg=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/latex/syntaxes/cpp-grammar-bailout.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			uGwEBwq88C4C8PTWMKKUKDD9l23VtLkYHmGg/a/sJTY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/latex/syntaxes/markdown-latex-combined.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			O6tiJ5Iam4WdxQJX3gBz+N7F4QYHJEHWay0ZtM598Js=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/less/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/less/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			iphT81hycKzX4/7yG7+6SmoYUwPGPD+F6VqdGhBLPS0=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/less/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			IY4vrA89vylUqWW79rB/tkgO3Gni73WXHPWIIaaj7HU=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/less/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			d5SaaL+GnWFNY1V5LivtQzqg56p69MAovHLfE1dzmyo=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/less/syntaxes/less.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			m+My116/bdGHj8+gRaUxLLVLioG3S/L+JXAbGDaHcXM=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/log/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/log/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+QsPnJaCd+a1tP2x4hBoaKMht3vuS/xPExquDKS1XO0=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/log/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			pxIh2jGt8gqP3cwW0c6ApTYkX9P5QdZhZZaKaK13bwE=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/log/syntaxes/log.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			r5uvlc3ZFDmafyCl4BvgNRjYuWd7z7EjfUoMwIsV7G0=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/lua/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/lua/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			31SKqaEpDK33+zdix3cOsoxo8F0ksI5OFQTg49PnOZg=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/lua/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			YQy2FyCm0XJHLgqdxaE5Vs9UFqJi7t/hZ+bb6jAg7EQ=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/lua/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			0GMYAIrmEellCrk4OLMMnO+Ge0Uhs9B04gvQJq9h9BI=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/lua/syntaxes/lua.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			9JGUa2MJlNrsr7us9mk3fnin/F5tMYp5u/WIgs/HD7c=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/make/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/make/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			WJsJNsJ/u9IQ+ZDAmPcrXvOITIbaoaQgkcyyMAue9cw=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/make/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			i4vS4pquHUwikVY5knF+tjj5bu26YH8nzaIHVU4xMT0=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/make/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			dDzcaiaSaF6NmyIoRDa6eTPaLFWCY/oecH012pmVItA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/make/syntaxes/make.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			CcWcrd93MUieQ8A/OqoRd4Vhh1PdN/U8AjWQxTHxI3k=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/markdown-basics/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			llYyogeq5gk0u/B8Jh8buPTa7xuscg0nJL5GyoCfWVU=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/markdown-basics/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			WMagBt8j0yXuUU2GqGULr0FPLkNCtLOND6v+uhPNqh8=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/markdown-basics/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			BTxXUV+fKsOwulHIzyrI1CuopiY4QZ1MDwFa3Nqsu+w=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/markdown-basics/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			MOQ9x8SxnQRgyt80O/646Ob4FGmoYQ69ilaEbPbnI5k=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/markdown-basics/snippets/markdown.code-snippets</key>
		<dict>
			<key>hash2</key>
			<data>
			3ZInO5vJY3NNSoDCDQddsW10sI0IVmSdKENN5x7uqnA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/markdown-basics/syntaxes/markdown.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			hQVUSzR41umEu9vHobzPgEVLi11Jm2bowPPup4XliOk=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/markdown-math/.gitignore</key>
		<dict>
			<key>hash2</key>
			<data>
			Ks+yI+S0M97GmbzQTaiUZ8JEHEjBMknYl9KK3SUoWB8=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/markdown-math/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			oYlMR7stDvIwsilMKEwMiXeewtbKFdidSziX8qUJU28=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/markdown-math/README.md</key>
		<dict>
			<key>hash2</key>
			<data>
			WBsbdz3BpHQaGn9IS5WS3jzhZoFnvj9UEFfml+SYUfc=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/markdown-math/notebook/tsconfig.json</key>
		<dict>
			<key>hash2</key>
			<data>
			vMQGZiAuPZeEDoMDx5s+SpTk6Z/RIJMKQ/nZQ2nkQ+E=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/markdown-math/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			IerDofsK8wkiLP34uGnVyOqHeWjwcIZSY26kF0Ii1EQ=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/markdown-math/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			5XitK7njSwKPTBTJyDiNzCc6okd76dsX9b6jCjqxJHA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/markdown-math/preview-styles/index.css</key>
		<dict>
			<key>hash2</key>
			<data>
			e3c3+JpGBsEOMQH+0SPf2DCTnVlZD/b5OPlZbKBCwsg=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/markdown-math/syntaxes/md-math-block.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			3GtUOHSVpCEb8XP2BB+ON3nzHwosvlFFaQLGixE0QrU=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/markdown-math/syntaxes/md-math-inline.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			M4B44Sbg8xP/DZJRDrUGBiUq2w6BcSAyVoB5mOIFZQM=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/markdown-math/syntaxes/md-math.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			XujgxDc4YC/G6/HjzsHlQroWP9/lOO99Y6+q1pHdLsk=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/markdown-math/tsconfig.json</key>
		<dict>
			<key>hash2</key>
			<data>
			39i9qO2fDrAoXVTqNZ2CAs/WXqPj3NUwMzwNogJBPfg=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/mdx/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			NU3I6q7Wk8O6WsMVjehfP9O9RTfyzS9ZTSAB4kEy9jw=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/mdx/license</key>
		<dict>
			<key>hash2</key>
			<data>
			sEy2CegWKoE/SQ3VGFSHMPbmh9bBY8XNiU11eTANcGE=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/mdx/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			TKZojTo5VXX4XIDJJ2CDaxKrXdj8owWiDafufQm2HjM=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/mdx/syntaxes/mdx.markdown.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			E6CuJHZsJrenBvPO6Lt4onrq/INA7C1IKC59JEw4FNI=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/mdx/syntaxes/mdx.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ur5LaGAXD/f9SDguOutgrkXCAGb8iRSbKpPlnmMykH8=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/mdx/syntaxes/source.mdx.tmLanguage</key>
		<dict>
			<key>hash2</key>
			<data>
			pH6OZ+PO6yifroratenJGF4IXv0biU22H/4C7fQymKk=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/objective-c/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			/e3KkwcVLZeN9J4PoaTxEgNPYIeBks9+t7gnIMrl2y8=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/objective-c/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			CIwaGX7pf6k7tw/xupSgZR6xOukhzsaiJJpW9RHBsQQ=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/objective-c/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			dVCFDwkvhdfqpCn3Fz7eofqJDtklRQh91IXIbwXVpeU=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/objective-c/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			79TdiNHJ/HMmxNJl3LMFT5YnBqaP8RNtKoXf0GGH0a8=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/objective-c/syntaxes/objective-c++.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			wNN+AnWh2e87FsRuu+E4f2D5jvjbhoMHZgt2lsFoCUE=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/objective-c/syntaxes/objective-c.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			a2XIg/uETsal+bm22Cd4GJthMALY1S2GzNfgBcjiu28=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/perl/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/perl/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			t5FSAuDiHIOlFUutzAWI39ouNZWSKcOCXY9ylM+1D8U=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/perl/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			kBLUzxqCcEFmrXTeW6k30tvnnxxoXQ3r2V6epdTVqEY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/perl/perl.language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			l8U32FogJ25g0h/y+iPKWWYQW5+47Z3ML+WTXnGNdhE=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/perl/perl6.language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			C77yJARybMDIXoFYmpjcnf5qro4p4PhSY8lJIFRqSUc=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/perl/syntaxes/perl.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			pYRA49ccOvJnyg+ugD1MnAnjI5iEjkRUhuH+ifLGXVw=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/perl/syntaxes/perl6.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			KeTiO2n3gM/SITQkxW1eCb6p8W97H6NIpotvkoZoTTg=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/php/.vscode/launch.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Mk1JkvHyARnRL6OP77iiR+jAKjDac32LIUDpO6EOjwE=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/php/.vscode/tasks.json</key>
		<dict>
			<key>hash2</key>
			<data>
			j8bkSy2fLWdHzwaUlqoBOprusnzRR8aEYtcxdknI8v4=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/php/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			Gsi2dqlIBk6xQFeX5m8KfmoYdH5woKQQhMGuUw44kKY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/php/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ZGL0SVOdZYLn+CCL8jhM56qEhvQjZr4s/+95/8tHv7E=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/php/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Xrig+TOFGYRjm0Q+oqPtwhbHC4TtH85HMEzRIBbSG3I=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/php/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			eLPY7qo8p3s8mMJU32VGR7yM7wvkgy5rtexJ/sOksRU=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/php/snippets/php.code-snippets</key>
		<dict>
			<key>hash2</key>
			<data>
			hZOlx/C16RTJDH/HexfG+tGL4UvjU1q7lnoWlcMjQyc=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/php/syntaxes/html.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			TuwA5QAXzYKz4kc+2fd0qb5rG7LeETcaC/J4XDrotPE=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/php/syntaxes/php.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			/1+AISA2PixAq05ni8cfmPyIyqNnx5AX6a/JQBu1NSI=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/powershell/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/powershell/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			TmSedk4cAs8grnPKZ1DkjKXtUF9bHJSMErZzledA4xA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/powershell/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			fPYTciwdEy1KTS7nwoLTBucsBa6p8wAyitgFQcf7WlE=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/powershell/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			AzImp+YNv6OoDQgwHiEosKNznBUiJXGbSrM2Recudpc=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/powershell/snippets/powershell.code-snippets</key>
		<dict>
			<key>hash2</key>
			<data>
			OTbMCfRjXVFriu+zoLRRvg4ykAuFFbG3eI68ELH1a0U=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/powershell/syntaxes/powershell.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Yn4uUbA0Qg0a5ZblfWnf2qA30yJAQ3Nx7oIG+z6azFM=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/pug/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/pug/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			4g+h2LQlma3QXxb5bGEkTc0chVEFEhrqaxvkO4GMl2U=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/pug/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			/1dSehLurpe2rU3i2tTu8Uuhz/M3FivVJzZ+3upKWAk=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/pug/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			8hX09SmYeG0O+u9PMQoR41Qgqj2ivQ5w50ml96NV8zk=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/pug/syntaxes/pug.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			T4voaFhBVRAuP82jouZDhl2wjzflU0vXDbbl3uJXr5k=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/python/.vscode/launch.json</key>
		<dict>
			<key>hash2</key>
			<data>
			7Hf7+N/zDMDLL01GA5yi+8xkbMk+E8rhhEhRHrhxfa4=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/python/.vscode/tasks.json</key>
		<dict>
			<key>hash2</key>
			<data>
			JPH+8eyfOaGzyH5UhgwELF0p76wN5oXDo3n/CT8nuQQ=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/python/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			13kHksb1Y3QKdlEPA+uxUoLqkaB6AxwmPb8+KVqoW1g=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/python/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			FB+TqZ6SudOZy+ThjzZ1f8a8uU33ZWB7V7FJwXSf2ig=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/python/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			pNfi3JZcW5zocn9+bnHhnucWU9l0ae0VV/jSjs5QUT0=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/python/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			I5Lv4sX79RCgMKxZyNOxHLH0UtKmlKoyCDf7511iXSQ=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/python/syntaxes/MagicPython.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ypgUMOs98AT0lVu3y6Ua0GxNhAjRaMge7HBZ19muxcg=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/python/syntaxes/MagicRegExp.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1E+IEVIb016mu59wSbnoostqBMUU3LnsNpYzqyyxOdE=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/r/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/r/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			XWem383TCax0Mx7M/y6fxo0kAgTM4UW9kd4N4aTjZLU=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/r/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			9QuhACfgsG00E5Th2o7g7UxEM6iYhLuQ6t6pvKiaIrM=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/r/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			JhfZdZ1EYNUeAWMr9RhQmNJqv+iFPH0SbYeQEEjVgZU=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/r/syntaxes/r.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			j+Q4CE9M935khoRNBlorp4jMlRH250RV4BMfG2y5PSE=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/razor/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/razor/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			PBRW6f+FnAm8QRCVIUNyvi19Tc+WlffppOSFdK7pDjQ=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/razor/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			v2C7MY+ngzvDY89wu5yluzvpjBLEInia9rgvDnfmzKA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/razor/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			gtXaNPTc6VsxibZCIJuFZSGVpbHbGd4YS2hFzyo8IFw=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/razor/syntaxes/cshtml.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			FZTbRiVyqIJMJ3G1ZKIOkdEJc/41XDdP2b4HcI7T/nk=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/restructuredtext/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/restructuredtext/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			3PndTdZkljZMaE9XL1kmfSF5u8Plu0VisGW3Ob/qZrg=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/restructuredtext/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			lqAx5ZUi20g4++13SV6F8DOWmtJLsH8NQTV0a6LKHwQ=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/restructuredtext/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Gre6Y547q7Tx54V1krLlx9crXfQjXq/itZM4xsQKYPQ=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/restructuredtext/syntaxes/rst.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			nsfn7EDLUNelQ8Fr2UjESJjK4Kq78Z6ts5UEIB+qzGk=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/ruby/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/ruby/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+08moX/oMvIALcnBmOPOJncPBV9/+3hkwJuraWL3n+U=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/ruby/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			r7zkvTgQa9dljpqOy+5vYkXCYioYt5EqUgEXBGq5AP8=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/ruby/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			kHFJ8FLjxDPocZkbVk1sMvPxJRt3Hw4Pf+7FA3FviEE=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/ruby/syntaxes/ruby.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			JBqNlTUHRs/wQhmWJWe8iHXiPbm04I8KsLTTf2QHE+E=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/rust/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/rust/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			znKwipK3J08S6rEmUwdvO93NyKB7xS/nB8GIwf252bg=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/rust/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			NQXULFjtjaQl+8bc8M6u0qUTIKu3QL/GEvYTrWGGd1A=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/rust/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			sCE/xPAzsJ0a9TyM+46aPFSZ1ehLyYPnTJouLZmv8i0=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/rust/syntaxes/rust.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			68pRA8nL1ffChF1KNr4JjT1ukp7MQlnhu0odePdhnnA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/scss/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/scss/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			A6JxOk9Otzi8sfyCnqRS6/7EDBQJ2iqvpg7lLJOSVKM=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/scss/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1XY5vEWs/edHF5HU+RSdqgDyFD+SioSBSW1QguFUZQg=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/scss/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			fBNSHxy5BWv/+MpyG3z9VCZVe1xYUgxZ7MU7eicSwzA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/scss/syntaxes/sassdoc.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			5zPZwsoC8lD/pVAXMvOhEX0vWdiK6O+qfEcVh/tyN7M=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/scss/syntaxes/scss.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			jygkqAp8b9VY/FOOxS0KekKk1+y33fINefDR8A+mYCs=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/search-result/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			5VbMa69W03BGAmbXc8oi7XANto1DKzrb2w1fxPmN2q4=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/search-result/README.md</key>
		<dict>
			<key>hash2</key>
			<data>
			xopxhGbs77f5aRgXN9UMGq21xYz1ekZXkJo1XdxSI40=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/search-result/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+Dq6OI4KHI+Ouqyk0kmICD2feEI3JwidjGJKcc4bKdM=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/search-result/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			W03aabrgUj7GLpScxM2LIVR3VKGxnUVjpoZHNPKW2AE=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/search-result/syntaxes/searchResult.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			TFYmnS/kThjUGkORL10cC9VXfKH4ifkX+n1y4GUIRQw=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/search-result/tsconfig.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ox67vi1p1A6hPCtubzhNLG92mr6ptL7T3Ky7wZKvQO4=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/shaderlab/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/shaderlab/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			aZXgW48rA79uBkwT80sEmdf+bY8WifdRxtUcV+BSxbs=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/shaderlab/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+S6L4+u9zcFMfdTuCExByRXEhJeqskMl9BDkDgedqpg=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/shaderlab/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			CPYLGWcxcdru+PGh05dxQY8Sx1s20oCxoQXrGOrDL5k=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/shaderlab/syntaxes/shaderlab.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Vvqk1d5u2gcANhGOUry+44o3d6F1QGUB+rHY6nM57I4=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/shellscript/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/shellscript/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+43sg9dLH8kohiuWb9hq2sHX+rXqqdQluoE8khaciok=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/shellscript/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ueUtzlf3FwebuH7FPNMvk12W/9e8jcQX8ffvJlxlysU=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/shellscript/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			5FWuGEARcfKz7A6fjjwA57TTQQoyemR6qXYyW8NEtXM=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/shellscript/syntaxes/shell-unix-bash.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			lVhTHFTOTleBko+16aYcUUl7L/hFl6aryq4ovJG9ISQ=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/sql/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			/e3KkwcVLZeN9J4PoaTxEgNPYIeBks9+t7gnIMrl2y8=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/sql/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			AMrsAeA5g2ATSaNnUXKlJ2KhXWfYWabJPPRiECOpcSY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/sql/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			IrwxZIzb2lA5DBr5EzvHEEWFrfDcwfcDB70nOyVmKlE=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/sql/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			zdqm8HI17hR+O6Mz3KzwVgnnmxJ7KZTxqsh0uSbAdss=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/sql/syntaxes/sql.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ymrcjn2b/0XaFoGqNDIHcDMpbO7zZTUSIqykzFtHzuA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/swift/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/swift/LICENSE.md</key>
		<dict>
			<key>hash2</key>
			<data>
			0IfsuYfkqgZ4sKvov+8tlYN2eOVCGZDwEsP3I8nlISA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/swift/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			4ZIa2z5EWGLkZOdV7ECLsJI14k8/qpK657rdwEe0PUY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/swift/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			XftCe6Zaa1EEFwZadr9URneTK47CcKmq3dnaPaOFUBE=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/swift/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			tMRtxYTMSUKGspH9KvaofPN5KAYBnTtu7MJ1LJ6OMTI=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/swift/snippets/swift.code-snippets</key>
		<dict>
			<key>hash2</key>
			<data>
			YYHn0C8uSXZvAzUDSIxCuSwnkVXzTVue8TTa2Qu19EE=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/swift/syntaxes/swift.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			KXd82hx1KNI/A1Hh9iafYNoAw6dJW8C7MZyXM0wfVGk=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/terraform/LICENSE</key>
		<dict>
			<key>hash2</key>
			<data>
			Yv2vdT4ZwArGnuAN/ZtQAhJKwrQXk2DkcWhCmtN/pfg=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/terraform/README.md</key>
		<dict>
			<key>hash2</key>
			<data>
			cQB3xWqrQ2bgSXNqscugwqfm8yDrhjhXL+iXS2b3zB8=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/terraform/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ig3X0wBA+zJUfsYI02TpD9pp56SSjpFCgTynCMiny90=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/terraform/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			WHMd7p3TC/gKaBd08vPem7ZZrpqEWIXWl7vkigRzphU=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/terraform/snippets/terraform.json</key>
		<dict>
			<key>hash2</key>
			<data>
			r7hFt/DewdQlBeIeKvszxD1xeAtTeb5VMGZviXCSQMw=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/terraform/syntaxes/hcl.tmGrammar.json</key>
		<dict>
			<key>hash2</key>
			<data>
			o5ytZgVWkDEdQZiuOUFguvAUWFnGPT8OQpnw9N2PEY4=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/terraform/syntaxes/terraform.tmGrammar.json</key>
		<dict>
			<key>hash2</key>
			<data>
			+WumwdFBZCpGhZwUQxAVjonHYYDEvAmK+nuOq/kEqtQ=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/twig/LICENSE.md</key>
		<dict>
			<key>hash2</key>
			<data>
			zXM1djsAyimuNjeon6M0+fvYRTueKs7178TUrouLCnw=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/twig/README.md</key>
		<dict>
			<key>hash2</key>
			<data>
			IifPDcONX9Q5MfCkBwbunb3eBGY4WOKu81zmU9OcitQ=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/twig/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			A54cYF3gn5VqgD7p9/rTlsxKohCN22zwMqb9QaxNzhk=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/twig/snippets/snippets.json</key>
		<dict>
			<key>hash2</key>
			<data>
			SS8ZVKoAJ7N/f2jchDG8f3qav+l4OWwjrnh8nw7ZRCw=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/twig/src/extension.js</key>
		<dict>
			<key>hash2</key>
			<data>
			Pk4Wkbl1vuKsyjJ5xyD+6hUtTjXsMkl4qob82h1nuFo=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/twig/src/hover/filters.json</key>
		<dict>
			<key>hash2</key>
			<data>
			qN1+GtRl+rjZiRt+qg3pcS2z6GfStNBGhJAtYl+NUoA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/twig/src/hover/functions.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Fr67lB8cHzRqKIjrkADHcW4e7NDibqUKqdrqhWBz1LA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/twig/src/hover/twig.json</key>
		<dict>
			<key>hash2</key>
			<data>
			E2h3Q1A7qLqAcQ/88fcwtpoUapJ3YdK14F/yAKdLXYs=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/twig/src/languages/twig.configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			kc4bGRS3kqfrvPnkbYw91XL98G33S6s0S/nIeJTZuRw=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/twig/src/snippets/snippets.json</key>
		<dict>
			<key>hash2</key>
			<data>
			SS8ZVKoAJ7N/f2jchDG8f3qav+l4OWwjrnh8nw7ZRCw=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/twig/src/syntaxes/twig.tmLanguage</key>
		<dict>
			<key>hash2</key>
			<data>
			+heb3poQSqgeMDmqFywjpiCBYQVqbjul2lbFnmlT6bg=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/typescript-basics/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			I1GXyUVAGj53zpmXzhYecDb77CbCIVXZic0+MSM/IDc=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/typescript-basics/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			jN4OTDJG2Wi8BUjl8GmYC56ydMmWOCCq+k1z0LMK0XQ=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/typescript-basics/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			V9jjOom3RkjfkFZI0+UpRdCocECsrMAcDHUobS4RSco=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/typescript-basics/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			sReKzzZizQcE1WTsYud/MbPBpkOTVHuzXsB0gDB3994=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/typescript-basics/snippets/typescript.code-snippets</key>
		<dict>
			<key>hash2</key>
			<data>
			hvjjMD8FdUZEaqrFodgWW7pMutQB9yJAP/UAuGSyv3U=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/typescript-basics/syntaxes/Readme.md</key>
		<dict>
			<key>hash2</key>
			<data>
			r/tld1/V4cbqVDrzBTiz1HVpQDsMRKnqWtW5l6sQuV0=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/typescript-basics/syntaxes/TypeScript.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			IKm4sQrDvn+/iz9POiVcOgVcvQ4sLwyt8MM07InDMWo=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/typescript-basics/syntaxes/TypeScriptReact.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			2ys0ndVLPJ52m5iabA06yRmiJVOY98j882ueRW7Bv30=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/typescript-basics/syntaxes/jsdoc.js.injection.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			wR/eXJuy2ba9b6qtUfOYsSdDipOzBHZBonBVKnYOdrY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/typescript-basics/syntaxes/jsdoc.ts.injection.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			UmB2r1PzMDWK4/UpOnd5rA29GdXvJ7ce/9lPru9PEkk=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/vb/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/vb/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ZQsJoMs/dB5a/L3gNRsBjwlZd3JK5vRDH7eoFjis5qA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/vb/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			le5FZe3fQqHEkJ9wnGPGnf/d5NtgB9gUVebi9Zc1WZs=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/vb/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			rhDNApPUiv/nk3ZZiO46hAE/jklPzr1XNfWiWWf7oC0=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/vb/snippets/vb.code-snippets</key>
		<dict>
			<key>hash2</key>
			<data>
			QLtzJh9VXcHo7u9jY31q2+7jSLBVKFDt1uXt6n5rPXI=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/vb/syntaxes/asp-vb-net.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			1QcgfvC73KU26fmy0+QYW7zUzYgZyW4flBFBkloK19w=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/viml/LICENSE.txt</key>
		<dict>
			<key>hash2</key>
			<data>
			km1gEUYEbBCozaas4P+Q8JHLOyKU4lPNjgFyZ77B0/E=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/viml/grammars/viml.json</key>
		<dict>
			<key>hash2</key>
			<data>
			ZG0E4iHXRcRUAljG+tm0BrzUs5hIv0Sxl8/kd1ldwvw=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/viml/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			nJnyXRb2SBhe6UiEvvzddkt5Y0RUVxuwafXdQsr6EMs=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/xml/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/xml/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			3ZrOTy99swWXQQLtB6zXHsII2qHt5iqGuzoDgt1x7Tw=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/xml/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			HZtnxXbv/SQXfMqD/ckEktbKek6ELC7WK1f28H+gHpI=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/xml/syntaxes/xml.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			u1HHsgLyAlR3LIj4aIng283HNOBFuVD+JcCiO1kSA9c=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/xml/syntaxes/xsl.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			O3ZsUnO9jvesuFws/SJ2ksD3WzXSYlhNdVnJpUMhmxI=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/xml/xml.language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			OFokqdpO2JNm1pGIHh382smE3mrH6AV3at1D7Rcx+nw=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/xml/xsl.language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			GyYTKWU0j+6j38YajS1rR3R2KWfuktAiCGQvQoyE/vA=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/yaml/.vscodeignore</key>
		<dict>
			<key>hash2</key>
			<data>
			8b2d90L760UZ0teDKXqliyYmKS8QpBLCkvtStK07LlY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/yaml/language-configuration.json</key>
		<dict>
			<key>hash2</key>
			<data>
			MGyPaVHd3GIU/4B2YSOgMV/GN3OgvSkJQjlo+nErSiY=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/yaml/package.json</key>
		<dict>
			<key>hash2</key>
			<data>
			/Z/3G9kDlcHRvlcn1bQDHChzSvX4XYhJCsNBthqZQbw=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/yaml/package.nls.json</key>
		<dict>
			<key>hash2</key>
			<data>
			2iHo0S0uizM8RnEs3Uly/KXlVqDdORe06YSp0UQ1N0A=
			</data>
		</dict>
		<key>plugins/textmate/lib/bundles/yaml/syntaxes/yaml.tmLanguage.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Dp42aaZbXR+Vc5iET1DublHnE/Whj3guxFjN+66/G7Y=
			</data>
		</dict>
		<key>plugins/textmate/lib/textmate.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			m61v6JC2fH2MVL2Wls62i0US1c6PZt+CVfup3I/wNZ4=
			</data>
		</dict>
		<key>plugins/toml/lib/toml-frontend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			9UdP4a0UKX7Hh3f3hg7xvPGcp3LTxPmfs3LJQF6TA2g=
			</data>
		</dict>
		<key>plugins/toml/lib/toml.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			QVruQjBT8Wzjju1NXtG3B4A0v8hn7oDaJsBWhLh1VOU=
			</data>
		</dict>
		<key>plugins/turboComplete/lib/modules/intellij.turboComplete.languages.kotlin.k1.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			r1hG42MQ0+GYyls5euIcGEDVbc9+b9s7RX2ooSTwz5E=
			</data>
		</dict>
		<key>plugins/turboComplete/lib/turboComplete.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			wwrTTAY0BvLjpdNyyjsGnEwd6PbrJ5L5LJ4yYiy2fP8=
			</data>
		</dict>
		<key>plugins/vcs-git-commit-modal/lib/vcs-git-commit-modal.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			OUCb6ivQW0Ifk/xsTUPesycOknG5uhwKGfbwZSE3RB4=
			</data>
		</dict>
		<key>plugins/vcs-git/lib/git4idea-rt.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			Fsn0/cclLLrbvyndt7B5TdKdWwKP8rzsQRT9NHUBt9E=
			</data>
		</dict>
		<key>plugins/vcs-git/lib/vcs-git.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			I2nYk/TXyyTryt1iirJWmVvONEoZfFtLRiqSwgeQz/E=
			</data>
		</dict>
		<key>plugins/vcs-github-IC/lib/vcs-github.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			u80g1JLJ0gQSbKkZHloZ9otriooecYfzpAZkZB1FoKM=
			</data>
		</dict>
		<key>plugins/vcs-gitlab-IC/lib/vcs-gitlab.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			7HG1zYcnn/qp+cSEEk7nf6pl17o8YirMAi1D1MZSidE=
			</data>
		</dict>
		<key>plugins/vcs-hg/lib/vcs-hg.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			6icMn2SS7GGeESxbUMf0eoxeLFUhLtzLziKUJFVIM90=
			</data>
		</dict>
		<key>plugins/vcs-perforce/lib/vcs-perforce.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			yUiqozpa9nzJPl1l/eqQvuSwtocT8Z1RiwNvHL7/vqs=
			</data>
		</dict>
		<key>plugins/vcs-svn/lib/vcs-svn.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			pBF/9yWJoh7BnuI3wU1u50jgyD0KSjje1W8ngNZJaC4=
			</data>
		</dict>
		<key>plugins/webp/lib/libwebp/mac/libwebp_jni.dylib</key>
		<dict>
			<key>hash2</key>
			<data>
			Rs15NI74ZtwPRrGWpWCOSrYoXR2UDRfwCnSIf6rzcbg=
			</data>
		</dict>
		<key>plugins/webp/lib/webp.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			VsEHUUwp2mEi1vpbMVBt8EA1knxRZ1X9rFpZcNBHnzU=
			</data>
		</dict>
		<key>plugins/yaml/lib/modules/intellij.yaml.backend.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			q5vuV8q2siuYZTUYIjD41+WSRO/Fn77IKRY2mCOhmSk=
			</data>
		</dict>
		<key>plugins/yaml/lib/modules/intellij.yaml.frontend.split.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			AXT+qnEaVwj7kWEDDUFB9opxPWY5hoAt7VV0DzNjIDk=
			</data>
		</dict>
		<key>plugins/yaml/lib/yaml.jar</key>
		<dict>
			<key>hash2</key>
			<data>
			prOpwebEppNXL0+TrLVC7kEP0e9CAPHz5IncI7esej4=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^Resources/</key>
		<true/>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^(Frameworks|SharedFrameworks|PlugIns|Plug-ins|XPCServices|Helpers|MacOS|Library/(Automator|Spotlight|LoginItems))/</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^Resources/.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^Resources/.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Resources/Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^[^/]+$</key>
		<dict>
			<key>nested</key>
			<true/>
			<key>weight</key>
			<real>10</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
